import React from 'react'
import { CheckCircle, AlertTriangle, DollarSign, FileText } from 'lucide-react'
import { SumarioAuditoria } from '@/services/auditoriaService'

interface ResumoAuditoriaTributoProps {
  sumario: SumarioAuditoria | null
  isLoading?: boolean
  className?: string
}

export function ResumoAuditoriaTributo({ 
  sumario, 
  isLoading = false, 
  className = '' 
}: ResumoAuditoriaTributoProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value)
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('pt-BR').format(value)
  }

  if (isLoading) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="animate-pulse">
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (!sumario) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <div className="text-gray-400 dark:text-gray-500 text-sm">
          <FileText className="w-4 h-4 mx-auto mb-2 opacity-50" />
          <p className="font-medium">Sem dados de auditoria</p>
          <p className="text-xs mt-1">Execute a auditoria para ver o resumo</p>
        </div>
      </div>
    )
  }

  const totalRegistros = sumario.total_conforme + sumario.total_inconsistente
  const percentualConformidade = totalRegistros > 0
    ? (sumario.total_conforme / totalRegistros) * 100
    : 0

  const valorTotalInconsistencias = sumario.valor_inconsistente_maior + sumario.valor_inconsistente_menor

  // Se não há registros auditados, mostrar estado vazio
  if (totalRegistros === 0 && sumario.total_notas === 0) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <div className="text-gray-400 dark:text-gray-500 text-sm">
          <FileText className="w-4 h-4 mx-auto mb-2 opacity-50" />
          <p className="font-medium">Auditoria não executada</p>
          <p className="text-xs mt-1">Execute a auditoria para ver o resumo</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Estatísticas principais */}
      <div className="grid grid-cols-2 gap-2 text-xs">
        {/* Conformidade */}
        <div className="flex items-center gap-1">
          <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
          <div className="min-w-0">
            <p className="text-green-600 dark:text-green-400 font-medium truncate">
              {formatNumber(sumario.total_conforme)} conformes
            </p>
            <p className="text-gray-500 dark:text-gray-400 text-xs">
              {percentualConformidade.toFixed(1)}%
            </p>
          </div>
        </div>

        {/* Inconsistências */}
        <div className="flex items-center gap-1">
          <AlertTriangle className="w-3 h-3 text-red-500 flex-shrink-0" />
          <div className="min-w-0">
            <p className="text-red-600 dark:text-red-400 font-medium truncate">
              {formatNumber(sumario.total_inconsistente)} inconsist.
            </p>
            <p className="text-gray-500 dark:text-gray-400 text-xs">
              {(100 - percentualConformidade).toFixed(1)}%
            </p>
          </div>
        </div>
      </div>

      {/* Valores financeiros */}
      <div className="space-y-1">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-600 dark:text-gray-400">Valor auditado:</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {formatCurrency(sumario.valor_total_cenarios)}
          </span>
        </div>
        
        {valorTotalInconsistencias > 0 && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-red-600 dark:text-red-400">Inconsistências:</span>
            <span className="font-medium text-red-600 dark:text-red-400">
              {formatCurrency(valorTotalInconsistencias)}
            </span>
          </div>
        )}
      </div>

      {/* Barra de progresso visual */}
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
        <div
          className="bg-green-500 h-1.5 rounded-full transition-all duration-300"
          style={{ width: `${percentualConformidade}%` }}
        ></div>
      </div>

      {/* Informações adicionais */}
      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
        <span>{formatNumber(sumario.total_notas)} notas</span>
        <span>{formatNumber(sumario.total_produtos)} produtos</span>
      </div>
    </div>
  )
}

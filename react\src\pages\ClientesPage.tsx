import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { clientesService, type ClienteFilters } from '@/services/clientesService'
import { ClienteModal } from '@/components/clientes/ClienteModal'
import { ClienteBulkEditModal } from '@/components/clientes/ClienteBulkEditModal'
import { ParticipantesStats } from '@/components/clientes/ParticipantesStats'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import type { Cliente } from '@/types/clientes'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'
import { Input } from '@/components/ui/Input'
import { Dropdown } from '@/components/ui/Dropdown'
import { HelpButton, HelpModal } from '@/components/ui'

const DEFAULT_ATIVIDADES = [
  'Serviço',
  'Não Contribuinte',
  'Indústria ou Equiparado',
  'Comércio Varejista',
  'Comércio Atacadista',
  'Distribuidor',
  'Produtor Rural',
  'Consumidor Final',
  'Órgão Público'
]

const DEFAULT_DESTINACOES = [
  'Revenda',
  'Industrialização',
  'Uso e Consumo',
  'Varejista',
  'Ativo Imobilizado'
]

export function ClientesPage() {
  const empresaId = useSelectedCompany()

  const [filters, setFilters] = useState<ClienteFilters>({})
  const [appliedFilters, setAppliedFilters] = useState<ClienteFilters>({})
  const [municipios, setMunicipios] = useState<string[]>([])
  const [page, setPage] = useState(1)
  const [perPage, setPerPage] = useState(20)
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedCliente, setSelectedCliente] = useState<Cliente | null>(null)
  const [selectedIds, setSelectedIds] = useState<number[]>([])
  const [bulkModalOpen, setBulkModalOpen] = useState(false)
  const [helpOpen, setHelpOpen] = useState(false)

  // Carrega opções de filtro
  const { data: options } = useQuery({
    queryKey: ['clientes-options', empresaId],
    queryFn: () => clientesService.getFilterOptions(empresaId),
    enabled: !!empresaId
  })

  // Atualiza municípios quando UF muda
  useEffect(() => {
    if (filters.uf && empresaId) {
      clientesService.getMunicipios(empresaId, filters.uf).then(setMunicipios)
    } else {
      setMunicipios([])
    }
  }, [filters.uf, empresaId])

  // Busca clientes
  const {
    data: clientesData,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ['clientes', empresaId, appliedFilters, page, perPage],
    queryFn: () =>
      clientesService.getClientes(empresaId, { ...appliedFilters, page, perPage }),
    enabled: !!empresaId
  })

  const clientes = clientesData?.clientes || []
  const pagination = clientesData?.pagination

  const pageNumbers = () => {
    if (!pagination) return []
    const maxLinks = 5
    let start = Math.max(1, pagination.current_page - Math.floor(maxLinks / 2))
    let end = Math.min(pagination.last_page, start + maxLinks - 1)
    if (end - start + 1 < maxLinks) {
      start = Math.max(1, end - maxLinks + 1)
    }
    const nums: number[] = []
    for (let i = start; i <= end; i++) nums.push(i)
    return nums
  }

  const handleApplyFilters = () => {
    setAppliedFilters(filters)
    setPage(1)
  }

  const handleClearFilters = () => {
    setFilters({})
    setAppliedFilters({})
    setPage(1)
  }

  const handleDelete = async (cliente: Cliente) => {
    if (!confirm(`Excluir o participante ${cliente.razao_social}?`)) return
    await clientesService.deleteCliente(cliente.id)
    refetch()
  }

  const toggleSelect = (id: number) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
    )
  }

  const handleSelectAll = () => {
    if (selectedIds.length === clientes.length) {
      setSelectedIds([])
    } else {
      setSelectedIds(clientes.map((c) => c.id))
    }
  }

  const openBulkEdit = () => {
    setBulkModalOpen(true)
  }

  const closeBulkEdit = () => {
    setBulkModalOpen(false)
  }

  const handleBulkSaved = () => {
    setSelectedIds([])
    refetch()
  }

  const handleFilterByAtividade = (atividade: string) => {
    setFilters({ ...filters, atividade })
    handleApplyFilters()
  }

  const handleFilterByDestinacao = (destinacao: string) => {
    setFilters({ ...filters, destinacao })
    handleApplyFilters()
  }

  const handleFilterByAtividadeDestinacao = (atividade: string, destinacao: string) => {
    setFilters({ ...filters, atividade, destinacao })
    handleApplyFilters()
  }

  const openNewModal = () => {
    setSelectedCliente(null)
    setModalOpen(true)
  }

  const openEditModal = (cliente: Cliente) => {
    setSelectedCliente(cliente)
    setModalOpen(true)
  }

  const closeModal = () => {
    setModalOpen(false)
    setSelectedCliente(null)
  }

  const handleSaved = () => {
    refetch()
  }

  return (
    <div className="space-y-6">
      <div className="flex items-start justify-between">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Participantes
            </h1>
            <HelpButton onClick={() => setHelpOpen(true)} aria-label="Ajuda" />
          </div>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Gerencie clientes, fornecedores e demais participantes
          </p>
        </div>
        <Button
          variant="primary"
          size="md"
          onClick={openNewModal}
          icon={
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
          }
          glow
        >
          Novo Participante
        </Button>
      </div>

      {/* Estatísticas de Participantes */}
      <ParticipantesStats
        empresaId={empresaId}
        onFilterByAtividade={handleFilterByAtividade}
        onFilterByDestinacao={handleFilterByDestinacao}
        onFilterByAtividadeDestinacao={handleFilterByAtividadeDestinacao}
      />

      {/* Filtros Modernos */}
      <Card gradient>
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 p-6 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Filtros Avançados
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Refine sua busca por localização, atividade e outros critérios
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Estado</label>
              <div className="relative z-20">
                <Dropdown
                  options={[
                    { value: '', label: 'Todos os Estados' },
                    ...(options?.ufs || []).map(uf => ({ value: uf, label: uf }))
                  ]}
                  value={filters.uf || ''}
                  onChange={(value) => setFilters({ ...filters, uf: value as string })}
                  placeholder="Todos os Estados"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Município</label>
              <div className="relative z-10">
                <Dropdown
                  options={[
                    { value: '', label: 'Todos os Municípios' },
                    ...municipios.map(m => ({ value: m, label: m }))
                  ]}
                  value={filters.municipio || ''}
                  onChange={(value) => setFilters({ ...filters, municipio: value as string })}
                  disabled={!filters.uf}
                  placeholder="Todos os Municípios"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Atividade</label>
              <div className="relative z-10">
                <Dropdown
                  options={[
                    { value: '', label: 'Todas as Atividades' },
                    ...(options?.atividades || DEFAULT_ATIVIDADES).map(a => ({ value: a, label: a }))
                  ]}
                  value={filters.atividade || ''}
                  onChange={(value) => setFilters({ ...filters, atividade: value as string })}
                  placeholder="Todas as Atividades"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Destinação</label>
              <div className="relative z-10">
                <Dropdown
                  options={[
                    { value: '', label: 'Todas as Destinações' },
                    ...(options?.destinacoes || DEFAULT_DESTINACOES).map(d => ({ value: d, label: d }))
                  ]}
                  value={filters.destinacao || ''}
                  onChange={(value) => setFilters({ ...filters, destinacao: value as string })}
                  placeholder="Todas as Destinações"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">CNAE</label>
              <Input
                type="text"
                value={filters.cnae || ''}
                onChange={(e) => setFilters({ ...filters, cnae: e.target.value })}
                placeholder="Digite o CNAE"
              />
            </div>
          </div>

          <div className="mt-6 flex flex-wrap gap-3">
            <Button
              variant="primary"
              size="md"
              onClick={handleApplyFilters}
              icon={
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              }
              glow
            >
              Aplicar Filtros
            </Button>
            <Button
              variant="secondary"
              size="md"
              onClick={handleClearFilters}
              icon={
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
              }
            >
              Limpar Filtros
            </Button>
          </div>
        </div>
      </Card>

      {/* Tabela */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-wrap items-center gap-3">
          <Button
            variant="secondary"
            size="sm"
            onClick={handleSelectAll}
            icon={
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            }
          >
            {selectedIds.length === clientes.length && clientes.length > 0
              ? 'Desmarcar Todos'
              : 'Selecionar Todos'}
          </Button>
          <Button
            variant="warning"
            size="sm"
            onClick={openBulkEdit}
            disabled={selectedIds.length === 0}
            icon={
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
            }
          >
            Edição em Massa ({selectedIds.length})
          </Button>
          {selectedIds.length > 0 && (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {selectedIds.length} participante(s) selecionado(s)
            </div>
          )}
        </div>
        {isLoading ? (
          <div className="p-6 text-center">
            <LoadingSpinner className="w-6 h-6 mx-auto" />
          </div>
        ) : (
          <TableScrollContainer>
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-4 py-2">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 border-gray-300 rounded"
                    onChange={handleSelectAll}
                    checked={selectedIds.length === clientes.length && clientes.length > 0}
                  />
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  CNPJ / CPF
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Razão Social
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Município
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Atividade
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Destinação
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  CNAE
                </th>
                <th className="px-4 py-2"></th>
              </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {clientes.map((cliente) => (
                <tr key={cliente.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td className="px-4 py-2">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 border-gray-300 rounded"
                      checked={selectedIds.includes(cliente.id)}
                      onChange={() => toggleSelect(cliente.id)}
                    />
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {cliente.cnpj}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {cliente.razao_social}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {cliente.uf || '-'}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {cliente.municipio || '-'}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {cliente.atividade || '-'}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {cliente.destinacao || '-'}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {cliente.cnae || '-'}
                  </td>
                  <td className="px-4 py-2 text-right text-sm">
                    <div className="flex items-center justify-end gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditModal(cliente)}
                        icon={
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                        }
                        className="text-blue-600 hover:text-blue-800 hover:bg-primary-50 dark:hover:bg-primary-900/20 p-1"
                        aria-label="Editar"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(cliente)}
                        icon={
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd" />
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                        }
                        className="text-danger-600 hover:text-danger-800 hover:bg-danger-50 dark:hover:bg-danger-900/20 p-1"
                        aria-label="Excluir"
                      />
                    </div>
                  </td>
                </tr>
              ))}
              </tbody>
            </table>
          </TableScrollContainer>
        )}

        {/* Paginação Moderna */}
        {pagination && (
          <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Mostrando {pagination.from} a {pagination.to} de {pagination.total} participantes
            </div>
            <div className="flex items-center space-x-2">
              <Dropdown
                className="w-40"
                options={[20, 50, 100, 200, 300].map((n) => ({
                  value: n.toString(),
                  label: `${n} por página`
                }))}
                value={perPage.toString()}
                onChange={(value) => {
                  setPerPage(parseInt(value as string))
                  setPage(1)
                }}
              />
              <nav className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={!pagination.has_prev}
                  onClick={() => setPage(1)}
                  className="px-2 py-1"
                >
                  «
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={!pagination.has_prev}
                  onClick={() => setPage(pagination.prev_page || 1)}
                  className="px-2 py-1"
                >
                  ‹
                </Button>
                {pageNumbers().map((n) => (
                  <Button
                    key={n}
                    variant={n === pagination.current_page ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setPage(n)}
                    className="px-2 py-1"
                  >
                    {n}
                  </Button>
                ))}
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={!pagination.has_next}
                  onClick={() => setPage(pagination.next_page || pagination.last_page)}
                  className="px-2 py-1"
                >
                  ›
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={!pagination.has_next}
                  onClick={() => setPage(pagination.last_page)}
                  className="px-2 py-1"
                >
                  »
                </Button>
              </nav>
            </div>
          </div>
        )}
      </div>

      <ClienteModal
        cliente={selectedCliente}
        isOpen={modalOpen}
        onClose={closeModal}
        onSaved={handleSaved}
        empresaId={empresaId}
        atividades={options?.atividades || DEFAULT_ATIVIDADES}
        destinacoes={options?.destinacoes || DEFAULT_DESTINACOES}
        ufs={options?.ufs}
      />
      <ClienteBulkEditModal
        isOpen={bulkModalOpen}
        onClose={closeBulkEdit}
        onSaved={handleBulkSaved}
        selectedIds={selectedIds}
        atividades={options?.atividades || DEFAULT_ATIVIDADES}
        destinacoes={options?.destinacoes || DEFAULT_DESTINACOES}
      />
      <HelpModal
        isOpen={helpOpen}
        onClose={() => setHelpOpen(false)}
        title="Ajuda - Participantes"
      >
        <div className="space-y-4 text-gray-700 dark:text-gray-300">
          <p>
            Os filtros permitem refinar a lista de participantes por estado,
            município, atividade, destinação e CNAE. Ajuste os critérios e
            clique em "Aplicar Filtros" para atualizar a tabela.
          </p>
          <p>
            <strong>Atividade/Destinação:</strong> a atividade indica o tipo de
            atuação do participante (como serviço ou comércio) e a destinação
            mostra o propósito das mercadorias (como revenda ou uso próprio).
          </p>
          <p>
            <strong>Edição em Massa:</strong> selecione vários participantes e
            utilize o botão "Edição em Massa" para alterar campos comuns de
            todos simultaneamente.
          </p>
          <p>
            As colunas da tabela exibem CNPJ/CPF, Razão Social, Estado,
            Município, Atividade, Destinação e CNAE de cada participante.
          </p>
        </div>
      </HelpModal>
    </div>
  )
}
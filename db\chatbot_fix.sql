-- =====================================================
-- CORREÇÃO PARA CHATBOT IA
-- Sistema de Auditoria Fiscal
-- =====================================================

-- Definir encoding
SET client_encoding = 'UTF8';

-- Remover tabelas se existirem (para recriar limpas)
DROP TABLE IF EXISTS chatbot_conversas CASCADE;
DROP TABLE IF EXISTS chatbot_templates CASCADE;
DROP MATERIALIZED VIEW IF EXISTS vw_chatbot_dados_completos CASCADE;

-- Tabela para armazenar conversas do chatbot
CREATE TABLE chatbot_conversas (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL REFERENCES usuario(id),
    empresa_id INTEGER REFERENCES empresa(id),
    escritorio_id INTEGER REFERENCES escritorio(id),
    pergunta TEXT NOT NULL,
    resposta TEXT NOT NULL,
    contexto_sql TEXT,
    dados_utilizados JSONB,
    tempo_resposta INTEGER,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    avaliacao INTEGER CHECK (avaliacao >= 1 AND avaliacao <= 5),
    feedback TEXT
);

-- Tabela para templates de perguntas
CREATE TABLE chatbot_templates (
    id SERIAL PRIMARY KEY,
    categoria VARCHAR(50) NOT NULL,
    pergunta_template TEXT NOT NULL,
    sql_template TEXT NOT NULL,
    descricao TEXT,
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índices básicos
CREATE INDEX idx_chatbot_conversas_usuario ON chatbot_conversas(usuario_id);
CREATE INDEX idx_chatbot_conversas_empresa ON chatbot_conversas(empresa_id);
CREATE INDEX idx_chatbot_conversas_data ON chatbot_conversas(data_criacao);

-- Inserir alguns templates básicos
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao) VALUES
('nota_fiscal', 'nota {numero}', 
 'SELECT nfi.*, e.nome as empresa_nome FROM nota_fiscal_item nfi JOIN empresa e ON nfi.empresa_id = e.id WHERE nfi.numero_nf = ''{numero}''',
 'Busca informações de uma nota fiscal específica'),

('empresa', 'empresa {nome}',
 'SELECT * FROM empresa WHERE nome ILIKE ''%{nome}%''',
 'Busca informações de uma empresa'),

('auditoria', 'auditoria inconsistente',
 'SELECT ar.* FROM auditoria_resultado ar WHERE ar.status = ''inconsistente'' LIMIT 10',
 'Busca inconsistências de auditoria');

-- Comentários
COMMENT ON TABLE chatbot_conversas IS 'Histórico de conversas do chatbot IA';
COMMENT ON TABLE chatbot_templates IS 'Templates de perguntas para o chatbot';

-- Conceder permissões (ajuste conforme necessário)
-- GRANT ALL PRIVILEGES ON chatbot_conversas TO seu_usuario;
-- GRANT ALL PRIVILEGES ON chatbot_templates TO seu_usuario;

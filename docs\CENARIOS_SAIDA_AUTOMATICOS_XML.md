# Documentação da Criação Automática de Cenários de Saída

Este documento explica detalhadamente como o sistema cria cenários fiscais automaticamente para **notas de saída** durante a importação de arquivos XML. O foco é a geração de cenários para ICMS, ICMS-ST, IPI, PIS, COFINS e DIFAL somente quando a nota é classificada como saída (tipo_nota = "1").

## 1. Detecção do Tipo de Nota

- O serviço `XMLImportService` determina se o XML é de **entrada** ou **saída** comparando os documentos do emitente/destinatário com o CNPJ da empresa. Caso o CNPJ da empresa seja encontrado no emitente e `tpNF` seja `1`, a nota é tratada como saída; se a empresa é destinatária ou `tpNF` é `0`, é tratada como entrada.
- Essa lógica garante que cenários sejam criados apenas para notas de saída, conforme mostrado nas correções realizadas no arquivo `CORRECOES_IMPLEMENTADAS.md`:

```text
15 - Alterada condição de criação de cenários de `tributo.tipo_operacao == '1'` para `tipo_nota == '1'`
```
【F:docs/CORRECOES_IMPLEMENTADAS.md†L5-L16】

## 2. Processamento de Produtos e Tributos

- Para cada produto do XML é criado um registro em `nota_fiscal_item` e `tributo`.
- Dentro do método `_process_tributo` do `XMLImportService`, a criação de cenários é condicionada ao tipo da nota. Apenas quando `tipo_nota == '1'` os cenários são gerados:

```python
# Criar cenários apenas para notas de saída (tipo_nota = '1')
if tipo_nota == '1':
    ...
```
【F:back/services/xml_import_service.py†L654-L657】

## 3. Chamada do `CenarioService`

- Quando a nota é de saída, o serviço instância `CenarioService` e chama `criar_cenario_importacao` para cada tributo relevante. Um exemplo simplificado para ICMS é:

```python
icms_data = {
    'origem': tributo.icms_origem,
    'cst': tributo.icms_cst,
    'mod_bc': tributo.icms_mod_bc,
    'p_red_bc': tributo.icms_p_red_bc,
    'aliquota': tributo.icms_aliquota,
    'p_dif': tributo.icms_p_dif,
    'direcao': 'saida',
    'tipo_operacao': tributo.tipo_operacao,
    'cfop': cfop,
    'ncm': ncm
}
cenario_icms = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'icms', icms_data)
if cenario_icms:
    tributo.cenario_icms_id = cenario_icms.id
```
【F:back/services/xml_import_service.py†L658-L675】

- O mesmo fluxo se repete para ICMS-ST, IPI, PIS, COFINS e DIFAL, salvando os IDs dos cenários correspondentes no tributo.

## 4. Regras de Criação e Atualização de Cenários

Ao chamar `criar_cenario_importacao`, o `CenarioService` verifica se já existe
um cenário para a mesma combinação empresa, cliente, produto e CFOP. As regras
principais são:

1. **Cenário existente em produção**
   - Se o NCM ou outros campos relevantes forem diferentes, é criado um novo
     cenário com status `incons_*` para análise posterior. O trecho de código
     responsável está no método `criar_cenario_importacao`:

```python
if cenario_existente.status == 'producao':
    if cenario_existente.ncm != ncm:
        suffix = str(int(time.time()))[-5:]
        status = f"incons_{suffix}"
        cenario = self._criar_cenario(cliente_id, produto_id, tipo_tributo,
                                     tributo_data, status)
        return cenario
```
【F:back/services/cenario_service.py†L53-L63】

   - Para IPI são comparados CST, alíquota e `ex`; para ICMS, ICMS-ST, PIS,
     COFINS e DIFAL são usadas funções de verificação específicas. Qualquer
     divergência também gera um novo cenário `incons_*`.
【F:back/services/cenario_service.py†L65-L116】

2. **Cenário existente não produtivo ou igual**
   - Quando os valores são idênticos ou o cenário não está em produção, os dados
     do cenário são apenas atualizados.
【F:back/services/cenario_service.py†L118-L126】

3. **CFOP inexistente**
   - Se não houver cenário com o mesmo CFOP, um novo cenário é criado com status
     `novo`.
【F:back/services/cenario_service.py†L128-L131】

## 5. Resultado Esperado

- Em notas de **entrada**, nenhum cenário é criado. Em notas de **saída**, todos os cenários necessários são gerados automaticamente. Essa regra está descrita na seção de testes em `CORRECOES_IMPLEMENTADAS.md`:

```text
56 1. Importar um XML de entrada
58 3. RESULTADO ESPERADO: Nenhum cenário criado

60 1. Importar um XML de saída
62 2. Verificar que `importacao_xml.tipo_nota = '1'`
63 3. RESULTADO ESPERADO: Cenários criados normalmente
```
【F:docs/CORRECOES_IMPLEMENTADAS.md†L56-L63】

- A otimização desse processo também é mencionada em `OTIMIZACAO_IMPORTACAO_XML.md`, destacando que cenários são criados apenas para notas de saída, reduzindo em cerca de 50% o volume de cenários:

```text
236 ### 2. Otimização na Criação de Cenários ✅
238 #### Cenários apenas para Saída:
239 - ANTES: Criava cenários para entrada e saída
240 - DEPOIS: Apenas para notas de saída (tipo_operacao = '1')
241 - MOTIVO: Notas de entrada são apenas para controle
242 - IMPACTO: Redução de ~50% na criação de cenários
```
【F:docs/OTIMIZACAO_IMPORTACAO_XML.md†L230-L242】

## 6. Resumo do Fluxo

1. **Leitura do XML** pelo `XMLProcessor`.
2. **Identificação do tipo de nota** (entrada/saída).
3. **Processamento de clientes e produtos**.
4. **Criação de tributo** para cada item.
5. **Geração de cenários** com `CenarioService` apenas quando `tipo_nota == '1'`.
6. **Armazenamento dos IDs dos cenários** nas colunas do tributo (`cenario_icms_id`, `cenario_ipi_id`, etc.).

Assim, o sistema mantém cenários atualizados automaticamente para todas as notas de saída importadas, garantindo base para auditorias e cálculos fiscais.
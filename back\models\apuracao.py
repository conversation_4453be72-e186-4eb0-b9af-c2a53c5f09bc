from .escritorio import db

class ApuracaoICMS(db.Model):
    __tablename__ = 'apuracao_icms'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    empresa_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('empresa.id'), nullable=False)
    ano = db.Column(db.Integer, nullable=False)
    mes = db.Column(db.Integer, nullable=False)
    tipo = db.Column(db.String(10), nullable=False)  # 'icms' ou 'icms_st'
    vl_tot_debitos = db.Column(db.Numeric(15, 2))
    vl_aj_debitos = db.Column(db.Numeric(15, 2))
    vl_tot_aj_debitos = db.Column(db.Numeric(15, 2))
    vl_estornos_cred = db.Column(db.<PERSON>ume<PERSON>(15, 2))
    vl_tot_creditos = db.Column(db.Numeric(15, 2))
    vl_aj_creditos = db.Column(db.Numeric(15, 2))
    vl_tot_aj_creditos = db.Column(db.Numeric(15, 2))
    vl_estornos_deb = db.Column(db.Numeric(15, 2))
    vl_sld_credor_ant = db.Column(db.Numeric(15, 2))
    vl_sld_apurado = db.Column(db.Numeric(15, 2))
    vl_tot_ded = db.Column(db.Numeric(15, 2))
    vl_icms_recolher = db.Column(db.Numeric(15, 2))
    vl_sld_credor_transportar = db.Column(db.Numeric(15, 2))
    deb_esp = db.Column(db.Numeric(15, 2))

    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'ano', 'mes', 'tipo', name='uq_apuracao_icms_empresa_mes_tipo'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'ano': self.ano,
            'mes': self.mes,
            'tipo': self.tipo,
            'vl_tot_debitos': float(self.vl_tot_debitos or 0),
            'vl_aj_debitos': float(self.vl_aj_debitos or 0),
            'vl_tot_aj_debitos': float(self.vl_tot_aj_debitos or 0),
            'vl_estornos_cred': float(self.vl_estornos_cred or 0),
            'vl_tot_creditos': float(self.vl_tot_creditos or 0),
            'vl_aj_creditos': float(self.vl_aj_creditos or 0),
            'vl_tot_aj_creditos': float(self.vl_tot_aj_creditos or 0),
            'vl_estornos_deb': float(self.vl_estornos_deb or 0),
            'vl_sld_credor_ant': float(self.vl_sld_credor_ant or 0),
            'vl_sld_apurado': float(self.vl_sld_apurado or 0),
            'vl_tot_ded': float(self.vl_tot_ded or 0),
            'vl_icms_recolher': float(self.vl_icms_recolher or 0),
            'vl_sld_credor_transportar': float(self.vl_sld_credor_transportar or 0),
            'deb_esp': float(self.deb_esp or 0)
        }

class ApuracaoIPI(db.Model):
    __tablename__ = 'apuracao_ipi'

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'), nullable=False)
    ano = db.Column(db.Integer, nullable=False)
    mes = db.Column(db.Integer, nullable=False)
    cfop = db.Column(db.String(4))
    cst_ipi = db.Column(db.String(2))
    vl_cont_ipi = db.Column(db.Numeric(15, 2))
    vl_bc_ipi = db.Column(db.Numeric(15, 2))
    vl_ipi = db.Column(db.Numeric(15, 2))

    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'ano', 'mes', 'cfop', 'cst_ipi', name='uq_apuracao_ipi_empresa_mes_cfop'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'ano': self.ano,
            'mes': self.mes,
            'cfop': self.cfop,
            'cst_ipi': self.cst_ipi,
            'vl_cont_ipi': float(self.vl_cont_ipi or 0),
            'vl_bc_ipi': float(self.vl_bc_ipi or 0),
            'vl_ipi': float(self.vl_ipi or 0)
        }


class IpiApuracaoGeral(db.Model):
    __tablename__ = 'ipi_apuracao_geral'

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'), nullable=False)
    ano = db.Column(db.Integer, nullable=False)
    mes = db.Column(db.Integer, nullable=False)
    vl_sd_ant_ipi = db.Column(db.Numeric(15, 2))
    vl_deb_ipi = db.Column(db.Numeric(15, 2))
    vl_cred_ipi = db.Column(db.Numeric(15, 2))
    vl_od_ipi = db.Column(db.Numeric(15, 2))
    vl_oc_ipi = db.Column(db.Numeric(15, 2))
    vl_sc_ipi = db.Column(db.Numeric(15, 2))
    vl_sd_ipi = db.Column(db.Numeric(15, 2))

    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'ano', 'mes', name='uq_ipi_apuracao_geral_empresa_mes'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'ano': self.ano,
            'mes': self.mes,
            'vl_sd_ant_ipi': float(self.vl_sd_ant_ipi or 0),
            'vl_deb_ipi': float(self.vl_deb_ipi or 0),
            'vl_cred_ipi': float(self.vl_cred_ipi or 0),
            'vl_od_ipi': float(self.vl_od_ipi or 0),
            'vl_oc_ipi': float(self.vl_oc_ipi or 0),
            'vl_sc_ipi': float(self.vl_sc_ipi or 0),
            'vl_sd_ipi': float(self.vl_sd_ipi or 0),
        }


class ApuracaoPIS(db.Model):
    __tablename__ = 'apuracao_pis'

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'), nullable=False)
    ano = db.Column(db.Integer, nullable=False)
    mes = db.Column(db.Integer, nullable=False)
    vl_tot_debitos = db.Column(db.Numeric(15, 2))
    vl_tot_creditos = db.Column(db.Numeric(15, 2))
    vl_pis_a_recolher = db.Column(db.Numeric(15, 2))

    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'ano', 'mes', name='uq_apuracao_pis_empresa_mes'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'ano': self.ano,
            'mes': self.mes,
            'vl_tot_debitos': float(self.vl_tot_debitos or 0),
            'vl_tot_creditos': float(self.vl_tot_creditos or 0),
            'vl_pis_a_recolher': float(self.vl_pis_a_recolher or 0),
        }


class ApuracaoCOFINS(db.Model):
    __tablename__ = 'apuracao_cofins'

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'), nullable=False)
    ano = db.Column(db.Integer, nullable=False)
    mes = db.Column(db.Integer, nullable=False)
    vl_tot_debitos = db.Column(db.Numeric(15, 2))
    vl_tot_creditos = db.Column(db.Numeric(15, 2))
    vl_cofins_a_recolher = db.Column(db.Numeric(15, 2))

    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'ano', 'mes', name='uq_apuracao_cofins_empresa_mes'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'ano': self.ano,
            'mes': self.mes,
            'vl_tot_debitos': float(self.vl_tot_debitos or 0),
            'vl_tot_creditos': float(self.vl_tot_creditos or 0),
            'vl_cofins_a_recolher': float(self.vl_cofins_a_recolher or 0),
        }
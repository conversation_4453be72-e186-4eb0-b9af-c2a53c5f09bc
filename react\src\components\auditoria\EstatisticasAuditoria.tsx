import React from 'react'
import { Card } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'

interface EstatisticasAuditoriaProps {
  totalRegistros: number
  registrosAprovados: number
  registrosPendentes: number
  registrosComDivergencia: number
  matchingStats?: {
    total: number
    matched: number
    unmatched: number
    percentage: number
  }
}

export function EstatisticasAuditoria({
  totalRegistros,
  registrosAprovados,
  registrosPendentes,
  registrosComDivergencia,
  matchingStats
}: EstatisticasAuditoriaProps) {
  const percentualAprovados = totalRegistros > 0 ? (registrosAprovados / totalRegistros * 100) : 0
  const percentualPendentes = totalRegistros > 0 ? (registrosPendentes / totalRegistros * 100) : 0
  const percentualDivergentes = totalRegistros > 0 ? (registrosComDivergencia / totalRegistros * 100) : 0

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {/* Total de Registros */}
      <Card className="p-4 text-center">
        <div className="w-12 h-12 mx-auto mb-3 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
          <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
          {totalRegistros.toLocaleString('pt-BR')}
        </h3>
        <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">
          Total de Registros
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Produtos analisados
        </p>
      </Card>

      {/* Registros Aprovados */}
      <Card className="p-4 text-center">
        <div className="w-12 h-12 mx-auto mb-3 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
          <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
          {registrosAprovados.toLocaleString('pt-BR')}
        </h3>
        <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">
          Aprovados
        </p>
        <div className="flex items-center justify-center gap-2">
          <Badge variant="success" size="sm">
            {percentualAprovados.toFixed(1)}%
          </Badge>
        </div>
      </Card>

      {/* Registros Pendentes */}
      <Card className="p-4 text-center">
        <div className="w-12 h-12 mx-auto mb-3 bg-yellow-100 dark:bg-yellow-900/50 rounded-full flex items-center justify-center">
          <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mb-1">
          {registrosPendentes.toLocaleString('pt-BR')}
        </h3>
        <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">
          Pendentes
        </p>
        <div className="flex items-center justify-center gap-2">
          <Badge variant="warning" size="sm">
            {percentualPendentes.toFixed(1)}%
          </Badge>
        </div>
      </Card>

      {/* Registros com Divergência */}
      <Card className="p-4 text-center border-red-200 dark:border-red-800">
        <div className="w-12 h-12 mx-auto mb-3 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
          <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-1">
          {registrosComDivergencia.toLocaleString('pt-BR')}
        </h3>
        <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">
          Com Divergência
        </p>
        <div className="flex items-center justify-center gap-2">
          <Badge variant="error" size="sm">
            {percentualDivergentes.toFixed(1)}%
          </Badge>
        </div>
      </Card>

      {/* Estatísticas de Matching (se disponível) */}
      {matchingStats && (
        <Card className="p-4 text-center md:col-span-2 lg:col-span-4">
          <div className="flex items-center justify-center gap-8">
            <div className="text-center">
              <div className="w-10 h-10 mx-auto mb-2 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h4 className="text-lg font-bold text-purple-600 dark:text-purple-400">
                {matchingStats.matched.toLocaleString('pt-BR')}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">Matched</p>
            </div>

            <div className="text-center">
              <div className="w-10 h-10 mx-auto mb-2 bg-orange-100 dark:bg-orange-900/50 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                </svg>
              </div>
              <h4 className="text-lg font-bold text-orange-600 dark:text-orange-400">
                {matchingStats.unmatched.toLocaleString('pt-BR')}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">Unmatched</p>
            </div>

            <div className="text-center">
              <div className="w-10 h-10 mx-auto mb-2 bg-indigo-100 dark:bg-indigo-900/50 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h4 className="text-lg font-bold text-indigo-600 dark:text-indigo-400">
                {matchingStats.percentage.toFixed(1)}%
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">Taxa de Match</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
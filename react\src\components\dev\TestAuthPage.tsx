import { useState } from 'react'

export function TestAuthPage() {
  const [token] = useState(
    'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G8ro7LnOTDnN8G_hnJw3uQjBulFNmVDhilYOy_VXe5o2GbxYx6D5Z8lwsiwAgw9iyz4IvFSC805NWob-U-LHbl7yn8MnE5zOczco0HJ3vu3OtPX7Cj6C1nqWgumCAP7BebteTVAz4R4e11EplF6V_iHE8JXsdWltn8XdFson22hH3gnpb9n7pneq9OSHsdMC_2A2Kftg8k_4tgckw3yBAhcc4zztZc-WnrJ4okSZie0cyuhzD9S7NmK1Pj5s5W0UAsKxGyPSstIW852kvziuiqMureWdkeIwx4HDl-6gVVjIP_NLsYBA3ra7ktZSfjPHurzmBLZET9sAYMaJPpuCxw'
  )

  const handleRedirect = () => {
    // Define o cookie com o token (mesmo comportamento do HTML)
    document.cookie = `token=${token}; path=/; samesite=lax`
    
    // Redireciona para a página de login React
    window.location.href = '/fiscal/login'
  }

  const handleDirectLogin = async () => {
    try {
      // Define o cookie primeiro
      document.cookie = `token=${token}; path=/; samesite=lax`
      
      // Chama diretamente a API de validação
      const response = await fetch('/fiscal/api/portal-login', {
        method: 'GET',
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Login direto bem-sucedido:', data)
        
        // Salva no localStorage (mesmo que o LoginPage faria)
        localStorage.setItem('token', data.access_token)
        localStorage.setItem('currentUser', JSON.stringify({
          id: data.usuario_id,
          nome: data.nome,
          tipo_usuario: data.tipo_usuario,
          is_admin: data.is_admin,
          escritorio_id: data.escritorio_id
        }))
        
        // Redireciona para o dashboard
        window.location.href = '/fiscal/dashboard'
      } else {
        const error = await response.json()
        console.error('Erro no login direto:', error)
        alert(`Erro: ${error.message}`)
      }
    } catch (error) {
      console.error('Erro na requisição:', error)
      alert('Erro na requisição de login')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-2xl">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            🧪 Teste de Autenticação - React
          </h1>
          <p className="text-gray-600 mb-8">
            Ambiente de desenvolvimento para simular o redirecionamento do portal com token JWT.
          </p>

          {/* Token Preview */}
          <div className="bg-gray-100 rounded-lg p-4 mb-8 text-left">
            <h3 className="font-semibold text-gray-900 mb-2">
              Token que será enviado:
            </h3>
            <div className="bg-white rounded border p-3 text-xs font-mono text-gray-700 break-all">
              {token}
            </div>
          </div>

          {/* Decoded Token Info */}
          <div className="bg-blue-50 rounded-lg p-4 mb-8 text-left">
            <h3 className="font-semibold text-blue-900 mb-2">
              Informações do Token:
            </h3>
            <div className="text-sm text-blue-800 space-y-1">
              <p><strong>Nome:</strong> Ricardo Samogin</p>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Role:</strong> admin</p>
              <p><strong>Escritório:</strong> CONTTROLARE SOLUCOES CONTABEIS E TRIBUTARIAS LTDA</p>
              <p><strong>Permissões:</strong> Fiscal: true</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <button
              onClick={handleRedirect}
              className="btn btn-primary btn-lg w-full"
            >
              🔄 Simular Fluxo Completo (via /login)
            </button>
            
            <button
              onClick={handleDirectLogin}
              className="btn btn-secondary btn-lg w-full"
            >
              ⚡ Login Direto (pular LoginPage)
            </button>
            
            <div className="pt-4 border-t">
              <p className="text-sm text-gray-500 mb-4">
                Ou acesse diretamente:
              </p>
              <div className="flex gap-2">
                <a
                  href="/login"
                  className="btn btn-sm flex-1 bg-green-100 text-green-700 hover:bg-green-200"
                >
                  Página de Login
                </a>
                <a
                  href="/dashboard"
                  className="btn btn-sm flex-1 bg-purple-100 text-purple-700 hover:bg-purple-200"
                >
                  Dashboard (se autenticado)
                </a>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-8 p-4 bg-yellow-50 rounded-lg text-left">
            <h4 className="font-semibold text-yellow-800 mb-2">
              📋 Como usar:
            </h4>
            <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
              <li>Clique em "Simular Fluxo Completo" para testar o fluxo normal</li>
              <li>Ou clique em "Login Direto" para pular a tela de loading</li>
              <li>O token será definido como cookie e validado pelo backend</li>
              <li>Se válido, você será redirecionado para o dashboard</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  )
}
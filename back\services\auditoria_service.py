from models import db, Tributo, CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL, TributoHistorico, AuditoriaResultado, AuditoriaSumario, NotaFiscalItem
from services.transactional import transactional_session
from datetime import datetime
from sqlalchemy import and_, or_
from sqlalchemy.orm import joinedload
from decimal import Decimal
from services.tributo_calculation_service import TributoCalculationService

class AuditoriaService:
    """
    Serviço para auditoria fiscal com base nos cenários em produção
    """

    def __init__(self, empresa_id, usuario_id=None, progress_callback=None):
        """
        Inicializa o serviço de auditoria fiscal

        Args:
            empresa_id (int): ID da empresa
            usuario_id (int, optional): ID do usuário que está realizando a auditoria
            progress_callback (callable, optional): Função callback para reportar progresso
        """
        self.empresa_id = empresa_id
        self.usuario_id = usuario_id
        self.progress_callback = progress_callback
        # Cache para cenários consultados, evitando queries repetidas
        # Chave: (tipo_tributo, empresa_id, cliente_id, produto_id, cfop, data)
        # Valor: (cenario, status)
        self.cenario_cache = {}

    def executar_auditoria(self, tributo_ids=None, produto_ids=None, cliente_ids=None, forcar_recalculo=False, tipo_tributo=None):
        """
        Executa a auditoria fiscal para os tributos especificados

        Args:
            tributo_ids (list, optional): Lista de IDs de tributos para auditar
            produto_ids (list, optional): Lista de IDs de produtos para auditar
            cliente_ids (list, optional): Lista de IDs de clientes para auditar
            forcar_recalculo (bool, optional): Se True, força o recálculo mesmo para tributos já auditados
            tipo_tributo (str, optional): Tipo de tributo a ser auditado ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
                                         Se None, audita todos os tipos de tributo

        Returns:
            dict: Resultado da auditoria
        """

        # A consulta base dos tributos

        # Construir query base com carregamento do relacionamento nota_fiscal_item
        query = Tributo.query.options(joinedload(Tributo.nota_fiscal_item)).filter_by(empresa_id=self.empresa_id)

        # Aplicar filtros
        if tributo_ids:
            query = query.filter(Tributo.id.in_(tributo_ids))
        if produto_ids:
            query = query.filter(Tributo.produto_id.in_(produto_ids))
        if cliente_ids:
            query = query.filter(Tributo.cliente_id.in_(cliente_ids))

        # Filtrar apenas tributos pendentes para o tipo específico, a menos que forcar_recalculo seja True
        if not forcar_recalculo:
            if tipo_tributo == 'icms':
                query = query.filter(Tributo.auditoria_icms_status != 'realizada')
            elif tipo_tributo == 'icms_st':
                query = query.filter(Tributo.auditoria_icms_st_status != 'realizada')
            elif tipo_tributo == 'ipi':
                query = query.filter(Tributo.auditoria_ipi_status != 'realizada')
            elif tipo_tributo == 'pis':
                query = query.filter(Tributo.auditoria_pis_status != 'realizada')
            elif tipo_tributo == 'cofins':
                query = query.filter(Tributo.auditoria_cofins_status != 'realizada')
            elif tipo_tributo == 'difal':
                query = query.filter(Tributo.auditoria_difal_status != 'realizada')
            else:
                # Se não especificar tipo, usa o status geral (para compatibilidade)
                query = query.filter(Tributo.auditoria_status != 'realizada')

        tributos = query.all()

        if forcar_recalculo and tipo_tributo:
            meses_ref = {(t.data_emissao.year, t.data_emissao.month) for t in tributos if t.data_emissao}
            tributo_ids_ref = [t.id for t in tributos]
            self._limpar_sumarios_tipo_tributo(tipo_tributo, meses_ref, tributo_ids_ref)

        # Logar detalhes dos tributos encontrados para diagnóstico
        if tributos:
            for i, tributo in enumerate(tributos[:5]):  # Limitar a 5 para não sobrecarregar o log
                if len(tributos) > 5:

                    if not tributos:
                        return {
                'success': False,
                'message': 'Nenhum tributo encontrado para os filtros especificados'
            }

        # Inicializar resultados
        results = {
            'success': True,
            'total': len(tributos),
            'auditados': 0,
            'nao_auditados': 0,
            'por_tipo': {
                'icms': {'auditados': 0, 'nao_auditados': 0},
                'icms_st': {'auditados': 0, 'nao_auditados': 0},
                'ipi': {'auditados': 0, 'nao_auditados': 0},
                'pis': {'auditados': 0, 'nao_auditados': 0},
                'cofins': {'auditados': 0, 'nao_auditados': 0},
                'difal': {'auditados': 0, 'nao_auditados': 0}
            }
        }

        with transactional_session():
            # Processar cada tributo
            for index, tributo in enumerate(tributos):

                # Reportar progresso se callback estiver disponível
                if self.progress_callback:
                    progress_data = {
                        'processed': index,
                        'total': len(tributos),
                        'current_tributo_id': tributo.id,
                        'percentage': round((index / len(tributos)) * 100, 1),
                        'auditados': results['auditados'],
                        'nao_auditados': results['nao_auditados'],
                        'tipo_tributo': tipo_tributo or 'todos'
                    }
                    self.progress_callback(progress_data)

                try:
                    # Inicializar variáveis de controle
                    ipi_auditado = False
                    icms_auditado = False
                    icms_st_auditado = False
                    pis_auditado = False
                    cofins_auditado = False
                    difal_auditado = False

                    # Se tipo_tributo for None ou 'ipi', auditar IPI
                    if tipo_tributo is None or tipo_tributo == 'ipi':
                        # Primeiro, calcular IPI, pois ICMS e ICMS-ST podem depender dele
                        ipi_auditado = self._auditar_ipi(tributo)

                        if ipi_auditado:
                            # Atualizar status específico de auditoria IPI
                            tributo.auditoria_ipi_status = 'realizada'
                            tributo.auditoria_ipi_data = datetime.now()

                    # Se tipo_tributo for None ou 'icms', auditar ICMS
                    if tipo_tributo is None or tipo_tributo == 'icms':
                        # Se for auditar ICMS e IPI não foi auditado agora, mas precisa do valor do IPI
                        if not ipi_auditado and tipo_tributo == 'icms':
                            # Verificar se IPI já foi auditado anteriormente
                            if tributo.auditoria_ipi_status == 'realizada' and tributo.cenario_ipi_valor is not None:
                                ipi_auditado = True

                        # Depois, calcular ICMS
                        icms_auditado = self._auditar_icms(tributo)

                        if icms_auditado:
                            # Atualizar status específico de auditoria ICMS
                            tributo.auditoria_icms_status = 'realizada'
                            tributo.auditoria_icms_data = datetime.now()

                    # Se tipo_tributo for None ou 'icms_st', auditar ICMS-ST
                    if tipo_tributo is None or tipo_tributo == 'icms_st':
                        # Se for auditar ICMS-ST e IPI/ICMS não foram auditados agora, mas precisa dos valores
                        if tipo_tributo == 'icms_st':
                            if not ipi_auditado and tributo.auditoria_ipi_status == 'realizada' and tributo.cenario_ipi_valor is not None:
                                ipi_auditado = True
                            if not icms_auditado and tributo.auditoria_icms_status == 'realizada' and tributo.cenario_icms_valor is not None:
                                icms_auditado = True

                        # Depois, calcular ICMS-ST
                        icms_st_auditado = self._auditar_icms_st(tributo)

                        if icms_st_auditado:
                            # Atualizar status específico de auditoria ICMS-ST
                            tributo.auditoria_icms_st_status = 'realizada'
                            tributo.auditoria_icms_st_data = datetime.now()

                    # Se tipo_tributo for None ou 'pis', auditar PIS
                    if tipo_tributo is None or tipo_tributo == 'pis':
                        # Se for auditar PIS e ICMS não foi auditado agora, mas precisa do valor do ICMS
                        if not icms_auditado and tipo_tributo == 'pis':
                            if tributo.auditoria_icms_status == 'realizada' and tributo.cenario_icms_valor is not None:
                                icms_auditado = True

                        # Calcular PIS
                        pis_auditado = self._auditar_pis(tributo, icms_auditado)

                        if pis_auditado:
                            # Atualizar status específico de auditoria PIS
                            tributo.auditoria_pis_status = 'realizada'
                            tributo.auditoria_pis_data = datetime.now()

                    # Se tipo_tributo for None ou 'cofins', auditar COFINS
                    if tipo_tributo is None or tipo_tributo == 'cofins':
                        # Se for auditar COFINS e ICMS não foi auditado agora, mas precisa do valor do ICMS
                        if not icms_auditado and tipo_tributo == 'cofins':
                            if tributo.auditoria_icms_status == 'realizada' and tributo.cenario_icms_valor is not None:
                                icms_auditado = True

                        # Calcular COFINS
                        cofins_auditado = self._auditar_cofins(tributo, icms_auditado)

                        if cofins_auditado:
                            # Atualizar status específico de auditoria COFINS
                            tributo.auditoria_cofins_status = 'realizada'
                            tributo.auditoria_cofins_data = datetime.now()

                    # Se tipo_tributo for None ou 'difal', auditar DIFAL
                    if tipo_tributo is None or tipo_tributo == 'difal':
                        # Calcular DIFAL
                        difal_auditado = self._auditar_difal(tributo)

                        if difal_auditado:
                            # Atualizar status específico de auditoria DIFAL
                            tributo.auditoria_difal_status = 'realizada'
                            tributo.auditoria_difal_data = datetime.now()

                    # Atualizar status geral de auditoria (para compatibilidade)
                    if tipo_tributo is None and (ipi_auditado or icms_auditado or icms_st_auditado or pis_auditado or cofins_auditado or difal_auditado):
                        tributo.auditoria_status = 'realizada'
                        tributo.auditoria_data = datetime.now()
                except Exception as e:
                    print(f"Erro ao auditar tributo ID={tributo.id}: {str(e)}")
                    # Continuar com o próximo tributo

                # Atualizar contadores
                if ipi_auditado:
                    results['por_tipo']['ipi']['auditados'] += 1
                else:
                    results['por_tipo']['ipi']['nao_auditados'] += 1

                if icms_auditado:
                    results['por_tipo']['icms']['auditados'] += 1
                else:
                    results['por_tipo']['icms']['nao_auditados'] += 1

                if icms_st_auditado:
                    results['por_tipo']['icms_st']['auditados'] += 1
                else:
                    results['por_tipo']['icms_st']['nao_auditados'] += 1

                if pis_auditado:
                    results['por_tipo']['pis']['auditados'] += 1
                else:
                    results['por_tipo']['pis']['nao_auditados'] += 1

                if cofins_auditado:
                    results['por_tipo']['cofins']['auditados'] += 1
                else:
                    results['por_tipo']['cofins']['nao_auditados'] += 1

                if difal_auditado:
                    results['por_tipo']['difal']['auditados'] += 1
                else:
                    results['por_tipo']['difal']['nao_auditados'] += 1

                # Verificar se pelo menos um tributo foi auditado
                if ipi_auditado or icms_auditado or icms_st_auditado or pis_auditado or cofins_auditado or difal_auditado:
                    results['auditados'] += 1
                else:
                    results['nao_auditados'] += 1

        # Reportar progresso final se callback estiver disponível
        if self.progress_callback:
            final_progress_data = {
                'processed': len(tributos),
                'total': len(tributos),
                'current_tributo_id': None,
                'percentage': 100,
                'auditados': results['auditados'],
                'nao_auditados': results['nao_auditados'],
                'tipo_tributo': tipo_tributo or 'todos',
                'completed': True
            }
            self.progress_callback(final_progress_data)

        # Se um tipo específico foi solicitado, sempre garantir que existe um sumário
        # para indicar que a auditoria foi executada, mesmo que nenhum tributo tenha sido auditado
        if tipo_tributo and tributos:
            print(f"DEBUG: Verificando necessidade de criar sumário para {tipo_tributo}")
            print(f"DEBUG: results['auditados'] = {results['auditados']}")
            print(f"DEBUG: len(tributos) = {len(tributos)}")
            
            # Verificar se já existe um sumário para este período
            primeiro_tributo = tributos[0]
            if primeiro_tributo.data_emissao:
                ano = primeiro_tributo.data_emissao.year
                mes = primeiro_tributo.data_emissao.month
                
                sumario_existente = AuditoriaSumario.query.filter_by(
                    empresa_id=self.empresa_id,
                    ano=ano,
                    mes=mes,
                    tipo_tributo=tipo_tributo
                ).first()
                
                if not sumario_existente:
                    print(f"DEBUG: Criando sumário vazio para {tipo_tributo} - {ano}/{mes:02d}")
                    self._criar_sumario_vazio(tipo_tributo, tributos)
                else:
                    print(f"DEBUG: Sumário já existe para {tipo_tributo} - {ano}/{mes:02d}")
            else:
                print(f"DEBUG: Primeiro tributo não tem data_emissao")

        return results

    

    def _auditar_ipi(self, tributo):
        """
        Audita o IPI para um tributo usando o vínculo direto com o cenário.

        Args:
            tributo (Tributo): Objeto do tributo

        Returns:
            bool: True se a auditoria foi realizada, False caso contrário
        """
        # Verificar se há um cenário de IPI vinculado diretamente ao tributo
        if not tributo.cenario_ipi_id:
            return False

        # Carregar o cenário vinculado
        cenario = db.session.get(CenarioIPI, tributo.cenario_ipi_id)

        # Se o cenário não for encontrado ou estiver pendente, não auditar
        if not cenario or cenario.status != 'producao':
            return False

        # O status do cenário é sempre 'producao' a partir deste ponto
        cenario_status = 'producao'

        # Calcular base de cálculo e valor do IPI usando o serviço de cálculo
        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None
        base_calculo, valor_ipi = TributoCalculationService.calcular_ipi(valor_total, cenario, valor_frete, valor_desconto)

        # Atualizar valores calculados no tributo (o vínculo cenario_ipi_id já existe)
        tributo.cenario_ipi_vbc = float(base_calculo)
        tributo.cenario_ipi_valor = float(valor_ipi)

        # Verificar se o tributo está vinculado a um item de nota fiscal
        if tributo.nota_fiscal_item_id:
            # Determinar o status da auditoria
            if tributo.ipi_valor is not None:
                valor_nota = Decimal(str(tributo.ipi_valor))
                valor_calculado = Decimal(str(valor_ipi))
                base_calculo_nota_valor = float(tributo.ipi_vbc) if tributo.ipi_vbc else None
                base_calculo_calculada_valor = float(base_calculo)

                # Usar a função de comparação completa
                comparacao = self._comparar_campos_fiscais(
                    tributo, cenario, 'ipi',
                    valor_nota, valor_calculado,
                    base_calculo_nota_valor, base_calculo_calculada_valor,
                    cenario_status
                )

                # Verificar se já existe um resultado de auditoria para este tributo e tipo
                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id,
                    tipo_tributo='ipi'
                ).first()

                if resultado_existente:
                    # Atualizar o resultado existente
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = base_calculo_nota_valor
                    resultado_existente.base_calculo_calculada = base_calculo_calculada_valor
                    resultado_existente.status = comparacao['status']
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                    # Atualizar campos de comparação
                    resultado_existente.ncm_nota = comparacao['ncm_nota']
                    resultado_existente.ncm_cenario = comparacao['ncm_cenario']
                    resultado_existente.cfop_nota = comparacao['cfop_nota']
                    resultado_existente.cfop_cenario = comparacao['cfop_cenario']
                    resultado_existente.cst_nota = comparacao['cst_nota']
                    resultado_existente.cst_cenario = comparacao['cst_cenario']
                    resultado_existente.aliquota_nota = comparacao['aliquota_nota']
                    resultado_existente.aliquota_cenario = comparacao['aliquota_cenario']
                    resultado_existente.inconsistencia_valor = comparacao['inconsistencia_valor']
                    resultado_existente.inconsistencia_ncm = comparacao['inconsistencia_ncm']
                    resultado_existente.inconsistencia_cfop = comparacao['inconsistencia_cfop']
                    resultado_existente.inconsistencia_cst = comparacao['inconsistencia_cst']
                    resultado_existente.inconsistencia_aliquota = comparacao['inconsistencia_aliquota']
                    resultado_existente.inconsistencia_base_calculo = comparacao['inconsistencia_base_calculo']
                else:
                    # Criar um novo resultado de auditoria
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='ipi',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=base_calculo_nota_valor,
                        base_calculo_calculada=base_calculo_calculada_valor,
                        status=comparacao['status'],
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now(),
                        # Campos de comparação
                        ncm_nota=comparacao['ncm_nota'],
                        ncm_cenario=comparacao['ncm_cenario'],
                        cfop_nota=comparacao['cfop_nota'],
                        cfop_cenario=comparacao['cfop_cenario'],
                        cst_nota=comparacao['cst_nota'],
                        cst_cenario=comparacao['cst_cenario'],
                        aliquota_nota=comparacao['aliquota_nota'],
                        aliquota_cenario=comparacao['aliquota_cenario'],
                        inconsistencia_valor=comparacao['inconsistencia_valor'],
                        inconsistencia_ncm=comparacao['inconsistencia_ncm'],
                        inconsistencia_cfop=comparacao['inconsistencia_cfop'],
                        inconsistencia_cst=comparacao['inconsistencia_cst'],
                        inconsistencia_aliquota=comparacao['inconsistencia_aliquota'],
                        inconsistencia_base_calculo=comparacao['inconsistencia_base_calculo']
                    )
                    db.session.add(resultado)

                # Atualizar o sumário de auditoria
                self._atualizar_sumario_auditoria(tributo, 'ipi', comparacao['status'])
                return True
            else:
                # Mesmo sem valor na nota, registrar no sumário como não auditado
                self._atualizar_sumario_auditoria(tributo, 'ipi', 'nao_auditado')
                return False

        return False

    def _auditar_icms(self, tributo):
        """
        Audita o ICMS para um tributo usando o vínculo direto com o cenário.
        """
        if not tributo.cenario_icms_id:
            return False

        cenario = db.session.get(CenarioICMS, tributo.cenario_icms_id)

        if not cenario or cenario.status != 'producao':
            return False

        cenario_status = 'producao'

        cliente_uso_consumo_ativo = False
        if tributo.cliente and (hasattr(tributo.cliente, 'destinacao') and tributo.cliente.destinacao in ['Uso e Consumo', 'Ativo Imobilizado']):
            cliente_uso_consumo_ativo = True

        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_ipi = Decimal(str(tributo.cenario_ipi_valor)) if tributo.cenario_ipi_valor else None
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

        base_calculo, valor_icms = TributoCalculationService.calcular_icms(
            valor_total, cenario, valor_ipi, cliente_uso_consumo_ativo, valor_frete, valor_desconto
        )

        tributo.cenario_icms_vbc = float(base_calculo)
        tributo.cenario_icms_valor = float(valor_icms)

        if tributo.nota_fiscal_item_id:
            if tributo.icms_valor is not None:
                valor_nota = Decimal(str(tributo.icms_valor))
                valor_calculado = Decimal(str(valor_icms))
                base_calculo_nota_valor = float(tributo.icms_vbc) if tributo.icms_vbc else None
                base_calculo_calculada_valor = float(base_calculo)

                comparacao = self._comparar_campos_fiscais(
                    tributo, cenario, 'icms',
                    valor_nota, valor_calculado,
                    base_calculo_nota_valor, base_calculo_calculada_valor,
                    cenario_status
                )

                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id, tipo_tributo='icms'
                ).first()

                if resultado_existente:
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = base_calculo_nota_valor
                    resultado_existente.base_calculo_calculada = base_calculo_calculada_valor
                    resultado_existente.status = comparacao['status']
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                    resultado_existente.ncm_nota = comparacao['ncm_nota']
                    resultado_existente.ncm_cenario = comparacao['ncm_cenario']
                    resultado_existente.cfop_nota = comparacao['cfop_nota']
                    resultado_existente.cfop_cenario = comparacao['cfop_cenario']
                    resultado_existente.cst_nota = comparacao['cst_nota']
                    resultado_existente.cst_cenario = comparacao['cst_cenario']
                    resultado_existente.origem_nota = comparacao['origem_nota']
                    resultado_existente.origem_cenario = comparacao['origem_cenario']
                    resultado_existente.aliquota_nota = comparacao['aliquota_nota']
                    resultado_existente.aliquota_cenario = comparacao['aliquota_cenario']
                    resultado_existente.inconsistencia_valor = comparacao['inconsistencia_valor']
                    resultado_existente.inconsistencia_ncm = comparacao['inconsistencia_ncm']
                    resultado_existente.inconsistencia_cfop = comparacao['inconsistencia_cfop']
                    resultado_existente.inconsistencia_cst = comparacao['inconsistencia_cst']
                    resultado_existente.inconsistencia_origem = comparacao['inconsistencia_origem']
                    resultado_existente.inconsistencia_aliquota = comparacao['inconsistencia_aliquota']
                    resultado_existente.inconsistencia_base_calculo = comparacao['inconsistencia_base_calculo']
                else:
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='icms',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=base_calculo_nota_valor,
                        base_calculo_calculada=base_calculo_calculada_valor,
                        status=comparacao['status'],
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now(),
                        ncm_nota=comparacao['ncm_nota'],
                        ncm_cenario=comparacao['ncm_cenario'],
                        cfop_nota=comparacao['cfop_nota'],
                        cfop_cenario=comparacao['cfop_cenario'],
                        cst_nota=comparacao['cst_nota'],
                        cst_cenario=comparacao['cst_cenario'],
                        origem_nota=comparacao['origem_nota'],
                        origem_cenario=comparacao['origem_cenario'],
                        aliquota_nota=comparacao['aliquota_nota'],
                        aliquota_cenario=comparacao['aliquota_cenario'],
                        inconsistencia_valor=comparacao['inconsistencia_valor'],
                        inconsistencia_ncm=comparacao['inconsistencia_ncm'],
                        inconsistencia_cfop=comparacao['inconsistencia_cfop'],
                        inconsistencia_cst=comparacao['inconsistencia_cst'],
                        inconsistencia_origem=comparacao['inconsistencia_origem'],
                        inconsistencia_aliquota=comparacao['inconsistencia_aliquota'],
                        inconsistencia_base_calculo=comparacao['inconsistencia_base_calculo']
                    )
                    db.session.add(resultado)

                self._atualizar_sumario_auditoria(tributo, 'icms', comparacao['status'])
                return True
            else:
                self._atualizar_sumario_auditoria(tributo, 'icms', 'nao_auditado')
                return False

        return False

    def _auditar_icms_st(self, tributo):
        """
        Audita o ICMS-ST para um tributo usando o vínculo direto com o cenário.
        """
        # Debug: Log detalhado dos motivos pelos quais ICMS-ST não é auditado
        if not tributo.cenario_icms_st_id:
            print(f"DEBUG ICMS-ST: Tributo ID {tributo.id} - Sem cenário ICMS-ST vinculado (cenario_icms_st_id é None)")
            print(f"  - ICMS CST: {tributo.icms_cst}")
            print(f"  - ICMS-ST Valor: {tributo.icms_st_valor}")
            print(f"  - Cliente ID: {tributo.cliente_id}")
            print(f"  - Produto ID: {tributo.produto_id}")
            if tributo.nota_fiscal_item:
                print(f"  - CFOP: {tributo.nota_fiscal_item.cfop}")
            return False

        cenario = db.session.get(CenarioICMSST, tributo.cenario_icms_st_id)

        if not cenario:
            print(f"DEBUG ICMS-ST: Tributo ID {tributo.id} - Cenário ICMS-ST ID {tributo.cenario_icms_st_id} não encontrado")
            return False
            
        if cenario.status != 'producao':
            print(f"DEBUG ICMS-ST: Tributo ID {tributo.id} - Cenário ICMS-ST ID {cenario.id} não está em produção (status: {cenario.status})")
            return False

        cenario_status = 'producao'

        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_ipi = Decimal(str(tributo.cenario_ipi_valor)) if tributo.cenario_ipi_valor else None
        valor_icms = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor else None
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

        base_calculo, valor_icms_st = TributoCalculationService.calcular_icms_st(
            valor_total, cenario, valor_ipi, valor_icms, valor_frete, valor_desconto
        )

        tributo.cenario_icms_st_vbc = float(base_calculo)
        tributo.cenario_icms_st_valor = float(valor_icms_st)

        if tributo.nota_fiscal_item_id:
            if tributo.icms_st_valor is not None:
                valor_nota = Decimal(str(tributo.icms_st_valor))
                valor_calculado = Decimal(str(valor_icms_st))
                base_calculo_nota_valor = float(tributo.icms_st_vbc) if tributo.icms_st_vbc else None
                base_calculo_calculada_valor = float(base_calculo)

                # Usar a função de comparação completa
                comparacao = self._comparar_campos_fiscais(
                    tributo, cenario, 'icms_st',
                    valor_nota, valor_calculado,
                    base_calculo_nota_valor, base_calculo_calculada_valor,
                    cenario_status
                )

                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id, tipo_tributo='icms_st'
                ).first()

                if resultado_existente:
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = base_calculo_nota_valor
                    resultado_existente.base_calculo_calculada = base_calculo_calculada_valor
                    resultado_existente.status = comparacao['status']
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                    # Atualizar campos de comparação
                    resultado_existente.ncm_nota = comparacao['ncm_nota']
                    resultado_existente.ncm_cenario = comparacao['ncm_cenario']
                    resultado_existente.cfop_nota = comparacao['cfop_nota']
                    resultado_existente.cfop_cenario = comparacao['cfop_cenario']
                    resultado_existente.cst_nota = comparacao['cst_nota']
                    resultado_existente.cst_cenario = comparacao['cst_cenario']
                    resultado_existente.origem_nota = comparacao['origem_nota']
                    resultado_existente.origem_cenario = comparacao['origem_cenario']
                    resultado_existente.aliquota_nota = comparacao['aliquota_nota']
                    resultado_existente.aliquota_cenario = comparacao['aliquota_cenario']
                    resultado_existente.inconsistencia_valor = comparacao['inconsistencia_valor']
                    resultado_existente.inconsistencia_ncm = comparacao['inconsistencia_ncm']
                    resultado_existente.inconsistencia_cfop = comparacao['inconsistencia_cfop']
                    resultado_existente.inconsistencia_cst = comparacao['inconsistencia_cst']
                    resultado_existente.inconsistencia_origem = comparacao['inconsistencia_origem']
                    resultado_existente.inconsistencia_aliquota = comparacao['inconsistencia_aliquota']
                    resultado_existente.inconsistencia_base_calculo = comparacao['inconsistencia_base_calculo']
                else:
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='icms_st',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=base_calculo_nota_valor,
                        base_calculo_calculada=base_calculo_calculada_valor,
                        status=comparacao['status'],
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now(),
                        # Campos de comparação
                        ncm_nota=comparacao['ncm_nota'],
                        ncm_cenario=comparacao['ncm_cenario'],
                        cfop_nota=comparacao['cfop_nota'],
                        cfop_cenario=comparacao['cfop_cenario'],
                        cst_nota=comparacao['cst_nota'],
                        cst_cenario=comparacao['cst_cenario'],
                        origem_nota=comparacao['origem_nota'],
                        origem_cenario=comparacao['origem_cenario'],
                        aliquota_nota=comparacao['aliquota_nota'],
                        aliquota_cenario=comparacao['aliquota_cenario'],
                        inconsistencia_valor=comparacao['inconsistencia_valor'],
                        inconsistencia_ncm=comparacao['inconsistencia_ncm'],
                        inconsistencia_cfop=comparacao['inconsistencia_cfop'],
                        inconsistencia_cst=comparacao['inconsistencia_cst'],
                        inconsistencia_origem=comparacao['inconsistencia_origem'],
                        inconsistencia_aliquota=comparacao['inconsistencia_aliquota'],
                        inconsistencia_base_calculo=comparacao['inconsistencia_base_calculo']
                    )
                    
                    db.session.add(resultado)

                self._atualizar_sumario_auditoria(tributo, 'icms_st', comparacao['status'])
                return True
            else:
                print(f"DEBUG ICMS-ST: Tributo ID {tributo.id} - Sem valor ICMS-ST na nota fiscal (icms_st_valor é None)")
                print(f"  - Cenário ID: {cenario.id}")
                print(f"  - Valor calculado: {valor_icms_st}")
                self._atualizar_sumario_auditoria(tributo, 'icms_st', 'nao_auditado')
                return False
        else:
            print(f"DEBUG ICMS-ST: Tributo ID {tributo.id} - Sem item de nota fiscal vinculado (nota_fiscal_item_id é None)")
            return False

        return False

    def _auditar_pis(self, tributo, icms_auditado):
        """
        Audita o PIS para um tributo usando o vínculo direto com o cenário.
        """
        if not tributo.cenario_pis_id:
            return False

        cenario = db.session.get(CenarioPIS, tributo.cenario_pis_id)

        if not cenario or cenario.status != 'producao':
            return False

        cenario_status = 'producao'

        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_icms = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor and icms_auditado else None
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

        base_calculo, valor_pis = TributoCalculationService.calcular_pis(
            valor_total, cenario, valor_icms, valor_frete, valor_desconto
        )

        tributo.cenario_pis_vbc = float(base_calculo)
        tributo.cenario_pis_valor = float(valor_pis)

        if tributo.nota_fiscal_item_id:
            if tributo.pis_valor is not None:
                valor_nota = Decimal(str(tributo.pis_valor))
                valor_calculado = Decimal(str(valor_pis))
                base_calculo_nota_valor = float(tributo.pis_vbc) if tributo.pis_vbc else None
                base_calculo_calculada_valor = float(base_calculo)

                # Usar a função de comparação completa
                comparacao = self._comparar_campos_fiscais(
                    tributo, cenario, 'pis',
                    valor_nota, valor_calculado,
                    base_calculo_nota_valor, base_calculo_calculada_valor,
                    cenario_status
                )

                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id, tipo_tributo='pis'
                ).first()

                if resultado_existente:
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = base_calculo_nota_valor
                    resultado_existente.base_calculo_calculada = base_calculo_calculada_valor
                    resultado_existente.status = comparacao['status']
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                    # Atualizar campos de comparação
                    resultado_existente.ncm_nota = comparacao['ncm_nota']
                    resultado_existente.ncm_cenario = comparacao['ncm_cenario']
                    resultado_existente.cfop_nota = comparacao['cfop_nota']
                    resultado_existente.cfop_cenario = comparacao['cfop_cenario']
                    resultado_existente.cst_nota = comparacao['cst_nota']
                    resultado_existente.cst_cenario = comparacao['cst_cenario']
                    resultado_existente.aliquota_nota = comparacao['aliquota_nota']
                    resultado_existente.aliquota_cenario = comparacao['aliquota_cenario']
                    resultado_existente.inconsistencia_valor = comparacao['inconsistencia_valor']
                    resultado_existente.inconsistencia_ncm = comparacao['inconsistencia_ncm']
                    resultado_existente.inconsistencia_cfop = comparacao['inconsistencia_cfop']
                    resultado_existente.inconsistencia_cst = comparacao['inconsistencia_cst']
                    resultado_existente.inconsistencia_aliquota = comparacao['inconsistencia_aliquota']
                    resultado_existente.inconsistencia_base_calculo = comparacao['inconsistencia_base_calculo']
                else:
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='pis',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=base_calculo_nota_valor,
                        base_calculo_calculada=base_calculo_calculada_valor,
                        status=comparacao['status'],
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now(),
                        # Campos de comparação
                        ncm_nota=comparacao['ncm_nota'],
                        ncm_cenario=comparacao['ncm_cenario'],
                        cfop_nota=comparacao['cfop_nota'],
                        cfop_cenario=comparacao['cfop_cenario'],
                        cst_nota=comparacao['cst_nota'],
                        cst_cenario=comparacao['cst_cenario'],
                        aliquota_nota=comparacao['aliquota_nota'],
                        aliquota_cenario=comparacao['aliquota_cenario'],
                        inconsistencia_valor=comparacao['inconsistencia_valor'],
                        inconsistencia_ncm=comparacao['inconsistencia_ncm'],
                        inconsistencia_cfop=comparacao['inconsistencia_cfop'],
                        inconsistencia_cst=comparacao['inconsistencia_cst'],
                        inconsistencia_aliquota=comparacao['inconsistencia_aliquota'],
                        inconsistencia_base_calculo=comparacao['inconsistencia_base_calculo']
                    )
                    db.session.add(resultado)

                self._atualizar_sumario_auditoria(tributo, 'pis', comparacao['status'])
                return True
            else:
                self._atualizar_sumario_auditoria(tributo, 'pis', 'nao_auditado')
                return False

        return False

    def _auditar_cofins(self, tributo, icms_auditado):
        """
        Audita o COFINS para um tributo usando o vínculo direto com o cenário.
        """
        if not tributo.cenario_cofins_id:
            return False

        cenario = db.session.get(CenarioCOFINS, tributo.cenario_cofins_id)

        if not cenario or cenario.status != 'producao':
            return False

        cenario_status = 'producao'

        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')
        valor_icms = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor and icms_auditado else None
        valor_frete = Decimal(str(tributo.valor_frete)) if tributo.valor_frete else None
        valor_desconto = Decimal(str(tributo.valor_desconto)) if tributo.valor_desconto else None

        base_calculo, valor_cofins = TributoCalculationService.calcular_cofins(
            valor_total, cenario, valor_icms, valor_frete, valor_desconto
        )

        tributo.cenario_cofins_vbc = float(base_calculo)
        tributo.cenario_cofins_valor = float(valor_cofins)

        if tributo.nota_fiscal_item_id:
            if tributo.cofins_valor is not None:
                valor_nota = Decimal(str(tributo.cofins_valor))
                valor_calculado = Decimal(str(valor_cofins))
                base_calculo_nota_valor = float(tributo.cofins_vbc) if tributo.cofins_vbc else None
                base_calculo_calculada_valor = float(base_calculo)

                # Usar a função de comparação completa
                comparacao = self._comparar_campos_fiscais(
                    tributo, cenario, 'cofins',
                    valor_nota, valor_calculado,
                    base_calculo_nota_valor, base_calculo_calculada_valor,
                    cenario_status
                )

                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id, tipo_tributo='cofins'
                ).first()

                if resultado_existente:
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = base_calculo_nota_valor
                    resultado_existente.base_calculo_calculada = base_calculo_calculada_valor
                    resultado_existente.status = comparacao['status']
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                    # Atualizar campos de comparação
                    resultado_existente.ncm_nota = comparacao['ncm_nota']
                    resultado_existente.ncm_cenario = comparacao['ncm_cenario']
                    resultado_existente.cfop_nota = comparacao['cfop_nota']
                    resultado_existente.cfop_cenario = comparacao['cfop_cenario']
                    resultado_existente.cst_nota = comparacao['cst_nota']
                    resultado_existente.cst_cenario = comparacao['cst_cenario']
                    resultado_existente.aliquota_nota = comparacao['aliquota_nota']
                    resultado_existente.aliquota_cenario = comparacao['aliquota_cenario']
                    resultado_existente.inconsistencia_valor = comparacao['inconsistencia_valor']
                    resultado_existente.inconsistencia_ncm = comparacao['inconsistencia_ncm']
                    resultado_existente.inconsistencia_cfop = comparacao['inconsistencia_cfop']
                    resultado_existente.inconsistencia_cst = comparacao['inconsistencia_cst']
                    resultado_existente.inconsistencia_aliquota = comparacao['inconsistencia_aliquota']
                    resultado_existente.inconsistencia_base_calculo = comparacao['inconsistencia_base_calculo']
                else:
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='cofins',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=base_calculo_nota_valor,
                        base_calculo_calculada=base_calculo_calculada_valor,
                        status=comparacao['status'],
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now(),
                        # Campos de comparação
                        ncm_nota=comparacao['ncm_nota'],
                        ncm_cenario=comparacao['ncm_cenario'],
                        cfop_nota=comparacao['cfop_nota'],
                        cfop_cenario=comparacao['cfop_cenario'],
                        cst_nota=comparacao['cst_nota'],
                        cst_cenario=comparacao['cst_cenario'],
                        aliquota_nota=comparacao['aliquota_nota'],
                        aliquota_cenario=comparacao['aliquota_cenario'],
                        inconsistencia_valor=comparacao['inconsistencia_valor'],
                        inconsistencia_ncm=comparacao['inconsistencia_ncm'],
                        inconsistencia_cfop=comparacao['inconsistencia_cfop'],
                        inconsistencia_cst=comparacao['inconsistencia_cst'],
                        inconsistencia_aliquota=comparacao['inconsistencia_aliquota'],
                        inconsistencia_base_calculo=comparacao['inconsistencia_base_calculo']
                    )
                    db.session.add(resultado)

                self._atualizar_sumario_auditoria(tributo, 'cofins', comparacao['status'])
                return True
            else:
                self._atualizar_sumario_auditoria(tributo, 'cofins', 'nao_auditado')
                return False

        return False

    def _auditar_difal(self, tributo):
        """
        Audita o DIFAL para um tributo usando o vínculo direto com o cenário.
        """
        if not tributo.cenario_difal_id:
            return False

        cenario = db.session.get(CenarioDIFAL, tributo.cenario_difal_id)

        if not cenario or cenario.status != 'producao':
            return False

        cenario_status = 'producao'

        valor_total = Decimal(str(tributo.valor_total)) if tributo.valor_total else Decimal('0.00')

        valor_difal = TributoCalculationService.calcular_difal(valor_total, cenario)

        tributo.cenario_difal_valor = float(valor_difal)

        if tributo.nota_fiscal_item_id:
            if tributo.difal_v_icms_uf_dest is not None:
                valor_nota = Decimal(str(tributo.difal_v_icms_uf_dest))
                valor_calculado = Decimal(str(valor_difal))
                base_calculo_nota_valor = float(tributo.difal_vbcuf_dest) if tributo.difal_vbcuf_dest else None

                # Usar a função de comparação completa
                comparacao = self._comparar_campos_fiscais(
                    tributo, cenario, 'difal',
                    valor_nota, valor_calculado,
                    base_calculo_nota_valor, None,
                    cenario_status
                )

                resultado_existente = AuditoriaResultado.query.filter_by(
                    tributo_id=tributo.id, tipo_tributo='difal'
                ).first()

                if resultado_existente:
                    resultado_existente.cenario_id = cenario.id
                    resultado_existente.valor_nota = float(valor_nota)
                    resultado_existente.valor_calculado = float(valor_calculado)
                    resultado_existente.base_calculo_nota = base_calculo_nota_valor
                    resultado_existente.base_calculo_calculada = None
                    resultado_existente.status = comparacao['status']
                    resultado_existente.cenario_status = cenario_status
                    resultado_existente.data_auditoria = datetime.now()
                    # Atualizar campos de comparação
                    resultado_existente.ncm_nota = comparacao['ncm_nota']
                    resultado_existente.ncm_cenario = comparacao['ncm_cenario']
                    resultado_existente.cfop_nota = comparacao['cfop_nota']
                    resultado_existente.cfop_cenario = comparacao['cfop_cenario']
                    resultado_existente.cst_nota = comparacao['cst_nota']
                    resultado_existente.cst_cenario = comparacao['cst_cenario']
                    resultado_existente.aliquota_nota = comparacao['aliquota_nota']
                    resultado_existente.aliquota_cenario = comparacao['aliquota_cenario']
                    resultado_existente.inconsistencia_valor = comparacao['inconsistencia_valor']
                    resultado_existente.inconsistencia_ncm = comparacao['inconsistencia_ncm']
                    resultado_existente.inconsistencia_cfop = comparacao['inconsistencia_cfop']
                    resultado_existente.inconsistencia_cst = comparacao['inconsistencia_cst']
                    resultado_existente.inconsistencia_aliquota = comparacao['inconsistencia_aliquota']
                    resultado_existente.inconsistencia_base_calculo = comparacao['inconsistencia_base_calculo']
                else:
                    resultado = AuditoriaResultado(
                        empresa_id=tributo.empresa_id,
                        escritorio_id=tributo.escritorio_id,
                        tributo_id=tributo.id,
                        nota_fiscal_item_id=tributo.nota_fiscal_item_id,
                        tipo_tributo='difal',
                        cenario_id=cenario.id,
                        valor_nota=float(valor_nota),
                        valor_calculado=float(valor_calculado),
                        base_calculo_nota=base_calculo_nota_valor,
                        base_calculo_calculada=None,
                        status=comparacao['status'],
                        cenario_status=cenario_status,
                        data_auditoria=datetime.now(),
                        # Campos de comparação
                        ncm_nota=comparacao['ncm_nota'],
                        ncm_cenario=comparacao['ncm_cenario'],
                        cfop_nota=comparacao['cfop_nota'],
                        cfop_cenario=comparacao['cfop_cenario'],
                        cst_nota=comparacao['cst_nota'],
                        cst_cenario=comparacao['cst_cenario'],
                        aliquota_nota=comparacao['aliquota_nota'],
                        aliquota_cenario=comparacao['aliquota_cenario'],
                        inconsistencia_valor=comparacao['inconsistencia_valor'],
                        inconsistencia_ncm=comparacao['inconsistencia_ncm'],
                        inconsistencia_cfop=comparacao['inconsistencia_cfop'],
                        inconsistencia_cst=comparacao['inconsistencia_cst'],
                        inconsistencia_aliquota=comparacao['inconsistencia_aliquota'],
                        inconsistencia_base_calculo=comparacao['inconsistencia_base_calculo']
                    )
                    db.session.add(resultado)

                self._atualizar_sumario_auditoria(tributo, 'difal', comparacao['status'])
                return True
            else:
                self._atualizar_sumario_auditoria(tributo, 'difal', 'nao_auditado')
                return False

        return False

    def _comparar_campos_fiscais(self, tributo, cenario, tipo_tributo, valor_nota, valor_calculado, base_calculo_nota, base_calculo_calculada, cenario_status='producao'):
        """
        Compara campos fiscais entre nota e cenário para determinar inconsistências

        Args:
            tributo: Objeto do tributo da nota fiscal
            cenario: Objeto do cenário aplicado
            tipo_tributo: Tipo do tributo ('icms', 'icms_st', etc.)
            valor_nota: Valor do tributo na nota
            valor_calculado: Valor calculado pelo cenário
            base_calculo_nota: Base de cálculo da nota
            base_calculo_calculada: Base de cálculo calculada

        Returns:
            dict: Dicionário com informações de comparação e inconsistências
        """
        # Obter campos da nota fiscal
        item_nota = tributo.nota_fiscal_item
        ncm_nota = item_nota.ncm if item_nota else None
        cfop_nota = item_nota.cfop if item_nota else None
        
        # Para ICMS-ST, usar o CST do ICMS (não existe icms_st_cst)
        if tipo_tributo == 'icms_st':
            cst_nota = tributo.icms_cst
            origem_nota = tributo.icms_origem
        else:
            cst_nota = getattr(tributo, f"{tipo_tributo.replace('-', '_')}_cst", None)
            origem_nota = getattr(tributo, f"{tipo_tributo.replace('-', '_')}_origem", None)

        # Para ICMS e ICMS-ST, usar campos específicos
        if tipo_tributo == 'icms':
            aliquota_nota = float(tributo.icms_aliquota) if tributo.icms_aliquota else None
        elif tipo_tributo == 'icms_st':
            aliquota_nota = float(tributo.icms_st_aliquota) if tributo.icms_st_aliquota else None
        elif tipo_tributo == 'ipi':
            aliquota_nota = float(tributo.ipi_aliquota) if tributo.ipi_aliquota else None
        elif tipo_tributo == 'pis':
            aliquota_nota = float(tributo.pis_aliquota) if tributo.pis_aliquota else None
        elif tipo_tributo == 'cofins':
            aliquota_nota = float(tributo.cofins_aliquota) if tributo.cofins_aliquota else None
        elif tipo_tributo == 'difal':
            aliquota_nota = float(tributo.difal_p_icms_uf_dest) if tributo.difal_p_icms_uf_dest else None
        else:
            aliquota_nota = None

        # Obter campos do cenário
        cst_cenario = cenario.cst if hasattr(cenario, 'cst') else None
        origem_cenario = cenario.origem if hasattr(cenario, 'origem') else None

        # Para ICMS-ST, usar alíquota específica se disponível
        if tipo_tributo == 'icms_st' and hasattr(cenario, 'icms_st_aliquota') and cenario.icms_st_aliquota:
            aliquota_cenario = float(cenario.icms_st_aliquota)
        elif hasattr(cenario, 'aliquota') and cenario.aliquota:
            aliquota_cenario = float(cenario.aliquota)
        else:
            aliquota_cenario = None

        # Comparar campos com tolerância para valores numéricos
        tolerancia_valor = Decimal('0.01')
        tolerancia_aliquota = Decimal('0.0001')  # Tolerância menor para alíquotas

        # Verificar inconsistências
        inconsistencia_valor = abs(Decimal(str(valor_nota)) - Decimal(str(valor_calculado))) > tolerancia_valor
        inconsistencia_cst = cst_nota != cst_cenario
        inconsistencia_origem = origem_nota != origem_cenario

        inconsistencia_aliquota = False
        if aliquota_nota is not None and aliquota_cenario is not None:
            inconsistencia_aliquota = abs(Decimal(str(aliquota_nota)) - Decimal(str(aliquota_cenario))) > tolerancia_aliquota

        inconsistencia_base_calculo = False
        if base_calculo_nota is not None and base_calculo_calculada is not None:
            inconsistencia_base_calculo = abs(Decimal(str(base_calculo_nota)) - Decimal(str(base_calculo_calculada))) > tolerancia_valor

        # Obter NCM e CFOP do cenário
        ncm_cenario = cenario.ncm if hasattr(cenario, 'ncm') else None
        cfop_cenario = cenario.cfop if hasattr(cenario, 'cfop') else None
        
        # Comparar NCM e CFOP
        inconsistencia_ncm = False
        if ncm_nota and ncm_cenario:
            inconsistencia_ncm = ncm_nota.strip() != ncm_cenario.strip()
            
        inconsistencia_cfop = False
        if cfop_nota and cfop_cenario:
            inconsistencia_cfop = cfop_nota.strip() != cfop_cenario.strip()

        # Determinar status geral
        # Se o cenário utilizado for inconsistente, marcar como inconsistente independente dos valores
        if cenario_status == 'inconsistente':
            status = 'inconsistente'
        else:
            tem_inconsistencia = (inconsistencia_valor or inconsistencia_ncm or inconsistencia_cfop or 
                                 inconsistencia_cst or inconsistencia_origem or inconsistencia_aliquota or
                                 inconsistencia_base_calculo)
            status = 'inconsistente' if tem_inconsistencia else 'conforme'

        return {
            'status': status,
            'ncm_nota': ncm_nota,
            'ncm_cenario': ncm_cenario,
            'cfop_nota': cfop_nota,
            'cfop_cenario': cfop_cenario,
            'cst_nota': cst_nota,
            'cst_cenario': cst_cenario,
            'origem_nota': origem_nota,
            'origem_cenario': origem_cenario,
            'aliquota_nota': aliquota_nota,
            'aliquota_cenario': aliquota_cenario,
            'inconsistencia_valor': inconsistencia_valor,
            'inconsistencia_ncm': inconsistencia_ncm,
            'inconsistencia_cfop': inconsistencia_cfop,
            'inconsistencia_cst': inconsistencia_cst,
            'inconsistencia_origem': inconsistencia_origem,
            'inconsistencia_aliquota': inconsistencia_aliquota,
            'inconsistencia_base_calculo': inconsistencia_base_calculo
        }

    def _limpar_sumarios_tipo_tributo(self, tipo_tributo, meses_ref=None, tributo_ids=None):
        """
        Limpa os sumários de auditoria para um tipo de tributo específico.

        Args:
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            meses_ref (set, optional): Conjunto de tuplas (ano, mes) a serem removidas. Se None, remove todos os meses.
            tributo_ids (list, optional): IDs de tributos para remover dos resultados.
        """
        try:
            with transactional_session():
                query_sumarios = AuditoriaSumario.query.filter_by(
                    empresa_id=self.empresa_id,
                    tipo_tributo=tipo_tributo
                )
                if meses_ref:
                    conds = [and_(AuditoriaSumario.ano == a, AuditoriaSumario.mes == m) for a, m in meses_ref]
                    query_sumarios = query_sumarios.filter(or_(*conds))
                query_sumarios.delete(synchronize_session=False)

                query_resultados = AuditoriaResultado.query.filter_by(
                    empresa_id=self.empresa_id,
                    tipo_tributo=tipo_tributo
                )
                if tributo_ids:
                    query_resultados = query_resultados.filter(AuditoriaResultado.tributo_id.in_(tributo_ids))
                query_resultados.delete(synchronize_session=False)

        except Exception as e:
            print(f"Erro ao limpar sumarios do tipo {tipo_tributo}: {e}")

    def _criar_sumario_vazio(self, tipo_tributo, tributos):
        """
        Cria um sumário vazio quando não há dados válidos para auditar,
        mas a auditoria foi executada para indicar que o processo foi concluído.
        
        Args:
            tipo_tributo (str): Tipo de tributo (icms, icms_st, ipi, pis, cofins, difal)
            tributos (list): Lista de tributos que foram processados
        """
        if not tributos:
            return
            
        # Usar o primeiro tributo para obter informações básicas
        primeiro_tributo = tributos[0]
        
        if not primeiro_tributo.data_emissao:
            return
            
        ano = primeiro_tributo.data_emissao.year
        mes = primeiro_tributo.data_emissao.month
        
        # Verificar se já existe um sumário para esta empresa/ano/mês/tipo
        sumario_existente = AuditoriaSumario.query.filter_by(
            empresa_id=self.empresa_id,
            ano=ano,
            mes=mes,
            tipo_tributo=tipo_tributo
        ).first()
        
        # Se já existe, não criar outro
        if sumario_existente:
            return
            
        # Criar sumário vazio
        sumario = AuditoriaSumario(
            empresa_id=self.empresa_id,
            escritorio_id=primeiro_tributo.escritorio_id,
            ano=ano,
            mes=mes,
            tipo_tributo=tipo_tributo,
            total_notas=0,
            total_produtos=0,
            valor_total_notas=0,
            valor_total_cenarios=0,
            valor_total_tributo=0,
            total_conforme=0,
            total_inconsistente=0,
            notas_conformes=0,
            notas_inconsistentes=0,
            valor_inconsistente_maior=0,
            valor_inconsistente_menor=0
        )
        
        db.session.add(sumario)
        
        try:
            db.session.commit()
            print(f"Sumário vazio criado para {tipo_tributo.upper()} - {ano}/{mes:02d}")
        except Exception as e:
            db.session.rollback()
            print(f"Erro ao criar sumário vazio para {tipo_tributo}: {e}")

    def _atualizar_sumario_auditoria(self, tributo, tipo_tributo, status):
        """
        Atualiza o sumário de auditoria para um tipo de tributo

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
            status (str): Status da auditoria ('conforme', 'inconsistente')

        Returns:
            bool: True se a atualização foi realizada, False caso contrário
        """
        # Obter o ano e mês da data de emissão
        if not tributo.data_emissao:
            return False

        ano = tributo.data_emissao.year
        mes = tributo.data_emissao.month

        # Verificar se já existe um sumário para esta empresa/ano/mês/tipo
        sumario = AuditoriaSumario.query.filter_by(
            empresa_id=tributo.empresa_id,
            ano=ano,
            mes=mes,
            tipo_tributo=tipo_tributo
        ).first()

        # Se não existir, criar um novo
        if not sumario:
            sumario = AuditoriaSumario(
                empresa_id=tributo.empresa_id,
                escritorio_id=tributo.escritorio_id,
                ano=ano,
                mes=mes,
                tipo_tributo=tipo_tributo,
                total_notas=0,
                total_produtos=0,
                valor_total_notas=0,
                valor_total_cenarios=0,
                valor_total_tributo=0,
                total_conforme=0,
                total_inconsistente=0,
                notas_conformes=0,
                notas_inconsistentes=0,
                valor_inconsistente_maior=0,
                valor_inconsistente_menor=0
            )
            db.session.add(sumario)
            db.session.flush()  # Obter o ID sem commit

        # Obter o item de nota fiscal
        nota_fiscal_item = NotaFiscalItem.query.get(tributo.nota_fiscal_item_id)
        if not nota_fiscal_item:
            return False

        # Atualizar contadores
        if status == 'conforme':
            sumario.total_conforme += 1
        elif status == 'inconsistente':
            sumario.total_inconsistente += 1

        # Cada item processado conta como um produto, mesmo que não tenha sido auditado
        sumario.total_produtos += 1

        # Atualizar valores
        # Obter as notas já contabilizadas
        notas_contabilizadas = sumario.get_notas_contabilizadas()

        # Verificar se é uma nova nota
        numero_nota = str(nota_fiscal_item.numero_nf)
        nova_nota = numero_nota not in notas_contabilizadas

        if nova_nota:
            sumario.total_notas += 1
            notas_contabilizadas.add(numero_nota)
            sumario.set_notas_contabilizadas(notas_contabilizadas)

            # Para nova nota, determinar se é conforme ou inconsistente
            # Uma nota é considerada inconsistente se pelo menos um item for inconsistente
            if status == 'inconsistente':
                sumario.notas_inconsistentes += 1
            elif status == 'conforme':
                # Verificar se já existe algum item inconsistente para esta nota
                # Se sim, não contar como conforme (já foi contada como inconsistente)
                tem_item_inconsistente = self._verificar_nota_tem_item_inconsistente(
                    nota_fiscal_item.numero_nf, tributo.empresa_id, tipo_tributo, ano, mes
                )
                if not tem_item_inconsistente:
                    sumario.notas_conformes += 1
        else:
            # Nota já existe, verificar se precisa atualizar status
            if status == 'inconsistente':
                # Se a nota já foi contabilizada como conforme, mover para inconsistente
                if self._nota_estava_conforme(numero_nota, sumario, tributo.id):
                    sumario.notas_conformes -= 1
                    sumario.notas_inconsistentes += 1

        # Obter valores do tributo da nota fiscal e do cenário calculado
        valor_nota = Decimal('0')
        valor_calculado = Decimal('0')

        if tipo_tributo == 'icms':
            valor_nota = Decimal(str(tributo.icms_valor)) if tributo.icms_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_icms_valor)) if tributo.cenario_icms_valor else Decimal('0')
        elif tipo_tributo == 'icms_st':
            valor_nota = Decimal(str(tributo.icms_st_valor)) if tributo.icms_st_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_icms_st_valor)) if tributo.cenario_icms_st_valor else Decimal('0')
        elif tipo_tributo == 'ipi':
            valor_nota = Decimal(str(tributo.ipi_valor)) if tributo.ipi_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_ipi_valor)) if tributo.cenario_ipi_valor else Decimal('0')
        elif tipo_tributo == 'pis':
            valor_nota = Decimal(str(tributo.pis_valor)) if tributo.pis_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_pis_valor)) if tributo.cenario_pis_valor else Decimal('0')
        elif tipo_tributo == 'cofins':
            valor_nota = Decimal(str(tributo.cofins_valor)) if tributo.cofins_valor else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_cofins_valor)) if tributo.cenario_cofins_valor else Decimal('0')
        elif tipo_tributo == 'difal':
            valor_nota = Decimal(str(tributo.difal_v_icms_uf_dest)) if tributo.difal_v_icms_uf_dest else Decimal('0')
            valor_calculado = Decimal(str(tributo.cenario_difal_valor)) if tributo.cenario_difal_valor else Decimal('0')

        # Atualizar valores totais dos tributos (nota vs cenário)
        sumario.valor_total_notas += valor_nota
        sumario.valor_total_cenarios += valor_calculado

        # Manter compatibilidade com valor_total_tributo (será igual ao valor_total_cenarios)
        sumario.valor_total_tributo = sumario.valor_total_cenarios

        # Atualizar valores inconsistentes (a maior ou a menor)
        if status == 'inconsistente':
            diferenca = valor_calculado - valor_nota
            if diferenca < 0:
                # Valor calculado é maior que o valor da nota (a maior)
                sumario.valor_inconsistente_maior += abs(diferenca)
            else:
                # Valor calculado é menor que o valor da nota (a menor)
                sumario.valor_inconsistente_menor += diferenca

        # Atualizar data de atualização
        sumario.data_atualizacao = datetime.now()

        return True

    @staticmethod
    def validar_empresa_auditoria_completa(empresa_id, year=None, month=None):
        """
        Valida se uma empresa está 100% auditada (todos os tributos aplicáveis foram auditados)

        Args:
            empresa_id (int): ID da empresa
            year (int, optional): Ano para filtrar
            month (int, optional): Mês para filtrar

        Returns:
            dict: {
                'auditoria_completa': bool,
                'tributos_aplicaveis': list,
                'tributos_auditados': list,
                'tributos_pendentes': list,
                'total_operacoes_tributarias': int,
                'detalhes': dict
            }
        """
        from models import AuditoriaSumario

        # Todos os tipos de tributos do sistema
        todos_tributos = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']

        # Buscar status manuais da empresa (tributos marcados como não aplicáveis)
        # Por enquanto, assumir que não há tributos não aplicáveis
        tributos_nao_aplicaveis = set()

        # Determinar tributos aplicáveis (todos menos os não aplicáveis)
        tributos_aplicaveis = [t for t in todos_tributos if t not in tributos_nao_aplicaveis]

        # Buscar sumários de auditoria da empresa
        query = AuditoriaSumario.query.filter_by(empresa_id=empresa_id)

        if year and month:
            query = query.filter_by(ano=year, mes=month)

        sumarios = query.all()
        tributos_com_sumario = {s.tipo_tributo for s in sumarios}

        # Tributos auditados = tributos com sumário + tributos não aplicáveis
        tributos_auditados = list(tributos_com_sumario.union(tributos_nao_aplicaveis))

        # Tributos pendentes = tributos aplicáveis - tributos auditados
        tributos_pendentes = [t for t in tributos_aplicaveis if t not in tributos_auditados]

        # Verificar se auditoria está completa
        auditoria_completa = len(tributos_pendentes) == 0

        # Calcular total de operações tributárias
        # Para cada tributo aplicável, contar o número de produtos únicos auditados
        total_operacoes = 0
        detalhes_por_tributo = {}

        for tributo_tipo in tributos_aplicaveis:
            if tributo_tipo in tributos_nao_aplicaveis:
                # Tributo não aplicável = 0 operações
                detalhes_por_tributo[tributo_tipo] = {
                    'status': 'nao_aplicavel',
                    'produtos_auditados': 0,
                    'operacoes': 0
                }
            else:
                # Buscar sumário para este tributo
                sumario = next((s for s in sumarios if s.tipo_tributo == tributo_tipo), None)
                if sumario:
                    produtos_auditados = sumario.total_produtos or 0
                    detalhes_por_tributo[tributo_tipo] = {
                        'status': 'auditado',
                        'produtos_auditados': produtos_auditados,
                        'operacoes': produtos_auditados
                    }
                    total_operacoes += produtos_auditados
                else:
                    detalhes_por_tributo[tributo_tipo] = {
                        'status': 'pendente',
                        'produtos_auditados': 0,
                        'operacoes': 0
                    }

        return {
            'auditoria_completa': auditoria_completa,
            'tributos_aplicaveis': tributos_aplicaveis,
            'tributos_auditados': tributos_auditados,
            'tributos_pendentes': tributos_pendentes,
            'total_operacoes_tributarias': total_operacoes,
            'detalhes': detalhes_por_tributo
        }

    def _verificar_nota_tem_item_inconsistente(self, numero_nf, empresa_id, tipo_tributo, ano, mes):
        """
        Verifica se uma nota fiscal já possui algum item inconsistente auditado

        Args:
            numero_nf (str): Número da nota fiscal
            empresa_id (int): ID da empresa
            tipo_tributo (str): Tipo de tributo
            ano (int): Ano da auditoria
            mes (int): Mês da auditoria

        Returns:
            bool: True se a nota tem pelo menos um item inconsistente
        """
        try:
            # Buscar resultados de auditoria inconsistentes para esta nota
            resultado_inconsistente = AuditoriaResultado.query.filter_by(
                empresa_id=empresa_id,
                tipo_tributo=tipo_tributo,
                numero=numero_nf,
                status='inconsistente'
            ).filter(
                db.func.extract('year', AuditoriaResultado.data_emissao) == ano,
                db.func.extract('month', AuditoriaResultado.data_emissao) == mes
            ).first()

            return resultado_inconsistente is not None

        except Exception as e:
            return False

    def _nota_estava_conforme(self, numero_nota, sumario, tributo_id):
        """Verifica se uma nota estava anteriormente marcada como conforme.

        A nota é considerada "conforme" apenas se, até o momento da
        verificação, não existe nenhum item inconsistente já registrado
        para ela. O tributo atualmente em processamento é ignorado na
        verificação para evitar que ele próprio faça a nota parecer
        inconsistente antes da atualização do sumário.

        Args:
            numero_nota (str): Número da nota fiscal.
            sumario (AuditoriaSumario): Objeto do sumário correspondente.
            tributo_id (int): ID do tributo atualmente sendo processado.

        Returns:
            bool: True se a nota estava contabilizada como conforme.
        """
        try:
            resultado_inconsistente = AuditoriaResultado.query.filter(
                AuditoriaResultado.empresa_id == sumario.empresa_id,
                AuditoriaResultado.tipo_tributo == sumario.tipo_tributo,
                AuditoriaResultado.numero == numero_nota,
                AuditoriaResultado.status == 'inconsistente',
                db.func.extract('year', AuditoriaResultado.data_emissao) == sumario.ano,
                db.func.extract('month', AuditoriaResultado.data_emissao) == sumario.mes,
                AuditoriaResultado.tributo_id != tributo_id
            ).first()

            # Se não existe nenhum outro item inconsistente, a nota estava
            # marcada como conforme.
            return resultado_inconsistente is None
        except Exception:
            return False


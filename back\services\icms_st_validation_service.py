"""
Serviço de validação ICMS-ST usando API externa
"""
import requests
import hashlib
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from sqlalchemy import and_
from decimal import Decimal

from models import db, CenarioICMSST, Empresa, <PERSON><PERSON><PERSON>, <PERSON>du<PERSON>
from models.icms_st_cache import ICMSSTCacheModel
from models.icms_st_validation_result import ICMSSTValidationResultModel

class ICMSSTValidationService:
    
    # Configurações da API
    API_BASE_URL_INTERNA = "https://www.legisweb.com.br/api/st-interna/"
    API_BASE_URL_INTERESTADUAL = "https://www.legisweb.com.br/api/st-interestadual/"
    API_TOKEN = "8741902a3f20e527c0b76c5b3a918b3f"
    CLIENTE_ID = "65597"
    
    # Mapeamento de destinação de mercadoria
    DESTINACAO_MAPPING = {
        'Revenda': '1',  # Op. Subsequente - Comercialização
        'Ativo Imobilizado': '2',  # Ativo Fixo ou Imobilizado
        'Uso e Consumo': '3',  # Uso e Consumo
        'Varejista': '4'  # Transferência para Varejista
    }
    
    # CFOPs válidos para validação ICMS-ST (saída)
    CFOPS_VALIDOS = ['5401', '5405', '6401', '6403', '5910', '5949']
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 30
    
    def _normalizar_cest(self, cest: str) -> str:
        """
        Normaliza CEST removendo pontos para comparação
        Exemplo: '17.096.00' -> '17096000' ou '1709600'
        """
        if not cest:
            return ''
        # Remove pontos e espaços
        return cest.replace('.', '').replace(' ', '').strip()
    
    def _verificar_vigencia(self, dados_api: Dict) -> Dict:
        """
        Verifica se os dados da API estão dentro da vigência
        Retorna informações sobre a vigência
        """
        from datetime import datetime
        
        # Verificar se dados_api é um dicionário válido
        if not isinstance(dados_api, dict):
            return {
                'vigente': True,
                'motivo': 'Dados inválidos para verificação de vigência',
                'vigencia_inicial': None,
                'vigencia_final': None
            }
        
        vigencia_inicial = dados_api.get('vigencia_inicial')
        vigencia_final = dados_api.get('vigencia_final')
        data_atual = datetime.now()
        
        resultado = {
            'vigente': True,
            'motivo': '',
            'vigencia_inicial': vigencia_inicial,
            'vigencia_final': vigencia_final
        }
        
        try:
            # Verificar vigência inicial
            if vigencia_inicial:
                # Formato esperado: "01/08/2023"
                data_inicial = datetime.strptime(vigencia_inicial, '%d/%m/%Y')
                if data_atual < data_inicial:
                    resultado['vigente'] = False
                    resultado['motivo'] = f'Vigência ainda não iniciada. Início: {vigencia_inicial}'
                    return resultado
            
            # Verificar vigência final
            if vigencia_final:
                data_final = datetime.strptime(vigencia_final, '%d/%m/%Y')
                if data_atual > data_final:
                    resultado['vigente'] = False
                    resultado['motivo'] = f'Vigência expirada em {vigencia_final}'
                    return resultado
            
        except ValueError as e:
            # Se não conseguir parsear as datas, considerar vigente
            print(f"⚠️  Erro ao verificar vigência: {str(e)}")
            resultado['motivo'] = 'Não foi possível verificar vigência (formato de data inválido)'
        
        return resultado
    
    def _gerar_hash_consulta(self, tipo_st: str, ncm: str, cest: str = None, **kwargs) -> str:
        """
        Gera hash único para a consulta incluindo CEST
        Cada CEST deve ter seu próprio registro no cache
        """
        if tipo_st == 'interna':
            dados = f"{tipo_st}_{ncm}_{kwargs.get('estado', '')}_{cest or ''}"
        else:  # interestadual
            dados = f"{tipo_st}_{ncm}_{kwargs.get('estado_origem', '')}_{kwargs.get('estado_destino', '')}_{kwargs.get('destinacao_mercadoria', '')}_{cest or ''}"
        
        return hashlib.sha256(dados.encode()).hexdigest()
    
    def _validar_resposta_api(self, resposta: Dict) -> bool:
        """
        Valida se a resposta da API é válida e contém dados úteis
        """
        if not isinstance(resposta, dict):
            print(f"⚠️  Resposta da API não é um dicionário: {type(resposta)}")
            return False
        
        # Verificar se tem a chave 'resposta'
        if 'resposta' not in resposta:
            print(f"⚠️  Resposta da API não contém chave 'resposta': {resposta}")
            return False
        
        resposta_data = resposta['resposta']
        
        # Se resposta é uma string (erro da API), não é válida
        if isinstance(resposta_data, str):
            print(f"⚠️  API retornou mensagem de erro: {resposta_data}")
            return False
        
        # Se resposta não é uma lista, não é válida
        if not isinstance(resposta_data, list):
            print(f"⚠️  Resposta da API não é uma lista: {type(resposta_data)}")
            return False
        
        # Se lista está vazia, não é válida
        if len(resposta_data) == 0:
            print(f"ℹ️  API retornou lista vazia (NCM não encontrado)")
            return False
        
        print(f"✅ Resposta da API válida com {len(resposta_data)} registros")
        return True
    
    def _consultar_api_st_interna(self, ncm: str, estado: str) -> Optional[Dict]:
        """
        Consulta a API de ST interna
        """
        try:
            params = {
                't': self.API_TOKEN,
                'c': self.CLIENTE_ID,
                'estado': estado,
                'ncm': ncm
            }
            
            response = self.session.get(self.API_BASE_URL_INTERNA, params=params)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            print(f"Erro ao consultar API ST interna: {str(e)}")
            return None
    
    def _consultar_api_st_interestadual(self, ncm: str, estado_origem: str, estado_destino: str, destinacao_mercadoria: str) -> Optional[Dict]:
        """
        Consulta a API de ST interestadual
        """
        try:
            destinacao_codigo = self.DESTINACAO_MAPPING.get(destinacao_mercadoria)
            if not destinacao_codigo:
                raise ValueError(f"Destinação de mercadoria inválida: {destinacao_mercadoria}")
            
            params = {
                't': self.API_TOKEN,
                'c': self.CLIENTE_ID,
                'estado_origem': estado_origem,
                'estado_destino': estado_destino,
                'destinacao_mercadoria': destinacao_codigo,
                'ncm': ncm
            }
            
            response = self.session.get(self.API_BASE_URL_INTERESTADUAL, params=params)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            print(f"Erro ao consultar API ST interestadual: {str(e)}")
            return None
    
    def _processar_resposta_st_interna(self, resposta: Dict, ncm: str, estado: str, hash_consulta_base: str) -> List[Dict]:
        """
        Processa resposta da API ST interna e salva no cache
        Cada CEST terá seu próprio hash único
        """
        resultados = []
        
        # Processar cada item da resposta individualmente
        for item in resposta.get('resposta', []):
            # Debug: verificar tipo do item
            if not isinstance(item, dict):
                print(f"⚠️  Item da API não é dicionário: {type(item)} - {item}")
                continue
                
            cest = item.get('cest', '')
            
            # Gerar hash específico para este CEST
            hash_cest = self._gerar_hash_consulta('interna', ncm, cest, estado=estado)
            
            # Verificar se já existe cache para este CEST específico
            cache_existente = ICMSSTCacheModel.query.filter_by(hash_consulta=hash_cest).first()
            if cache_existente:
                print(f"ℹ️  Cache ST interna já existe para CEST {cest}")
                resultados.append(cache_existente.to_dict())
                continue
            
            # Criar novo registro no cache para este CEST
            cache_item = ICMSSTCacheModel(
                tipo_st='interna',
                ncm=ncm,
                estado=estado,
                hash_consulta=hash_cest,  # Hash específico do CEST
                codigo=item.get('codigo'),
                cest=cest,
                descricao=item.get('descricao'),
                observacao=item.get('observacao'),
                segmento=item.get('segmento'),
                aliquota_interna=Decimal(str(item.get('aliquota_interna', 0))) if item.get('aliquota_interna') else None,
                fundo_pobreza=item.get('fundo_pobreza'),
                mva=Decimal(str(item.get('mva', 0))) if item.get('mva') else None,
                mva_ajustada=Decimal(str(item.get('mva_ajustada', 0))) if item.get('mva_ajustada') else None,
                mva_ajustada_4=Decimal(str(item.get('mva_ajustada_4', 0))) if item.get('mva_ajustada_4') else None,
                mva_ajustada_7=Decimal(str(item.get('mva_ajustada_7', 0))) if item.get('mva_ajustada_7') else None,
                mva_ajustada_12=Decimal(str(item.get('mva_ajustada_12', 0))) if item.get('mva_ajustada_12') else None,
                mva_positiva=Decimal(str(item.get('mva_positiva', 0))) if item.get('mva_positiva') else None,
                mva_negativa=Decimal(str(item.get('mva_negativa', 0))) if item.get('mva_negativa') else None,
                mva_neutra=Decimal(str(item.get('mva_neutra', 0))) if item.get('mva_neutra') else None,
                vigencia_inicial=item.get('vigencia_inicial'),
                vigencia_final=item.get('vigencia_final'),
                base_legal_st=item.get('base_legal_st'),
                data_efeito_st=item.get('data_efeito_st'),
                base_calculo=item.get('base_calculo'),
                prazo_recolhimento=item.get('prazo_recolhimento'),
                aplicabilidade=item.get('aplicabilidade'),
                nao_aplicabilidade=item.get('nao_aplicabilidade'),
                variacao_mva=item.get('variacao_mva'),
                reducao_mva=item.get('reducao_mva')
            )
            
            # Tentar salvar este CEST específico
            try:
                db.session.add(cache_item)
                db.session.commit()
                print(f"✅ Cache ST interna salvo para CEST {cest} (hash: {hash_cest[:16]}...)")
                resultados.append(cache_item.to_dict())
            except Exception as e:
                db.session.rollback()
                print(f"⚠️  Erro ao salvar cache ST interna para CEST {cest}: {str(e)}")
                # Mesmo com erro, adicionar aos resultados
                resultados.append(cache_item.to_dict())
        
        return resultados
    
    def _processar_resposta_st_interestadual(self, resposta: Dict, ncm: str, estado_origem: str, estado_destino: str, destinacao_mercadoria: str, hash_consulta_base: str) -> List[Dict]:
        """
        Processa resposta da API ST interestadual e salva no cache
        Cada CEST terá seu próprio hash único
        """
        resultados = []
        
        # Processar cada item da resposta individualmente
        for item in resposta.get('resposta', []):
            # Debug: verificar tipo do item
            if not isinstance(item, dict):
                print(f"⚠️  Item da API ST interestadual não é dicionário: {type(item)} - {item}")
                continue
                
            cest = item.get('cest', '')
            
            # Gerar hash específico para este CEST
            hash_cest = self._gerar_hash_consulta(
                'interestadual', ncm, cest,
                estado_origem=estado_origem,
                estado_destino=estado_destino,
                destinacao_mercadoria=destinacao_mercadoria
            )
            
            # Verificar se já existe cache para este CEST específico
            cache_existente = ICMSSTCacheModel.query.filter_by(hash_consulta=hash_cest).first()
            if cache_existente:
                print(f"ℹ️  Cache ST interestadual já existe para CEST {cest}")
                resultados.append(cache_existente.to_dict())
                continue
            
            # Criar novo registro no cache para este CEST
            cache_item = ICMSSTCacheModel(
                tipo_st='interestadual',
                ncm=ncm,
                estado_origem=estado_origem,
                estado_destino=estado_destino,
                destinacao_mercadoria=destinacao_mercadoria,
                hash_consulta=hash_cest,  # Hash específico do CEST
                codigo=item.get('codigo'),
                cest=cest,
                descricao=item.get('descricao'),
                observacao=item.get('observacao'),
                segmento=item.get('segmento'),
                codigo_segmento=item.get('codigo_segmento'),
                aliquota_interna=Decimal(str(item.get('aliquota_interna', 0))) if item.get('aliquota_interna') else None,
                aliquota_interestadual=Decimal(str(item.get('aliquota_interestadual', 0))) if item.get('aliquota_interestadual') else None,
                fundo_pobreza=item.get('fundo_pobreza'),
                mva=Decimal(str(item.get('mva', 0))) if item.get('mva') else None,
                mva_ajustada=Decimal(str(item.get('mva_ajustada', 0))) if item.get('mva_ajustada') else None,
                mva_ajustada_4=Decimal(str(item.get('mva_ajustada_4', 0))) if item.get('mva_ajustada_4') else None,
                mva_positiva=Decimal(str(item.get('mva_positiva', 0))) if item.get('mva_positiva') else None,
                mva_negativa=Decimal(str(item.get('mva_negativa', 0))) if item.get('mva_negativa') else None,
                mva_neutra=Decimal(str(item.get('mva_neutra', 0))) if item.get('mva_neutra') else None,
                vigencia_inicial=item.get('vigencia_inicial'),
                vigencia_final=item.get('vigencia_final'),
                base_legal_st=item.get('base_legal_st'),
                data_efeito_st=item.get('data_efeito_st'),
                norma_base_calculo=item.get('norma_base_calculo'),
                aplicabilidade=item.get('aplicabilidade'),
                nao_aplicabilidade=item.get('nao_aplicabilidade'),
                regime_origem=item.get('regime_origem'),
                regime_destino=item.get('regime_destino'),
                destino_produto=item.get('destino_produto'),
                base_legal_int=item.get('base_legal_int'),
                observacao_int=item.get('observacao_int'),
                base_calculo_int=item.get('base_calculo_int'),
                prazo_recolhimento_int=item.get('prazo_recolhimento_int'),
                norma_observacao_st=item.get('norma_observacao_st'),
                norma_prazo_recolhimento=item.get('norma_prazo_recolhimento'),
                variacao_mva=json.dumps(item.get('variacao_mva', [])) if item.get('variacao_mva') else None,
                reducao_mva=item.get('reducao_mva')
            )
            
            # Tentar salvar este CEST específico
            try:
                db.session.add(cache_item)
                db.session.commit()
                print(f"✅ Cache ST interestadual salvo para CEST {cest} (hash: {hash_cest[:16]}...)")
                resultados.append(cache_item.to_dict())
            except Exception as e:
                db.session.rollback()
                print(f"⚠️  Erro ao salvar cache ST interestadual para CEST {cest}: {str(e)}")
                # Mesmo com erro, adicionar aos resultados
                resultados.append(cache_item.to_dict())
        
        return resultados
    
    def obter_dados_icms_st(self, ncm: str, estado_empresa: str, estado_cliente: str, destinacao_mercadoria: str, cest_filtro: str = None, forcar_api: bool = False) -> List[Dict]:
        """
        Obtém dados ICMS-ST, primeiro do cache, depois da API se necessário
        
        Filtros utilizados:
        - ST Interna: NCM + Estado
        - ST Interestadual: NCM + Estado Origem + Estado Destino + Destinação Mercadoria
        - CEST: Usado para filtrar o resultado específico após obter os dados
        """
        # Determinar tipo de ST
        tipo_st = 'interna' if estado_empresa == estado_cliente else 'interestadual'
        
        # Gerar hash da consulta (sem CEST, pois CEST é filtro pós-consulta)
        if tipo_st == 'interna':
            hash_consulta = self._gerar_hash_consulta(tipo_st, ncm, estado=estado_empresa)
        else:
            hash_consulta = self._gerar_hash_consulta(
                tipo_st, ncm, 
                estado_origem=estado_empresa, 
                estado_destino=estado_cliente, 
                destinacao_mercadoria=destinacao_mercadoria
            )
        
        # Verificar cache primeiro (se não forçar API)
        dados_resultado = []
        if not forcar_api:
            if tipo_st == 'interna':
                dados_cache = ICMSSTCacheModel.buscar_st_interna(ncm, estado_empresa)
            else:
                dados_cache = ICMSSTCacheModel.buscar_st_interestadual(
                    ncm, estado_empresa, estado_cliente, destinacao_mercadoria
                )
            
            # Verificar se o cache não está muito antigo (30 dias)
            if dados_cache:
                data_limite = datetime.now() - timedelta(days=30)
                if dados_cache[0].data_consulta > data_limite:
                    dados_resultado = [item.to_dict() for item in dados_cache]
        
        # Se não encontrou no cache, consultar API
        if not dados_resultado:
            if tipo_st == 'interna':
                resposta_api = self._consultar_api_st_interna(ncm, estado_empresa)
                if resposta_api and self._validar_resposta_api(resposta_api):
                    dados_resultado = self._processar_resposta_st_interna(resposta_api, ncm, estado_empresa, hash_consulta)
            else:
                resposta_api = self._consultar_api_st_interestadual(ncm, estado_empresa, estado_cliente, destinacao_mercadoria)
                if resposta_api and self._validar_resposta_api(resposta_api):
                    dados_resultado = self._processar_resposta_st_interestadual(
                        resposta_api, ncm, estado_empresa, estado_cliente, destinacao_mercadoria, hash_consulta
                    )
        
        # Filtrar por CEST específico se fornecido
        if cest_filtro and dados_resultado:
            dados_filtrados = [item for item in dados_resultado if item.get('cest') == cest_filtro]
            return dados_filtrados if dados_filtrados else dados_resultado
        
        return dados_resultado
    
    def validar_cenarios_icms_st(self, empresa_id: int, cfop_filtro: str = None, status_filtro: str = 'novo') -> Dict:
        """
        Valida cenários ICMS-ST de uma empresa
        """
        try:
            # Buscar dados da empresa
            empresa = Empresa.query.get(empresa_id)
            if not empresa:
                return {'erro': 'Empresa não encontrada'}
            
            estado_empresa = empresa.estado
            if not estado_empresa:
                return {'erro': 'Empresa sem estado definido'}
            
            # Buscar cenários ICMS-ST
            query = CenarioICMSST.query.filter(
                CenarioICMSST.empresa_id == empresa_id,
                CenarioICMSST.direcao == 'saida',
                CenarioICMSST.status == status_filtro  # Filtrar pelo status da aba ativa
            )
            
            if cfop_filtro:
                query = query.filter(CenarioICMSST.cfop == cfop_filtro)
            else:
                query = query.filter(CenarioICMSST.cfop.in_(self.CFOPS_VALIDOS))
            
            cenarios = query.all()
            print(f"[DEBUG] Encontrados {len(cenarios)} cenários ICMS-ST para empresa {empresa_id} com status '{status_filtro}'")
            
            sugestoes = []
            
            # Validar cada cenário
            for cenario in cenarios:
                sugestao = self._validar_cenario_individual(cenario, estado_empresa)
                if sugestao:
                    sugestoes.append(sugestao)
            
            return {
                'empresa_id': empresa_id,
                'total_sugestoes': len(sugestoes),
                'sugestoes': sugestoes
            }
            
        except Exception as e:
            return {'erro': f'Erro na validação: {str(e)}'}
    
    def _validar_cenario_individual(self, cenario: CenarioICMSST, estado_empresa: str) -> Optional[Dict]:
        """
        Valida um cenário individual ICMS-ST
        """
        try:
            ncm = cenario.ncm
            if not ncm:
                return None
            
            # Buscar CEST do produto relacionado ao cenário
            produto = Produto.query.get(cenario.produto_id) if cenario.produto_id else None
            cest_cenario = produto.cest if produto else None
            
            # CEST é obrigatório para validação ICMS-ST
            if not cest_cenario:
                return {
                    'cenario_id': cenario.id,
                    'ncm': ncm,
                    'cfop': cenario.cfop,
                    'cst': cenario.cst,
                    'tipo_st': 'N/A',
                    'divergencias': [{
                        'campo': 'cest',
                        'valor_atual': '',
                        'valor_sugerido': 'OBRIGATÓRIO',
                        'motivo': 'CEST é obrigatório para validação ICMS-ST. Consulte a tabela CEST oficial.'
                    }],
                    'dados_api': None,
                    'opcoes_disponiveis': 0,
                    'dados_originais': {
                        'icms_st_aliquota': float(cenario.icms_st_aliquota or 0),
                        'icms_st_p_mva': float(cenario.icms_st_p_mva or 0),
                        'cest': None
                    }
                }
            
            # Buscar dados do cliente para obter estado e destinação
            cliente = Cliente.query.get(cenario.cliente_id) if cenario.cliente_id else None
            if not cliente:
                return None
            
            estado_cliente = cliente.uf or estado_empresa
            destinacao_mercadoria = cliente.destinacao or 'Revenda'
            
            # Obter dados da API/cache (sem filtro CEST para ver todas as opções disponíveis)
            dados_api = self.obter_dados_icms_st(ncm, estado_empresa, estado_cliente, destinacao_mercadoria)
            
            if not dados_api:
                # Determinar motivo específico da falha
                tipo_st = 'interna' if estado_empresa == estado_cliente else 'interestadual'
                
                if tipo_st == 'interna':
                    motivo = f'❌ NCM {ncm} não encontrado na base oficial de ICMS-ST interna para o estado {estado_empresa}. Possíveis causas: NCM incorreto, produto não sujeito à ST ou base desatualizada.'
                else:
                    motivo = f'❌ NCM {ncm} não encontrado na base oficial de ICMS-ST interestadual para {estado_empresa}→{estado_cliente} com destinação "{destinacao_mercadoria}". Possíveis causas: NCM incorreto, operação não sujeita à ST ou combinação de estados/destinação inválida.'
                
                return {
                    'cenario_id': cenario.id,
                    'ncm': ncm,
                    'cfop': cenario.cfop,
                    'cst': cenario.cst,
                    'cest': cest_cenario,
                    'tipo_st': tipo_st,
                    'divergencias': [{
                        'campo': 'ncm',
                        'valor_atual': ncm,
                        'valor_sugerido': 'VERIFICAR NCM',
                        'motivo': motivo
                    }],
                    'dados_api': None,
                    'opcoes_disponiveis': 0,
                    'dados_originais': {
                        'aliquota': float(cenario.aliquota or 0),
                        'icms_st_aliquota': float(cenario.icms_st_aliquota or 0),
                        'icms_st_p_mva': float(cenario.icms_st_p_mva or 0),
                        'cest': cest_cenario
                    }
                }
            
            # Buscar dados específicos para o CEST do cenário (normalizar para comparação)
            cest_normalizado = self._normalizar_cest(cest_cenario)
            dados_cest = None
            
            for opcao in dados_api:
                # Verificar se opcao é um dicionário válido
                if not isinstance(opcao, dict):
                    continue
                    
                cest_api_normalizado = self._normalizar_cest(opcao.get('cest', ''))
                if cest_api_normalizado == cest_normalizado:
                    dados_cest = opcao
                    break
            
            if not dados_cest:
                # Listar CESTs disponíveis (normalizados e originais para debug)
                cests_disponiveis = []
                for i, d in enumerate(dados_api[:5]):  # Mostrar até 5 opções
                    # Debug: verificar tipo de cada item
                    if not isinstance(d, dict):
                        print(f"⚠️  Cenário {cenario.id}: Item {i} na lista de CESTs não é dicionário: {type(d)} - {str(d)[:50]}")
                        cests_disponiveis.append(f"ERRO: {type(d).__name__}")
                        continue
                        
                    cest_original = d.get("cest", "")
                    cest_norm = self._normalizar_cest(cest_original)
                    cests_disponiveis.append(f"{cest_original} ({cest_norm})")
                
                return {
                    'cenario_id': cenario.id,
                    'ncm': ncm,
                    'cfop': cenario.cfop,
                    'cst': cenario.cst,
                    'cest': cest_cenario,
                    'tipo_st': 'interna' if estado_empresa == estado_cliente else 'interestadual',
                    'divergencias': [{
                        'campo': 'cest',
                        'valor_atual': cest_cenario,
                        'valor_sugerido': 'VERIFICAR',
                        'motivo': f'CEST {cest_cenario} (normalizado: {cest_normalizado}) não encontrado para NCM {ncm}. CESTs disponíveis: {", ".join(cests_disponiveis)}'
                    }],
                    'dados_api': None,
                    'opcoes_disponiveis': len(dados_api),
                    'dados_originais': {
                        'aliquota': float(cenario.aliquota or 0),
                        'icms_st_aliquota': float(cenario.icms_st_aliquota or 0),
                        'icms_st_p_mva': float(cenario.icms_st_p_mva or 0),
                        'cest': cest_cenario
                    }
                }
            
            # Verificar vigência dos dados encontrados
            try:
                vigencia_info = self._verificar_vigencia(dados_cest)
                if not vigencia_info['vigente']:
                    return {
                        'cenario_id': cenario.id,
                        'ncm': ncm,
                        'cfop': cenario.cfop,
                        'cst': cenario.cst,
                        'cest': cest_cenario,
                        'tipo_st': 'interna' if estado_empresa == estado_cliente else 'interestadual',
                        'divergencias': [{
                            'campo': 'vigencia',
                            'valor_atual': f"{vigencia_info['vigencia_inicial']} a {vigencia_info['vigencia_final']}",
                            'valor_sugerido': 'VERIFICAR VIGÊNCIA',
                            'motivo': f'⚠️ {vigencia_info["motivo"]}. Dados podem estar desatualizados.'
                        }],
                        'dados_api': dados_cest,
                        'opcoes_disponiveis': len(dados_api),
                        'dados_originais': {
                            'aliquota': float(cenario.aliquota or 0),
                            'icms_st_aliquota': float(cenario.icms_st_aliquota or 0),
                            'icms_st_p_mva': float(cenario.icms_st_p_mva or 0),
                            'cest': cest_cenario
                        }
                    }
            except Exception as e:
                print(f"⚠️  Erro ao verificar vigência para cenário {cenario.id}: {str(e)}")
                # Continuar sem verificação de vigência se houver erro
            
            # Comparar dados do cenário com os dados oficiais do CEST específico
            divergencias = []
            
            # Comparar alíquota ICMS-ST
            aliquota_api = dados_cest.get('aliquota_interna') or dados_cest.get('aliquota_interestadual') or 0
            aliquota_cenario = float(cenario.icms_st_aliquota or 0)
            
            if abs(aliquota_api - aliquota_cenario) > 0.01:
                divergencias.append({
                    'campo': 'icms_st_aliquota',
                    'valor_atual': aliquota_cenario,
                    'valor_sugerido': aliquota_api,
                    'motivo': f'Alíquota ICMS-ST divergente da tabela oficial para CEST {cest_cenario}'
                })
            
            # Comparar MVA (priorizar mva_ajustada se disponível)
            mva_api = dados_cest.get('mva_ajustada') or dados_cest.get('mva') or 0
            mva_cenario = float(cenario.icms_st_p_mva or 0)
            
            if abs(mva_api - mva_cenario) > 0.01:
                divergencias.append({
                    'campo': 'icms_st_p_mva',
                    'valor_atual': mva_cenario,
                    'valor_sugerido': mva_api,
                    'motivo': f'MVA ICMS-ST divergente da tabela oficial para CEST {cest_cenario}'
                })
            
            # Se não há divergências, não retornar sugestão
            if not divergencias:
                return None
            
            return {
                'cenario_id': cenario.id,
                'ncm': ncm,
                'cfop': cenario.cfop,
                'cst': cenario.cst,
                'cest': cest_cenario,
                'tipo_st': 'interna' if estado_empresa == estado_cliente else 'interestadual',
                'divergencias': divergencias,
                'dados_api': dados_cest,
                'opcoes_disponiveis': len(dados_api),
                'dados_originais': {
                    'aliquota': float(cenario.aliquota or 0),  # Alíquota ICMS
                    'icms_st_aliquota': aliquota_cenario,      # Alíquota ICMS-ST
                    'icms_st_p_mva': mva_cenario,              # MVA
                    'cest': cest_cenario
                }
            }
            
        except Exception as e:
            print(f"Erro ao validar cenário ICMS-ST {cenario.id}: {str(e)}")
            return None
    
    def aplicar_sugestao(self, cenario_id: int, sugestoes: List[Dict], usuario: str = None) -> Dict:
        """
        Aplica sugestões de correção em um cenário ICMS-ST
        """
        try:
            # Buscar cenário
            cenario = CenarioICMSST.query.get(cenario_id)
            if not cenario:
                return {'erro': 'Cenário não encontrado'}
            
            # Salvar dados originais
            dados_originais = {
                'icms_st_aliquota': float(cenario.icms_st_aliquota or 0),
                'icms_st_p_mva': float(cenario.icms_st_p_mva or 0),
                'cest': getattr(cenario, 'cest', None)
            }
            
            # Aplicar sugestões
            for sugestao in sugestoes:
                campo = sugestao.get('campo')
                valor_sugerido = sugestao.get('valor_sugerido')
                
                if campo == 'icms_st_aliquota':
                    cenario.icms_st_aliquota = Decimal(str(valor_sugerido))
                elif campo == 'icms_st_p_mva':
                    cenario.icms_st_p_mva = Decimal(str(valor_sugerido))
                elif campo == 'cest':
                    # CEST pode não existir no modelo base, verificar se existe
                    if hasattr(cenario, 'cest'):
                        cenario.cest = valor_sugerido
            
            # Salvar alterações
            db.session.commit()
            
            # Registrar no histórico
            resultado = ICMSSTValidationResultModel(
                empresa_id=cenario.empresa_id,
                cenario_id=cenario_id,
                dados_originais=dados_originais,
                sugestoes=sugestoes,
                tipo_validacao='ICMS_ST_COMPLETA',
                status='aplicado',
                aplicado_em=datetime.now(),
                aplicado_por=usuario
            )
            
            db.session.add(resultado)
            db.session.commit()
            
            return {'sucesso': True, 'cenario_id': cenario_id}
            
        except Exception as e:
            db.session.rollback()
            return {'erro': f'Erro ao aplicar sugestão: {str(e)}'}
    
    def obter_historico_validacoes(self, empresa_id: int) -> List[Dict]:
        """
        Obtém histórico de validações ICMS-ST de uma empresa
        """
        try:
            resultados = ICMSSTValidationResultModel.query.filter(
                ICMSSTValidationResultModel.empresa_id == empresa_id
            ).order_by(ICMSSTValidationResultModel.data_validacao.desc()).all()
            
            return [resultado.to_dict() for resultado in resultados]
            
        except Exception as e:
            print(f"Erro ao obter histórico ICMS-ST: {str(e)}")
            return []
import { AdvancedFilterDropdown } from '@/components/cenarios/filters/AdvancedFilterDropdown'
import { SimpleTextFilter } from '@/components/cenarios/filters/SimpleTextFilter'
import { Button } from '@/components/ui/Button'
import type {
  AuditoriaComparativaFilterState,
  AuditoriaComparativaFilterOptions,
} from '@/hooks/useAuditoriaComparativaFilters'

interface AuditoriaComparativaFilterRowProps {
  columns: Array<{ key: string }>
  filters: AuditoriaComparativaFilterState
  options: AuditoriaComparativaFilterOptions
  isLoading: boolean
  hasActiveFilters: boolean
  onUpdateFilter: (
    filter: keyof AuditoriaComparativaFilterState,
    values: string[]
  ) => void
  onUpdateTextFilter: (
    filter: keyof AuditoriaComparativaFilterState,
    value: string
  ) => void
  onClearAllFilters: () => void
}

export function AuditoriaComparativaFilterRow({
  columns,
  filters,
  options,
  isLoading,
  hasActiveFilters,
  onUpdateFilter,
  onUpdateTextFilter,
  onClearAllFilters,
}: AuditoriaComparativaFilterRowProps) {
  return (
    <tr className="bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700">
      {columns.map((column, index) => {
        if (index === 0) {
          return (
            <th key={column.key} className="px-3 py-2 w-12">
              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="xs"
                  onClick={onClearAllFilters}
                  className="text-error-600 hover:text-error-700 hover:bg-error-50 dark:text-error-400 dark:hover:bg-error-900/20"
                  title="Limpar filtros"
                >
                  <svg
                    className="w-3 h-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </Button>
              )}
            </th>
          )
        }

        switch (column.key) {
          case 'numero_nf':
            return (
              <th key={column.key} className="px-3 py-2">
                <SimpleTextFilter
                  placeholder="Nota..."
                  onFilter={(v) => onUpdateTextFilter('numero_nf', v)}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'data_emissao':
            return (
              <th key={column.key} className="px-3 py-2">
                <SimpleTextFilter
                  placeholder="Data..."
                  onFilter={(v) => onUpdateTextFilter('data_emissao', v)}
                  className="min-w-[90px]"
                />
              </th>
            )
          case 'participante':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.parceiros}
                  selectedValues={filters.parceiros}
                  placeholder="Parceiros"
                  onApply={(vals) => onUpdateFilter('parceiros', vals)}
                  onClear={() => onUpdateFilter('parceiros', [])}
                  isLoading={isLoading}
                  className="min-w-[120px]"
                />
              </th>
            )
          case 'uf':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.ufs}
                  selectedValues={filters.ufs}
                  placeholder="UF"
                  onApply={(vals) => onUpdateFilter('ufs', vals)}
                  onClear={() => onUpdateFilter('ufs', [])}
                  isLoading={isLoading}
                  className="min-w-[60px]"
                />
              </th>
            )
          case 'regime':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.regimes}
                  selectedValues={filters.regimes}
                  placeholder="Regime"
                  onApply={(vals) => onUpdateFilter('regimes', vals)}
                  onClear={() => onUpdateFilter('regimes', [])}
                  isLoading={isLoading}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'produto_codigo_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <SimpleTextFilter
                  placeholder="Cod. SPED"
                  onFilter={(v) => onUpdateTextFilter('produto_codigo_sped', v)}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'produto_codigo_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <SimpleTextFilter
                  placeholder="Cod. Nota"
                  onFilter={(v) => onUpdateTextFilter('produto_codigo_nota', v)}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'descricao':
            return (
              <th key={column.key} className="px-3 py-2">
                <SimpleTextFilter
                  placeholder="Produto..."
                  onFilter={(v) => onUpdateTextFilter('produto_descricao', v)}
                  className="min-w-[120px]"
                />
              </th>
            )
          case 'ncm_nfe':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.ncms_xml}
                  selectedValues={filters.ncms_xml}
                  placeholder="NCM NFe"
                  onApply={(vals) => onUpdateFilter('ncms_xml', vals)}
                  onClear={() => onUpdateFilter('ncms_xml', [])}
                  isLoading={isLoading}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'ncm_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.ncms_sped}
                  selectedValues={filters.ncms_sped}
                  placeholder="NCM SPED"
                  onApply={(vals) => onUpdateFilter('ncms_sped', vals)}
                  onClear={() => onUpdateFilter('ncms_sped', [])}
                  isLoading={isLoading}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'tipo':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.tipos}
                  selectedValues={filters.tipos}
                  placeholder="Tipo"
                  onApply={(vals) => onUpdateFilter('tipos', vals)}
                  onClear={() => onUpdateFilter('tipos', [])}
                  isLoading={isLoading}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'descricao_tipo':
            return (
              <th key={column.key} className="px-3 py-2">
                <SimpleTextFilter
                  placeholder="Desc. Tipo"
                  onFilter={(v) => onUpdateTextFilter('descricao_tipo', v)}
                  className="min-w-[120px]"
                />
              </th>
            )
          case 'produto_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <SimpleTextFilter
                  placeholder="Produto Nota"
                  onFilter={(v) => onUpdateTextFilter('produto_nota', v)}
                  className="min-w-[120px]"
                />
              </th>
            )
          case 'origem_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.origens_sped}
                  selectedValues={filters.origens_sped}
                  placeholder="Origem SPED"
                  onApply={(vals) => onUpdateFilter('origens_sped', vals)}
                  onClear={() => onUpdateFilter('origens_sped', [])}
                  isLoading={isLoading}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'origem_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.origens_xml}
                  selectedValues={filters.origens_xml}
                  placeholder="Origem Nota"
                  onApply={(vals) => onUpdateFilter('origens_xml', vals)}
                  onClear={() => onUpdateFilter('origens_xml', [])}
                  isLoading={isLoading}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'cfop_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.cfops_sped}
                  selectedValues={filters.cfops_sped}
                  placeholder="CFOP SPED"
                  onApply={(vals) => onUpdateFilter('cfops_sped', vals)}
                  onClear={() => onUpdateFilter('cfops_sped', [])}
                  isLoading={isLoading}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'cfop_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.cfops_xml}
                  selectedValues={filters.cfops_xml}
                  placeholder="CFOP Nota"
                  onApply={(vals) => onUpdateFilter('cfops_xml', vals)}
                  onClear={() => onUpdateFilter('cfops_xml', [])}
                  isLoading={isLoading}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'cst_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.csts_sped}
                  selectedValues={filters.csts_sped}
                  placeholder="CST SPED"
                  onApply={(vals) => onUpdateFilter('csts_sped', vals)}
                  onClear={() => onUpdateFilter('csts_sped', [])}
                  isLoading={isLoading}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'cst_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.csts_xml}
                  selectedValues={filters.csts_xml}
                  placeholder="CST Nota"
                  onApply={(vals) => onUpdateFilter('csts_xml', vals)}
                  onClear={() => onUpdateFilter('csts_xml', [])}
                  isLoading={isLoading}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'cst_pis_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.csts_sped_pis}
                  selectedValues={filters.csts_sped_pis}
                  placeholder="CST PIS SPED"
                  onApply={(vals) => onUpdateFilter('csts_sped_pis', vals)}
                  onClear={() => onUpdateFilter('csts_sped_pis', [])}
                  isLoading={isLoading}
                  className="min-w-[90px]"
                />
              </th>
            )
          case 'cst_cofins_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.csts_sped_cofins}
                  selectedValues={filters.csts_sped_cofins}
                  placeholder="CST COFINS SPED"
                  onApply={(vals) => onUpdateFilter('csts_sped_cofins', vals)}
                  onClear={() => onUpdateFilter('csts_sped_cofins', [])}
                  isLoading={isLoading}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'cst_pis_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.csts_xml_pis}
                  selectedValues={filters.csts_xml_pis}
                  placeholder="CST PIS Nota"
                  onApply={(vals) => onUpdateFilter('csts_xml_pis', vals)}
                  onClear={() => onUpdateFilter('csts_xml_pis', [])}
                  isLoading={isLoading}
                  className="min-w-[90px]"
                />
              </th>
            )
          case 'cst_cofins_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.csts_xml_cofins}
                  selectedValues={filters.csts_xml_cofins}
                  placeholder="CST COFINS Nota"
                  onApply={(vals) => onUpdateFilter('csts_xml_cofins', vals)}
                  onClear={() => onUpdateFilter('csts_xml_cofins', [])}
                  isLoading={isLoading}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'csosn_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.csosns}
                  selectedValues={filters.csosns}
                  placeholder="CSOSN"
                  onApply={(vals) => onUpdateFilter('csosns', vals)}
                  onClear={() => onUpdateFilter('csosns', [])}
                  isLoading={isLoading}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'reducao_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.reducoes_sped}
                  selectedValues={filters.reducoes_sped}
                  placeholder="Red. SPED"
                  onApply={(vals) => onUpdateFilter('reducoes_sped', vals)}
                  onClear={() => onUpdateFilter('reducoes_sped', [])}
                  isLoading={isLoading}
                  className="min-w-[90px]"
                />
              </th>
            )
          case 'reducao_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.reducoes_xml}
                  selectedValues={filters.reducoes_xml}
                  placeholder="Red. Nota"
                  onApply={(vals) => onUpdateFilter('reducoes_xml', vals)}
                  onClear={() => onUpdateFilter('reducoes_xml', [])}
                  isLoading={isLoading}
                  className="min-w-[90px]"
                />
              </th>
            )
          case 'mva_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.mvas}
                  selectedValues={filters.mvas}
                  placeholder="MVA%"
                  onApply={(vals) => onUpdateFilter('mvas', vals)}
                  onClear={() => onUpdateFilter('mvas', [])}
                  isLoading={isLoading}
                  className="min-w-[80px]"
                />
              </th>
            )
          case 'aliquota_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.aliquotas_sped}
                  selectedValues={filters.aliquotas_sped}
                  placeholder="Alíq SPED"
                  onApply={(vals) => onUpdateFilter('aliquotas_sped', vals)}
                  onClear={() => onUpdateFilter('aliquotas_sped', [])}
                  isLoading={isLoading}
                  className="min-w-[90px]"
                />
              </th>
            )
          case 'aliquota_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.aliquotas_xml}
                  selectedValues={filters.aliquotas_xml}
                  placeholder="Alíq Nota"
                  onApply={(vals) => onUpdateFilter('aliquotas_xml', vals)}
                  onClear={() => onUpdateFilter('aliquotas_xml', [])}
                  isLoading={isLoading}
                  className="min-w-[90px]"
                />
              </th>
            )
          case 'aliq_pis_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.aliquotas_sped_pis}
                  selectedValues={filters.aliquotas_sped_pis}
                  placeholder="Alíq PIS SPED"
                  onApply={(vals) => onUpdateFilter('aliquotas_sped_pis', vals)}
                  onClear={() => onUpdateFilter('aliquotas_sped_pis', [])}
                  isLoading={isLoading}
                  className="min-w-[90px]"
                />
              </th>
            )
          case 'aliq_cofins_sped':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.aliquotas_sped_cofins}
                  selectedValues={filters.aliquotas_sped_cofins}
                  placeholder="Alíq COFINS SPED"
                  onApply={(vals) => onUpdateFilter('aliquotas_sped_cofins', vals)}
                  onClear={() => onUpdateFilter('aliquotas_sped_cofins', [])}
                  isLoading={isLoading}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'aliq_pis_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.aliquotas_xml_pis}
                  selectedValues={filters.aliquotas_xml_pis}
                  placeholder="Alíq PIS Nota"
                  onApply={(vals) => onUpdateFilter('aliquotas_xml_pis', vals)}
                  onClear={() => onUpdateFilter('aliquotas_xml_pis', [])}
                  isLoading={isLoading}
                  className="min-w-[90px]"
                />
              </th>
            )
          case 'aliq_cofins_nota':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.aliquotas_xml_cofins}
                  selectedValues={filters.aliquotas_xml_cofins}
                  placeholder="Alíq COFINS Nota"
                  onApply={(vals) => onUpdateFilter('aliquotas_xml_cofins', vals)}
                  onClear={() => onUpdateFilter('aliquotas_xml_cofins', [])}
                  isLoading={isLoading}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'match':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.match_types}
                  selectedValues={filters.match_types}
                  placeholder="Match"
                  onApply={(vals) => onUpdateFilter('match_types', vals)}
                  onClear={() => onUpdateFilter('match_types', [])}
                  isLoading={isLoading}
                  className="min-w-[100px]"
                />
              </th>
            )
          case 'status':
            return (
              <th key={column.key} className="px-3 py-2">
                <AdvancedFilterDropdown
                  options={options.status}
                  selectedValues={filters.status}
                  placeholder="Status"
                  onApply={(vals) => onUpdateFilter('status', vals)}
                  onClear={() => onUpdateFilter('status', [])}
                  isLoading={isLoading}
                  className="min-w-[100px]"
                />
              </th>
            )
          default:
            return <th key={column.key} className="px-3 py-2"></th>
        }
      })}
    </tr>
  )
}
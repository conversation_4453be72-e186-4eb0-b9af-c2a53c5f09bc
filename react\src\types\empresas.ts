export interface Empresa {
    id: number;
    razao_social: string;
    cnpj: string;
    inscricao_estadual?: string;
    nome_fantasia?: string;
    email?: string;
    responsavel?: string;
    cep?: string;
    logradouro?: string;
    numero?: string;
    complemento?: string;
    bairro?: string;
    cidade?: string;
    estado?: string;
    cnae?: string;
    tributacao?: string;
    atividade?: string;
    pis_cofins?: string;
    observacoes?: string;
  }
  
  export interface EmpresasResponse {
    empresas: Empresa[];
  }
-- Migração para corrigir a precisão do campo percentual_divergencia
-- O campo atual NUMERIC(5,2) só suporta até 999.99%
-- Alterando para NUMERIC(10,2) para suportar até 99,999,999.99%

BEGIN;

-- Dropar a view que depende da coluna
DROP VIEW IF EXISTS vw_auditoria_escrituracao;

-- Alterar o campo percentual_divergencia para suportar valores maiores
ALTER TABLE auditoria_entrada 
ALTER COLUMN percentual_divergencia TYPE NUMERIC(10,2);

-- Adicionar comentário explicativo
COMMENT ON COLUMN auditoria_entrada.percentual_divergencia IS 'Percentual de divergência entre XML e SPED - NUMERIC(10,2) para suportar valores altos';

-- Recriar a view com a definição original
CREATE VIEW vw_auditoria_escrituracao AS 
SELECT 
    ae.id,
    ae.empresa_id,
    e.razao_social AS empresa_nome,
    ae.numero_nf,
    ae.chave_nf,
    ae.xml_data_entrada,
    ae.sped_data_entrada_saida,
    ae.xml_valor_total_nota,
    ae.sped_valor_total_nota,
    ae.divergencia_valor_total,
    ae.percentual_divergencia,
    ae.status_escrituracao,
    ae.justificativa_escrituracao,
    ae.data_aprovacao_escrituracao,
    u.nome AS usuario_aprovacao_nome,
    ae.mes_referencia,
    ae.ano_referencia,
    CASE
        WHEN (abs(COALESCE(ae.divergencia_valor_total, (0)::numeric)) <= 0.01) THEN 'Conforme'::text
        WHEN ((ae.status_escrituracao)::text = 'aprovado'::text) THEN 'Aprovado com Justificativa'::text
        ELSE 'Divergente'::text
    END AS status_display
FROM ((auditoria_entrada ae
    JOIN empresa e ON ((ae.empresa_id = e.id)))
    LEFT JOIN usuario u ON ((ae.usuario_aprovacao_escrituracao = u.id)))
WHERE ((ae.xml_valor_total_nota IS NOT NULL) AND (ae.sped_valor_total_nota IS NOT NULL));

COMMIT;
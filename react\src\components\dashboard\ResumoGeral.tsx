interface ResumoGeralProps {
  data?: {
    total_notas: number
    total_valor: number
    total_inconsistencias: number
    valor_inconsistente: number
    percentual_inconsistencias: number
  }
}

export function ResumoGeral({ data }: ResumoGeralProps) {
  if (!data) {
    return (
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          <svg className="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Resumo Geral
        </h3>
        <div className="text-center py-8">
          <div className="text-gray-400 dark:text-gray-500 mb-2">
            <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Nenhum dado disponível para o período selecionado
          </p>
        </div>
      </div>
    )
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value)
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('pt-BR').format(value)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`
  }

  return (
    <div className="card p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
        <svg className="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Resumo Geral
      </h3>

      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        {/* Total de Notas */}
        <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {formatNumber(data.total_notas)}
          </div>
          <div className="text-sm text-blue-700 dark:text-blue-300 mt-1">
            Total de Notas
          </div>
        </div>

        {/* Valor Total */}
        <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="text-lg font-bold text-green-600 dark:text-green-400">
            {formatCurrency(data.total_valor)}
          </div>
          <div className="text-sm text-green-700 dark:text-green-300 mt-1">
            Valor Total
          </div>
        </div>

        {/* Total de Inconsistências */}
        <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {formatNumber(data.total_inconsistencias)}
          </div>
          <div className="text-sm text-red-700 dark:text-red-300 mt-1">
            Inconsistências
          </div>
        </div>

        {/* Valor Inconsistente */}
        <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
            {formatCurrency(data.valor_inconsistente)}
          </div>
          <div className="text-sm text-orange-700 dark:text-orange-300 mt-1">
            Valor Inconsistente
          </div>
        </div>

        {/* Percentual */}
        <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {formatPercentage(data.percentual_inconsistencias)}
          </div>
          <div className="text-sm text-purple-700 dark:text-purple-300 mt-1">
            % Inconsistências
          </div>
        </div>
      </div>

      {/* Barra de Progresso Visual */}
      <div className="mt-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Qualidade dos Dados
          </span>
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {formatPercentage(100 - data.percentual_inconsistencias)} OK
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div 
            className="bg-gradient-to-r from-green-500 to-green-600 h-3 rounded-full transition-all duration-500"
            style={{ width: `${Math.max(0, 100 - data.percentual_inconsistencias)}%` }}
          />
        </div>
      </div>
    </div>
  )
}
-- Migration to add specific audit status fields for each tax type

-- Add status fields for each tax type
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_icms_status VARCHAR(20) DEFAULT 'pendente';
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_icms_st_status VARCHAR(20) DEFAULT 'pendente';
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_ipi_status VARCHAR(20) DEFAULT 'pendente';
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_pis_status VARCHAR(20) DEFAULT 'pendente';
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_cofins_status VARCHAR(20) DEFAULT 'pendente';
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_difal_status VARCHAR(20) DEFAULT 'pendente';

-- Add date fields for each tax type
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_icms_data TIMESTAMP;
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_icms_st_data TIMESTAMP;
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_ipi_data TIMESTAMP;
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_pis_data TIMESTAMP;
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_cofins_data TIMESTAMP;
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_difal_data TIMESTAMP;

-- Add comments for documentation
COMMENT ON COLUMN tributo.auditoria_icms_status IS 'Status da auditoria de ICMS (pendente, realizada)';
COMMENT ON COLUMN tributo.auditoria_icms_st_status IS 'Status da auditoria de ICMS-ST (pendente, realizada)';
COMMENT ON COLUMN tributo.auditoria_ipi_status IS 'Status da auditoria de IPI (pendente, realizada)';
COMMENT ON COLUMN tributo.auditoria_pis_status IS 'Status da auditoria de PIS (pendente, realizada)';
COMMENT ON COLUMN tributo.auditoria_cofins_status IS 'Status da auditoria de COFINS (pendente, realizada)';
COMMENT ON COLUMN tributo.auditoria_difal_status IS 'Status da auditoria de DIFAL (pendente, realizada)';

COMMENT ON COLUMN tributo.auditoria_icms_data IS 'Data da última auditoria de ICMS';
COMMENT ON COLUMN tributo.auditoria_icms_st_data IS 'Data da última auditoria de ICMS-ST';
COMMENT ON COLUMN tributo.auditoria_ipi_data IS 'Data da última auditoria de IPI';
COMMENT ON COLUMN tributo.auditoria_pis_data IS 'Data da última auditoria de PIS';
COMMENT ON COLUMN tributo.auditoria_cofins_data IS 'Data da última auditoria de COFINS';
COMMENT ON COLUMN tributo.auditoria_difal_data IS 'Data da última auditoria de DIFAL';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tributo_auditoria_icms_status ON tributo(auditoria_icms_status);
CREATE INDEX IF NOT EXISTS idx_tributo_auditoria_icms_st_status ON tributo(auditoria_icms_st_status);
CREATE INDEX IF NOT EXISTS idx_tributo_auditoria_ipi_status ON tributo(auditoria_ipi_status);
CREATE INDEX IF NOT EXISTS idx_tributo_auditoria_pis_status ON tributo(auditoria_pis_status);
CREATE INDEX IF NOT EXISTS idx_tributo_auditoria_cofins_status ON tributo(auditoria_cofins_status);
CREATE INDEX IF NOT EXISTS idx_tributo_auditoria_difal_status ON tributo(auditoria_difal_status);

-- Update existing records to set specific status fields based on the general auditoria_status
UPDATE tributo SET 
    auditoria_icms_status = auditoria_status,
    auditoria_icms_st_status = auditoria_status,
    auditoria_ipi_status = auditoria_status,
    auditoria_pis_status = auditoria_status,
    auditoria_cofins_status = auditoria_status,
    auditoria_difal_status = auditoria_status,
    auditoria_icms_data = auditoria_data,
    auditoria_icms_st_data = auditoria_data,
    auditoria_ipi_data = auditoria_data,
    auditoria_pis_data = auditoria_data,
    auditoria_cofins_data = auditoria_data,
    auditoria_difal_data = auditoria_data
WHERE auditoria_status = 'realizada';

import { useEffect, useMemo, useState } from 'react'
import {
  pisCofinsValidationService,
  PisCofinsValidationResult,
  PisCofinsDivergence
} from '@/services/pisCofinsValidationService'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'

interface Props {
  isOpen: boolean
  result: PisCofinsValidationResult | null
  onClose: () => void
  onApplied?: () => void
  onFilter?: (ids: number[]) => void
  onClearFilters?: () => void
}

interface TaxField {
  atual?: string | number
  sugerido?: string | number
  motivo?: string
}

interface CombinedRow {
  index: number
  cenario_ids: number[]
  ncm: string
  cfop: string
  codigo_produto?: string
  estado?: string
  destinacao_empresa?: string
  // regime_empresa?: string
  destinacao_cliente?: string
  pis: {
    cst: TaxField
    aliquota: TaxField
  }
  cofins: {
    cst: TaxField
    aliquota: TaxField
  }
  groupSize?: number
}

export function PisCofinsValidationModal({
  isOpen,
  result,
  onClose,
  onApplied,
  onFilter,
  onClearFilters
}: Props) {
  const [selected, setSelected] = useState<Set<number>>(new Set())
  const [loading, setLoading] = useState(false)
  const [groupMode, setGroupMode] = useState<'none' | 'ncm' | 'produto'>('none')

  const baseRows: CombinedRow[] = useMemo(() => {
    if (!result) return []
    const map = new Map<number, CombinedRow>()
    const parseCst = (v: any) => (typeof v === 'string' ? v.split('-')[0].trim() : v)
    result.sugestoes.forEach(s => {
      let row = map.get(s.cenario_id)
      if (!row) {
        row = {
          index: 0,
          cenario_ids: [s.cenario_id],
          ncm: s.ncm,
          cfop: s.cfop,
          codigo_produto: s.codigo_produto,
          estado: s.estado,
          destinacao_empresa: s.destinacao_empresa,
          // regime_empresa: s.regime_empresa,
          destinacao_cliente: s.destinacao_cliente,
          pis: { cst: {}, aliquota: {} },
          cofins: { cst: {}, aliquota: {} }
        }
        map.set(s.cenario_id, row)
      }
      const target = s.tributo === 'PIS' ? row!.pis : row!.cofins
      s.divergencias.forEach(d => {
        if (d.campo.toLowerCase() === 'cst') {
          target.cst = {
            atual: parseCst(d.valor_atual),
            sugerido: parseCst(d.valor_sugerido),
            motivo: d.motivo
          }
        } else if (d.campo.toLowerCase() === 'aliquota') {
          target.aliquota = { atual: d.valor_atual, sugerido: d.valor_sugerido, motivo: d.motivo }
        }
      })
      if (target.cst.atual == null && s.dados_originais?.cst != null) {
        target.cst.atual = parseCst(s.dados_originais.cst)
      }
    })
    return Array.from(map.values()).map((r, idx) => ({ ...r, index: idx }))
  }, [result])

  const hasPis = useMemo(() => result?.sugestoes.some(s => s.tributo === 'PIS') ?? false, [result])
  const hasCofins = useMemo(() => result?.sugestoes.some(s => s.tributo === 'COFINS') ?? false, [result])
  const modalTitle = hasPis && hasCofins ? 'Análise PIS/COFINS' : hasPis ? 'Análise PIS' : 'Análise COFINS'

  const rows = useMemo(() => {
    if (groupMode === 'none') return baseRows
    const groups = new Map<string, CombinedRow[]>()
    baseRows.forEach(r => {
      const keyBase = `${r.pis.cst.sugerido ?? r.pis.cst.atual}|${r.pis.aliquota.sugerido ?? r.pis.aliquota.atual}|${r.cofins.cst.sugerido ?? r.cofins.cst.atual}|${r.cofins.aliquota.sugerido ?? r.cofins.aliquota.atual}|${r.destinacao_cliente}|${r.estado}`
      const key = groupMode === 'ncm'
        ? `${r.ncm}|${keyBase}`
        : `${r.codigo_produto}|${keyBase}`
      const list = groups.get(key)
      if (list) list.push(r)
      else groups.set(key, [r])
    })
    const res: CombinedRow[] = []
    let idx = 0
    groups.forEach(group => {
      const base = { ...group[0] }
      base.index = idx++
      base.cenario_ids = group.flatMap(g => g.cenario_ids)
      base.groupSize = group.length
      res.push(base)
    })
    return res
  }, [baseRows, groupMode])

  useEffect(() => {
    setSelected(new Set())
  }, [groupMode])

  if (!isOpen || !result) return null

  const toggle = (idx: number, checked: boolean) => {
    const newSet = new Set(selected)
    if (checked) newSet.add(idx)
    else newSet.delete(idx)
    setSelected(newSet)
  }

  const toggleAll = (checked: boolean) => {
    if (checked) setSelected(new Set(rows.map(r => r.index)))
    else setSelected(new Set())
  }

  const getClasses = (a?: any, s?: any): [string, string] => {
    return a != null && s != null && a !== s
      ? ['text-error-600 dark:text-error-400', 'text-success-600 dark:text-success-400']
      : ['text-gray-900 dark:text-gray-100', 'text-gray-900 dark:text-gray-100']
  }

  const buildDivergencias = (row: CombinedRow, tributo: 'PIS' | 'COFINS'): PisCofinsDivergence[] => {
    const divergencias: PisCofinsDivergence[] = []
    const data = tributo === 'PIS' ? row.pis : row.cofins
    if (data.cst.atual !== data.cst.sugerido) {
      divergencias.push({
        campo: 'cst',
        valor_atual: data.cst.atual || '',
        valor_sugerido: data.cst.sugerido || '',
        motivo: data.cst.motivo || ''
      })
    }
    if (data.aliquota.atual !== data.aliquota.sugerido) {
      divergencias.push({
        campo: 'aliquota',
        valor_atual: data.aliquota.atual || '',
        valor_sugerido: data.aliquota.sugerido || '',
        motivo: data.aliquota.motivo || ''
      })
    }
    return divergencias
  }

  const applyRows = async (rowsToApply: CombinedRow[]) => {
    setLoading(true)
    try {
      for (const r of rowsToApply) {
        for (const id of r.cenario_ids) {
          const divPis = buildDivergencias(r, 'PIS')
          if (divPis.length) {
            await pisCofinsValidationService.applySuggestion(id, 'PIS', divPis)
          }
          const divCof = buildDivergencias(r, 'COFINS')
          if (divCof.length) {
            await pisCofinsValidationService.applySuggestion(id, 'COFINS', divCof)
          }
        }
      }
    } catch (e) {
      console.error(e)
    }
    setLoading(false)
    onApplied?.()
    onClose()
  }

  const handleApplyAll = () => applyRows(rows)
  const handleApplySelected = () => applyRows(rows.filter(r => selected.has(r.index)))

  const applyRow = (row: CombinedRow) => {
    if (row.cenario_ids.length > 1) {
      if (!window.confirm('Aplicar para todos os cenários agrupados?')) return
    }
    applyRows([row])
  }

  const editRow = async (row: CombinedRow) => {
    const newPisCst = hasPis
      ? prompt('CST PIS', String(row.pis.cst.sugerido ?? row.pis.cst.atual ?? ''))
      : null
    const newPisAliq = hasPis
      ? prompt('Alíquota PIS', String(row.pis.aliquota.sugerido ?? row.pis.aliquota.atual ?? ''))
      : null
    const newCofCst = hasCofins
      ? prompt('CST COFINS', String(row.cofins.cst.sugerido ?? row.cofins.cst.atual ?? ''))
      : null
    const newCofAliq = hasCofins
      ? prompt('Alíquota COFINS', String(row.cofins.aliquota.sugerido ?? row.cofins.aliquota.atual ?? ''))
      : null
    const updated: CombinedRow = { ...row }
    if (hasPis) {
      updated.pis = {
        cst: { ...row.pis.cst, sugerido: newPisCst ?? row.pis.cst.sugerido },
        aliquota: { ...row.pis.aliquota, sugerido: newPisAliq ?? row.pis.aliquota.sugerido }
      }
    }
    if (hasCofins) {
      updated.cofins = {
        cst: { ...row.cofins.cst, sugerido: newCofCst ?? row.cofins.cst.sugerido },
        aliquota: { ...row.cofins.aliquota, sugerido: newCofAliq ?? row.cofins.aliquota.sugerido }
      }
    }
    if (row.cenario_ids.length > 1) {
      if (!window.confirm('Aplicar alterações para todos os cenários agrupados?')) return
    }
    await applyRows([updated])
  }

  const handleFilterSelected = () => {
    if (!onFilter) return
    const ids = Array.from(
      new Set(rows.filter(r => selected.has(r.index)).flatMap(r => r.cenario_ids))
    )
    if (ids.length > 0) {
      onFilter(ids)
      onClose()
    }
  }

  const filterRow = (row: CombinedRow) => {
    if (onFilter) {
      onFilter(row.cenario_ids)
      onClose()
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={modalTitle}
      size="2xl"
      footer={
        <Button variant="ghost" onClick={onClose}>
          Fechar
        </Button>
      }
    >
      <div className="space-y-6">
        <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          {result.message || 'Resultados da validação.'} Total de sugestões{' '}
          <span className="font-semibold">{result.total_sugestoes}</span>
        </div>

        {rows.length > 0 && (
          <div className="flex flex-wrap gap-3 items-center">
            <div className="flex gap-2">
              <Button
                variant="success"
                size="sm"
                onClick={handleApplyAll}
                disabled={loading}
                loading={loading}
                icon={<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg>}
              >
                Aplicar Todas
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={handleApplySelected}
                disabled={loading || selected.size === 0}
                loading={loading}
                icon={<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" /></svg>}
              >
                Aplicar Selecionadas ({selected.size})
              </Button>
            </div>
            {onFilter && (
              <div className="flex gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleFilterSelected}
                  disabled={selected.size === 0}
                  icon={<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" /></svg>}
                >
                  Filtrar Selecionados
                </Button>
                {onClearFilters && (
                  <Button variant="ghost" size="sm" onClick={onClearFilters}>
                    Limpar Filtros
                  </Button>
                )}
              </div>
            )}
            <div className="flex gap-2 ml-auto">
              <Button
                variant={groupMode === 'ncm' ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setGroupMode(groupMode === 'ncm' ? 'none' : 'ncm')}
              >
                Agrupar por NCM
              </Button>
              <Button
                variant={groupMode === 'produto' ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setGroupMode(groupMode === 'produto' ? 'none' : 'produto')}
              >
                Agrupar por Produto
              </Button>
            </div>
          </div>
        )}

        <TableScrollContainer containerClassName="max-h-[60vh] overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900 sticky top-0">
              <tr>
                <th className="px-4 py-3">
                  <input
                    type="checkbox"
                    className="modern-checkbox"
                    checked={selected.size === rows.length && rows.length > 0}
                    ref={input => {
                      if (input) {
                        input.indeterminate = selected.size > 0 && selected.size < rows.length
                      }
                    }}
                    onChange={e => toggleAll(e.target.checked)}
                  />
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Dest. Empresa</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Dest. Cliente</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Estado</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">NCM</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">CFOP</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Código Produto</th>
                {hasPis && (
                  <>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">CST PIS</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">CST Sugerido</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Alíquota PIS</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Alíq. Sugerida</th>
                  </>
                )}
                {hasCofins && (
                  <>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">CST COFINS</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">CST Sugerido</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Alíquota COFINS</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Alíq. Sugerida</th>
                  </>
                )}
                {/* <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Regime Empresa</th> */}
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Ações</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {rows.map(r => {
                const [cstPisClass, cstPisSugClass] = getClasses(r.pis.cst.atual, r.pis.cst.sugerido)
                const [aliPisClass, aliPisSugClass] = getClasses(r.pis.aliquota.atual, r.pis.aliquota.sugerido)
                const [cstCofClass, cstCofSugClass] = getClasses(r.cofins.cst.atual, r.cofins.cst.sugerido)
                const [aliCofClass, aliCofSugClass] = getClasses(r.cofins.aliquota.atual, r.cofins.aliquota.sugerido)
                return (
                  <tr key={r.index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="px-4 py-2">
                      <input
                        type="checkbox"
                        className="modern-checkbox"
                        checked={selected.has(r.index)}
                        onChange={e => toggle(r.index, e.target.checked)}
                      />
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{r.destinacao_empresa ?? '-'}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{r.destinacao_cliente ?? '-'}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{r.estado ?? '-'}</td>

                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{r.ncm}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{r.cfop}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{r.codigo_produto ?? '-'}</td>
                  {hasPis && (
                      <>
                        <td className={`px-4 py-2 text-sm ${cstPisClass}`}>{r.pis.cst.atual ?? '-'}</td>
                        <td className={`px-4 py-2 text-sm ${cstPisSugClass}`}>{r.pis.cst.sugerido ?? '-'}</td>
                        <td className={`px-4 py-2 text-sm ${aliPisClass}`}>{r.pis.aliquota.atual != null ? `${r.pis.aliquota.atual}%` : '-'}</td>
                        <td className={`px-4 py-2 text-sm ${aliPisSugClass}`}>{r.pis.aliquota.sugerido != null ? `${r.pis.aliquota.sugerido}%` : '-'}</td>
                      </>
                    )}
                    {hasCofins && (
                      <>
                        <td className={`px-4 py-2 text-sm ${cstCofClass}`}>{r.cofins.cst.atual ?? '-'}</td>
                        <td className={`px-4 py-2 text-sm ${cstCofSugClass}`}>{r.cofins.cst.sugerido ?? '-'}</td>
                        <td className={`px-4 py-2 text-sm ${aliCofClass}`}>{r.cofins.aliquota.atual != null ? `${r.cofins.aliquota.atual}%` : '-'}</td>
                        <td className={`px-4 py-2 text-sm ${aliCofSugClass}`}>{r.cofins.aliquota.sugerido != null ? `${r.cofins.aliquota.sugerido}%` : '-'}</td>
                      </>
                    )}
                    {/* <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{r.regime_empresa ?? '-'}</td> */}
                    <td className="px-4 py-2 text-sm">
                      <div className="flex gap-2">
                        <Button variant="success" size="xs" onClick={() => applyRow(r)}>
                          Aplicar
                        </Button>
                        <Button variant="secondary" size="xs" onClick={() => editRow(r)}>
                          Editar
                        </Button>
                        {onFilter && (
                          <Button variant="ghost" size="xs" onClick={() => filterRow(r)}>
                            Filtrar
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </TableScrollContainer>
      </div>
    </Modal>
  )
}
-- Migration to integrate portal authentication

-- Remove local login fields
ALTER TABLE usuario DROP COLUMN IF EXISTS email;
ALTER TABLE usuario DROP COLUMN IF EXISTS senha_hash;

-- Add fields to store identifiers from the portal
ALTER TABLE usuario ADD COLUMN IF NOT EXISTS portal_user_id VARCHAR(255) UNIQUE;
ALTER TABLE usuario ADD COLUMN IF NOT EXISTS tipo_usuario VARCHAR(20) DEFAULT 'normal';

-- Escritório external id
ALTER TABLE escritorio ADD COLUMN IF NOT EXISTS portal_office_id VARCHAR(255) UNIQUE;

-- Ensure branding fields exist
ALTER TABLE escritorio ADD COLUMN IF NOT EXISTS logo_path VARCHAR(500);
ALTER TABLE escritorio ADD COLUMN IF NOT EXISTS cor_relatorio VARCHAR(7) DEFAULT '#6f42c1';
ALTER TABLE escritorio ADD COLUMN IF NOT EXISTS responsavel VARCHAR(255);

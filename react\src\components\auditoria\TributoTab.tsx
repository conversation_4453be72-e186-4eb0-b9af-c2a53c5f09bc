import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { useFilterStore } from '@/store/filterStore'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import {
  auditoriaComparativaService,
  AuditoriaComparativaItem,
} from '@/services/auditoriaComparativaService'
import { TabelaAuditoriaComparativa } from './TabelaAuditoriaComparativa'
import { ModalRegrasAnalise } from './ModalRegrasAnalise'
import { ModalDetalhesAnalise } from './ModalDetalhesAnalise'
import { ProgressoAuditoria } from './ProgressoAuditoria'
import { EstatisticasAuditoria } from './EstatisticasAuditoria'
import { useAuditoriaWebSocket } from '@/hooks/useAuditoriaWebSocket'
import { mapAuditoriaComparativaItem } from '@/utils/auditoriaComparativa'
import {
  ANALYSIS_CARDS_CONFIG,
  isCfopInconsistent,
  isCfopCstInconsistent,
  isProductTypeInconsistent,
  isOriginInconsistent,
  isAliquotaInconsistent,
  isIpiInconsistent,
  hasPisCofinsViolation,
  getIpiRuleViolationByAnalysis,
} from '@/utils/fiscalAnalysisRules'
import { useAuditoriaComparativaFilters } from '@/hooks/useAuditoriaComparativaFilters'
import { LucideIcon } from 'lucide-react'

interface TributoTabProps {
  tributo: string
}

interface AnalysisCard {
  type: string
  title: string
  description: string
  icon: LucideIcon
  count: number
  hasProblems: boolean
  color: string
}

export function TributoTab({ tributo }: TributoTabProps) {
  const empresaId = useSelectedCompany()
  const { selectedYear, selectedMonth } = useFilterStore()
  const queryClient = useQueryClient()
  const [filtroAnalise, setFiltroAnalise] = useState<string | null>(null)
  const [filtrosPendentes, setFiltrosPendentes] = useState(false)
  const [currentAuditId, setCurrentAuditId] = useState<string | null>(null)
  const [modalRegras, setModalRegras] = useState<{
    isOpen: boolean
    analysisType: string
  }>({
    isOpen: false,
    analysisType: '',
  })
  const [modalDetalhes, setModalDetalhes] = useState<{
    isOpen: boolean
    analysisType: string
  }>({
    isOpen: false,
    analysisType: '',
  })

  // WebSocket para acompanhar progresso da auditoria
  const { subscribeToAudit, getAuditStatus } = useAuditoriaWebSocket({
    onComplete: () => {
      queryClient.invalidateQueries({ queryKey: ['auditoria-comparativa'] })
      setCurrentAuditId(null)
    },
  })

  // Status da auditoria atual
  const auditStatus = currentAuditId ? getAuditStatus(currentAuditId) : null

  // Query para dados da auditoria comparativa por tributo
  const {
    data: auditoriaData,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      'auditoria-comparativa',
      empresaId,
      selectedYear,
      selectedMonth,
      tributo,
    ],
    queryFn: () =>
      auditoriaComparativaService.buscarPorPeriodo({
        empresaId: empresaId!,
        mes: selectedMonth,
        ano: selectedYear,
        tributo: tributo === 'pis_cofins' ? 'pis' : tributo,
      }),
    enabled: !!empresaId,
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 404) {
        return false
      }
      return failureCount < 2
    },
    staleTime: 5 * 60 * 1000,
  })

  // Mutation para gerar auditoria comparativa
  const gerarAuditoriaMutation = useMutation({
    mutationFn: () =>
      auditoriaComparativaService.gerarPorPeriodo({
        empresaId: empresaId!,
        mes: selectedMonth,
        ano: selectedYear,
        tributos: tributo === 'pis_cofins' ? ['pis', 'cofins'] : [tributo],
        forceRecalculate: true,
      }),
    onSuccess: (data) => {
      if (data.audit_id) {
        setCurrentAuditId(data.audit_id)
        subscribeToAudit(data.audit_id)
      }
      queryClient.invalidateQueries({ queryKey: ['auditoria-comparativa'] })
    },
  })

  const auditoriasMapeadas = React.useMemo(() => {
    return (
      auditoriaData?.auditorias.map((item) =>
        mapAuditoriaComparativaItem(
          item,
          tributo === 'pis_cofins' ? 'pis' : tributo
        )
      ) || []
    )
  }, [auditoriaData, tributo])

  const {
    filters,
    options,
    data: dadosServidor,
    totalCount,
    isLoading: filtrosLoading,
    isLoadingData: dadosLoading,
    hasActiveFilters,
    hasMore,
    loadMore,
    updateFilter,
    updateTextFilter,
    clearAllFilters,
  } = useAuditoriaComparativaFilters({
    empresaId: auditoriaData ? empresaId || undefined : undefined,
    mes: selectedMonth,
    ano: selectedYear,
    tributo: tributo === 'pis_cofins' ? 'pis' : tributo,
    initialData: auditoriaData?.auditorias || [],
    initialTotal: auditoriaData?.total_registros,
  })

  // Análise fiscal dos dados para gerar cards
  const analysisCards = React.useMemo(() => {
    if (!auditoriasMapeadas.length) return []

    return generateAnalysisCards(auditoriasMapeadas, tributo)
  }, [auditoriasMapeadas, tributo])

  // Filtrar dados baseado na análise selecionada e filtros
  const dadosFiltrados = React.useMemo(() => {
    let dados = dadosServidor

    // Aplicar filtro de pendentes primeiro
    if (filtrosPendentes) {
      dados = dados.filter((auditoria) => !auditoria.aprovado)
    }

    // Aplicar filtro de análise específica
    if (filtroAnalise) {
      dados = dados.filter((auditoria) => {
        if (auditoria.aprovado) return false // Não mostrar aprovados nos filtros de análise

        switch (filtroAnalise) {
          case 'cfop':
            return isCfopInconsistent(
              auditoria.cfop_sped,
              auditoria.cfop_nota,
              auditoria.tipo
            )

          case 'cfop_cst':
            return isCfopCstInconsistent(auditoria)

          case 'product_type':
            return isProductTypeInconsistent(
              auditoria.cfop_sped,
              auditoria.cst_sped,
              auditoria.tipo
            )

          case 'origin':
            return (
              tributo === 'icms' &&
              isOriginInconsistent(auditoria.origem_sped, auditoria.origem_nota)
            )

          case 'aliquota':
            if (tributo === 'pis_cofins') {
              const pis = isAliquotaInconsistent(
                auditoria.tributos?.pis?.aliquota,
                auditoria.xml_data?.pis_aliquota,
                auditoria.tipo,
                auditoria.cfop_sped
              )
              const cofins = isAliquotaInconsistent(
                auditoria.tributos?.cofins?.aliquota,
                auditoria.xml_data?.cofins_aliquota,
                auditoria.tipo,
                auditoria.cfop_sped
              )
              return pis || cofins
            }
            return (
              isAliquotaInconsistent(
                auditoria.aliquota_sped,
                auditoria.aliquota_nota,
                auditoria.tipo,
                auditoria.cfop_sped
              ) ||
              (tributo === 'ipi' &&
                getIpiRuleViolationByAnalysis('aliquota', auditoria) !== '')
            )

          case 'ipi':
            return tributo === 'ipi' && isIpiInconsistent(auditoria)

          case 'pis_cofins':
            return (
              ['pis', 'cofins', 'pis_cofins'].includes(tributo) &&
              hasPisCofinsViolation(auditoria)
            )

          default:
            return auditoria.match_status !== 'matched'
        }
      })
    }

    return dados
  }, [dadosServidor, filtroAnalise, filtrosPendentes, tributo])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Carregando auditoria comparativa de {tributo.toUpperCase()}...
          </p>
        </div>
      </div>
    )
  }

  if (error || !auditoriasMapeadas.length) {
    return (
      <div className="text-center space-y-4">
        <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Nenhuma auditoria comparativa encontrada
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Nenhum item encontrado para o tributo {tributo.toUpperCase()}. Use os
          botões acima para gerar.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Estatísticas da Auditoria */}
      <EstatisticasAuditoria
        totalRegistros={auditoriasMapeadas.length}
        registrosAprovados={
          auditoriasMapeadas.filter((item) => item.aprovado).length
        }
        registrosPendentes={
          auditoriasMapeadas.filter((item) => !item.aprovado).length
        }
        registrosComDivergencia={
          auditoriasMapeadas.filter((item) => {
            if (item.aprovado) return false;
            
            // Verificar se há alguma regra violada usando as funções de análise fiscal
            const hasAnyViolation = 
              isCfopInconsistent(item.cfop_sped, item.cfop_nota, item.tipo) ||
              isCfopCstInconsistent(item) ||
              isProductTypeInconsistent(item.cfop_sped, item.cst_sped, item.tipo) ||
              (tributo === 'icms' && isOriginInconsistent(item.origem_sped, item.origem_nota)) ||
              isAliquotaInconsistent(
                item.aliquota_sped,
                item.aliquota_nota,
                item.tipo,
                item.cfop_sped
              ) ||
              (tributo === 'ipi' && isIpiInconsistent(item)) ||
              (['pis', 'cofins', 'pis_cofins'].includes(tributo) && hasPisCofinsViolation(item));
            
            return hasAnyViolation;
          }).length
        }
        matchingStats={auditoriaData.matching_stats}
      />

      {/* Cards de Análise Fiscal */}
      {analysisCards.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
          {analysisCards.map((card) => {
            const Icon = card.icon
            return (
             <div
                key={card.type}
                className={`p-4 cursor-pointer hover:shadow-lg transition-all duration-200 rounded-lg border ${
                  card.hasProblems
                   ? 'border-red-200 dark:border-red-800 bg-white dark:bg-gray-800'
                    : 'border-green-200 dark:border-green-800 bg-white dark:bg-gray-800'
                } ${filtroAnalise === card.type ? 'ring-2 ring-primary-500' : ''}`}
                onClick={() => {
                  setFiltroAnalise(filtroAnalise === card.type ? null : card.type)
               }}
             >
               <div className="flex items-center justify-between">
                 <div className="flex-1">
                   <div
                     className={`w-10 h-10 rounded-full flex items-center justify-center mb-3 ${
                      card.hasProblems
                        ? 'bg-red-100 dark:bg-red-900/20'
                        : 'bg-green-100 dark:bg-green-900/20'
                    }`}
                  >
                    <Icon
                      className={`w-5 h-5 ${
                        card.hasProblems
                          ? 'text-red-600 dark:text-red-400'
                          : 'text-green-600 dark:text-green-400'
                      }`}
                    />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                    {card.count}
                  </h3>
                  <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                    {card.title}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {card.description}
                  </p>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      setModalDetalhes({
                        isOpen: true,
                        analysisType: card.type,
                      })
                    }}
                    className="w-8 h-8 p-0 rounded-full bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/50 dark:hover:bg-blue-800/50"
                    title="Detalhes da Análise"
                    icon={
                      <svg
                        className="w-4 h-4 text-blue-600 dark:text-blue-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                    }
                  />
                  <Button
                      variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      setModalRegras({ isOpen: true, analysisType: card.type })
                    }}
                    className="w-8 h-8 p-0 rounded-full bg-purple-100 hover:bg-purple-200 dark:bg-purple-900/50 dark:hover:bg-purple-800/50"
                    title="Regras: Análise"
                    icon={
                      <svg
                        className="w-4 h-4 text-purple-600 dark:text-purple-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 6h16M4 10h16M4 14h16M4 18h16"
                        />
                      </svg>
                    }
                  />
                </div>
              </div>
            </div>
            )
          })}
        </div>
      )}

      {/* Progresso da Auditoria */}
      <ProgressoAuditoria
        isVisible={!!auditStatus && auditStatus.status === 'processing'}
        progress={auditStatus?.progress || 0}
        message={auditStatus?.message || 'Processando auditoria...'}
        currentStep={auditStatus?.current_step}
        totalSteps={auditStatus?.total_steps}
        onCancel={() => setCurrentAuditId(null)}
      />

      {/* Indicador de filtro ativo */}
      {filtroAnalise && (
        <Card className="p-4 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                <svg
                  className="w-4 h-4 text-blue-600 dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
                  />
                </svg>
              </div>
              <div>
                <p className="font-medium text-blue-900 dark:text-blue-100">
                  Filtro ativo:{' '}
                  {
                    analysisCards.find((card) => card.type === filtroAnalise)
                      ?.title
                  }
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Mostrando {dadosFiltrados.length} de {totalCount} registros
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFiltroAnalise(null)}
              icon={
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              }
            >
              Limpar Filtro
            </Button>
          </div>
        </Card>
      )}

      {/* Tabela de Auditoria Comparativa com filtros */}
      <TabelaAuditoriaComparativa
        data={dadosFiltrados}
        tributo={tributo}
        loading={isLoading || dadosLoading}
        onGerarAuditoria={() => gerarAuditoriaMutation.mutate()}
        onFiltrarPendentes={() => setFiltrosPendentes(!filtrosPendentes)}
        filtrosPendentes={filtrosPendentes}
        filters={filters}
        options={options}
        filtersLoading={filtrosLoading}
        hasActiveFilters={hasActiveFilters}
        onUpdateFilter={updateFilter}
        onUpdateTextFilter={updateTextFilter}
        onClearAllFilters={clearAllFilters}
        hasMore={hasMore}
        onLoadMore={loadMore}
        isLoadingMore={dadosLoading}
      />

      {/* Modal Detalhes da Análise */}
      <ModalDetalhesAnalise
        isOpen={modalDetalhes.isOpen}
        onClose={() => setModalDetalhes({ isOpen: false, analysisType: '' })}
        analysisType={modalDetalhes.analysisType}
        tributo={tributo}
        auditorias={auditoriasMapeadas}
      />

      {/* Modal de Regras */}
      <ModalRegrasAnalise
        isOpen={modalRegras.isOpen}
        onClose={() => setModalRegras({ isOpen: false, analysisType: '' })}
        analysisType={modalRegras.analysisType}
        tributo={tributo}
      />
    </div>
  )
}

// Função para gerar cards de análise fiscal baseado no tributo
function generateAnalysisCards(
  auditorias: AuditoriaComparativaItem[],
  tributo: string
): AnalysisCard[] {
  const cards: AnalysisCard[] = []

  Object.entries(ANALYSIS_CARDS_CONFIG as Record<string, any>).forEach(
    ([type, config]) => {
      if ((config.tributos as string[]).includes(tributo)) {
        const count = performAnalysis(auditorias, type, tributo)
        cards.push({
          type,
          title: config.title,
          description: config.description,
          icon: config.icon,
          count,
          hasProblems: count > 0,
          color: count > 0 ? 'red' : 'green',
        })
      }
    }
  )

  return cards
}

// Função para realizar análise específica
function performAnalysis(
  auditorias: AuditoriaComparativaItem[],
  analysisType: string,
  tributo: string
): number {
  let count = 0

  auditorias.forEach((auditoria) => {
    if (auditoria.aprovado) return

    switch (analysisType) {
      case 'cfop':
        if (
          isCfopInconsistent(
            auditoria.cfop_sped,
            auditoria.cfop_nota,
            auditoria.tipo
          )
        ) {
          count++
        }
        break

      case 'cfop_cst':
        if (isCfopCstInconsistent(auditoria)) {
          count++
        }
        break

      case 'product_type':
        if (
          isProductTypeInconsistent(
            auditoria.cfop_sped,
            auditoria.cst_sped,
            auditoria.tipo
          )
        ) {
          count++
        }
        break

      case 'origin':
        if (
          tributo === 'icms' &&
          isOriginInconsistent(auditoria.origem_sped, auditoria.origem_nota)
        ) {
          count++
        }
        break

      case 'aliquota':
        if (tributo === 'pis_cofins') {
          const pis = isAliquotaInconsistent(
            auditoria.tributos?.pis?.aliquota,
            auditoria.xml_data?.pis_aliquota,
            auditoria.tipo,
            auditoria.cfop_sped
          )
          const cofins = isAliquotaInconsistent(
            auditoria.tributos?.cofins?.aliquota,
            auditoria.xml_data?.cofins_aliquota,
            auditoria.tipo,
            auditoria.cfop_sped
          )
          if (pis || cofins) count++
        } else if (
          isAliquotaInconsistent(
            auditoria.aliquota_sped,
            auditoria.aliquota_nota,
            auditoria.tipo,
            auditoria.cfop_sped
          ) ||
          (tributo === 'ipi' &&
            getIpiRuleViolationByAnalysis('aliquota', auditoria) !== '')
        ) {
          count++
        }
        break

      case 'ipi':
        if (tributo === 'ipi' && isIpiInconsistent(auditoria)) {
          count++
        }
        break

      case 'pis_cofins':
        if (
          ['pis', 'cofins', 'pis_cofins'].includes(tributo) &&
          hasPisCofinsViolation(auditoria)
        ) {
          count++
        }
        break
    }
  })

  return count
}
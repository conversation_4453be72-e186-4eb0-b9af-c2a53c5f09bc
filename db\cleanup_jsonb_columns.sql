-- Lim<PERSON>za das colunas JSONB desnecessárias
-- Data: 2025-06-16
-- Motivo: Usar colunas específicas por tributo que já existem

-- Dropar as colunas JSONB desnecessárias
ALTER TABLE auditoria_comparativa_impostos DROP COLUMN IF EXISTS status_tributos;
ALTER TABLE auditoria_comparativa_impostos DROP COLUMN IF EXISTS aprovacoes_tributos;

-- Dropar os índices também
DROP INDEX IF EXISTS idx_auditoria_status_tributos;
DROP INDEX IF EXISTS idx_auditoria_aprovacoes_tributos;

-- Verificar se as colunas específicas por tributo existem (devem existir)
DO $$
BEGIN
    -- Verificar se as colunas de status por tributo existem
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'auditoria_comparativa_impostos' AND column_name = 'status_icms') THEN
        RAISE EXCEPTION 'Coluna status_icms não existe! Verifique a estrutura da tabela.';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'auditoria_comparativa_impostos' AND column_name = 'status_icms_st') THEN
        RAISE EXCEPTION 'Coluna status_icms_st não existe! Verifique a estrutura da tabela.';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'auditoria_comparativa_impostos' AND column_name = 'status_ipi') THEN
        RAISE EXCEPTION 'Coluna status_ipi não existe! Verifique a estrutura da tabela.';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'auditoria_comparativa_impostos' AND column_name = 'status_pis') THEN
        RAISE EXCEPTION 'Coluna status_pis não existe! Verifique a estrutura da tabela.';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'auditoria_comparativa_impostos' AND column_name = 'status_cofins') THEN
        RAISE EXCEPTION 'Coluna status_cofins não existe! Verifique a estrutura da tabela.';
    END IF;
    
    RAISE NOTICE 'Todas as colunas de status por tributo existem. Limpeza concluída com sucesso.';
END $$;

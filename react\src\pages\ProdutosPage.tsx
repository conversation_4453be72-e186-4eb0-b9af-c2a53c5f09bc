import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { produtoService } from '@/services/produtoService'
import { ProdutoModal } from '@/components/produtos/ProdutoModal'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import type { Produto } from '@/types/produto'
import { Edit, Trash2, Plus } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'
import { HelpButton, HelpModal } from '@/components/ui'
import { Dropdown } from '@/components/ui/Dropdown'

export function ProdutosPage() {
  const empresaId = useSelectedCompany()
  const [page, setPage] = useState(1)
  const [perPage, setPerPage] = useState(20)
  const [modalOpen, setModalOpen] = useState(false)
  const [editingProduto, setEditingProduto] = useState<Produto | null>(null)
  const [helpOpen, setHelpOpen] = useState(false)

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['produtos', empresaId, page, perPage],
    queryFn: () => produtoService.getProdutos(empresaId!, page, perPage),
    enabled: !!empresaId
  })

  const openNew = () => {
    setEditingProduto(null)
    setModalOpen(true)
  }

  const openEdit = async (id: number) => {
    const prod = await produtoService.getProduto(id)
    setEditingProduto(prod)
    setModalOpen(true)
  }

  const handleSave = async (form: Partial<Produto>) => {
    try {
      if (editingProduto) {
        await produtoService.updateProduto(editingProduto.id, form)
      } else if (empresaId) {
        await produtoService.createProduto({ ...form, empresa_id: empresaId })
      }
      alert('Produto salvo com sucesso!')
      setModalOpen(false)
      refetch()
    } catch (error: any) {
      alert(error.message || 'Erro ao salvar produto')
    }
  }

  const handleDelete = async (id: number, nome: string) => {
    if (!confirm(`Tem certeza que deseja excluir o produto "${nome}"?`)) return
    try {
      await produtoService.deleteProduto(id)
      alert('Produto excluído com sucesso!')
      refetch()
    } catch (error: any) {
      alert(error.message || 'Erro ao excluir produto')
    }
  }

  const pagination = data?.pagination

  const pageNumbers = () => {
    if (!pagination) return []
    const maxLinks = 5
    let start = Math.max(1, pagination.current_page - Math.floor(maxLinks / 2))
    let end = Math.min(pagination.last_page, start + maxLinks - 1)
    if (end - start + 1 < maxLinks) {
      start = Math.max(1, end - maxLinks + 1)
    }
    const nums: number[] = []
    for (let i = start; i <= end; i++) nums.push(i)
    return nums
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Produtos</h1>
            <HelpButton onClick={() => setHelpOpen(true)} />
          </div>
          <p className="text-gray-600 dark:text-gray-400 mt-1">Gerencie os produtos da empresa selecionada</p>
        </div>
        <Button
          variant="primary"
          size="md"
          onClick={openNew}
          icon={<Plus className="w-4 h-4" />}
          glow
        >
          Novo Produto
        </Button>
      </div>

      {!empresaId && (
        <div className="p-4 bg-yellow-50 text-yellow-800 rounded-lg">
          Selecione uma empresa para visualizar os produtos.
        </div>
      )}

      {empresaId && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <TableScrollContainer>
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Código</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Descrição</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Unidade</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">NCM</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">SPED</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">cEAN</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">CEST</th>
                <th className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300">Ações</th>
              </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {isLoading && (
                <tr>
                  <td colSpan={8} className="px-4 py-8 text-center text-gray-500">
                    Carregando...
                  </td>
                </tr>
              )}
              {!isLoading && data?.produtos.map((p) => (
                <tr key={p.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{p.codigo}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{p.descricao}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{p.unidade_comercial || '-'}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{p.ncm || '-'}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{p.tipo_sped || '-'}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{p.codigo_ean || '-'}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{p.cest || '-'}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEdit(p.id)}
                        icon={<Edit className="w-4 h-4" />}
                        className="text-blue-600 hover:text-blue-800"
                      >
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(p.id, p.descricao)}
                        icon={<Trash2 className="w-4 h-4" />}
                        className="text-red-600 hover:text-red-800"
                      >
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
              {!isLoading && data?.produtos.length === 0 && (
                <tr>
                  <td colSpan={8} className="px-4 py-8 text-center text-gray-500">
                    Nenhum produto encontrado.
                  </td>
                </tr>
              )}
              </tbody>
            </table>
          </TableScrollContainer>

          {pagination && (
            <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Mostrando {pagination.from} a {pagination.to} de {pagination.total} registros
              </div>
              <div className="flex items-center space-x-2">
                <Dropdown
                  className="w-40"
                  options={[20, 50, 100, 200, 500].map((n) => ({
                    value: n.toString(),
                    label: `${n} por página`
                  }))}
                  value={perPage.toString()}
                  onChange={(value) => {
                    setPerPage(parseInt(value as string))
                    setPage(1)
                  }}
                />
                <nav className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    disabled={!pagination.has_prev}
                    onClick={() => setPage(1)}
                    className="px-2 py-1"
                  >
                    «
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    disabled={!pagination.has_prev}
                    onClick={() => setPage(pagination.prev_page || 1)}
                    className="px-2 py-1"
                  >
                    ‹
                  </Button>
                  {pageNumbers().map((n) => (
                    <Button
                      key={n}
                      variant={n === pagination.current_page ? 'primary' : 'ghost'}
                      size="sm"
                      onClick={() => setPage(n)}
                      className="px-2 py-1"
                    >
                      {n}
                    </Button>
                  ))}
                  <Button
                    variant="ghost"
                    size="sm"
                    disabled={!pagination.has_next}
                    onClick={() => setPage(pagination.next_page || pagination.last_page)}
                    className="px-2 py-1"
                  >
                    ›
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    disabled={!pagination.has_next}
                    onClick={() => setPage(pagination.last_page)}
                    className="px-2 py-1"
                  >
                    »
                  </Button>
                </nav>
              </div>
            </div>
          )}
        </div>
      )}

      <ProdutoModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        onSave={handleSave}
        produto={editingProduto}
      />
      <HelpModal
        isOpen={helpOpen}
        onClose={() => setHelpOpen(false)}
        title="Ajuda - Produtos"
      >
        <div className="space-y-4">
          <p>A tabela apresenta os produtos cadastrados e suas principais informações.</p>
          <p>Utilize os filtros e a paginação para localizar registros específicos.</p>
          <p>O campo "SPED" indica o tipo de produto no Sistema Público de Escrituração Digital.</p>
          <p>Nas ações é possível criar, editar ou excluir um produto.</p>
        </div>
      </HelpModal>
    </div>
  )
}
from sqlalchemy import func, or_
from models import db, Tributo, NotaFiscalItem

class ApuracaoPisCofinsService:
    def __init__(self, empresa_id):
        self.empresa_id = empresa_id

    def _calcular(self, ano, mes, campo):
        query = (
            db.session.query(getattr(Tributo, campo))
            .join(NotaFiscalItem, NotaFiscalItem.id == Tributo.nota_fiscal_item_id)
            .filter(
                Tributo.empresa_id == self.empresa_id,
                func.extract('year', Tributo.data_emissao) == ano,
                func.extract('month', Tributo.data_emissao) == mes,
            )
        )

        creditos = (
            query.filter(
                or_(
                    NotaFiscalItem.cfop.like('1%'),
                    NotaFiscalItem.cfop.like('2%'),
                    NotaFiscalItem.cfop.like('3%'),
                )
            )
            .with_entities(func.sum(getattr(Tributo, campo)))
            .scalar()
            or 0
        )

        debitos = (
            query.filter(
                or_(
                    NotaFiscalItem.cfop.like('5%'),
                    NotaFiscalItem.cfop.like('6%'),
                )
            )
            .with_entities(func.sum(getattr(Tributo, campo)))
            .scalar()
            or 0
        )

        return float(debitos), float(creditos), float(debitos - creditos)

    def calcular_pis(self, ano, mes):
        deb, cred, rec = self._calcular(ano, mes, 'pis_valor')
        return {'vl_tot_debitos': deb, 'vl_tot_creditos': cred, 'vl_pis_a_recolher': rec}

    def calcular_cofins(self, ano, mes):
        deb, cred, rec = self._calcular(ano, mes, 'cofins_valor')
        return {'vl_tot_debitos': deb, 'vl_tot_creditos': cred, 'vl_cofins_a_recolher': rec}
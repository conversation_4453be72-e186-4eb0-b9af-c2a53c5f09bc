# Serviço Cliente XML - Configuração e Testes

## 🚀 Como Testar Rapidamente

### 1. Iniciar <PERSON><PERSON><PERSON>

```bash
# Terminal 1 - Servidor de teste
cd back/scripts
python test_server.py
```

O servidor iniciará em `http://localhost:5001`

### 2. Configurar Cliente

```bash
# Terminal 2 - Cliente
cd back/scripts
python xml_service_client.py
```

Na interface que abrir:
- **API Key**: `test_key_123`
- **Diretório**: Escolha uma pasta para monitorar
- **Endpoint**: `http://localhost:5001/fiscal/api/service-upload`

### 3. Testar

1. Clique "Salvar Configurações"
2. Clique "Iniciar Serviço"
3. Coloque arquivos XML na pasta monitorada
4. Observe os logs no servidor e cliente

## ⚙️ Configurações Avançadas

### Arquivo de Configuração

O arquivo `xml_service_config.json` é criado automaticamente. Você pode editá-lo diretamente:

```json
{
  "api_key": "test_key_123",
  "monitor_directory": "C:\\temp\\xmls",
  "api_endpoint": "http://localhost:5001/fiscal/api/service-upload",
  "escritorio_id": 1,
  "retry_attempts": 5,
  "retry_interval": 30,
  "failure_cooldown_hours": 2,
  "max_failure_days": 5
}
```

### Parâmetros Configuráveis

| Parâmetro | Descrição | Valor Padrão | Como Alterar |
|-----------|-----------|--------------|--------------|
| `retry_attempts` | Tentativas antes do cooldown | 5 | 1-10 recomendado |
| `retry_interval` | Segundos entre verificações | 30 | 10-300 segundos |
| `failure_cooldown_hours` | Horas de cooldown após falhas | 2 | 1-24 horas |
| `max_failure_days` | Dias antes da auto-destruição | 5 | 1-30 dias |
| `escritorio_id` | ID do escritório | 1 | Fornecido pelo provedor |

### Configurações de Tempo para Testes Rápidos

Para testes, edite o arquivo de configuração:

```json
{
  "retry_attempts": 2,
  "retry_interval": 5,
  "failure_cooldown_hours": 0.1,
  "max_failure_days": 1
}
```

Isso fará:
- Apenas 2 tentativas antes do cooldown
- Verificar arquivos a cada 5 segundos
- Cooldown de 6 minutos (0.1 horas)
- Auto-destruição após 1 dia

## 🧪 Cenários de Teste

### Teste 1: Fluxo Normal

1. Servidor rodando
2. Cliente configurado corretamente
3. Colocar arquivo XML na pasta
4. **Resultado esperado**: Arquivo movido para `_importados`

### Teste 2: Falha de API Key

1. Configurar API Key inválida: `chave_errada`
2. Colocar arquivo XML
3. **Resultado esperado**: Erro 403, arquivo fica em `_pendentes`

### Teste 3: Servidor Offline

1. Parar o servidor de teste
2. Colocar arquivo XML
3. **Resultado esperado**: Tentativas de retry, arquivo fica em `_pendentes`

### Teste 4: Simulação de Erro

```bash
# Configurar endpoint para simular erro 500
# Endpoint: http://localhost:5001/fiscal/api/simulate-error/500
```

### Teste 5: Auto-destruição (Cuidado!)

1. Configurar `max_failure_days: 0` (menos de 1 dia)
2. Gerar falhas consecutivas
3. **Resultado**: Aplicação se auto-destrói

## 📁 Estrutura de Pastas de Teste

```
📁 Pasta de Teste/
├── _pendentes/           # Arquivos aguardando envio
├── _importados/          # Arquivos enviados com sucesso
├── teste1.xml           # Coloque XMLs aqui para testar
├── teste2.xml
└── ...
```

## 🔧 Modificações no Código

### Alterar Intervalos de Tempo

No arquivo `xml_service_client.py`, procure por:

```python
# Linha ~200 - Intervalo entre verificações
time.sleep(self.config['retry_interval'])

# Linha ~150 - Cooldown após falhas
self.cooldown_until = datetime.now() + timedelta(hours=self.config['failure_cooldown_hours'])

# Linha ~30 - Auto-start da interface
self.auto_start_timer = self.root.after(30000, self.auto_start)  # 30 segundos
```

### Alterar Comportamento de Retry

```python
# Linha ~180 - Lógica de retry
if self.consecutive_failures >= self.config['retry_attempts']:
    # Entrar em cooldown
```

### Desabilitar Auto-destruição

Comente as linhas relacionadas à auto-destruição:

```python
# Linha ~160 - Verificação de dias de falha
# if failure_duration.days >= self.config['max_failure_days']:
#     logger.critical("Limite de dias de falha excedido. Encerrando aplicação.")
#     self.create_self_destruct_script()
#     return
```

## 🐛 Debug e Logs

### Logs Detalhados

O arquivo `xml_service.log` contém logs detalhados:

```bash
# Ver logs em tempo real (Windows)
type xml_service.log

# Ver logs em tempo real (Linux/Mac)
tail -f xml_service.log
```

### Logs do Servidor de Teste

O servidor de teste mostra logs no terminal:

```
[14:30:15] Nova requisição recebida
✅ API Key válida: test_key_123
📋 Escritório ID: 1
📁 Arquivo recebido: temp_upload_1234567890.zip
📦 Arquivos no ZIP: 2
   - nota1.xml
   - nota2.xml
💾 Arquivo salvo: test_uploads/20240718_143015_temp_upload_1234567890.zip
✅ Upload processado com sucesso
```

### Verificar Status

Acesse no navegador:
- `http://localhost:5001` - Página inicial do servidor
- `http://localhost:5001/fiscal/api/uploads` - Lista de arquivos recebidos
- `http://localhost:5001/fiscal/api/test-connection` - Teste de conectividade

## 🔑 API Keys de Teste

O servidor de teste aceita estas chaves:

- `test_key_123`
- `demo_api_key`
- `3f50c92ae6154040b27de74347605e94`

## 📊 Monitoramento

### Verificar Arquivos Processados

```bash
# Listar uploads recebidos
curl http://localhost:5001/fiscal/api/uploads
```

### Testar Conectividade

```bash
# Teste simples
curl http://localhost:5001/fiscal/api/test-connection
```

### Simular Erros

```bash
# Simular erro 403
curl -X POST http://localhost:5001/fiscal/api/simulate-error/403

# Simular erro 500
curl -X POST http://localhost:5001/fiscal/api/simulate-error/500
```

## 🚨 Cuidados Importantes

### ⚠️ Auto-destruição

- **NUNCA** configure `max_failure_days: 0` em produção
- Para testes, use valores baixos com cuidado
- A auto-destruição remove o executável e configurações

### ⚠️ Configurações de Tempo

- Intervalos muito baixos podem sobrecarregar o servidor
- `retry_interval` menor que 5 segundos não é recomendado
- Cooldown muito baixo pode causar spam de requisições

### ⚠️ Testes com Arquivos Reais

- Use arquivos XML pequenos para testes
- Não use dados sensíveis em ambiente de teste
- Limpe a pasta `test_uploads` regularmente

## 🔄 Reset Completo

Para resetar tudo:

```bash
# Parar cliente e servidor
# Deletar arquivos de configuração
del xml_service_config.json
del xml_service.log

# Limpar uploads de teste
rmdir /s test_uploads

# Reiniciar tudo
python test_server.py
python xml_service_client.py
```
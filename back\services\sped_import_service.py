"""
Service para importação de arquivos SPED
"""

import re
import traceback
from typing import Dict, List, Optional
from datetime import datetime

from models import (
    db, Empresa, Cliente, ProdutoEntrada, NotaEntrada,
    ItemNotaEntrada, ImportacaoSped, ApuracaoICMS, ApuracaoIPI, IpiApuracaoGeral
)
from services.transactional import transactional_session
from utils.sped_processor import SPEDProcessor
from utils.api_client import fetch_cnpj_data
from utils.sped_tipo_mapper import get_tipo_produto_descricao


class SPEDImportService:
    """
    Service para importar arquivos SPED e processar notas de entrada
    """

    def __init__(self, empresa_id: int, escritorio_id: int, usuario_id: int, websocket_service=None, import_id=None):
        """
        Inicializa o service de importação SPED

        Args:
            empresa_id (int): ID da empresa
            escritorio_id (int): ID do escritório
            usuario_id (int): ID do usuário
            websocket_service: Serviço WebSocket para notificações
            import_id: ID único da importação
        """
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id
        self.websocket_service = websocket_service
        self.import_id = import_id

    @staticmethod
    def _clean_cnpj(cnpj: str) -> str:
        """Remove caracteres não numéricos do CNPJ"""
        if not cnpj:
            return ''
        return re.sub(r'[^0-9]', '', cnpj)

    def _send_progress_update(self, etapa: str, progresso: int, total: int, mensagem: str = ''):
        """
        Envia atualização de progresso via WebSocket
        
        Args:
            etapa (str): Nome da etapa atual
            progresso (int): Progresso atual
            total (int): Total de itens a serem processados
            mensagem (str): Mensagem opcional
        """
        if not self.websocket_service or not self.import_id:
            return
            
        porcentagem = int((progresso / total) * 100) if total > 0 else 0
        
        self.websocket_service.send_sped_import_progress(self.import_id, {
            'etapa': etapa,
            'progresso': progresso,
            'total': total,
            'porcentagem': porcentagem,
            'mensagem': mensagem,
            'status': 'processando'
        })

    def import_sped(self, sped_content: str, filename: str) -> Dict:
        """
        Importa um arquivo SPED
        
        Args:
            sped_content (str): Conteúdo do arquivo SPED
            filename (str): Nome do arquivo
            
        Returns:
            dict: Resultado da importação
        """
        importacao = None
        try:
            with transactional_session():
                # Processar o arquivo SPED
                processor = SPEDProcessor(sped_content)
                
                # Obter dados da empresa do SPED
                empresa_data = processor.get_empresa_data()
                
                # Verificar se a empresa existe e se o CNPJ confere
                empresa = Empresa.query.get(self.empresa_id)
                if not empresa:
                    importacao = self._create_error_import_record(
                        filename, 'Empresa não encontrada', empresa_data
                    )
                    return {
                        'success': False,
                        'message': 'Empresa não encontrada',
                        'importacao': importacao.to_dict() if importacao else None
                    }

                # Verificar se o CNPJ da empresa confere com o do SPED
                cnpj_sped = self._clean_cnpj(empresa_data.get('cnpj', ''))
                cnpj_empresa = self._clean_cnpj(empresa.cnpj)
                if cnpj_sped and cnpj_empresa != cnpj_sped:
                    importacao = self._create_error_import_record(
                        filename,
                        f'CNPJ da empresa ({empresa.cnpj}) não confere com o CNPJ do SPED ({cnpj_sped})',
                        empresa_data
                    )
                    return {
                        'success': False,
                        'message': f'CNPJ da empresa não confere com o CNPJ do SPED',
                        'importacao': importacao.to_dict() if importacao else None
                    }

                # Processar participantes (fornecedores)
                self._send_progress_update('processando_fornecedores', 0, 1, 'Obtendo dados de fornecedores...')
                participantes = processor.get_participantes_entrada()
                total_participantes = len(participantes)
                
                self._send_progress_update('processando_fornecedores', 0, total_participantes, f'Processando {total_participantes} fornecedores...')
                clientes_processados = self._process_clientes(participantes)
                self._send_progress_update('processando_fornecedores', total_participantes, total_participantes, 'Fornecedores processados com sucesso')

                # Processar produtos
                self._send_progress_update('processando_produtos', 0, 1, 'Obtendo dados de produtos...')
                produtos = processor.get_produtos_entrada()
                total_produtos = len(produtos)
                
                self._send_progress_update('processando_produtos', 0, total_produtos, f'Processando {total_produtos} produtos...')
                produtos_processados = self._process_produtos(produtos)
                self._send_progress_update('processando_produtos', total_produtos, total_produtos, 'Produtos processados com sucesso')

                # Processar notas de entrada
                self._send_progress_update('processando_notas', 0, 1, 'Obtendo dados de notas fiscais...')
                notas_e_itens = processor.get_all_itens_entrada()
                total_notas = len(notas_e_itens)
                
                self._send_progress_update('processando_notas', 0, total_notas, f'Processando {total_notas} notas fiscais...')
                notas_processadas, itens_processados = self._process_notas_entrada(
                    notas_e_itens, clientes_processados, produtos_processados
                )
                self._send_progress_update('processando_notas', total_notas, total_notas, 'Notas fiscais processadas com sucesso')

                # Processar registros de apuração
                self._process_apuracoes(processor, empresa_data)

                # Criar registro de importação com sucesso
                importacao = ImportacaoSped(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    usuario_id=self.usuario_id,
                    arquivo_nome=filename,
                    cnpj_empresa=cnpj_sped,
                    razao_social_empresa=empresa_data.get('nome'),
                    data_inicio=empresa_data.get('dt_ini'),
                    data_fim=empresa_data.get('dt_fim'),
                    total_notas=len(notas_processadas),
                    total_itens=len(itens_processados),
                    total_clientes=len(clientes_processados),
                    total_produtos=len(produtos_processados),
                    status='concluido'
                )

                db.session.add(importacao)
            
            # Enviar notificação de conclusão
            if self.websocket_service and self.import_id:
                self.websocket_service.send_sped_import_complete(self.import_id, {
                    'message': 'Importação do SPED concluída com sucesso!',
                    'total_notas': len(notas_processadas),
                    'total_itens': len(itens_processados),
                    'total_clientes': len(clientes_processados),
                    'total_produtos': len(produtos_processados),
                    'importacao_id': importacao.id,
                    'status': 'concluido'
                })

            # Removida a criação automática de cenários para SPED
            # A criação de cenários será feita apenas na importação de XML

            return {
                'success': True,
                'message': 'SPED importado com sucesso',
                'importacao': importacao.to_dict(),
                'totais': {
                    'notas': len(notas_processadas),
                    'itens': len(itens_processados),
                    'clientes': len(clientes_processados),
                    'produtos': len(produtos_processados)
                }
            }

        except Exception as e:
            error_msg = str(e)
            stack_trace = traceback.format_exc()

            # Criar registro de importação com erro
            if not importacao:
                with transactional_session():
                    importacao = self._create_error_import_record(filename, error_msg)
                
            # Enviar notificação de erro
            if self.websocket_service and self.import_id:
                self.websocket_service.send_sped_import_error(self.import_id, {
                    'message': 'Erro ao importar SPED',
                    'error': error_msg,
                    'status': 'erro'
                })

            return {
                'success': False,
                'message': f'Erro ao importar SPED: {error_msg}',
                'error': error_msg,
                'stack_trace': stack_trace,
                'importacao': importacao.to_dict() if importacao else None
            }

    def _process_clientes(self, participantes: Dict) -> List[Cliente]:
        """
        Processa e salva os clientes (fornecedores) de entrada
        """
        clientes_processados = []
        total = len(participantes)
        
        for i, (cod_part, dados) in enumerate(participantes.items(), 1):
            # Enviar atualização de progresso a cada 10 clientes
            if i % 10 == 0 or i == 1 or i == total:
                self._send_progress_update(
                    'processando_fornecedores', 
                    i, 
                    total, 
                    f'Processando fornecedor {i} de {total} - {dados.get("nome", "")}'
                )
            try:
                # Obter CNPJ do fornecedor
                cnpj = dados.get('cnpj')
                cpf = dados.get('cpf')

                # Verificar se o cliente já existe (primeiro por CNPJ/CPF, depois por cod_part)
                cliente_existente = None
                if cnpj:
                    cliente_existente = Cliente.query.filter_by(
                        empresa_id=self.empresa_id,
                        cnpj=cnpj
                    ).first()
                elif cpf:
                    cliente_existente = Cliente.query.filter_by(
                        empresa_id=self.empresa_id,
                        cpf=cpf
                    ).first()

                # Se não encontrou por CNPJ/CPF, buscar por cod_part
                if not cliente_existente:
                    cliente_existente = Cliente.query.filter_by(
                        empresa_id=self.empresa_id,
                        cod_part=cod_part
                    ).first()

                if cliente_existente:
                    # Atualizar cod_part se não estiver definido
                    if not cliente_existente.cod_part:
                        cliente_existente.cod_part = cod_part
                        db.session.flush()
                    clientes_processados.append(cliente_existente)
                    continue

                # Variáveis para dados da API
                cnae = None
                descricao = None
                atividade = None
                destinacao = None
                simples_nacional = False
                natureza_juridica = None

                # Se for um novo cliente com CNPJ (não CPF), buscar dados adicionais na API
                if cnpj and not cpf:
                    try:
                        # Buscar dados do CNPJ na API
                        api_data = fetch_cnpj_data(cnpj)

                        if api_data:
                            # Extrair CNAE, atividade, destinação e status do Simples Nacional
                            cnae = api_data.get('cnae')
                            descricao_cnae = api_data.get('cnae')
                            simples_nacional = api_data.get('simples_nacional', False)

                            # Verificar se a natureza jurídica é 'Produtor Rural' e tem prioridade
                            natureza_juridica_data = api_data.get('natureza_juridica', {})
                            descricao_natureza_juridica = natureza_juridica_data.get('descricao', '').lower() if natureza_juridica_data else ''
                            natureza_juridica = natureza_juridica_data.get('descricao', '') if natureza_juridica_data else ''

                            if 'produtor rural' in descricao_natureza_juridica:
                                # Se a natureza jurídica for 'Produtor Rural', definir a atividade como 'Produtor Rural'
                                # independentemente do CNAE
                                atividade = 'Produtor Rural'
                            elif descricao_natureza_juridica.startswith('órgão público'):
                                atividade = 'Orgão Público'
                            else:
                                # Se não for 'Produtor Rural', usar atividade e destinação da API
                                atividade = api_data.get('atividade')
                                destinacao = api_data.get('destinacao')

                        else:
                            print(f"Não foi possível obter dados do CNPJ {cnpj} na API.")
                    except Exception as e:
                        print(f"Erro ao buscar dados do CNPJ {cnpj} na API: {str(e)}")

                # Criar novo cliente apenas com campos obrigatórios
                try:
                    cliente_data = {
                        'empresa_id': self.empresa_id,
                        'escritorio_id': self.escritorio_id,
                        'cod_part': cod_part,
                        'razao_social': (dados.get('nome') or f'FORNECEDOR {cod_part}')[:255],
                        'cnpj': cnpj or '',
                        'cpf': cpf or None,
                        'inscricao_estadual': (dados.get('ie') or '')[:30],
                        'codigo_municipio': dados.get('cod_mun'),
                        'suframa': dados.get('suframa'),
                        'logradouro': dados.get('endereco'),  # Mapeando 'endereco' para 'logradouro'
                        'numero': dados.get('numero'),
                        'complemento': dados.get('complemento'),
                        'bairro': dados.get('bairro'),
                        'cnae': cnae,
                        'descricao': descricao,
                        'atividade': atividade,
                        'destinacao': destinacao,
                        'simples_nacional': simples_nacional,
                        'natureza_juridica': natureza_juridica
                    }
                    
                    # Remover campos None para evitar erros
                    cliente_data = {k: v for k, v in cliente_data.items() if v is not None}
                    
                    cliente = Cliente(**cliente_data)
                    db.session.add(cliente)
                    db.session.flush()  # Força o salvamento para obter o ID
                    
                    clientes_processados.append(cliente)
                except Exception as e:
                    continue

            except Exception as e:
                continue

        return clientes_processados

    def _process_produtos(self, produtos: Dict) -> List[ProdutoEntrada]:
        """
        Processa e salva os produtos de entrada
        """
        produtos_processados = []
        total = len(produtos)
        
        for i, (cod_item, dados) in enumerate(produtos.items(), 1):
            # Enviar atualização de progresso a cada 50 produtos
            if i % 50 == 0 or i == 1 or i == total:
                self._send_progress_update(
                    'processando_produtos',
                    i,
                    total,
                    f'Processando produto {i} de {total} - {dados.get("descr_item", "")}'
                )
            
            try:
                # Verificar se o produto já existe
                produto_existente = ProdutoEntrada.query.filter_by(
                    empresa_id=self.empresa_id,
                    cod_item=cod_item
                ).first()

                if produto_existente:
                    produtos_processados.append(produto_existente)
                    continue

                # Obter descrição do tipo de produto SPED
                tipo_item_codigo = dados.get('tipo_item')
                tipo_item_descricao = get_tipo_produto_descricao(tipo_item_codigo)

                # Criar novo produto
                produto = ProdutoEntrada(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    cod_item=cod_item,
                    descricao=dados.get('descr_item', '')[:255],
                    codigo_barra=dados.get('cod_barra'),
                    codigo_anterior=dados.get('cod_ant_item'),
                    unidade=dados.get('unid_inv'),
                    tipo_item=tipo_item_codigo,
                    ncm=dados.get('cod_ncm'),
                    ex_ipi=dados.get('ex_ipi'),
                    codigo_genero=dados.get('cod_gen'),
                    codigo_lst=dados.get('cod_lst'),
                    aliquota_icms=dados.get('aliq_icms'),
                    cest=dados.get('cest')
                )

                db.session.add(produto)
                produtos_processados.append(produto)

            except Exception as e:
                continue

        return produtos_processados

    def _process_notas_entrada(self, notas_e_itens: List, clientes_map: List, produtos_map: List) -> tuple:
        """
        Processa e salva as notas de entrada e seus itens
        """
        notas_processadas = []
        itens_processados = []
        total = len(notas_e_itens)
        
        # Criar mapas para busca rápida
        clientes_dict = {c.cod_part: c for c in clientes_map}
        produtos_dict = {p.cod_item: p for p in produtos_map}
        
        for i, (nota_data, itens_data) in enumerate(notas_e_itens, 1):
            # Enviar atualização de progresso a cada 10 notas
            if i % 10 == 0 or i == 1 or i == total:
                self._send_progress_update(
                    'processando_notas',
                    i,
                    total,
                    f'Processando nota {i} de {total} - {nota_data.get("num_doc", "")}'
                )
                
            try:
                # Verificar se a nota já existe
                chave_nf = nota_data.get('chv_nfe')
                if chave_nf:
                    nota_existente = NotaEntrada.query.filter_by(
                        empresa_id=self.empresa_id,
                        chave_nf=chave_nf
                    ).first()
                    
                    if nota_existente:
                        continue

                # Buscar cliente
                cod_part = nota_data.get('cod_part')
                cliente = clientes_dict.get(cod_part)
                
                if not cliente:
                    # Tentar buscar o cliente no banco de dados caso não esteja no mapa
                    cliente = Cliente.query.filter_by(
                        empresa_id=self.empresa_id,
                        cod_part=cod_part
                    ).first()
                    
                    if cliente:
                        clientes_dict[cod_part] = cliente  # Adiciona ao dicionário para próximas buscas
                    else:
                        continue

                # Criar nota de entrada
                nota = NotaEntrada(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    cliente_id=cliente.id,
                    ind_oper=nota_data.get('ind_oper'),
                    ind_emit=nota_data.get('ind_emit'),
                    cod_part=cod_part,
                    cod_mod=nota_data.get('cod_mod'),
                    cod_sit=nota_data.get('cod_sit'),
                    serie=nota_data.get('ser'),
                    numero_nf=nota_data.get('num_doc'),
                    chave_nf=chave_nf,
                    data_documento=nota_data.get('dt_doc'),
                    data_entrada_saida=nota_data.get('dt_e_s'),
                    valor_documento=nota_data.get('vl_doc'),
                    ind_pgto=nota_data.get('ind_pgto'),
                    valor_desconto=nota_data.get('vl_desc'),
                    valor_abatimento=nota_data.get('vl_abat_nt'),
                    valor_mercadorias=nota_data.get('vl_merc'),
                    ind_frt=nota_data.get('ind_frt'),
                    valor_frete=nota_data.get('vl_frt'),
                    valor_seguro=nota_data.get('vl_seg'),
                    valor_outras_despesas=nota_data.get('vl_out_da'),
                    valor_bc_icms=nota_data.get('vl_bc_icms'),
                    valor_icms=nota_data.get('vl_icms'),
                    valor_bc_icms_st=nota_data.get('vl_bc_icms_st'),
                    valor_icms_st=nota_data.get('vl_icms_st'),
                    valor_ipi=nota_data.get('vl_ipi'),
                    valor_pis=nota_data.get('vl_pis'),
                    valor_cofins=nota_data.get('vl_cofins'),
                    valor_pis_st=nota_data.get('vl_pis_st'),
                    valor_cofins_st=nota_data.get('vl_cofins_st')
                )

                db.session.add(nota)
                db.session.flush()  # Para obter o ID da nota
                notas_processadas.append(nota)

                # Processar itens da nota
                for item_data in itens_data:
                    cod_item = item_data.get('cod_item')
                    produto = produtos_dict.get(cod_item)
                    if not produto:
                        continue

                    cst_icms_full = item_data.get('cst_icms')
                    origem_icms = cst_icms_full[0] if cst_icms_full else None
                    cst_icms = cst_icms_full[1:] if cst_icms_full and len(cst_icms_full) == 3 else cst_icms_full
                    item = ItemNotaEntrada(
                        empresa_id=self.empresa_id,
                        escritorio_id=self.escritorio_id,
                        nota_entrada_id=nota.id,
                        produto_entrada_id=produto.id,
                        num_item=item_data.get('num_item'),
                        cod_item=cod_item,
                        descricao_complementar=item_data.get('descr_compl'),
                        quantidade=item_data.get('qtd'),
                        unidade=item_data.get('unid'),
                        valor_item=item_data.get('vl_item'),
                        valor_desconto=item_data.get('vl_desc'),
                        ind_mov=item_data.get('ind_mov'),
                        cst_icms=cst_icms,
                        origem_icms=origem_icms,
                        cfop=item_data.get('cfop'),
                        codigo_natureza=item_data.get('cod_nat'),
                        valor_bc_icms=item_data.get('vl_bc_icms'),
                        aliquota_icms=item_data.get('aliq_icms'),
                        valor_icms=item_data.get('vl_icms'),
                        valor_bc_icms_st=item_data.get('vl_bc_icms_st'),
                        aliquota_st=item_data.get('aliq_st'),
                        valor_icms_st=item_data.get('vl_icms_st'),
                        ind_apur=item_data.get('ind_apur'),
                        cst_ipi=item_data.get('cst_ipi'),
                        codigo_enquadramento=item_data.get('cod_enq'),
                        valor_bc_ipi=item_data.get('vl_bc_ipi'),
                        aliquota_ipi=item_data.get('aliq_ipi'),
                        valor_ipi=item_data.get('vl_ipi'),
                        cst_pis=item_data.get('cst_pis'),
                        valor_bc_pis=item_data.get('vl_bc_pis'),
                        aliquota_pis=item_data.get('aliq_pis'),
                        quantidade_bc_pis=item_data.get('quant_bc_pis'),
                        aliquota_pis_reais=item_data.get('aliq_pis_reais'),
                        valor_pis=item_data.get('vl_pis'),
                        cst_cofins=item_data.get('cst_cofins'),
                        valor_bc_cofins=item_data.get('vl_bc_cofins'),
                        aliquota_cofins=item_data.get('aliq_cofins'),
                        quantidade_bc_cofins=item_data.get('quant_bc_cofins'),
                        aliquota_cofins_reais=item_data.get('aliq_cofins_reais'),
                        valor_cofins=item_data.get('vl_cofins'),
                        codigo_conta=item_data.get('cod_cta'),
                        valor_abatimento=item_data.get('vl_abat_nt')
                    )

                    db.session.add(item)
                    itens_processados.append(item)

            except Exception as e:
                continue

        return notas_processadas, itens_processados

    def _create_error_import_record(self, filename: str, error_msg: str, empresa_data: Dict = None) -> ImportacaoSped:
        """
        Cria um registro de importação com erro
        """
        try:
            importacao = ImportacaoSped(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                usuario_id=self.usuario_id,
                arquivo_nome=filename,
                cnpj_empresa=empresa_data.get('cnpj') if empresa_data else None,
                razao_social_empresa=empresa_data.get('nome') if empresa_data else None,
                data_inicio=empresa_data.get('dt_ini') if empresa_data else None,
                data_fim=empresa_data.get('dt_fim') if empresa_data else None,
                status='erro',
                mensagem=error_msg
            )
            
            db.session.add(importacao)
            # Não fazemos commit aqui, o controle da transação é feito no método import_sped
            return importacao
        except Exception as e:
            return None
            
    def _process_apuracoes(self, processor: SPEDProcessor, empresa_data: Dict):
        """Salva registros de apuração ICMS e IPI"""
        ano = None
        mes = None
        dt_ini = empresa_data.get('dt_ini') if empresa_data else None
        if dt_ini:
            ano = dt_ini.year
            mes = dt_ini.month
        if ano is None or mes is None:
            return

        for registro in processor.get_apuracoes_icms():
            tipo = registro.get('tipo')
            
            # Verificar se já existe uma apuração para este período e tipo
            apuracao_existente = ApuracaoICMS.query.filter_by(
                empresa_id=self.empresa_id,
                ano=ano,
                mes=mes,
                tipo=tipo
            ).first()
            
            if apuracao_existente:
                # Atualizar o registro existente
                apuracao_existente.vl_tot_debitos = registro.get('vl_tot_debitos')
                apuracao_existente.vl_aj_debitos = registro.get('vl_aj_debitos')
                apuracao_existente.vl_tot_aj_debitos = registro.get('vl_tot_aj_debitos')
                apuracao_existente.vl_estornos_cred = registro.get('vl_estornos_cred')
                apuracao_existente.vl_tot_creditos = registro.get('vl_tot_creditos')
                apuracao_existente.vl_aj_creditos = registro.get('vl_aj_creditos')
                apuracao_existente.vl_tot_aj_creditos = registro.get('vl_tot_aj_creditos')
                apuracao_existente.vl_estornos_deb = registro.get('vl_estornos_deb')
                apuracao_existente.vl_sld_credor_ant = registro.get('vl_sld_credor_ant')
                apuracao_existente.vl_sld_apurado = registro.get('vl_sld_apurado')
                apuracao_existente.vl_tot_ded = registro.get('vl_tot_ded')
                apuracao_existente.vl_icms_recolher = registro.get('vl_icms_recolher')
                apuracao_existente.vl_sld_credor_transportar = registro.get('vl_sld_credor_transportar')
                apuracao_existente.deb_esp = registro.get('deb_esp')
                
                # Atualizar data de atualização
                apuracao_existente.data_atualizacao = datetime.utcnow()
                
                db.session.add(apuracao_existente)
            else:
                # Criar novo registro
                ap = ApuracaoICMS(
                    empresa_id=self.empresa_id,
                    ano=ano,
                    mes=mes,
                    tipo=tipo,
                    vl_tot_debitos=registro.get('vl_tot_debitos'),
                    vl_aj_debitos=registro.get('vl_aj_debitos'),
                    vl_tot_aj_debitos=registro.get('vl_tot_aj_debitos'),
                    vl_estornos_cred=registro.get('vl_estornos_cred'),
                    vl_tot_creditos=registro.get('vl_tot_creditos'),
                    vl_aj_creditos=registro.get('vl_aj_creditos'),
                    vl_tot_aj_creditos=registro.get('vl_tot_aj_creditos'),
                    vl_estornos_deb=registro.get('vl_estornos_deb'),
                    vl_sld_credor_ant=registro.get('vl_sld_credor_ant'),
                    vl_sld_apurado=registro.get('vl_sld_apurado'),
                    vl_tot_ded=registro.get('vl_tot_ded'),
                    vl_icms_recolher=registro.get('vl_icms_recolher'),
                    vl_sld_credor_transportar=registro.get('vl_sld_credor_transportar'),
                    deb_esp=registro.get('deb_esp'),
                )
                db.session.add(ap)

        for registro in processor.get_apuracoes_ipi():
            cfop = registro.get('cfop')
            cst_ipi = registro.get('cst_ipi')
            
            # Verificar se já existe uma apuração de IPI para este período, CFOP e CST
            apuracao_ipi_existente = ApuracaoIPI.query.filter_by(
                empresa_id=self.empresa_id,
                ano=ano,
                mes=mes,
                cfop=cfop,
                cst_ipi=cst_ipi
            ).first()
            
            if apuracao_ipi_existente:
                # Atualizar o registro existente
                apuracao_ipi_existente.vl_cont_ipi = registro.get('vl_cont_ipi')
                apuracao_ipi_existente.vl_bc_ipi = registro.get('vl_bc_ipi')
                apuracao_ipi_existente.vl_ipi = registro.get('vl_ipi')
                
                # Atualizar data de atualização
                apuracao_ipi_existente.data_atualizacao = datetime.utcnow()
                
                db.session.add(apuracao_ipi_existente)
            else:
                # Criar novo registro
                ap = ApuracaoIPI(
                    empresa_id=self.empresa_id,
                    ano=ano,
                    mes=mes,
                    cfop=cfop,
                    cst_ipi=cst_ipi,
                    vl_cont_ipi=registro.get('vl_cont_ipi'),
                    vl_bc_ipi=registro.get('vl_bc_ipi'),
                    vl_ipi=registro.get('vl_ipi'),
                )
                db.session.add(ap)

        for registro in processor.get_apuracoes_ipi_geral():
            apuracao_existente = IpiApuracaoGeral.query.filter_by(
                empresa_id=self.empresa_id,
                ano=ano,
                mes=mes,
            ).first()

            if apuracao_existente:
                apuracao_existente.vl_sd_ant_ipi = registro.get('vl_sd_ant_ipi')
                apuracao_existente.vl_deb_ipi = registro.get('vl_deb_ipi')
                apuracao_existente.vl_cred_ipi = registro.get('vl_cred_ipi')
                apuracao_existente.vl_od_ipi = registro.get('vl_od_ipi')
                apuracao_existente.vl_oc_ipi = registro.get('vl_oc_ipi')
                apuracao_existente.vl_sc_ipi = registro.get('vl_sc_ipi')
                apuracao_existente.vl_sd_ipi = registro.get('vl_sd_ipi')
                apuracao_existente.data_atualizacao = datetime.utcnow()
                db.session.add(apuracao_existente)
            else:
                ap = IpiApuracaoGeral(
                    empresa_id=self.empresa_id,
                    ano=ano,
                    mes=mes,
                    vl_sd_ant_ipi=registro.get('vl_sd_ant_ipi'),
                    vl_deb_ipi=registro.get('vl_deb_ipi'),
                    vl_cred_ipi=registro.get('vl_cred_ipi'),
                    vl_od_ipi=registro.get('vl_od_ipi'),
                    vl_oc_ipi=registro.get('vl_oc_ipi'),
                    vl_sc_ipi=registro.get('vl_sc_ipi'),
                    vl_sd_ipi=registro.get('vl_sd_ipi'),
                )
                db.session.add(ap)

    # Método removido - A criação de cenários será feita apenas na importação de XML

import api from './authService'
import type {
  Cliente,
  ClientesResponse,
  ClienteFilterOptions,
} from '@/types/clientes'

interface ClienteFilters {
  uf?: string
  municipio?: string
  atividade?: string
  destinacao?: string
  cnae?: string
  page?: number
  perPage?: number
}

export const clientesService = {
  async getClientes(
    empresaId?: number,
    filtros: ClienteFilters = {}
  ): Promise<ClientesResponse> {
    const params = new URLSearchParams()
    if (empresaId) params.append('empresa_id', empresaId.toString())
    if (filtros.uf) params.append('uf', filtros.uf)
    if (filtros.municipio) params.append('municipio', filtros.municipio)
    if (filtros.atividade) params.append('atividade', filtros.atividade)
    if (filtros.destinacao) params.append('destinacao', filtros.destinacao)
    if (filtros.cnae) params.append('cnae', filtros.cnae)
    params.append('page', filtros.page ? filtros.page.toString() : '1')
    params.append(
      'per_page',
      filtros.perPage ? filtros.perPage.toString() : '20'
    )

    const response = await api.get(`/clientes?${params.toString()}`)
    return response.data
  },

  async getFilterOptions(empresaId?: number): Promise<ClienteFilterOptions> {
    const url = empresaId
      ? `/clientes/filter-options?empresa_id=${empresaId}`
      : '/clientes/filter-options'
    const response = await api.get(url)
    return response.data.options
  },

  async getMunicipios(empresaId: number, uf: string): Promise<string[]> {
    const response = await api.get(
      `/clientes/municipios?empresa_id=${empresaId}&uf=${uf}`
    )
    return response.data.municipios || []
  },

  async createCliente(data: Partial<Cliente> & { empresa_id?: number }) {
    const response = await api.post('/clientes', data)
    return response.data
  },

  async updateCliente(id: number, data: Partial<Cliente>) {
    const response = await api.put(`/clientes/${id}`, data)
    return response.data
  },

  async bulkUpdateClientes(ids: number[], data: Partial<Cliente>) {
    const response = await api.put('/clientes/bulk-update', {
      cliente_ids: ids,
      ...data,
    })
    return response.data
  },

  async deleteCliente(id: number) {
    const response = await api.delete(`/clientes/${id}`)
    return response.data
  }
}

export type { ClienteFilters }
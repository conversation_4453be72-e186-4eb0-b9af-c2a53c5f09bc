from .escritorio import db
from sqlalchemy.dialects.postgresql import JSONB

class Usuario(db.Model):
    __tablename__ = 'usuario'
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    portal_user_id = db.Column(db.String(255), unique=True, nullable=False)
    nome = db.Column(db.String(255), nullable=False)
    empresas_permitidas = db.Column(JSONB)
    escritorio_id = db.<PERSON>umn(db.In<PERSON><PERSON>, db.<PERSON><PERSON>('escritorio.id'))
    is_admin = db.Column(db.<PERSON><PERSON>, default=False)
    tipo_usuario = db.Column(db.String(20), default='normal')  # 'super_admin', 'admin', 'normal'

    def __repr__(self):
        return f"<Usuario {self.nome}>"

    @property
    def is_escritorio(self):
        return self.tipo_usuario == 'escritorio'
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'nome': self.nome,
            'portal_user_id': self.portal_user_id,
            'escritorio_id': self.escritorio_id,
            'is_admin': self.is_admin,
            'tipo_usuario': self.tipo_usuario,
            'empresas_permitidas': self.empresas_permitidas or []
        }

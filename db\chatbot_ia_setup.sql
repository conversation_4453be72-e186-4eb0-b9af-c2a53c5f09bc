-- =====================================================
-- SETUP DO BANCO DE DADOS PARA CHATBOT IA
-- Sistema de Auditoria Fiscal
-- =====================================================

-- Definir encoding para evitar problemas
SET client_encoding = 'UTF8';

-- Tabela para armazenar contexto e histórico do chatbot
CREATE TABLE IF NOT EXISTS chatbot_conversas (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL REFERENCES usuario(id),
    empresa_id INTEGER REFERENCES empresa(id),
    escritorio_id INTEGER REFERENCES escritorio(id),
    pergunta TEXT NOT NULL,
    resposta TEXT NOT NULL,
    contexto_sql TEXT, -- Query SQL gerada para responder a pergunta
    dados_utilizados JSONB, -- Dados que foram utilizados na resposta
    tempo_resposta INTEGER, -- Tempo em milissegundos para gerar a resposta
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    avaliacao INTEGER CHECK (avaliacao >= 1 AND avaliacao <= 5), -- Avaliação do usuário (1-5)
    feedback TEXT -- Feedback opcional do usuário
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_chatbot_conversas_usuario ON chatbot_conversas(usuario_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_conversas_empresa ON chatbot_conversas(empresa_id);
CREATE INDEX IF NOT EXISTS idx_chatbot_conversas_data ON chatbot_conversas(data_criacao);

-- Tabela para armazenar templates de perguntas frequentes
CREATE TABLE IF NOT EXISTS chatbot_templates (
    id SERIAL PRIMARY KEY,
    categoria VARCHAR(50) NOT NULL, -- 'nota_fiscal', 'auditoria', 'cenario', 'tributo', 'empresa'
    pergunta_template TEXT NOT NULL,
    sql_template TEXT NOT NULL,
    descricao TEXT,
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inserir templates básicos
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao) VALUES
('nota_fiscal', 'nota {numero} da empresa {empresa}',
 'SELECT nfi.*, e.nome as empresa_nome FROM nota_fiscal_item nfi JOIN empresa e ON nfi.empresa_id = e.id WHERE nfi.numero_nf = ''{numero}'' AND e.nome ILIKE ''%{empresa}%''',
 'Busca informações de uma nota fiscal específica'),

('auditoria', 'auditoria da empresa {empresa}',
 'SELECT ar.*, e.nome as empresa_nome FROM auditoria_resultado ar JOIN empresa e ON ar.empresa_id = e.id WHERE e.nome ILIKE ''%{empresa}%''',
 'Busca resultados de auditoria de uma empresa'),

('tributo', 'tributo {tipo} da nota {numero}',
 'SELECT t.*, nfi.numero_nf FROM tributo t JOIN nota_fiscal_item nfi ON t.nota_fiscal_item_id = nfi.id WHERE nfi.numero_nf = ''{numero}'' AND ''{tipo}'' IN (''icms'', ''ipi'', ''pis'', ''cofins'')',
 'Busca informações de tributo específico de uma nota'),

('inconsistencia', 'inconsistências do {tributo}',
 'SELECT ar.* FROM auditoria_resultado ar WHERE ar.tipo_tributo = ''{tributo}'' AND ar.status = ''inconsistente''',
 'Busca inconsistências de um tipo de tributo'),

('produto', 'produto {nome}',
 'SELECT p.*, COUNT(nfi.id) as total_notas FROM produto p LEFT JOIN nota_fiscal_item nfi ON p.id = nfi.produto_id WHERE p.nome ILIKE ''%{nome}%'' GROUP BY p.id',
 'Busca informações de um produto específico');

-- View materializada para consultas rápidas do chatbot
CREATE MATERIALIZED VIEW IF NOT EXISTS vw_chatbot_dados_completos AS
SELECT
    -- Dados da nota fiscal
    nfi.id as nota_id,
    nfi.numero_nf,
    nfi.chave_nf,
    nfi.data_emissao,
    nfi.cfop,
    nfi.ncm,
    nfi.valor_total as valor_nota,

    -- Dados da empresa
    e.id as empresa_id,
    e.nome as empresa_nome,
    e.cnpj as empresa_cnpj,

    -- Dados do cliente
    c.id as cliente_id,
    c.nome as cliente_nome,
    c.cnpj as cliente_cnpj,
    c.atividade as cliente_atividade,

    -- Dados do produto
    p.id as produto_id,
    p.nome as produto_nome,
    p.ncm as produto_ncm,
    p.cest as produto_cest,

    -- Dados dos tributos
    t.icms_valor,
    t.icms_aliquota,
    t.icms_vbc as icms_base_calculo,
    t.icms_cst,

    t.ipi_valor,
    t.ipi_aliquota,
    t.ipi_vbc as ipi_base_calculo,
    t.ipi_cst,

    t.pis_valor,
    t.pis_aliquota,
    t.pis_vbc as pis_base_calculo,
    t.pis_cst,

    t.cofins_valor,
    t.cofins_aliquota,
    t.cofins_vbc as cofins_base_calculo,
    t.cofins_cst,

    -- Dados da auditoria
    ar.id as auditoria_id,
    ar.tipo_tributo,
    ar.status as auditoria_status,
    ar.valor_nota as auditoria_valor_nota,
    ar.valor_calculado as auditoria_valor_calculado,
    ar.diferenca_valor,
    ar.diferenca_percentual,

    -- Dados dos cenários
    CASE
        WHEN ar.tipo_tributo = 'icms' THEN ci.status
        WHEN ar.tipo_tributo = 'ipi' THEN cip.status
        WHEN ar.tipo_tributo = 'pis' THEN cp.status
        WHEN ar.tipo_tributo = 'cofins' THEN cc.status
    END as cenario_status,

    -- Metadados
    EXTRACT(YEAR FROM nfi.data_emissao) as ano,
    EXTRACT(MONTH FROM nfi.data_emissao) as mes

FROM nota_fiscal_item nfi
LEFT JOIN empresa e ON nfi.empresa_id = e.id
LEFT JOIN cliente c ON nfi.cliente_id = c.id
LEFT JOIN produto p ON nfi.produto_id = p.id
LEFT JOIN tributo t ON t.nota_fiscal_item_id = nfi.id
LEFT JOIN auditoria_resultado ar ON ar.nota_fiscal_item_id = nfi.id
LEFT JOIN cenario_icms ci ON ar.cenario_id = ci.id AND ar.tipo_tributo = 'icms'
LEFT JOIN cenario_ipi cip ON ar.cenario_id = cip.id AND ar.tipo_tributo = 'ipi'
LEFT JOIN cenario_pis cp ON ar.cenario_id = cp.id AND ar.tipo_tributo = 'pis'
LEFT JOIN cenario_cofins cc ON ar.cenario_id = cc.id AND ar.tipo_tributo = 'cofins';

-- Índices para a view materializada
CREATE INDEX IF NOT EXISTS idx_vw_chatbot_empresa ON vw_chatbot_dados_completos(empresa_id);
CREATE INDEX IF NOT EXISTS idx_vw_chatbot_nota ON vw_chatbot_dados_completos(numero_nf);
CREATE INDEX IF NOT EXISTS idx_vw_chatbot_produto ON vw_chatbot_dados_completos(produto_nome);
CREATE INDEX IF NOT EXISTS idx_vw_chatbot_cliente ON vw_chatbot_dados_completos(cliente_nome);
CREATE INDEX IF NOT EXISTS idx_vw_chatbot_tributo ON vw_chatbot_dados_completos(tipo_tributo);
CREATE INDEX IF NOT EXISTS idx_vw_chatbot_status ON vw_chatbot_dados_completos(auditoria_status);
CREATE INDEX IF NOT EXISTS idx_vw_chatbot_data ON vw_chatbot_dados_completos(data_emissao);

-- Função para atualizar a view materializada
CREATE OR REPLACE FUNCTION refresh_chatbot_view()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW vw_chatbot_dados_completos;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar a view quando houver mudanças relevantes
CREATE OR REPLACE FUNCTION trigger_refresh_chatbot_view()
RETURNS trigger AS $$
BEGIN
    PERFORM refresh_chatbot_view();
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Aplicar triggers nas tabelas principais
DROP TRIGGER IF EXISTS trigger_chatbot_refresh_nota ON nota_fiscal_item;
CREATE TRIGGER trigger_chatbot_refresh_nota
    AFTER INSERT OR UPDATE OR DELETE ON nota_fiscal_item
    FOR EACH STATEMENT EXECUTE FUNCTION trigger_refresh_chatbot_view();

DROP TRIGGER IF EXISTS trigger_chatbot_refresh_auditoria ON auditoria_resultado;
CREATE TRIGGER trigger_chatbot_refresh_auditoria
    AFTER INSERT OR UPDATE OR DELETE ON auditoria_resultado
    FOR EACH STATEMENT EXECUTE FUNCTION trigger_refresh_chatbot_view();

-- Comentários nas tabelas
COMMENT ON TABLE chatbot_conversas IS 'Armazena histórico de conversas do chatbot IA';
COMMENT ON TABLE chatbot_templates IS 'Templates de perguntas e consultas SQL para o chatbot';
COMMENT ON MATERIALIZED VIEW vw_chatbot_dados_completos IS 'View otimizada com todos os dados necessários para o chatbot';

-- Atualizar a view pela primeira vez
SELECT refresh_chatbot_view();

import { useState, useEffect } from 'react'
import type { Produto } from '@/types/produto'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'

interface ProdutoModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: Partial<Produto>) => void
  produto?: Produto | null
}

const emptyProduto: Partial<Produto> = {
  codigo: '',
  descricao: '',
  unidade_comercial: '',
  unidade_tributavel: '',
  unidade_tributaria: '',
  tipo_sped: '',
  codigo_ean: '',
  codigo_ean_tributavel: '',
  cest: '',
  classificacao_tributaria: '',
  ncm: ''
}

export function ProdutoModal({ isOpen, onClose, onSave, produto }: ProdutoModalProps) {
  const [formData, setFormData] = useState<Partial<Produto>>(emptyProduto)
  const [activeTab, setActiveTab] = useState<'geral' | 'fiscal'>('geral')

  useEffect(() => {
    if (produto) {
      setFormData(produto)
    } else {
      setFormData(emptyProduto)
    }
    setActiveTab('geral')
  }, [produto, isOpen])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={produto ? 'Editar Produto' : 'Novo Produto'}
      size="lg"
      footer={
        <div className="flex gap-3">
          <Button
            variant="ghost"
            onClick={onClose}
          >
            Cancelar
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            glow
          >
            Salvar
          </Button>
        </div>
      }
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Modern Tabs */}
        <div className="mb-6">
          <div className="flex gap-2 p-1 bg-gray-100 dark:bg-gray-700 rounded-xl">
            <Button
              variant={activeTab === 'geral' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab('geral')}
              className="flex-1 justify-center"
              glow={activeTab === 'geral'}
              type="button"
            >
              Informações Gerais
            </Button>
            <Button
              variant={activeTab === 'fiscal' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab('fiscal')}
              className="flex-1 justify-center"
              glow={activeTab === 'fiscal'}
              type="button"
            >
              Informações Fiscais
            </Button>
          </div>
        </div>

        {activeTab === 'geral' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Código *</label>
              <input
                type="text"
                name="codigo"
                value={formData.codigo || ''}
                onChange={handleChange}
                required
                className="modern-input"
                placeholder="Digite o código do produto"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Descrição *</label>
              <input
                type="text"
                name="descricao"
                value={formData.descricao || ''}
                onChange={handleChange}
                required
                className="modern-input"
                placeholder="Digite a descrição do produto"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Unidade Comercial</label>
              <input
                type="text"
                name="unidade_comercial"
                value={formData.unidade_comercial || ''}
                onChange={handleChange}
                className="modern-input"
                placeholder="Ex: UN, KG, M"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Unidade Tributável</label>
              <input
                type="text"
                name="unidade_tributavel"
                value={formData.unidade_tributavel || ''}
                onChange={handleChange}
                className="modern-input"
                placeholder="Ex: UN, KG, M"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Unidade Tributária</label>
              <input
                type="text"
                name="unidade_tributaria"
                value={formData.unidade_tributaria || ''}
                onChange={handleChange}
                className="modern-input"
                placeholder="Ex: UN, KG, M"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Tipo SPED</label>
              <input
                type="text"
                name="tipo_sped"
                value={formData.tipo_sped || ''}
                onChange={handleChange}
                className="modern-input"
                placeholder="Digite o tipo SPED"
              />
            </div>
          </div>
        )}

        {activeTab === 'fiscal' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">NCM</label>
              <input
                type="text"
                name="ncm"
                value={formData.ncm || ''}
                onChange={handleChange}
                readOnly
                className="modern-input opacity-60 cursor-not-allowed"
                placeholder="Obtido automaticamente"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                O NCM é obtido das notas fiscais e cenários
              </p>
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Classificação Tributária</label>
              <input
                type="text"
                name="classificacao_tributaria"
                value={formData.classificacao_tributaria || ''}
                onChange={handleChange}
                className="modern-input"
                placeholder="Digite a classificação tributária"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Código EAN</label>
              <input
                type="text"
                name="codigo_ean"
                value={formData.codigo_ean || ''}
                onChange={handleChange}
                className="modern-input"
                placeholder="Digite o código EAN"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Código EAN Tributável</label>
              <input
                type="text"
                name="codigo_ean_tributavel"
                value={formData.codigo_ean_tributavel || ''}
                onChange={handleChange}
                className="modern-input"
                placeholder="Digite o código EAN tributável"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">CEST</label>
              <input
                type="text"
                name="cest"
                value={formData.cest || ''}
                onChange={handleChange}
                className="modern-input"
                placeholder="Digite o código CEST"
              />
            </div>
          </div>
        )}
      </form>
    </Modal>
  )
}
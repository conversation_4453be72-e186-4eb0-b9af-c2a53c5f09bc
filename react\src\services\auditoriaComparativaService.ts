import api from './authService'

export interface AuditoriaComparativaItem {
  id: number
  chave_nf: string
  numero_nf: string
  data_emissao: string
  participante_razao_social: string
  participante_cnpj: string
  participante_uf: string
  regime_parceiro: string
  produto_codigo_sped: string
  produto_codigo_nota: string
  produto_descricao: string
  ncm_nfe: string
  ncm_sped: string
  tipo?: string
  descricao_tipo?: string
  produto_nota: string
  origem_sped?: string
  origem_nota?: string
  cfop_sped: string
  cfop_nota: string
  cst_sped: string
  cst_nota: string
  csosn_nota?: string
  reducao_sped?: number
  reducao_nota?: number
  mva_nota?: number
  base_calculo_sped: number
  base_calculo_nota: number
  aliquota_sped: number
  aliquota_nota: number
  valor_sped: number
  valor_nota: number
  match_status: string
  status: string
  aprovado: boolean
  data_aprovacao?: string
  usuario_aprovacao?: string
  observacoes?: string
  tributos?: {
    [key: string]: {
      cst?: string
      bc?: number
      aliquota?: number
      valor?: number
      reducao?: number
    }
  }
  xml_data?: Record<string, any>
  sped_data?: Record<string, any>
  // Campos adicionais para compatibilidade com o frontend antigo
  xml_item_id?: number
  sped_item_id?: number
  match_type?: string
  confidence_score?: number
  // Campos alternativos que podem vir do backend
  sped_cfop?: string
  xml_cfop?: string
  sped_cst?: string
  xml_cst?: string
  sped_origem?: string
  xml_origem?: string
  sped_ncm?: string
  xml_ncm?: string
  sped_codigo_produto?: string
  xml_codigo_produto?: string
  sped_descricao?: string
  xml_descricao?: string
  parceiro_nome?: string
  cliente_nome?: string
  cliente_uf?: string
  // Campos de tipo de produto vindos do backend antigo
  tipo_produto?: string
  descricao_tipo_produto?: string
  sped_tipo_item?: string
}

export interface MatchingStats {
  total_xml_items: number
  total_sped_items: number
  total_matches: number
  direct_matches: number
  embedding_matches: number
  unmatched_xml_items: number
  unmatched_sped_items: number
  match_rate: number
  average_confidence: number
  high_confidence_matches: number
  medium_confidence_matches: number
  low_confidence_matches: number
}

export interface AuditoriaComparativaData {
  auditorias: AuditoriaComparativaItem[]
  total_registros: number
  matching_stats?: {
    total: number
    matched: number
    unmatched: number
    percentage: number
  }
}

export interface GerarAuditoriaComparativaParams {
  empresaId: number
  mes: number
  ano: number
  tributos?: string[]
  forceRecalculate?: boolean
}

export interface GerarEscrituracaoParams {
  empresaId: number
  mes: number
  ano: number
  forcarRecalculo?: boolean
  tolerancia?: number
}

export const auditoriaComparativaService = {
  async buscarPorPeriodo({
    empresaId,
    mes,
    ano,
    tributo,
    filtros,
    page,
    pageSize,
  }: {
    empresaId: number
    mes: number
    ano: number
    tributo?: string
    filtros?: Record<string, string | string[]>
    page?: number
    pageSize?: number
  }): Promise<AuditoriaComparativaData> {
    const params: any = {
      empresa_id: empresaId,
      mes,
      ano,
    }
    if (tributo) {
      params.tributo = tributo
    }
    if (page) params.page = page
    if (pageSize) params.page_size = pageSize
    if (filtros) {
      Object.entries(filtros).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          if (value.length > 0) params[key] = value.join(',')
        } else if (value !== undefined && value !== null && value !== '') {
          params[key] = value
        }
      })
    }
    const response = await api.get('/auditoria-comparativa/periodo', {
      params,
    })
    return response.data
  },

  async buscarPorChave({
    chaveNF,
    empresaId,
  }: {
    chaveNF: string
    empresaId: number
  }): Promise<AuditoriaComparativaData> {
    const response = await api.get(`/auditoria-comparativa/nota/${chaveNF}`, {
      params: {
        empresa_id: empresaId,
      },
    })
    return response.data
  },

  async gerarPorPeriodo({
    empresaId,
    mes,
    ano,
    tributos,
    forceRecalculate = true,
  }: GerarAuditoriaComparativaParams) {
    const response = await api.post('/auditoria-comparativa/gerar-periodo', {
      empresa_id: empresaId,
      mes,
      ano,
      tributos_filter: tributos,
      force_recalculate: forceRecalculate,
    })
    return response.data
  },

  async gerarPorChave({
    chaveNF,
    empresaId,
    tributos,
    forceRecalculate = true,
  }: {
    chaveNF: string
    empresaId: number
    tributos?: string[]
    forceRecalculate?: boolean
  }) {
    const response = await api.post('/auditoria-comparativa/gerar', {
      chave_nf: chaveNF,
      empresa_id: empresaId,
      tributos_filter: tributos,
      force_recalculate: forceRecalculate,
    })
    return response.data
  },

  async gerarEscrituracao({
    empresaId,
    mes,
    ano,
    forcarRecalculo = true,
    tolerancia = 0.01,
  }: GerarEscrituracaoParams) {
    const response = await api.post('/auditoria-entrada/gerar-escrituracao', {
      empresa_id: empresaId,
      mes,
      ano,
      forcar_recalculo: forcarRecalculo,
      tolerancia,
    })
    return response.data
  },

  async obterOpcoesFiltro({
    empresaId,
    mes,
    ano,
    tributo,
    filtros,
  }: {
    empresaId: number
    mes: number
    ano: number
    tributo?: string
    filtros?: Record<string, string | string[]>
  }) {
    const params: any = {
      empresa_id: empresaId,
      mes,
      ano,
    }
    if (tributo) params.tributo = tributo
    if (filtros) {
      Object.entries(filtros).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          if (value.length > 0) params[key] = value.join(',')
        } else if (value !== undefined && value !== null && value !== '') {
          params[key] = value
        }
      })
    }
    const response = await api.get('/auditoria-comparativa/filter-options', {
      params,
    })
    return response.data
  },

  async aprovarSelecionados({
    matches,
    empresaId,
    tributo,
    feedback,
  }: {
    matches: { xml_item_id: number; sped_item_id: number }[]
    empresaId: number
    tributo: string
    feedback?: string
  }) {
    // Validar parâmetros obrigatórios
    if (!matches || !Array.isArray(matches) || matches.length === 0) {
      throw new Error('Matches são obrigatórios e devem ser um array não vazio')
    }
    
    if (!empresaId) {
      throw new Error('Empresa ID é obrigatório')
    }
    
    if (!tributo) {
      throw new Error('Tributo é obrigatório')
    }
    
    const response = await api.post('/auditoria-comparativa/aprovar-matches-massa', {
      matches: matches,
      empresa_id: empresaId,
      tributo: tributo,
      feedback: feedback || 'Aprovação em massa',
    })
    return response.data
  },

  async aprovarItem(
    xmlItemId: number,
    spedItemId: number,
    empresaId: number,
    tributo: string,
    observacoes?: string
  ) {
    const response = await api.post('/auditoria-comparativa/aprovar-match', {
      xml_item_id: xmlItemId,
      sped_item_id: spedItemId,
      empresa_id: empresaId,
      tributo: tributo,
      feedback: observacoes || 'Match aprovado pelo usuário',
    })
    return response.data
  },

  async editarSpedMassa({
    ids,
    empresaId,
    tributo,
    dados,
  }: {
    ids: number[]
    empresaId: number
    tributo: string
    dados: any
  }) {
    const payload: any = {
      auditoria_ids: ids,
      empresa_id: empresaId,
    }

    if (dados.cfop) {
      payload.cfop = dados.cfop
    }

    const tributoKey = tributo.toLowerCase()
    payload[tributoKey] = {}

    if (dados.cst) {
      payload[tributoKey].cst = dados.cst
    }
    if (dados.base_calculo) {
      payload[tributoKey].bc = parseFloat(dados.base_calculo)
    }
    if (dados.aliquota) {
      payload[tributoKey].aliquota = parseFloat(dados.aliquota)
    }
    if (dados.valor) {
      payload[tributoKey].valor = parseFloat(dados.valor)
    }

    const response = await api.put(
      '/auditoria-comparativa/editar-sped-massa',
      payload
    )
    return response.data
  },

  async obterDetalhes(id: number, empresaId: number) {
    const response = await api.get(`/auditoria-comparativa/detalhes/${id}`, {
      params: { empresa_id: empresaId },
    })
    return response.data.detalhes
  },

  async gerarRelatorio(config: {
    empresaId: number
    mes: number
    ano: number
    tributo?: string
    formato: 'pdf' | 'excel'
    incluirDetalhes: boolean
    filtros?: any
  }) {
    const response = await api.post(
      '/auditoria-comparativa/relatorio',
      config,
      {
        responseType: 'blob',
      }
    )
    return response.data
  },
}
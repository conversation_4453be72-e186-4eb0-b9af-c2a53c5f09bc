import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { useFilterStore } from '@/store/filterStore'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Table } from '@/components/ui/Table'
import { Badge } from '@/components/ui/Badge'
import { Modal } from '@/components/ui/Modal'
import { Textarea } from '@/components/ui/Textarea'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { escrituracaoService, EscrituracaoItem, EscrituracaoData } from '@/services/escrituracaoService'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'

interface DetalhesEscrituracaoModalProps {
  isOpen: boolean
  onClose: () => void
  item: EscrituracaoItem | null
}

interface AprovarModalProps {
  isOpen: boolean
  onClose: () => void
  selectedIds: number[]
  onConfirm: (justificativa: string) => void
}

function DetalhesEscrituracaoModal({ isOpen, onClose, item }: DetalhesEscrituracaoModalProps) {
  const empresaId = useSelectedCompany()
  const queryClient = useQueryClient()
  const [detalhes, setDetalhes] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [justificativa, setJustificativa] = useState('')

  React.useEffect(() => {
    if (isOpen && item) {
      setLoading(true)
      escrituracaoService.obterDetalhes(item.id)
        .then(setDetalhes)
        .catch(console.error)
        .finally(() => setLoading(false))
    }
  }, [isOpen, item])

  if (!item) return null

  const divergencia = Math.abs(item.divergencia_valor || 0)
  const tolerancia = parseFloat(localStorage.getItem('toleranciaEscrituracao') || '0.01')
  const percentualDivergencia = item.valor_xml > 0 ? (divergencia / item.valor_xml * 100) : 0

  const formatCurrency = (value: number) => {
    if (isNaN(value) || value === null || value === undefined) return 'R$ 0,00'
    return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(value)
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    try {
      return new Date(dateString).toLocaleDateString('pt-BR')
    } catch {
      return '-'
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={`Detalhes Comparativos - NF ${item.numero_nf}`} size="xl">
      <div className="space-y-6">
        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Informações Gerais e Resumo Comparativo */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Dados Gerais */}
              <Card className="p-4">
                <div className="bg-primary-600 text-white p-3 rounded-t-lg -m-4 mb-4">
                  <h4 className="font-semibold flex items-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Dados Gerais
                  </h4>
                </div>
                <div className="space-y-2 text-sm">
                  <p><strong>Empresa:</strong> {detalhes?.empresa?.nome || detalhes?.auditoria?.empresa_nome || '-'}</p>
                  <p><strong>CNPJ:</strong> {detalhes?.empresa?.cnpj || detalhes?.auditoria?.empresa_cnpj || item.participante_cnpj}</p>
                  <p><strong>Chave NF:</strong> <span className="font-mono text-xs">{item.chave_nf || detalhes?.auditoria?.chave_nf || '-'}</span></p>
                  <p><strong>Número NF:</strong> {item.numero_nf}</p>
                  <p><strong>Participante:</strong> {item.participante_razao_social}</p>
                </div>
              </Card>

              {/* Resumo Comparativo */}
              <Card className="p-4">
                <div className={`${divergencia <= tolerancia ? 'bg-green-600' : 'bg-red-600'} text-white p-3 rounded-t-lg -m-4 mb-4`}>
                  <h4 className="font-semibold flex items-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    Resumo Comparativo
                  </h4>
                </div>
                <div className="space-y-2 text-sm">
                  <p><strong>Valor Total XML:</strong> {formatCurrency(item.valor_xml)}</p>
                  <p><strong>Valor Total SPED:</strong> {formatCurrency(item.valor_sped)}</p>
                  <p><strong>Divergência:</strong> {formatCurrency(divergencia)}</p>
                  <p><strong>Percentual:</strong> {percentualDivergencia.toFixed(2)}%</p>
                </div>
              </Card>
            </div>

            {/* Comparação Lado a Lado */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Dados XML */}
              <Card className="p-4">
                <div className="bg-blue-600 text-white p-3 rounded-t-lg -m-4 mb-4">
                  <h4 className="font-semibold flex items-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    Dados XML
                  </h4>
                </div>
                <div className="space-y-2 text-sm">
                  <p><strong>Data Emissão:</strong> {formatDate(detalhes?.xml?.dados_gerais?.data_emissao || item.data_emissao || '')}</p>
                  <p><strong>Data Entrada:</strong> {formatDate(detalhes?.xml?.dados_gerais?.data_entrada || item.data_entrada)}</p>
                  <p><strong>Emitente:</strong> {detalhes?.xml?.dados_gerais?.razao_social_emitente || item.participante_razao_social}</p>
                  <p><strong>Valor Total XML:</strong> {formatCurrency(item.valor_xml)}</p>
                  
                  <div className="mt-4">
                    <h6 className="font-semibold">Itens ({detalhes?.xml?.itens?.length || 0}):</h6>
                    <TableScrollContainer containerClassName="max-h-48 overflow-y-auto mt-2">
                      <table className="w-full text-xs">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-1">Produto</th>
                            <th className="text-right p-1">Qtd</th>
                            <th className="text-right p-1">Valor Unit</th>
                            <th className="text-right p-1">Valor Total</th>
                          </tr>
                        </thead>
                        <tbody>
                          {detalhes?.xml?.itens?.map((item: any, index: number) => (
                            <tr key={index} className="border-b">
                              <td className="p-1 truncate max-w-20" title={item.descricao_produto}>
                                {item.descricao_produto || '-'}
                              </td>
                              <td className="text-right p-1">{item.quantidade || '-'}</td>
                              <td className="text-right p-1">
                                {item.valor_unitario ? formatCurrency(parseFloat(item.valor_unitario)) : '-'}
                              </td>
                              <td className="text-right p-1">
                                {item.valor_total ? formatCurrency(parseFloat(item.valor_total)) : '-'}
                              </td>
                            </tr>
                          )) || (
                            <tr>
                              <td colSpan={4} className="text-center p-4 text-gray-500">
                                Dados XML não disponíveis
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                      </TableScrollContainer>
                  </div>
                </div>
              </Card>

              {/* Dados SPED */}
              <Card className="p-4">
                <div className="bg-yellow-600 text-white p-3 rounded-t-lg -m-4 mb-4">
                  <h4 className="font-semibold flex items-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                    </svg>
                    Dados SPED
                  </h4>
                </div>
                <div className="space-y-2 text-sm">
                  <p><strong>Data Documento:</strong> {formatDate(detalhes?.sped?.dados_gerais?.data_documento || item.data_entrada)}</p>
                  <p><strong>Data Entrada/Saída:</strong> {formatDate(detalhes?.sped?.dados_gerais?.data_entrada_saida || item.data_entrada)}</p>
                  <p><strong>Emitente:</strong> {detalhes?.sped?.dados_gerais?.razao_social || item.participante_razao_social}</p>
                  <p><strong>Valor Total SPED:</strong> {formatCurrency(item.valor_sped)}</p>
                  
                  <div className="mt-4">
                    <h6 className="font-semibold">Itens ({detalhes?.sped?.itens?.length || 0}):</h6>
                    <TableScrollContainer containerClassName="max-h-48 overflow-y-auto mt-2">
                      <table className="w-full text-xs">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-1">Produto</th>
                            <th className="text-right p-1">Qtd</th>
                            <th className="text-right p-1">Valor Unit</th>
                            <th className="text-right p-1">Valor Total</th>
                          </tr>
                        </thead>
                        <tbody>
                          {detalhes?.sped?.itens?.map((item: any, index: number) => (
                            <tr key={index} className="border-b">
                              <td className="p-1 truncate max-w-20" title={item.descricao}>
                                {item.descricao || '-'}
                              </td>
                              <td className="text-right p-1">{item.quantidade || '-'}</td>
                              <td className="text-right p-1">
                                {item.valor_unitario ? formatCurrency(parseFloat(item.valor_unitario)) : '-'}
                              </td>
                              <td className="text-right p-1">
                                {item.valor_item ? formatCurrency(parseFloat(item.valor_item)) : '-'}
                              </td>
                            </tr>
                          )) || (
                            <tr>
                              <td colSpan={4} className="text-center p-4 text-gray-500">
                                Dados SPED não disponíveis
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </TableScrollContainer>
                  </div>
                </div>
              </Card>
            </div>

            {/* Justificativa para Aprovação (se divergente) */}
            {divergencia > tolerancia && item.status_escrituracao !== 'aprovado' && (
              <Card className="p-4 border-yellow-200 dark:border-yellow-800">
                <div className="bg-yellow-600 text-white p-3 rounded-t-lg -m-4 mb-4">
                  <h4 className="font-semibold flex items-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    Justificativa para Aprovação
                  </h4>
                </div>
                <Textarea
                  value={justificativa}
                  onChange={(e) => setJustificativa(e.target.value)}
                  placeholder="Digite a justificativa para aprovação desta divergência..."
                  rows={3}
                />
              </Card>
            )}

            {/* Justificativa existente (se já aprovado) */}
            {item.justificativa && (
              <Card className="p-4 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
                <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">
                  Justificativa da Aprovação
                </h4>
                <p className="text-green-800 dark:text-green-200 text-sm">
                  {item.justificativa}
                </p>
                {item.data_aprovacao && (
                  <p className="text-green-600 dark:text-green-400 text-xs mt-2">
                    Aprovado em {formatDate(item.data_aprovacao)} por {item.usuario_aprovacao || 'Sistema'}
                  </p>
                )}
              </Card>
            )}
          </div>
        )}

        {/* Footer com botões */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button variant="ghost" onClick={onClose}>
            Fechar
          </Button>
          {divergencia > tolerancia && item.status_escrituracao !== 'aprovado' && (
            <Button
              variant="success"
              onClick={async () => {
                try {
                  await escrituracaoService.aprovarEscrituracao({ 
                    auditoriaIds: [item.id], 
                    justificativa 
                  })
                  // Recarregar dados
                  queryClient.invalidateQueries({ queryKey: ['escrituracao'] })
                  onClose()
                } catch (error) {
                  console.error('Erro ao aprovar:', error)
                }
              }}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              }
            >
              Aprovar com Justificativa
            </Button>
          )}
          {divergencia <= tolerancia && item.status_escrituracao !== 'aprovado' && (
            <Button
              variant="success"
              onClick={async () => {
                try {
                  await escrituracaoService.aprovarEscrituracao({ 
                    auditoriaIds: [item.id] 
                  })
                  // Recarregar dados
                  queryClient.invalidateQueries({ queryKey: ['escrituracao'] })
                  onClose()
                } catch (error) {
                  console.error('Erro ao aprovar:', error)
                }
              }}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              }
            >
              Aprovar
            </Button>
          )}
        </div>
      </div>
    </Modal>
  )
}

function AprovarModal({ isOpen, onClose, selectedIds, onConfirm }: AprovarModalProps) {
  const [justificativa, setJustificativa] = useState('')

  const handleConfirm = () => {
    onConfirm(justificativa)
    setJustificativa('')
    onClose()
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Aprovar Escrituração">
      <div className="space-y-4">
        <p className="text-gray-600 dark:text-gray-400">
          Você está prestes a aprovar {selectedIds.length} registro(s) de escrituração.
        </p>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Justificativa (opcional)
          </label>
          <Textarea
            value={justificativa}
            onChange={(e) => setJustificativa(e.target.value)}
            placeholder="Digite uma justificativa para a aprovação..."
            rows={3}
          />
        </div>

        <div className="flex justify-end gap-3">
          <Button variant="ghost" onClick={onClose}>
            Cancelar
          </Button>
          <Button variant="success" onClick={handleConfirm}>
            Aprovar {selectedIds.length} registro(s)
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export function EscrituracaoTab() {
  const empresaId = useSelectedCompany()
  const { selectedYear, selectedMonth } = useFilterStore()
  const queryClient = useQueryClient()
  
  const [selectedIds, setSelectedIds] = useState<number[]>([])
  const [detalhesModal, setDetalhesModal] = useState<{ isOpen: boolean; item: EscrituracaoItem | null }>({
    isOpen: false,
    item: null
  })
  const [aprovarModal, setAprovarModal] = useState(false)

  // Query para dados da escrituração
  const { data: escrituracaoData, isLoading, error } = useQuery<EscrituracaoData>({
    queryKey: ['escrituracao', empresaId, selectedYear, selectedMonth],
    queryFn: () =>
      escrituracaoService.buscarEscrituracao({
        empresaId: empresaId!,
        ano: selectedYear,
        mes: selectedMonth,
      }),
    enabled: !!empresaId,
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 404) {
        return false
      }
      return failureCount < 2
    },
    staleTime: 5 * 60 * 1000,
  })

  // Mutation para gerar escrituração
  const gerarEscrituracaoMutation = useMutation({
    mutationFn: () => escrituracaoService.gerarEscrituracao({
      empresaId: empresaId!,
      mes: selectedMonth,
      ano: selectedYear,
      forcarRecalculo: true,
      tolerancia: parseFloat(localStorage.getItem('toleranciaEscrituracao') || '0.01')
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['escrituracao'] })
    },
  })

  // Mutation para aprovar escrituração
  const aprovarMutation = useMutation({
    mutationFn: ({ ids, justificativa }: { ids: number[], justificativa?: string }) =>
      escrituracaoService.aprovarEscrituracao({ auditoriaIds: ids, justificativa }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['escrituracao'] })
      setSelectedIds([])
    },
  })

  const formatCurrency = (value: number) => {
    if (isNaN(value) || value === null || value === undefined) return 'R$ 0,00'
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value)
  }

  const toggleSelectAll = () => {
    const selectableItems = escrituracaoData?.auditorias.filter(item => item.status_escrituracao !== 'aprovado') || []
    const selectableIds = selectableItems.map(item => item.id)
    
    if (selectedIds.length === selectableIds.length && selectableIds.length > 0) {
      setSelectedIds([])
    } else {
      setSelectedIds(selectableIds)
    }
  }

  const toggleSelectRow = (id: number) => {
    const item = escrituracaoData?.auditorias.find(item => item.id === id)
    
    if (item?.status_escrituracao === 'aprovado') {
      return
    }
    
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
    )
  }

  const handleAprovar = (justificativa: string) => {
    aprovarMutation.mutate({ ids: selectedIds, justificativa })
  }

  const columns = [
    {
      key: 'select',
      title: (
        <input
          type="checkbox"
          checked={selectedIds.length === (escrituracaoData?.auditorias.filter(item => item.status_escrituracao !== 'aprovado').length || 0) && (escrituracaoData?.auditorias.filter(item => item.status_escrituracao !== 'aprovado').length || 0) > 0}
          onChange={toggleSelectAll}
          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
        />
      ),
      width: '40px',
      render: (_, record: EscrituracaoItem) => {
        const isApproved = record.status_escrituracao === 'aprovado'
        
        return (
          <input
            type="checkbox"
            checked={selectedIds.includes(record.id)}
            onChange={() => toggleSelectRow(record.id)}
            disabled={isApproved}
            className={`rounded border-gray-300 text-primary-600 focus:ring-primary-500 ${
              isApproved ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            title={isApproved ? 'Este registro já foi aprovado' : ''}
          />
        )
      },
    },
    {
      key: 'numero_nf',
      title: 'Número NF',
      width: '120px',
      render: (_, record: EscrituracaoItem) => (
        <span className="font-medium">{record.numero_nf}</span>
      ),
    },
    {
      key: 'participante',
      title: 'Participante',
      render: (_, record: EscrituracaoItem) => (
        <div>
          <div className="font-medium truncate max-w-xs">{record.participante_razao_social}</div>
          <div className="text-xs text-gray-500">{record.participante_cnpj}</div>
        </div>
      ),
    },
    {
      key: 'data_entrada',
      title: 'Data Entrada',
      width: '120px',
      render: (_, record: EscrituracaoItem) => {
        if (!record.data_entrada) return '-'
        try {
          return new Date(record.data_entrada).toLocaleDateString('pt-BR')
        } catch {
          return '-'
        }
      },
    },
    {
      key: 'valor_xml',
      title: 'Valor XML',
      align: 'left' as const,
      render: (_, record: EscrituracaoItem) => {
        const isDivergent = record.status_conformidade === 'divergente'
        return (
          <span className={isDivergent ? 'text-red-600 font-medium' : 'text-green-600 font-medium'}>
            {formatCurrency(record.valor_xml)}
          </span>
        )
      },
    },
    {
      key: 'valor_sped',
      title: 'Valor SPED',
      align: 'left' as const,
      render: (_, record: EscrituracaoItem) => {
        const isDivergent = record.status_conformidade === 'divergente'
        return (
          <span className={isDivergent ? 'text-red-600 font-medium' : 'text-green-600 font-medium'}>
            {formatCurrency(record.valor_sped)}
          </span>
        )
      },
    },
    {
      key: 'status',
      title: 'Status',
      width: '125px',
      render: (_, record: EscrituracaoItem) => {
        let variant: 'success' | 'warning' | 'error' = 'success'
        let text = 'Conforme'
        let icon = '✓'

        if (record.status_conformidade === 'divergente') {
          if (record.status_escrituracao === 'aprovado') {
            variant = 'warning'
            text = 'Aprovado'
            icon = '✓'
          } else {
            variant = 'error'
            text = 'Divergente'
            icon = '⚠'
          }
        }

        return (
          <div className="space-y-1">
            <Badge variant={variant} size="sm">
              {icon} {text}
            </Badge>
          </div>
        )
      },
    },
    {
      key: 'acoes',
      title: 'Ações',
      width: '115px',
      render: (_, record: EscrituracaoItem) => (
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDetalhesModal({ isOpen: true, item: record })}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            }
          />
          
          {record.status_conformidade === 'divergente' && record.status_escrituracao !== 'aprovado' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedIds([record.id])
                setAprovarModal(true)
              }}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              }
            />
          )}
        </div>
      ),
    },
  ]

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Carregando auditoria de escrituração...
          </p>
        </div>
      </div>
    )
  }

  if (error || !escrituracaoData) {
    return (
      <div className="space-y-6">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Nenhuma auditoria de escrituração encontrada
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Não há dados de auditoria de escrituração para este período.
            Execute a auditoria para visualizar os resultados.
          </p>
          <Button
            onClick={() => gerarEscrituracaoMutation.mutate()}
            loading={gerarEscrituracaoMutation.isPending}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            }
          >
            Gerar Auditoria de Escrituração
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Cards de Resumo */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">XML</h3>
          <p className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
            {escrituracaoData.resumo.notas_xml}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">notas</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white mt-2">
            {formatCurrency(escrituracaoData.resumo.total_xml)}
          </p>
        </Card>

        <Card className="p-6 text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">SPED</h3>
          <p className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
            {escrituracaoData.resumo.notas_sped}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">notas</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white mt-2">
            {formatCurrency(escrituracaoData.resumo.total_sped)}
          </p>
        </Card>

        <Card className="p-6 text-center border-red-200 dark:border-red-800">
          <h3 className="text-lg font-semibold text-red-600 dark:text-red-400 mb-2">Divergentes</h3>
          <p className="text-2xl font-bold text-red-600 dark:text-red-400 mb-1">
            {escrituracaoData.resumo.divergentes}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">notas</p>
          <p className="text-lg font-semibold text-red-600 dark:text-red-400 mt-2">
            {formatCurrency(escrituracaoData.resumo.valor_divergente)}
          </p>
        </Card>
      </div>

      {/* Ações */}
      <Card className="p-4">
        <div className="flex flex-wrap gap-2 items-center justify-between">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Auditoria de Escrituração
            </h3>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button
              variant="success"
              size="sm"
              onClick={() => setAprovarModal(true)}
              disabled={selectedIds.length === 0}
              loading={aprovarMutation.isPending}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              }
            >
              Aprovar Selecionados ({selectedIds.length})
            </Button>
            
            <Button
              variant="info"
              size="sm"
              onClick={() => gerarEscrituracaoMutation.mutate()}
              loading={gerarEscrituracaoMutation.isPending}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              }
            >
              Gerar Auditoria de Escrituração
            </Button>
          </div>
        </div>
      </Card>

      {/* Tabela */}
      <Card>
        <Table
          columns={columns}
          data={escrituracaoData.auditorias}
          loading={isLoading}
          emptyMessage="Nenhum registro de escrituração encontrado"
        />
      </Card>

      {/* Modais */}
      <DetalhesEscrituracaoModal
        isOpen={detalhesModal.isOpen}
        onClose={() => setDetalhesModal({ isOpen: false, item: null })}
        item={detalhesModal.item}
      />
      
      <AprovarModal
        isOpen={aprovarModal}
        onClose={() => setAprovarModal(false)}
        selectedIds={selectedIds}
        onConfirm={handleAprovar}
      />
    </div>
  )
}
-- Migration to create table for IPI apuracao geral (E520)
BEGIN;

CREATE TABLE IF NOT EXISTS ipi_apuracao_geral (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id),
    ano INTEGER NOT NULL,
    mes INTEGER NOT NULL,
    vl_sd_ant_ipi NUMERIC(15,2),
    vl_deb_ipi NUMERIC(15,2),
    vl_cred_ipi NUMERIC(15,2),
    vl_od_ipi NUMERIC(15,2),
    vl_oc_ipi NUMERIC(15,2),
    vl_sc_ipi NUMERIC(15,2),
    vl_sd_ipi NUMERIC(15,2),
    UNIQUE(empresa_id, ano, mes)
);

COMMIT;
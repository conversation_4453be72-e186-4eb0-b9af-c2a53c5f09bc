-- =====================================================
-- REMOÇÃO COMPLETA DO CHATBOT IA
-- Remove TUDO relacionado ao chatbot para limpar o sistema
-- =====================================================

-- Definir encoding
SET client_encoding = 'UTF8';

-- Desabilitar verificações de chave estrangeira temporariamente
SET session_replication_role = replica;

-- 1. REMOVER TODAS AS TABELAS DO CHATBOT
DROP TABLE IF EXISTS chatbot_conversas CASCADE;
DROP TABLE IF EXISTS chatbot_templates CASCADE;

-- 2. REMOVER VIEWS MATERIALIZADAS
DROP MATERIALIZED VIEW IF EXISTS vw_chatbot_dados_completos CASCADE;

-- 3. REMOVER FUNÇÕES RELACIONADAS AO CHATBOT
DROP FUNCTION IF EXISTS refresh_chatbot_view() CASCADE;
DROP FUNCTION IF EXISTS trigger_refresh_chatbot_view() CASCADE;
DROP FUNCTION IF EXISTS clean_text(TEXT) CASCADE;

-- 4. REMOVER TRIGGERS RELACIONADOS AO CHATBOT
DROP TRIGGER IF EXISTS trigger_chatbot_refresh_nota ON nota_fiscal_item;
DROP TRIGGER IF EXISTS trigger_chatbot_refresh_auditoria ON auditoria_resultado;

-- 5. REMOVER ÍNDICES ESPECÍFICOS DO CHATBOT (se existirem)
DROP INDEX IF EXISTS idx_chatbot_conversas_usuario;
DROP INDEX IF EXISTS idx_chatbot_conversas_empresa;
DROP INDEX IF EXISTS idx_chatbot_conversas_data;
DROP INDEX IF EXISTS idx_chatbot_templates_categoria;
DROP INDEX IF EXISTS idx_vw_chatbot_empresa;
DROP INDEX IF EXISTS idx_vw_chatbot_nota;
DROP INDEX IF EXISTS idx_vw_chatbot_produto;
DROP INDEX IF EXISTS idx_vw_chatbot_cliente;
DROP INDEX IF EXISTS idx_vw_chatbot_tributo;
DROP INDEX IF EXISTS idx_vw_chatbot_status;
DROP INDEX IF EXISTS idx_vw_chatbot_data;

-- 6. LIMPAR QUALQUER SEQUÊNCIA RELACIONADA
DROP SEQUENCE IF EXISTS chatbot_conversas_id_seq CASCADE;
DROP SEQUENCE IF EXISTS chatbot_templates_id_seq CASCADE;

-- 7. VERIFICAR E CORRIGIR PROBLEMAS DE ENCODING NAS TABELAS EXISTENTES
-- Limpar caracteres problemáticos que podem ter sido introduzidos

-- Função temporária para limpar texto
CREATE OR REPLACE FUNCTION temp_clean_text(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
    IF input_text IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Converter para UTF-8 válido e remover caracteres problemáticos
    RETURN convert_from(convert_to(input_text, 'UTF8'), 'UTF8');
EXCEPTION
    WHEN OTHERS THEN
        -- Se falhar, retornar versão limpa
        RETURN regexp_replace(input_text, '[^\x20-\x7E\x80-\xFF]', '', 'g');
END;
$$ LANGUAGE plpgsql;

-- Corrigir encoding em tabelas críticas
UPDATE cenario_icms SET 
    status = COALESCE(temp_clean_text(status), 'novo'),
    cst = temp_clean_text(cst),
    cfop = temp_clean_text(cfop),
    ncm = temp_clean_text(ncm)
WHERE status IS NULL OR status = '' OR status !~ '^[a-zA-Z0-9_]+$';

UPDATE cenario_icms_st SET 
    status = COALESCE(temp_clean_text(status), 'novo'),
    cst = temp_clean_text(cst),
    cfop = temp_clean_text(cfop),
    ncm = temp_clean_text(ncm)
WHERE status IS NULL OR status = '' OR status !~ '^[a-zA-Z0-9_]+$';

UPDATE cenario_ipi SET 
    status = COALESCE(temp_clean_text(status), 'novo'),
    cst = temp_clean_text(cst),
    cfop = temp_clean_text(cfop),
    ncm = temp_clean_text(ncm)
WHERE status IS NULL OR status = '' OR status !~ '^[a-zA-Z0-9_]+$';

UPDATE cenario_pis SET 
    status = COALESCE(temp_clean_text(status), 'novo'),
    cst = temp_clean_text(cst),
    cfop = temp_clean_text(cfop),
    ncm = temp_clean_text(ncm)
WHERE status IS NULL OR status = '' OR status !~ '^[a-zA-Z0-9_]+$';

UPDATE cenario_cofins SET 
    status = COALESCE(temp_clean_text(status), 'novo'),
    cst = temp_clean_text(cst),
    cfop = temp_clean_text(cfop),
    ncm = temp_clean_text(ncm)
WHERE status IS NULL OR status = '' OR status !~ '^[a-zA-Z0-9_]+$';

UPDATE cenario_difal SET 
    status = COALESCE(temp_clean_text(status), 'novo'),
    cst = temp_clean_text(cst),
    cfop = temp_clean_text(cfop),
    ncm = temp_clean_text(ncm)
WHERE status IS NULL OR status = '' OR status !~ '^[a-zA-Z0-9_]+$';

-- Garantir que todos os status sejam válidos
UPDATE cenario_icms SET status = 'novo' 
WHERE status NOT IN ('novo', 'producao', 'inconsistente') 
   OR status IS NULL 
   OR status = '';

UPDATE cenario_icms_st SET status = 'novo' 
WHERE status NOT IN ('novo', 'producao', 'inconsistente') 
   OR status IS NULL 
   OR status = '';

UPDATE cenario_ipi SET status = 'novo' 
WHERE status NOT IN ('novo', 'producao', 'inconsistente') 
   OR status IS NULL 
   OR status = '';

UPDATE cenario_pis SET status = 'novo' 
WHERE status NOT IN ('novo', 'producao', 'inconsistente') 
   OR status IS NULL 
   OR status = '';

UPDATE cenario_cofins SET status = 'novo' 
WHERE status NOT IN ('novo', 'producao', 'inconsistente') 
   OR status IS NULL 
   OR status = '';

UPDATE cenario_difal SET status = 'novo' 
WHERE status NOT IN ('novo', 'producao', 'inconsistente') 
   OR status IS NULL 
   OR status = '';

-- Remover função temporária
DROP FUNCTION IF EXISTS temp_clean_text(TEXT);

-- 8. REABILITAR VERIFICAÇÕES DE CHAVE ESTRANGEIRA
SET session_replication_role = DEFAULT;

-- 9. VACUUM PARA LIMPAR ESPAÇO
VACUUM ANALYZE cenario_icms;
VACUUM ANALYZE cenario_icms_st;
VACUUM ANALYZE cenario_ipi;
VACUUM ANALYZE cenario_pis;
VACUUM ANALYZE cenario_cofins;
VACUUM ANALYZE cenario_difal;

-- 10. COMMIT FINAL
COMMIT;

-- Mensagem de confirmação
DO $$
BEGIN
    RAISE NOTICE 'CHATBOT REMOVIDO COMPLETAMENTE!';
    RAISE NOTICE 'Todas as tabelas, funções, triggers e índices do chatbot foram removidos.';
    RAISE NOTICE 'Problemas de encoding foram corrigidos.';
    RAISE NOTICE 'Sistema limpo e pronto para uso normal.';
END $$;

import api from './authService'

export interface PisCofinsDivergence {
  campo: string
  valor_atual: string | number
  valor_sugerido: string | number
  motivo: string
}

export interface PisCofinsSuggestion {
  cenario_id: number
  tributo: 'PIS' | 'COFINS'
  ncm: string
  cfop: string
  codigo_produto?: string
  estado?: string
  destinacao_empresa?: string
  regime_empresa?: string
  destinacao_cliente?: string
  divergencias: PisCofinsDivergence[]
  dados_originais?: {
    aliquota?: number
    cst?: string
  }
}

export interface PisCofinsValidationResult {
  total_sugestoes: number
  sugestoes: PisCofinsSuggestion[]
  message?: string
}

export const pisCofinsValidationService = {
  async validate(
    empresaId: number,
    tributo: 'PIS' | 'COFINS',
    status?: 'novo' | 'producao' | 'inconsistente'
  ): Promise<PisCofinsValidationResult> {
    const response = await api.post('/cenarios/pis-cofins/validate', {
      empresa_id: empresaId,
      cfop: null,
      tributo_tipo: tributo,
      status_filtro: status,
    })
    return response.data
  },

  async applySuggestion(
    cenarioId: number,
    tributo: 'PIS' | 'COFINS',
    divergencias: PisCofinsDivergence[]
  ) {
    const sugestoes = divergencias.filter(
      d => d.valor_sugerido !== '' && d.valor_sugerido !== null && d.valor_sugerido !== undefined
    )
    const response = await api.post('/cenarios/pis-cofins/apply-suggestion', {
      cenario_id: cenarioId,
      tributo_tipo: tributo,
      sugestoes
    })
    return response.data
  }
}

export default pisCofinsValidationService
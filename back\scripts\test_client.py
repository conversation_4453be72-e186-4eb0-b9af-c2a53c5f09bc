#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de teste para o serviço cliente XML
"""

import os
import sys
import json
import time
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock

# Adicionar o diretório atual ao path para importar o módulo
sys.path.insert(0, str(Path(__file__).parent))

def create_test_xml(directory, filename="test_nfe.xml"):
    """Cria um arquivo XML de teste"""
    xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<nfeProc xmlns="http://www.portalfiscal.inf.br/nfe">
    <NFe>
        <infNFe Id="NFe35200714200166000187550010000000046550000046">
            <ide>
                <cUF>35</cUF>
                <cNF>55000004</cNF>
                <natOp>Venda</natOp>
                <mod>55</mod>
                <serie>1</serie>
                <nNF>46</nNF>
                <dhEmi>2020-07-01T10:00:00-03:00</dhEmi>
                <tpNF>1</tpNF>
                <idDest>1</idDest>
                <cMunFG>3550308</cMunFG>
                <tpImp>1</tpImp>
                <tpEmis>1</tpEmis>
                <cDV>5</cDV>
                <tpAmb>2</tpAmb>
                <finNFe>1</finNFe>
                <indFinal>1</indFinal>
                <indPres>1</indPres>
            </ide>
            <emit>
                <CNPJ>14200166000187</CNPJ>
                <xNome>EMPRESA TESTE LTDA</xNome>
                <enderEmit>
                    <xLgr>RUA TESTE</xLgr>
                    <nro>123</nro>
                    <xBairro>CENTRO</xBairro>
                    <cMun>3550308</cMun>
                    <xMun>SAO PAULO</xMun>
                    <UF>SP</UF>
                    <CEP>01000000</CEP>
                </enderEmit>
                <IE>123456789</IE>
                <CRT>3</CRT>
            </emit>
        </infNFe>
    </NFe>
</nfeProc>"""
    
    file_path = Path(directory) / filename
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(xml_content)
    
    return file_path

def test_config_manager():
    """Testa o gerenciador de configurações"""
    print("🧪 Testando ConfigManager...")
    
    try:
        from xml_service_client import ConfigManager
        
        # Configuração de teste
        test_config = {
            "api_key": "test_key_123",
            "monitor_directory": "C:\\test_dir",
            "api_endpoint": "https://test.api.com"
        }
        
        # Salvar configuração
        if not ConfigManager.save_config(test_config):
            print("❌ Falha ao salvar configuração")
            return False
        
        # Carregar configuração
        loaded_config = ConfigManager.load_config()
        
        if loaded_config["api_key"] != "test_key_123":
            print("❌ Configuração não foi carregada corretamente")
            return False
        
        print("✅ ConfigManager - OK")
        
        # Limpar arquivo de teste
        if os.path.exists(ConfigManager.CONFIG_FILE):
            os.remove(ConfigManager.CONFIG_FILE)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste ConfigManager: {e}")
        return False

def test_file_upload_service():
    """Testa o serviço de upload de arquivos"""
    print("🧪 Testando FileUploadService...")
    
    try:
        from xml_service_client import FileUploadService
        
        # Configuração de teste
        config = {
            "api_key": "test_key",
            "api_endpoint": "https://httpbin.org/post",  # Endpoint de teste
            "escritorio_id": 1
        }
        
        service = FileUploadService(config)
        
        # Criar arquivo de teste
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = create_test_xml(temp_dir, "test.xml")
            
            # Testar criação de ZIP
            zip_path = Path(temp_dir) / "test.zip"
            if not service.create_zip_from_files([test_file], zip_path):
                print("❌ Falha ao criar ZIP")
                return False
            
            if not zip_path.exists():
                print("❌ Arquivo ZIP não foi criado")
                return False
            
            print("✅ FileUploadService - Criação de ZIP OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste FileUploadService: {e}")
        return False

def test_xml_service():
    """Testa o serviço principal XML"""
    print("🧪 Testando XMLService...")
    
    try:
        from xml_service_client import XMLService
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Configuração de teste
            config = {
                "api_key": "test_key",
                "monitor_directory": temp_dir,
                "api_endpoint": "https://httpbin.org/post",
                "escritorio_id": 1,
                "retry_attempts": 2,
                "retry_interval": 1,
                "failure_cooldown_hours": 1,
                "max_failure_days": 1
            }
            
            service = XMLService(config)
            
            # Testar setup de diretórios
            if not service.setup_directories():
                print("❌ Falha ao criar diretórios")
                return False
            
            # Verificar se diretórios foram criados
            if not os.path.exists(service.pending_dir):
                print("❌ Diretório de pendentes não foi criado")
                return False
            
            if not os.path.exists(service.sent_dir):
                print("❌ Diretório de enviados não foi criado")
                return False
            
            # Testar movimentação de arquivos
            test_file = create_test_xml(temp_dir, "test_move.xml")
            service.move_to_pending(str(test_file))
            
            pending_file = Path(service.pending_dir) / "test_move.xml"
            if not pending_file.exists():
                print("❌ Arquivo não foi movido para pendentes")
                return False
            
            print("✅ XMLService - Setup e movimentação OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste XMLService: {e}")
        return False

def test_gui_components():
    """Testa componentes da GUI (sem abrir janela)"""
    print("🧪 Testando componentes GUI...")
    
    try:
        from xml_service_client import XMLServiceGUI
        
        # Testar apenas a inicialização da classe (sem GUI)
        with patch('tkinter.Tk'):
            gui = XMLServiceGUI.__new__(XMLServiceGUI)
            gui.config = {
                "api_key": "test",
                "monitor_directory": "C:\\test",
                "api_endpoint": "https://test.com"
            }
            
            print("✅ GUI Components - Inicialização OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste GUI: {e}")
        return False

def test_imports():
    """Testa todas as importações necessárias"""
    print("🧪 Testando importações...")
    
    required_modules = [
        "os", "sys", "time", "json", "shutil", "zipfile",
        "requests", "threading", "tkinter", "datetime",
        "pathlib", "watchdog", "logging"
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - {e}")
            return False
    
    return True

def run_integration_test():
    """Executa teste de integração completo"""
    print("🧪 Teste de integração...")
    
    try:
        from xml_service_client import XMLService, ConfigManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Configuração de teste
            config = {
                "api_key": "test_integration",
                "monitor_directory": temp_dir,
                "api_endpoint": "https://httpbin.org/status/200",  # Sempre retorna 200
                "escritorio_id": 1,
                "retry_attempts": 1,
                "retry_interval": 1,
                "failure_cooldown_hours": 1,
                "max_failure_days": 1
            }
            
            # Salvar configuração
            ConfigManager.save_config(config)
            
            # Criar serviço
            service = XMLService(config)
            service.setup_directories()
            
            # Criar arquivo XML de teste
            test_file = create_test_xml(temp_dir, "integration_test.xml")
            
            # Simular detecção de arquivo
            service.move_to_pending(str(test_file))
            
            # Verificar se arquivo está em pendentes
            pending_files = list(Path(service.pending_dir).glob("*.xml"))
            if not pending_files:
                print("❌ Arquivo não foi movido para pendentes")
                return False
            
            print("✅ Teste de integração - Fluxo básico OK")
            
            # Limpar configuração de teste
            if os.path.exists(ConfigManager.CONFIG_FILE):
                os.remove(ConfigManager.CONFIG_FILE)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de integração: {e}")
        return False

def main():
    """Executa todos os testes"""
    print("=" * 50)
    print("🧪 TESTES DO SERVIÇO CLIENTE XML")
    print("=" * 50)
    
    tests = [
        ("Importações", test_imports),
        ("ConfigManager", test_config_manager),
        ("FileUploadService", test_file_upload_service),
        ("XMLService", test_xml_service),
        ("GUI Components", test_gui_components),
        ("Integração", run_integration_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSOU")
            else:
                print(f"❌ {test_name} - FALHOU")
        except Exception as e:
            print(f"❌ {test_name} - ERRO: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 RESULTADO: {passed}/{total} testes passaram")
    print("=" * 50)
    
    if passed == total:
        print("🎉 Todos os testes passaram! O serviço está pronto para uso.")
        return True
    else:
        print("⚠️  Alguns testes falharam. Verifique os erros acima.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Testes cancelados pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro inesperado nos testes: {e}")
        sys.exit(1)
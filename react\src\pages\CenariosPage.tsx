import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useLocation, Navigate } from 'react-router-dom'
import {
  AlertCircle,
  CheckCircle,
  Factory,
  Landmark,
  ListChecks,
  Network,
  Recycle,
  Scale,
  TrendingUp,
  Users
} from 'lucide-react'
import { cenariosService } from '@/services/cenariosService'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { CenarioCard } from '@/components/cenarios/CenarioCard'
import { Card, StatsCard } from '@/components/ui/Card'
import { HelpButton, HelpModal } from '@/components/ui'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import type { TipoTributo } from '@/types/cenarios'

interface TributoCard {
  tipo: TipoTributo
  nome: string
  descricao: string
  icon: React.ReactNode
  color: string
  bgColor: string
}

const TRIBUTOS: TributoCard[] = [
  {
    tipo: 'icms',
    nome: 'ICMS',
    descricao: 'Imposto sobre Circulação de Mercadorias e Serviços',
    icon: <Landmark className="w-6 h-6" />,
    color: 'text-black',
    bgColor: 'bg-blue-50 hover:bg-blue-100'
  },
  {
    tipo: 'icms_st',
    nome: 'ICMS-ST',
    descricao: 'ICMS Substituição Tributária',
    icon: <Recycle className="w-6 h-6" />,
    color: 'text-black',
    bgColor: 'bg-purple-50 hover:bg-purple-100'
  },
  {
    tipo: 'ipi',
    nome: 'IPI',
    descricao: 'Imposto sobre Produtos Industrializados',
    icon: <Factory className="w-6 h-6" />,
    color: 'text-black',
    bgColor: 'bg-green-50 hover:bg-green-100'
  },
  {
    tipo: 'pis',
    nome: 'PIS',
    descricao: 'Programa de Integração Social',
    icon: <Users className="w-6 h-6" />,
    color: 'text-black',
    bgColor: 'bg-orange-50 hover:bg-orange-100'
  },
  {
    tipo: 'cofins',
    nome: 'COFINS',
    descricao: 'Contribuição para o Financiamento da Seguridade Social',
    icon: <Landmark className="w-6 h-6" />,
    color: 'text-black',
    bgColor: 'bg-red-50 hover:bg-red-100'
  },
  {
    tipo: 'difal',
    nome: 'DIFAL',
    descricao: 'Diferencial de Alíquota do ICMS',
    icon: <Scale className="w-6 h-6" />,
    color: 'text-black',
    bgColor: 'bg-indigo-50 hover:bg-indigo-100'
  }
]

export function CenariosPage() {
  const location = useLocation()
  const empresaId = useSelectedCompany()
  const [helpOpen, setHelpOpen] = useState(false)

  const { data: estatisticas, isLoading } = useQuery({
    queryKey: ['cenarios-estatisticas', empresaId],
    queryFn: async () => {
      // SÓ buscar se há empresa selecionada (como no sistema atual)
      if (!empresaId) {
        return {
          icms: { total: 0, ativos: 0, inconsistentes: 0 },
          icms_st: { total: 0, ativos: 0, inconsistentes: 0 },
          ipi: { total: 0, ativos: 0, inconsistentes: 0 },
          pis: { total: 0, ativos: 0, inconsistentes: 0 },
          cofins: { total: 0, ativos: 0, inconsistentes: 0 },
          difal: { total: 0, ativos: 0, inconsistentes: 0 }
        }
      }
            
      try {
        const result = await cenariosService.getEstatisticas(empresaId)
        return result
      } catch (error) {
        throw error
      }
    },
    enabled: !!empresaId, // SÓ executar se há empresa
    refetchOnWindowFocus: false,
  })

  // Redirecionar /cenarios para /cenarios/saida (como no sistema atual)
  // DEPOIS dos hooks para não quebrar a ordem
  if (location.pathname === '/fiscal/cenarios') {
    return <Navigate to="/fiscal/cenarios/saida" replace />
  }



  return (
    <>
    <div className="space-y-6">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-rounded-2xl">
        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-2">
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-2xl text-gray-900 font-semibold">
                  Cenários Tributários
                </h1>
                <HelpButton
                  aria-label="Ajuda"
                  onClick={() => setHelpOpen(true)}
                />
              </div>
              <p className="text-gray-500 mt-1">
                Configure e gerencie os cenários de cálculo de impostos
              </p>
            </div>
            </div>
          </div>

        {/* Background decoration */}
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
        </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center py-12">
          <LoadingSpinner className="w-8 h-8" />
        </div>
      )}

      {/* Summary Stats */}
      {!isLoading && estatisticas && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Resumo Geral
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="Total de Cenários"
              value={Object.values(estatisticas).reduce((acc, curr) => acc + curr.total, 0).toLocaleString()}
              icon={<ListChecks className="w-6 h-6" />}
              color="primary"
            />

            <StatsCard
              title="Em Produção"
              value={Object.values(estatisticas).reduce((acc, curr) => acc + curr.ativos, 0).toLocaleString()}
              change={{
                value: 12,
                type: 'increase',
                period: 'vs mês anterior'
              }}
              icon={<CheckCircle className="w-6 h-6" />}
              color="success"
            />

            <StatsCard
              title="Inconsistentes"
              value={Object.values(estatisticas).reduce((acc, curr) => acc + curr.inconsistentes, 0).toLocaleString()}
              change={{
                value: 5,
                type: 'decrease',
                period: 'vs semana anterior'
              }}
              icon={<AlertCircle className="w-6 h-6" />}
              color="error"
            />

            <StatsCard
              title="Taxa de Sucesso"
              value={`${Math.round(
                (Object.values(estatisticas).reduce((acc, curr) => acc + curr.ativos, 0) /
                  Object.values(estatisticas).reduce((acc, curr) => acc + curr.total, 0)) * 100
              ) || 0}%`}
              change={{
                value: 3,
                type: 'increase',
                period: 'vs mês anterior'
              }}
              icon={<TrendingUp className="w-6 h-6" />}
              color="primary"
            />
          </div>
        </div>
      )}
      {/* Cards Grid */}
      {!isLoading && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Cenários por Tributo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {TRIBUTOS.map((tributo) => (
              <CenarioCard
                key={tributo.tipo}
                tipo={tributo.tipo}
                nome={tributo.nome}
                descricao={tributo.descricao}
                icon={tributo.icon}
                color={tributo.color}
                bgColor={tributo.bgColor}
                stats={estatisticas?.[tributo.tipo]}
              />
            ))}
          </div>
        </div>
      )}
    </div>

      <HelpModal
        isOpen={helpOpen}
        onClose={() => setHelpOpen(false)}
        title="Ajuda - Cenários Tributários"
      >
        <div className="space-y-4">
          <section>
            <h4 className="font-semibold mb-2">Tributos disponíveis</h4>
            <ul className="list-disc pl-5 space-y-1">
              {TRIBUTOS.map((t) => (
                <li key={t.tipo}>
                  <strong>{t.nome}:</strong> {t.descricao}
                </li>
              ))}
            </ul>
          </section>
          <section>
            <h4 className="font-semibold mb-2">Validação</h4>
            <p>
              Utilize a validação para revisar as configurações e identificar
              possíveis inconsistências antes de colocar cenários em
              produção.
            </p>
          </section>
          <section>
            <h4 className="font-semibold mb-2">Cenário específico</h4>
            <p>
              A navegação para "Cenário específico" permite acessar e ajustar
              regras individuais de um tributo conforme necessário.
            </p>
          </section>
        </div>
      </HelpModal>
    </>
  )
}

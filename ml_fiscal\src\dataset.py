# src/dataset.py
import torch
from torch.utils.data import Dataset, DataLoader

class CenarioDataset(Dataset):
    def __init__(self, df, cat_cols, num_cols, label_col):
        self.X_cat = torch.tensor(df[cat_cols].values, dtype=torch.long)
        self.X_num = torch.tensor(df[num_cols].values, dtype=torch.float)
        self.y     = torch.tensor(df[label_col].values, dtype=torch.float)

    def __len__(self):
        return len(self.y)

    def __getitem__(self, idx):
        return self.X_cat[idx], self.X_num[idx], self.y[idx]

from sklearn.model_selection import train_test_split

def get_loaders(df, cat_cols, num_cols, batch_size=64):
    # Split estratificado para manter proporção de 0s e 1s em treino/val
    train_df, val_df = train_test_split(
        df,
        test_size=0.2,
        shuffle=True,
        stratify=df['label'],
        random_state=42
    )
    train_ds = CenarioDataset(train_df, cat_cols, num_cols, 'label')
    val_ds   = CenarioDataset(val_df,   cat_cols, num_cols, 'label')

    return (
        DataLoader(train_ds, batch_size=batch_size, shuffle=True),
        DataLoader(val_ds,   batch_size=batch_size)
    )

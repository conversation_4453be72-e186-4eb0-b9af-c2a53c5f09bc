"""
Optimized data loader service for virtual scrolling
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy import and_, or_, extract
from sqlalchemy.orm import joinedload, selectinload
from models import (
    db, CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, 
    CenarioCOFINS, CenarioDIFAL, Cliente, Produto, Tributo
)
from decimal import Decimal, InvalidOperation
from .memory_monitor_service import MemoryMonitorService

logger = logging.getLogger(__name__)

class OptimizedDataLoaderService:
    """Service for optimized data loading with proper joins and memory management"""
    
    # Mapping of tax types to models
    TIPO_TRIBUTO_MODELS = {
        'icms': CenarioICMS,
        'icms_st': CenarioICMSST,
        'ipi': CenarioIPI,
        'pis': CenarioPIS,
        'cofins': CenarioCOFINS,
        'difal': CenarioDIFAL
    }
    
    def __init__(self):
        self.memory_monitor = MemoryMonitorService()
    
    def _parse_list(self, value: str) -> List[str]:
        """Parse comma-separated string into list"""
        if not value:
            return []
        return [v.strip() for v in value.split(',') if v.strip()]
    
    def _build_base_query(self, tipo_tributo: str, filters: Dict[str, Any]):
        """Build base query with optimized joins"""
        if tipo_tributo not in self.TIPO_TRIBUTO_MODELS:
            raise ValueError(f'Invalid tax type: {tipo_tributo}')
        
        CenarioModel = self.TIPO_TRIBUTO_MODELS[tipo_tributo]
        
        # Check if we need explicit joins for text filters
        has_text_filters = any(filters.get(f) for f in [
            'produto_codigo', 'produto_descricao', 'cliente_razao', 'cliente_cnpj', 'cest'
        ])
        
        # Start with base query
        query = CenarioModel.query
        
        # Use different loading strategies based on whether we have text filters
        if has_text_filters:
            # When we have text filters, we'll use explicit joins for filtering
            # We still need to load the relationships, but we'll do it after the joins
            logger.info("🔍 Detectados filtros de texto - usando estratégia de JOIN explícito")
            # We'll add the relationship loading after the joins are applied
        else:
            # When no text filters, use joinedload for all relationships (more efficient)
            query = query.options(
                joinedload(CenarioModel.cliente),
                joinedload(CenarioModel.produto),
                joinedload(CenarioModel.empresa),
                joinedload(CenarioModel.escritorio)
            )
        
        # Apply basic filters
        if filters.get('empresa_id'):
            query = query.filter(CenarioModel.empresa_id == filters['empresa_id'])
        
        if filters.get('cliente_id'):
            query = query.filter(CenarioModel.cliente_id == filters['cliente_id'])
        
        if filters.get('produto_id'):
            query = query.filter(CenarioModel.produto_id == filters['produto_id'])
        
        if filters.get('direcao'):
            query = query.filter(CenarioModel.direcao == filters['direcao'])
            # Add tipo_operacao filter
            tipo_operacao = '0' if filters['direcao'] == 'entrada' else '1'
            query = query.filter(CenarioModel.tipo_operacao == tipo_operacao)
        
        # Handle status filter with special case for 'inconsistente'
        if filters.get('status'):
            status = filters['status']
            if status == 'inconsistente':
                query = query.filter(
                    or_(
                        CenarioModel.status == 'inconsistente',
                        CenarioModel.status.like('incons_%')
                    )
                )
            else:
                query = query.filter(CenarioModel.status == status)
        
        if filters.get('ativo') is not None:
            query = query.filter(CenarioModel.ativo == filters['ativo'])
        
        # Apply list-based filters
        if filters.get('cfop'):
            cfop_list = self._parse_list(filters['cfop'])
            if cfop_list:
                query = query.filter(CenarioModel.cfop.in_(cfop_list))
        
        if filters.get('ncm'):
            ncm_list = self._parse_list(filters['ncm'])
            if ncm_list:
                query = query.filter(CenarioModel.ncm.in_(ncm_list))
        
        if filters.get('cst'):
            cst_list = self._parse_list(filters['cst'])
            if cst_list:
                query = query.filter(CenarioModel.cst.in_(cst_list))
        
        # Apply decimal-based filters
        if filters.get('aliquota'):
            try:
                values = [Decimal(v) for v in self._parse_list(filters['aliquota'])]
                query = query.filter(CenarioModel.aliquota.in_(values))
            except InvalidOperation:
                logger.warning(f"Invalid aliquota values: {filters['aliquota']}")
        
        # Handle tax-specific filters
        if filters.get('aliquota_st') and tipo_tributo == 'icms_st':
            try:
                values = [Decimal(v) for v in self._parse_list(filters['aliquota_st'])]
                query = query.filter(CenarioModel.icms_st_aliquota.in_(values))
            except InvalidOperation:
                logger.warning(f"Invalid aliquota_st values: {filters['aliquota_st']}")
        
        # Handle reduction filters
        if filters.get('reducao'):
            try:
                values = [Decimal(v) for v in self._parse_list(filters['reducao'])]
                if tipo_tributo == 'icms_st':
                    query = query.filter(CenarioModel.icms_st_p_red_bc.in_(values))
                elif hasattr(CenarioModel, 'p_red_bc'):
                    query = query.filter(CenarioModel.p_red_bc.in_(values))
            except InvalidOperation:
                logger.warning(f"Invalid reducao values: {filters['reducao']}")
        
        if filters.get('reducao_st') and tipo_tributo == 'icms_st':
            try:
                values = [Decimal(v) for v in self._parse_list(filters['reducao_st'])]
                query = query.filter(CenarioModel.icms_st_p_red_bc.in_(values))
            except InvalidOperation:
                logger.warning(f"Invalid reducao_st values: {filters['reducao_st']}")
        
        # Handle MVA filter
        if filters.get('mva') and tipo_tributo == 'icms_st':
            try:
                values = [Decimal(v) for v in self._parse_list(filters['mva'])]
                query = query.filter(CenarioModel.icms_st_p_mva.in_(values))
            except InvalidOperation:
                logger.warning(f"Invalid mva values: {filters['mva']}")
        
        # Apply client-based filters (requires join)
        client_filters = ['uf', 'atividade', 'destinacao']
        needs_client_join = any(filters.get(f) for f in client_filters)
        
        if needs_client_join:
            # Join once with explicit condition to avoid cartesian products
            query = query.join(Cliente, CenarioModel.cliente_id == Cliente.id)

            if filters.get('uf'):
                uf_list = self._parse_list(filters['uf'])
                if uf_list:
                    query = query.filter(Cliente.uf.in_(uf_list))

            if filters.get('atividade'):
                atividade_list = self._parse_list(filters['atividade'])
                if atividade_list:
                    query = query.filter(Cliente.atividade.in_(atividade_list))

            if filters.get('destinacao'):
                destinacao_list = self._parse_list(filters['destinacao'])
                if destinacao_list:
                    query = query.filter(Cliente.destinacao.in_(destinacao_list))
        
        # Apply text-based filters
        # Note: We need to use explicit joins for filtering, even though we have joinedload for eager loading
        # The joinedload is for loading data, explicit joins are for filtering
        
        # Product-based text filters
        produto_filters = []
        if filters.get('produto_codigo'):
            produto_filters.append(Produto.codigo.ilike(f"%{filters['produto_codigo']}%"))
        
        if filters.get('produto_descricao'):
            produto_filters.append(Produto.descricao.ilike(f"%{filters['produto_descricao']}%"))
            logger.info(f"✅ Filtro produto_descricao preparado: ILIKE %{filters['produto_descricao']}%")
        # Apply product filters if any exist
        if produto_filters:
            # Use explicit join for filtering (different from joinedload for eager loading)
            # Use the relationship defined in the model
            query = query.join(CenarioModel.produto)
            # Apply all product filters with AND logic
            for produto_filter in produto_filters:
                query = query.filter(produto_filter)
            logger.info(f"🔗 JOIN explícito com Produto aplicado para {len(produto_filters)} filtros")
        
        # Client-based text filters
        cliente_filters = []
        if filters.get('cliente_razao'):
            cliente_filters.append(Cliente.razao_social.ilike(f"%{filters['cliente_razao']}%"))
        
        if filters.get('cliente_cnpj'):
            # Remove formatting from CNPJ for search
            cnpj_search = filters['cliente_cnpj'].replace('.', '').replace('/', '').replace('-', '')
            cliente_filters.append(Cliente.cnpj.ilike(f"%{cnpj_search}%"))
        
        # Apply client filters if any exist and no previous client join
        if cliente_filters and not needs_client_join:
            # Use explicit join for filtering
            query = query.join(Cliente, CenarioModel.cliente_id == Cliente.id)
            # Apply all client filters with AND logic
            for cliente_filter in cliente_filters:
                query = query.filter(cliente_filter)
        elif cliente_filters and needs_client_join:
            # Client join already exists from dropdown filters, just add text filters
            for cliente_filter in cliente_filters:
                query = query.filter(cliente_filter)
        
        # Scenario-specific text filters
        if filters.get('origem'):
            if hasattr(CenarioModel, 'origem'):
                query = query.filter(CenarioModel.origem.ilike(f"%{filters['origem']}%"))
        
        if filters.get('cest'):
            if hasattr(CenarioModel, 'cest'):
                query = query.filter(CenarioModel.cest.ilike(f"%{filters['cest']}%"))
                logger.info(f"✅ Filtro cest aplicado no modelo: ILIKE %{filters['cest']}%")
            elif produto_filters:
                # If CEST is in Produto table and we already have product filters
                query = query.filter(Produto.cest.ilike(f"%{filters['cest']}%"))
                logger.info(f"✅ Filtro cest aplicado na tabela Produto: ILIKE %{filters['cest']}%")
        
        if filters.get('ex') and tipo_tributo == 'ipi':
            if hasattr(CenarioModel, 'ex'):
                query = query.filter(CenarioModel.ex.ilike(f"%{filters['ex']}%"))
        
        # Apply date filters if needed (requires Tributo join)
        if filters.get('year') or filters.get('month'):
            query = query.join(Tributo, and_(
                Tributo.empresa_id == CenarioModel.empresa_id,
                Tributo.cliente_id == CenarioModel.cliente_id,
                Tributo.produto_id == CenarioModel.produto_id
            ))
            
            if filters.get('year'):
                query = query.filter(extract('year', Tributo.data_emissao) == filters['year'])
            
            if filters.get('month'):
                query = query.filter(extract('month', Tributo.data_emissao) == filters['month'])
        
        return query
    
    def load_cenarios_optimized(self, tipo_tributo: str, filters: Dict[str, Any], 
                               load_all: bool = False) -> Dict[str, Any]:
        """
        Load cenarios with optimized queries and memory management
        
        Args:
            tipo_tributo: Type of tax scenario
            filters: Filter parameters
            load_all: Whether to load all records or use pagination
            
        Returns:
            Dict with cenarios data and metadata
        """
        try:
            # Log memory usage before operation
            self.memory_monitor.log_memory_usage("load_cenarios_start")
                        
            # Build optimized query
            query = self._build_base_query(tipo_tributo, filters)
                        
            # Get total count efficiently
            total_count = query.count()
            
            # Check memory constraints for full load
            if load_all:
                memory_check = self.memory_monitor.check_memory_constraints(total_count)
                
                if not memory_check['can_load_all']:
                    logger.warning(f"Memory constraint violation: {memory_check['message']}")
                    return {
                        'success': False,
                        'error': 'memory_constraint',
                        'message': memory_check['message'],
                        'suggested_action': memory_check['suggested_action'],
                        'total_count': total_count,
                        'memory_stats': memory_check['memory_stats']
                    }
            
            # Execute query based on load_all parameter
            if load_all:
                # Load all records with optimized query
                cenarios_items = query.order_by(
                    self.TIPO_TRIBUTO_MODELS[tipo_tributo].id.desc()
                ).all()
                
                # Log memory usage after loading
                self.memory_monitor.log_memory_usage("load_cenarios_complete", len(cenarios_items))
                
                # Convert to dictionaries
                cenarios_dict = self._convert_to_dict_optimized(cenarios_items)
                
                return {
                    'success': True,
                    'cenarios': cenarios_dict,
                    'total_count': total_count,
                    'loaded_count': len(cenarios_items),
                    'pagination': {
                        'total': total_count,
                        'per_page': len(cenarios_items),
                        'current_page': 1,
                        'last_page': 1,
                        'from': 1 if cenarios_items else 0,
                        'to': len(cenarios_items) if cenarios_items else 0,
                        'has_prev': False,
                        'has_next': False,
                        'prev_page': None,
                        'next_page': None
                    },
                    'memory_stats': self.memory_monitor.get_memory_stats()
                }
            else:
                # Use pagination
                page = filters.get('page', 1)
                per_page = min(filters.get('per_page', 100), 700)  # Limit per_page
                
                cenarios = query.order_by(
                    self.TIPO_TRIBUTO_MODELS[tipo_tributo].id.desc()
                ).paginate(page=page, per_page=per_page, error_out=False)
                
                # Convert to dictionaries
                cenarios_dict = self._convert_to_dict_optimized(cenarios.items)
                
                return {
                    'success': True,
                    'cenarios': cenarios_dict,
                    'total_count': total_count,
                    'loaded_count': len(cenarios_dict),
                    'pagination': {
                        'total': total_count,
                        'per_page': per_page,
                        'current_page': page,
                        'last_page': cenarios.pages,
                        'from': (page - 1) * per_page + 1 if cenarios.items else 0,
                        'to': (page - 1) * per_page + len(cenarios.items) if cenarios.items else 0,
                        'has_prev': cenarios.has_prev,
                        'has_next': cenarios.has_next,
                        'prev_page': cenarios.prev_num if cenarios.has_prev else None,
                        'next_page': cenarios.next_num if cenarios.has_next else None
                    }
                }
                
        except Exception as e:
            logger.error(f"Error loading cenarios: {str(e)}")
            return {
                'success': False,
                'error': 'database_error',
                'message': f'Error loading cenarios: {str(e)}',
                'total_count': 0,
                'loaded_count': 0
            }
    
    def _convert_to_dict_optimized(self, cenarios: List) -> List[Dict[str, Any]]:
        """Convert cenarios to dictionaries with optimized relationship access"""
        cenarios_dict = []
        
        for cenario in cenarios:
            cenario_dict = cenario.to_dict()
            
            # Add client information (loaded via joinedload or explicit join)
            if hasattr(cenario, 'cliente') and cenario.cliente:
                cenario_dict['cliente'] = cenario.cliente.to_dict()
            else:
                # If cliente wasn't loaded, we need to fetch it
                logger.warning(f"Cliente não carregado para cenário {cenario.id}, fazendo query adicional")
                if cenario.cliente_id:
                    from models import Cliente
                    cliente = Cliente.query.get(cenario.cliente_id)
                    if cliente:
                        cenario_dict['cliente'] = cliente.to_dict()
            
            # Add product information (loaded via joinedload or explicit join)
            if hasattr(cenario, 'produto') and cenario.produto:
                cenario_dict['produto'] = cenario.produto.to_dict()
            else:
                # If produto wasn't loaded, we need to fetch it
                logger.warning(f"Produto não carregado para cenário {cenario.id}, fazendo query adicional")
                if cenario.produto_id:
                    from models import Produto
                    produto = Produto.query.get(cenario.produto_id)
                    if produto:
                        cenario_dict['produto'] = produto.to_dict()
            
            cenarios_dict.append(cenario_dict)
        
        return cenarios_dict
    
    def get_progressive_batch(self, tipo_tributo: str, filters: Dict[str, Any], 
                             offset: int, batch_size: int) -> Dict[str, Any]:
        """
        Load a batch of records for progressive loading
        
        Args:
            tipo_tributo: Type of tax scenario
            filters: Filter parameters
            offset: Starting offset
            batch_size: Number of records to load
            
        Returns:
            Dict with batch data
        """
        try:
            query = self._build_base_query(tipo_tributo, filters)
            
            # Get batch with offset and limit
            cenarios_items = query.order_by(
                self.TIPO_TRIBUTO_MODELS[tipo_tributo].id.desc()
            ).offset(offset).limit(batch_size).all()
            
            # Convert to dictionaries
            cenarios_dict = self._convert_to_dict_optimized(cenarios_items)
            
            return {
                'success': True,
                'cenarios': cenarios_dict,
                'offset': offset,
                'batch_size': batch_size,
                'loaded_count': len(cenarios_dict),
                'has_more': len(cenarios_dict) == batch_size
            }
            
        except Exception as e:
            logger.error(f"Error loading batch: {str(e)}")
            return {
                'success': False,
                'error': 'database_error',
                'message': f'Error loading batch: {str(e)}',
                'loaded_count': 0
            }
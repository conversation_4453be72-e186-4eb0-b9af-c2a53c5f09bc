@import './styles/design-tokens.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@theme {
  --font-sans: "Fredoka", sans-serif;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.8);
}

/* Scrollbar oculta para elementos específicos */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Estilos específicos para a barra de rolagem superior da tabela */
.table-top-scrollbar {
  height: 8px;
  margin-bottom: 0px;
  overflow-y: hidden;
  cursor: pointer;
}

.table-top-scrollbar::-webkit-scrollbar {
  height: 8px;
}

.table-top-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.table-top-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

.dark .table-top-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark .table-top-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.8);
}

/* Animações personalizadas */
@keyframes animate-in {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-in {
  animation: animate-in 0.2s ease-out;
}

/* Smooth focus transitions */
* {
  transition-property: box-shadow, border-color;
  transition-duration: 150ms;
  transition-timing-function: ease-in-out;
}

@layer base {
  html {
    font-family: 'Fredoka', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800 focus-visible:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600 focus-visible:ring-gray-500;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-12 px-8 text-base;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-400;
  }

  /* Modern Select Styles */
  .modern-select {
    @apply w-full px-4 py-3 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm;
    @apply hover:border-primary-400 dark:hover:border-primary-500;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    @apply transition-all duration-200;
    @apply text-gray-900 dark:text-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
    appearance: none;
  }

  .modern-select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  .dark .modern-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  .dark .modern-select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%3c60a5fa' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  /* Modern Input Styles */
  .modern-input {
    @apply w-full px-4 py-3 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm;
    @apply hover:border-primary-400 dark:hover:border-primary-500;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    @apply transition-all duration-200;
    @apply text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400;
  }

  /* Modern Checkbox Styles */
  .modern-checkbox {
    @apply w-4 h-4 text-primary-600 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded;
    @apply focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
    @apply hover:border-primary-400 dark:hover:border-primary-500;
    @apply transition-all duration-200;
  }

  .modern-checkbox:checked {
    @apply bg-primary-600 border-primary-600;
  }

  /* Modern Radio Styles */
  .modern-radio {
    @apply w-4 h-4 text-primary-600 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600;
    @apply focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
    @apply hover:border-primary-400 dark:hover:border-primary-500;
    @apply transition-all duration-200;
  }

  .modern-radio:checked {
    @apply bg-primary-600 border-primary-600;
  }

  /* Modern Button Hover Effects */
  .modern-button-hover {
    @apply transform transition-all duration-200;
  }

  .modern-button-hover:hover {
    @apply scale-105 shadow-lg;
  }

  .modern-button-hover:active {
    @apply scale-95;
  }

  /* Modern Card Hover Effects */
  .modern-card-hover {
    @apply transform transition-all duration-300;
  }

  .modern-card-hover:hover {
    @apply shadow-xl;
    transform: scale(1.02) translateY(-4px);
  }

  /* Advanced Custom Dropdown Styles */
  .custom-dropdown {
    @apply relative;
  }

  .custom-dropdown-menu {
    list-style: none;
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    right: 0;
    max-height: 220px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid rgb(229 231 235);
    border-radius: 12px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    opacity: 0;
    pointer-events: none;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    z-index: 1000;
    padding: 8px 0;
  }

  .custom-dropdown.open .custom-dropdown-menu {
    opacity: 1;
    pointer-events: auto;
    transform: translateY(0);
  }

  /* Modern Custom Scrollbar */
  .custom-dropdown-menu::-webkit-scrollbar {
    width: 8px;
  }

  .custom-dropdown-menu::-webkit-scrollbar-track {
    background: rgb(243 244 246);
    border-radius: 12px;
    margin: 8px 0;
  }

  .custom-dropdown-menu::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(37 99 235) 100%);
    border-radius: 12px;
    border: 2px solid rgb(243 244 246);
    transition: all 0.2s ease;
  }

  .custom-dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgb(37 99 235) 0%, rgb(29 78 216) 100%);
    transform: scale(1.1);
  }

  .custom-dropdown-menu::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, rgb(29 78 216) 0%, rgb(30 64 175) 100%);
  }

  /* Dark theme scrollbar */
  .dark .custom-dropdown-menu {
    background-color: rgb(31 41 55);
    border-color: rgb(75 85 99);
  }

  .dark .custom-dropdown-menu::-webkit-scrollbar-track {
    background: rgb(31 41 55);
  }

  .dark .custom-dropdown-menu::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgb(37 99 235) 0%, rgb(29 78 216) 100%);
    border-color: rgb(31 41 55);
  }

  .dark .custom-dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(37 99 235) 100%);
  }

  .dark .custom-dropdown-menu::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, rgb(96 165 250) 0%, rgb(59 130 246) 100%);
  }

  .custom-dropdown-item {
    padding: 12px 16px;
    cursor: pointer;
    font-size: 14px;
    color: rgb(55 65 81);
    border-radius: 8px;
    margin: 0 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .custom-dropdown-item:hover,
  .custom-dropdown-item.active {
    background-color: rgb(239 246 255);
    color: rgb(29 78 216);
    transform: translateX(2px);
  }

  .dark .custom-dropdown-item {
    color: rgb(209 213 219);
  }

  .dark .custom-dropdown-item:hover,
  .dark .custom-dropdown-item.active {
    background-color: rgb(30 58 138);
    color: rgb(147 197 253);
  }

  /* Modern Analysis Button Styles */
  .analysis-button {
    @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-xl;
    @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white;
    @apply hover:from-blue-600 hover:to-blue-700 hover:shadow-lg hover:scale-105;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
    @apply transition-all duration-200;
  }

  .validation-button {
    @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-xl;
    @apply bg-gradient-to-r from-emerald-500 to-emerald-600 text-white;
    @apply hover:from-emerald-600 hover:to-emerald-700 hover:shadow-lg hover:scale-105;
    @apply focus:outline-none focus:ring-2 focus:ring-emerald-500/20 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
    @apply transition-all duration-200;
  }

  .warning-button {
    @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-xl;
    @apply bg-gradient-to-r from-amber-500 to-amber-600 text-white;
    @apply hover:from-amber-600 hover:to-amber-700 hover:shadow-lg hover:scale-105;
    @apply focus:outline-none focus:ring-2 focus:ring-amber-500/20 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
    @apply transition-all duration-200;
  }

  .danger-button {
    @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-xl;
    @apply bg-gradient-to-r from-red-500 to-red-600 text-white;
    @apply hover:from-red-600 hover:to-red-700 hover:shadow-lg hover:scale-105;
    @apply focus:outline-none focus:ring-2 focus:ring-red-500/20 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
    @apply transition-all duration-200;
  }
}
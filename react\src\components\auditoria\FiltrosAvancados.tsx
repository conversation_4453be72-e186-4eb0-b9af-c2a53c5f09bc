import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Dropdown } from '@/components/ui/Dropdown'
import { FiltroOptions } from '@/services/auditoriaService'

interface FiltrosAvancadosProps {
  filtroOptions?: FiltroOptions
  onFiltrosChange: (filtros: FiltrosState) => void
  loading?: boolean
}

export interface FiltrosState {
  analista_visualizou: string
  atividade: string
  destinacao: string
  numero: string
  produto_numero: string
  cfops: string[]
  ncms: string[]
  csts: string[]
  aliquotas: string[]
}

const initialFiltros: FiltrosState = {
  analista_visualizou: '',
  atividade: '',
  destinacao: '',
  numero: '',
  produto_numero: '',
  cfops: [],
  ncms: [],
  csts: [],
  aliquotas: [],
}

const atividadeOptions = [
  'Indústria ou Equiparado',
  'Comércio Varejista',
  'Comércio Atacadista',
  'Distribuidor',
  'Produtor Rural',
  'Consumidor Final',
  'Não Contribuinte',
  'Órgão Público',
  'Serviços',
]

const destinacaoOptions = [
  'Industrialização',
  'Revenda',
  'Varejista',
  'Ativo Imobilizado',
  'Uso e Consumo',
]

export function FiltrosAvancados({ filtroOptions, onFiltrosChange, loading }: FiltrosAvancadosProps) {
  const [filtros, setFiltros] = useState<FiltrosState>(initialFiltros)
  const [searchTerms, setSearchTerms] = useState({
    cfop: '',
    ncm: '',
    cst: '',
    aliquota: '',
  })

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onFiltrosChange(filtros)
    }, 100) // Debounce pequeno para evitar múltiplas chamadas

    return () => clearTimeout(timeoutId)
  }, [filtros]) // Removido onFiltrosChange das dependências

  const handleInputChange = (field: keyof FiltrosState, value: string) => {
    setFiltros(prev => ({ ...prev, [field]: value }))
  }

  const handleMultiSelectChange = (field: 'cfops' | 'ncms' | 'csts' | 'aliquotas', values: string[]) => {
    setFiltros(prev => ({ ...prev, [field]: values }))
  }

  const limparFiltros = () => {
    setFiltros(initialFiltros)
    setSearchTerms({ cfop: '', ncm: '', cst: '', aliquota: '' })
  }

  const getFilteredOptions = (type: 'cfops' | 'ncms' | 'csts' | 'aliquotas') => {
    if (!filtroOptions) return []
    
    const searchTerm = searchTerms[type.slice(0, -1) as keyof typeof searchTerms].toLowerCase()
    return filtroOptions[type].filter(option => 
      option.value.toLowerCase().includes(searchTerm)
    )
  }

  const formatAliquota = (value: string) => {
    const num = parseFloat(value)
    return isNaN(num) ? value : `${num.toFixed(2)}%`
  }

  return (
    <Card className="p-4 space-y-4">
      {/* Primeira linha de filtros */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            Análise
          </label>
          <Dropdown
            options={[
              { value: '', label: 'Todas' },
              { value: 'true', label: 'Analisadas' },
              { value: 'false', label: 'Não analisadas' }
            ]}
            value={filtros.analista_visualizou}
            onChange={(value) => handleInputChange('analista_visualizou', value as string)}
            placeholder="Todas"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            Atividade
          </label>
          <Dropdown
            options={[
              { value: '', label: 'Todas' },
              ...atividadeOptions.map(atividade => ({ value: atividade, label: atividade }))
            ]}
            value={filtros.atividade}
            onChange={(value) => handleInputChange('atividade', value as string)}
            placeholder="Todas"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            Destinação
          </label>
          <Dropdown
            options={[
              { value: '', label: 'Todas' },
              ...destinacaoOptions.map(destinacao => ({ value: destinacao, label: destinacao }))
            ]}
            value={filtros.destinacao}
            onChange={(value) => handleInputChange('destinacao', value as string)}
            placeholder="Todas"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            Número NF
          </label>
          <Input
            type="text"
            placeholder="Filtrar por número"
            value={filtros.numero}
            onChange={(e) => handleInputChange('numero', e.target.value)}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            Produto
          </label>
          <Input
            type="text"
            placeholder="Filtrar por produto"
            value={filtros.produto_numero}
            onChange={(e) => handleInputChange('produto_numero', e.target.value)}
          />
        </div>
      </div>

      {/* Segunda linha - Filtros dropdown */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* CFOP */}
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            CFOP
          </label>
          <Dropdown
            placeholder="Selecionar CFOPs..."
            multiple
            searchable
            searchPlaceholder="Buscar CFOP..."
            value={filtros.cfops}
            onChange={(values) => handleMultiSelectChange('cfops', values)}
            options={getFilteredOptions('cfops').map(option => ({
              value: option.value,
              label: option.value,
            }))}
            loading={loading}
            onSearch={(term) => setSearchTerms(prev => ({ ...prev, cfop: term }))}
          />
        </div>

        {/* NCM */}
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            NCM
          </label>
          <Dropdown
            placeholder="Selecionar NCMs..."
            multiple
            searchable
            searchPlaceholder="Buscar NCM..."
            value={filtros.ncms}
            onChange={(values) => handleMultiSelectChange('ncms', values)}
            options={getFilteredOptions('ncms').map(option => ({
              value: option.value,
              label: option.value,
            }))}
            loading={loading}
            onSearch={(term) => setSearchTerms(prev => ({ ...prev, ncm: term }))}
          />
        </div>

        {/* CST */}
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            CST
          </label>
          <Dropdown
            placeholder="Selecionar CSTs..."
            multiple
            searchable
            searchPlaceholder="Buscar CST..."
            value={filtros.csts}
            onChange={(values) => handleMultiSelectChange('csts', values)}
            options={getFilteredOptions('csts').map(option => ({
              value: option.value,
              label: option.value,
            }))}
            loading={loading}
            onSearch={(term) => setSearchTerms(prev => ({ ...prev, cst: term }))}
          />
        </div>

        {/* Alíquota */}
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            Alíquota
          </label>
          <Dropdown
            placeholder="Selecionar Alíquotas..."
            multiple
            searchable
            searchPlaceholder="Buscar Alíquota..."
            value={filtros.aliquotas}
            onChange={(values) => handleMultiSelectChange('aliquotas', values)}
            options={getFilteredOptions('aliquotas').map(option => ({
              value: option.value,
              label: formatAliquota(option.value),
            }))}
            loading={loading}
            onSearch={(term) => setSearchTerms(prev => ({ ...prev, aliquota: term }))}
          />
        </div>
      </div>

      {/* Botões de ação */}
      <div className="flex justify-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={limparFiltros}
          icon={
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          }
        >
          Limpar Filtros
        </Button>
      </div>
    </Card>
  )
}
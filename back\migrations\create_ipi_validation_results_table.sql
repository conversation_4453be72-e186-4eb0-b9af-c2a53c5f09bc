-- Migration: <PERSON><PERSON><PERSON> tabela para armazenar resultados de validações IPI
-- Data: 2025-01-31
-- Descrição: Tabela para histórico de validações IPI (TIPI e CFOP x CST)

-- Criar tabela ipi_validation_results
CREATE TABLE IF NOT EXISTS ipi_validation_results (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER NOT NULL REFERENCES empresa(id) ON DELETE CASCADE,
    escritorio_id INTEGER NOT NULL REFERENCES escritorio(id) ON DELETE CASCADE,
    cenario_id INTEGER NOT NULL REFERENCES cenario_ipi(id) ON DELETE CASCADE,
    
    -- Dados da validação
    tipo_problema VARCHAR(50) NOT NULL, -- NCM_NAO_ENCONTRADO, CST_ALIQUOTA_INCORRETA, CFOP_CST_ALIQUOTA_INCORRETA
    descricao_problema TEXT NOT NULL,
    
    -- <PERSON>stado antes da correção (JSON)
    dados_originais JSONB NOT NULL, -- NCM, CFOP, CST, alíquota originais
    
    -- Sugestão aplicada (JSON)
    sugestao_aplicada JSONB, -- CST sugerido, alíquota sugerida, etc.
    
    -- Dados da TIPI consultada (JSON)
    tipi_data JSONB, -- NCM, EX, descrição, alíquota da TIPI ou dados da validação CFOP x CST
    
    -- Status da validação
    status VARCHAR(20) DEFAULT 'pendente' CHECK (status IN ('pendente', 'aplicada', 'rejeitada')),
    aplicada_automaticamente BOOLEAN DEFAULT FALSE,
    
    -- Metadados
    data_validacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_aplicacao TIMESTAMP,
    usuario_aplicacao VARCHAR(100),
    
    -- Índices para performance
    CONSTRAINT ipi_validation_results_empresa_fk FOREIGN KEY (empresa_id) REFERENCES empresa(id),
    CONSTRAINT ipi_validation_results_escritorio_fk FOREIGN KEY (escritorio_id) REFERENCES escritorio(id),
    CONSTRAINT ipi_validation_results_cenario_fk FOREIGN KEY (cenario_id) REFERENCES cenario_ipi(id)
);

-- Criar índices para otimizar consultas
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_empresa_id ON ipi_validation_results(empresa_id);
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_escritorio_id ON ipi_validation_results(escritorio_id);
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_cenario_id ON ipi_validation_results(cenario_id);
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_status ON ipi_validation_results(status);
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_tipo_problema ON ipi_validation_results(tipo_problema);
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_data_validacao ON ipi_validation_results(data_validacao);

-- Criar índice composto para consultas por empresa e status
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_empresa_status ON ipi_validation_results(empresa_id, status);

-- Criar índice GIN para consultas JSON
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_dados_originais_gin ON ipi_validation_results USING GIN (dados_originais);
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_sugestao_aplicada_gin ON ipi_validation_results USING GIN (sugestao_aplicada);
CREATE INDEX IF NOT EXISTS idx_ipi_validation_results_tipi_data_gin ON ipi_validation_results USING GIN (tipi_data);

-- Comentários na tabela e colunas
COMMENT ON TABLE ipi_validation_results IS 'Histórico de validações IPI (TIPI e CFOP x CST)';
COMMENT ON COLUMN ipi_validation_results.tipo_problema IS 'Tipo do problema: NCM_NAO_ENCONTRADO, CST_ALIQUOTA_INCORRETA, CFOP_CST_ALIQUOTA_INCORRETA';
COMMENT ON COLUMN ipi_validation_results.dados_originais IS 'Estado original do cenário antes da correção (JSON)';
COMMENT ON COLUMN ipi_validation_results.sugestao_aplicada IS 'Sugestão de correção aplicada (JSON)';
COMMENT ON COLUMN ipi_validation_results.tipi_data IS 'Dados da TIPI consultada ou informações da validação CFOP x CST (JSON)';
COMMENT ON COLUMN ipi_validation_results.status IS 'Status: pendente, aplicada, rejeitada';
COMMENT ON COLUMN ipi_validation_results.aplicada_automaticamente IS 'Se a correção foi aplicada automaticamente ou manualmente';

-- Inserir dados de exemplo (opcional - remover em produção)
-- INSERT INTO ipi_validation_results (
--     empresa_id, escritorio_id, cenario_id, tipo_problema, descricao_problema,
--     dados_originais, sugestao_aplicada, tipi_data, status
-- ) VALUES (
--     1, 1, 1, 'CST_ALIQUOTA_INCORRETA',
--     'CST 50 deve ter alíquota positiva. TIPI indica 15%',
--     '{"ncm": "84141000", "cfop": "5101", "cst": "50", "aliquota": 0.0}',
--     '{"cst_sugerido": "50", "aliquota_sugerida": 15.0}',
--     '{"ncm": "84141000", "descricao": "Bombas para líquidos", "aliquota_tipi": 15.0}',
--     'pendente'
-- );

-- Verificar se a tabela foi criada corretamente
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'ipi_validation_results' 
ORDER BY ordinal_position;
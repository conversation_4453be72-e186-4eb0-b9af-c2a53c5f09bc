-- Migração para suporte aos cards de análise fiscal
-- Data: 2024-12-23
-- Descrição: Adiciona índices e campos necessários para otimizar as análises fiscais
--
-- REGRAS IMPLEMENTADAS:
--
-- 1. ANÁLISE CFOP:
--    - CFOP SPED 1101, 1102, 1401, 1407, 1556 não podem ter CFOP XML 1.9**
--    - CFOP SPED 1101, 1102 não podem ter CFOP XML 5.40*
--    - Mapeamento entrada-saída: 1401→5401/5405, 1407→5401/5405, 1403→5401/5405
--    - 1556→5101/5102, 1101→5101, 1102→5102
--
-- 2. ANÁLISE CFOP x CST:
--    - CST SPED deve ser igual ao CST XML (exceto Simples Nacional)
--
-- 3. ANÁLISE TIPO DE PRODUTO:
--    - CFOP 1101: não pode ter CST 10,60,70 para matéria prima/embalagem (tipos 02,03)
--    - CFOP 1401: só pode ter CST 10,60,70 para matéria prima/embalagem (tipos 02,03)
--    - CFOP 1556: não pode ter CST 10,60,70 para uso e consumo (tipo 04)
--    - CFOP 1407: só pode ter CST 10,60,70 para uso e consumo (tipo 04)
--    - CFOP 1102: não pode ter CST 10,60,70 para revenda (tipo 01)
--    - CFOP 1403: só pode ter CST 10,60,70 para revenda (tipo 01)
--
-- 4. ANÁLISE ORIGEM:
--    - Origem SPED deve ser igual à origem XML
--    - Exceção: origem 2 no SPED pode ter origem 1 na XML
--
-- 5. ANÁLISE ALÍQUOTA:
--    - Para produtos tipo revenda, matéria-prima, embalagem: alíquota SPED = alíquota XML
--    - Para produtos tipo uso e consumo: alíquota SPED deve ser 0

-- Índices para melhorar performance das consultas de análise
CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_cfop_analysis 
ON auditoria_comparativa_impostos (empresa_id, sped_cfop, xml_cfop) 
WHERE sped_cfop IS NOT NULL AND xml_cfop IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_cst_analysis 
ON auditoria_comparativa_impostos (empresa_id, sped_cst_icms, xml_icms_cst) 
WHERE sped_cst_icms IS NOT NULL AND xml_icms_cst IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_origem_analysis
ON auditoria_comparativa_impostos (empresa_id, sped_origem, xml_icms_origem)
WHERE sped_origem IS NOT NULL AND xml_icms_origem IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_aliquota_analysis
ON auditoria_comparativa_impostos (empresa_id, sped_icms_aliquota, xml_icms_aliquota)
WHERE sped_icms_aliquota IS NOT NULL AND xml_icms_aliquota IS NOT NULL;

-- Índice para relacionar com produto_entrada para tipo de item
CREATE INDEX IF NOT EXISTS idx_item_nota_entrada_produto 
ON item_nota_entrada (produto_entrada_id);

CREATE INDEX IF NOT EXISTS idx_produto_entrada_tipo_item 
ON produto_entrada (empresa_id, tipo_item) 
WHERE tipo_item IS NOT NULL;

-- Adicionar campo para armazenar origem SPED se não existir
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'auditoria_comparativa_impostos' 
        AND column_name = 'sped_origem'
    ) THEN
        ALTER TABLE auditoria_comparativa_impostos 
        ADD COLUMN sped_origem VARCHAR(2);
    END IF;
END $$;

-- Comentários para documentar as regras de análise
COMMENT ON TABLE auditoria_comparativa_impostos IS 'Tabela para auditoria comparativa com suporte a análises fiscais automatizadas';

-- Comentários sobre as regras implementadas
COMMENT ON COLUMN auditoria_comparativa_impostos.sped_cfop IS 'CFOP do SPED - usado para análise de consistência';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_cfop IS 'CFOP do XML - usado para análise de consistência';
COMMENT ON COLUMN auditoria_comparativa_impostos.sped_cst_icms IS 'CST ICMS do SPED - usado para análise CFOP x CST';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_icms_cst IS 'CST ICMS do XML - usado para análise CFOP x CST';
COMMENT ON COLUMN auditoria_comparativa_impostos.sped_origem IS 'Origem ICMS do SPED - usado para análise de origem';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_icms_origem IS 'Origem ICMS do XML - usado para análise de origem';

-- Função para validar CFOP (pode ser usada em triggers futuros)
CREATE OR REPLACE FUNCTION validate_cfop_consistency(
    sped_cfop VARCHAR(4),
    xml_cfop VARCHAR(10)
) RETURNS BOOLEAN AS $$
BEGIN
    -- Regra 1: CFOP SPED problemático com CFOP XML 1.9**
    IF sped_cfop IN ('1101', '1102', '1401', '1407', '1556')
       AND xml_cfop ~ '^1\.9\d{2}$' THEN
        RETURN FALSE;
    END IF;

    -- Regra 2: CFOP 1101 e 1102 com CFOP 5.40*
    IF sped_cfop IN ('1101', '1102')
       AND xml_cfop ~ '^5\.40' THEN
        RETURN FALSE;
    END IF;

    -- Regra 3: Validar mapeamento entrada-saída
    -- CFOP 1401 deve corresponder a 5401 ou 5405
    IF sped_cfop = '1401' AND xml_cfop NOT IN ('5.401', '5.405') THEN
        RETURN FALSE;
    END IF;

    -- CFOP 1407 deve corresponder a 5401 ou 5405
    IF sped_cfop = '1407' AND xml_cfop NOT IN ('5.401', '5.405') THEN
        RETURN FALSE;
    END IF;

    -- CFOP 1403 deve corresponder a 5401 ou 5405
    IF sped_cfop = '1403' AND xml_cfop NOT IN ('5.401', '5.405') THEN
        RETURN FALSE;
    END IF;

    -- CFOP 1556 deve corresponder a 5101 ou 5102
    IF sped_cfop = '1556' AND xml_cfop NOT IN ('5.101', '5.102') THEN
        RETURN FALSE;
    END IF;

    -- CFOP 1101 deve corresponder a 5101
    IF sped_cfop = '1101' AND xml_cfop != '5.101' THEN
        RETURN FALSE;
    END IF;

    -- CFOP 1102 deve corresponder a 5102
    IF sped_cfop = '1102' AND xml_cfop != '5.102' THEN
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Função para validar tipo de produto vs CFOP/CST
CREATE OR REPLACE FUNCTION validate_product_type_consistency(
    sped_cfop VARCHAR(4),
    sped_cst VARCHAR(3),
    tipo_item VARCHAR(2)
) RETURNS BOOLEAN AS $$
BEGIN
    -- CFOP 1101: não pode ter CST 10, 60, 70 para matéria prima/embalagem
    IF sped_cfop = '1101' 
       AND sped_cst IN ('10', '60', '70') 
       AND tipo_item IN ('02', '03') THEN
        RETURN FALSE;
    END IF;
    
    -- CFOP 1401: só pode ter CST 10, 60, 70 para matéria prima/embalagem
    IF sped_cfop = '1401' 
       AND sped_cst NOT IN ('10', '60', '70') 
       AND tipo_item IN ('02', '03') THEN
        RETURN FALSE;
    END IF;
    
    -- CFOP 1556: não pode ter CST 10, 60, 70 para uso e consumo
    IF sped_cfop = '1556' 
       AND sped_cst IN ('10', '60', '70') 
       AND tipo_item = '04' THEN
        RETURN FALSE;
    END IF;
    
    -- CFOP 1407: só pode ter CST 10, 60, 70 para uso e consumo
    IF sped_cfop = '1407' 
       AND sped_cst NOT IN ('10', '60', '70') 
       AND tipo_item = '04' THEN
        RETURN FALSE;
    END IF;
    
    -- CFOP 1102: não pode ter CST 10, 60, 70 para revenda
    IF sped_cfop = '1102' 
       AND sped_cst IN ('10', '60', '70') 
       AND tipo_item = '01' THEN
        RETURN FALSE;
    END IF;
    
    -- CFOP 1403: só pode ter CST 10, 60, 70 para revenda
    IF sped_cfop = '1403' 
       AND sped_cst NOT IN ('10', '60', '70') 
       AND tipo_item = '01' THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Função para validar origem
CREATE OR REPLACE FUNCTION validate_origin_consistency(
    sped_origem VARCHAR(2),
    xml_origem VARCHAR(2)
) RETURNS BOOLEAN AS $$
BEGIN
    -- Exceção: origem 2 no SPED deve ter 1 na NOTA
    IF sped_origem = '2' AND xml_origem = '1' THEN
        RETURN TRUE;
    END IF;

    -- Outras origens devem ser iguais
    RETURN sped_origem = xml_origem;
END;
$$ LANGUAGE plpgsql;

-- Função para validar alíquotas
CREATE OR REPLACE FUNCTION validate_aliquota_consistency(
    sped_aliquota NUMERIC,
    xml_aliquota NUMERIC,
    tipo_item VARCHAR(2)
) RETURNS BOOLEAN AS $$
BEGIN
    -- Para uso e consumo (tipo 04), alíquota SPED deve ser 0
    IF tipo_item = '04' THEN
        RETURN COALESCE(sped_aliquota, 0) <= 0.01;
    END IF;

    -- Para outros tipos (01, 02, 03), alíquotas devem ser iguais (tolerância 0.01)
    IF tipo_item IN ('01', '02', '03') THEN
        RETURN ABS(COALESCE(sped_aliquota, 0) - COALESCE(xml_aliquota, 0)) <= 0.01;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- View para facilitar consultas de análise
CREATE OR REPLACE VIEW v_auditoria_analysis AS
SELECT
    aci.*,
    pe.tipo_item,
    pe.descricao as produto_descricao,
    -- Análises
    NOT validate_cfop_consistency(aci.sped_cfop, aci.xml_cfop) as cfop_inconsistent,
    NOT validate_product_type_consistency(aci.sped_cfop, aci.sped_cst_icms, pe.tipo_item) as product_type_inconsistent,
    NOT validate_origin_consistency(aci.sped_origem, aci.xml_icms_origem) as origin_inconsistent,
    NOT validate_aliquota_consistency(aci.sped_icms_aliquota, aci.xml_icms_aliquota, pe.tipo_item) as aliquota_inconsistent,
    (aci.sped_cst_icms != aci.xml_icms_cst) as cst_inconsistent
FROM auditoria_comparativa_impostos aci
LEFT JOIN item_nota_entrada ine ON aci.sped_item_id = ine.id
LEFT JOIN produto_entrada pe ON ine.produto_entrada_id = pe.id;

-- Comentário na view
COMMENT ON VIEW v_auditoria_analysis IS 'View que facilita a análise de inconsistências fiscais com flags booleanos';

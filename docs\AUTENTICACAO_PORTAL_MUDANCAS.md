# Integração com Portal de Autenticação

Este documento descreve as modificações necessárias para que o sistema Fiscal utilize autenticação centralizada via portal.

## Visão Geral
- O usuário realiza login em um portal externo e é redirecionado para o sistema Fiscal com um token JWT.
- O token contém as informações de usuário e do escritório, além das permissões contratadas.
- Caso a permissão `Fiscal` não esteja presente, o acesso é negado.
- Usuários e escritórios são criados automaticamente no primeiro acesso.

## Alterações no Banco
O arquivo `db/migration_portal_auth.sql` contém os comandos SQL necessários:
- Remoção das colunas de email e senha da tabela `usuario`.
- Inclusão dos campos `portal_user_id` e `tipo_usuario` em `usuario`.
- Inclusão do campo `portal_office_id` na tabela `escritorio` e garantia dos campos de branding.

Execute este script após realizar o backup do banco de dados.

## Fluxo de Autenticação
1. O portal cria um cookie `token` contendo o JWT e redireciona o usuário para `/web`.
2. O JavaScript em `front/static/js/auth.js` lê esse cookie e envia o token para `/api/portal-login`.
3. O backend valida a assinatura, verifica as permissões e sincroniza os dados de usuário e escritório.
   - O token pode trazer os dados do usuário diretamente na raiz ou no campo `user`.
4. Um novo JWT interno é gerado e armazenado no `localStorage`.
5. O usuário é redirecionado para o dashboard.

### Depuração

Use a rota `/api/debug-portal-token` (método `POST`) para visualizar o payload decodificado. O token pode ser enviado em `{ "token": "SEU_JWT" }`, via query `?token=` ou no cookie `token`.

## Novos Campos
- **usuario.portal_user_id**: identifica o usuário vindo do portal.
- **usuario.tipo_usuario**: `super_admin`, `admin` ou `normal`.
  - A role `admin` enviada pelo portal é armazenada internamente como `escritorio`,
    garantindo que o usuário visualize somente recursos do seu escritório.
- **escritorio.portal_office_id**: identifica o escritório no portal.

## Arquivos Relevantes
- `back/app.py` – nova rota `/api/portal-login` e remoção do login antigo.
- `back/models/usuario.py` e `back/models/escritorio.py` – novos campos dos modelos.
- `front/templates/index.html` e `front/static/js/auth.js` – nova lógica de autenticação.
- `db/migration_portal_auth.sql` – script de migração.

### Variáveis de Ambiente

Adicione a chave pública usada para validar o token do portal no arquivo `.env`:

```env
PORTAL_JWT_PUBLIC_KEY="""
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwgfbIU5G1o7qUGgpG1hw
+qLQqSqV4o20Xx2vQOre89Bac9AE4D9TDYTtJf0J9Y4gEx6IKNMVXGRNy/JgnazK
naGrAK/Y9CGJmvfqZxk51+m3QwHWVzOqrRTg6qx3BxM5+R9nwCFf5xbmdXLi2jF7
bgteFUsI5wHi+4d0t4et8ATOEPcDwN//X8bSC7D4XDteC0uXDrCKbMSDKKHs4dE4
VEl0BA1UHt9zFZqUlUvaRSzl42ee3qw8TnbTSC3fLxYwEoEmLfT7o6PXaievkoF3
NxJkk+sxmPNI/2AnOAxnrvsNJM4oNduttWgJsE5wlmLE+B3UCZyM0O1Ni3OPM/iU
6QIDAQAB
-----END PUBLIC KEY-----
"""
```

## Rota para verificação do token:

```python
@app.route("/api/debug-portal-token", methods=["POST"])
def debug_portal_token():
    try:
        data = request.get_json()
        token = data.get("token")
        if not token:
            return {"message": "Token não fornecido"}, 400

        try:
            payload = pyjwt.decode(token, portal_key, algorithms=["RS256"])
            return {"payload": payload}, 200
        except Exception:
            return {"message": "Token inválido"}, 401
    except Exception as e:
        return {"message": str(e)}, 500
```

## Rota para portal login
```python
@app.route("/api/portal-login", methods=["POST"])

{ "token": "TOKEN JWT" }

```

## Rota para ver informações do usuário
```python
@app.route("/me", methods=["GET"])

Bearer Token JWT

```
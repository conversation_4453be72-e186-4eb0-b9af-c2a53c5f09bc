import { useAuthStore } from '@/store/authStore'
import { authService } from '@/services/authService'

export function useAuth() {
  const store = useAuthStore()

  const logout = async () => {
    try {
      // Chama o service para limpar tudo
      authService.logout()
      
      // Atualiza o store
      store.logout()
      
      // Redireciona para login
      window.location.href = '/fiscal/login'
    } catch (error) {
      console.error('Erro no logout:', error)
      // Mesmo com erro, força o logout local
      store.logout()
      window.location.href = '/fiscal/login'
    }
  }

  return {
    ...store,
    logout,
  }
}
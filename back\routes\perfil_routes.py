from flask import Blueprint, request, jsonify, redirect
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
import os
from models import db, Usuario, Escritorio

perfil_bp = Blueprint('perfil', __name__)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'svg'}
UPLOAD_FOLDER = 'front/static/uploads/logos'

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@perfil_bp.route('/perfil')
def perfil_page():
    """Redireciona para o dashboard com parâmetro de perfil"""
    return redirect('/dashboard?section=perfil')

@perfil_bp.route('/api/perfil', methods=['GET'])
@jwt_required()
def obter_perfil():
    """
    Obtém os dados do perfil do escritório do usuário logado
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        if not usuario.escritorio_id:
            return jsonify({"message": "Usuário não está vinculado a um escritório"}), 400

        escritorio = db.session.get(Escritorio, usuario.escritorio_id)
        if not escritorio:
            return jsonify({"message": "Escritório não encontrado"}), 404

        return jsonify({
            "success": True,
            "escritorio": escritorio.to_dict()
        }), 200

    except Exception as e:
        print(f"Erro ao obter perfil: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@perfil_bp.route('/api/perfil', methods=['PUT'])
@jwt_required()
def atualizar_perfil():
    """
    Atualiza os dados do perfil do escritório
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        if not usuario.escritorio_id:
            return jsonify({"message": "Usuário não está vinculado a um escritório"}), 400

        escritorio = db.session.get(Escritorio, usuario.escritorio_id)
        if not escritorio:
            return jsonify({"message": "Escritório não encontrado"}), 404

        data = request.get_json()

        # Atualizar campos permitidos
        if 'nome' in data:
            escritorio.nome = data['nome']
        if 'cnpj' in data:
            escritorio.cnpj = data['cnpj']
        if 'endereco' in data:
            escritorio.endereco = data['endereco']
        if 'responsavel' in data:
            escritorio.responsavel = data['responsavel']
        if 'cor_relatorio' in data:
            escritorio.cor_relatorio = data['cor_relatorio']

        db.session.commit()

        return jsonify({
            "success": True,
            "message": "Perfil atualizado com sucesso",
            "escritorio": escritorio.to_dict()
        }), 200

    except Exception as e:
        print(f"Erro ao atualizar perfil: {str(e)}")
        db.session.rollback()
        return jsonify({"message": "Erro interno do servidor"}), 500

@perfil_bp.route('/api/perfil/logo', methods=['POST'])
@jwt_required()
def upload_logo():
    """
    Faz upload do logo do escritório
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        if not usuario.escritorio_id:
            return jsonify({"message": "Usuário não está vinculado a um escritório"}), 400

        escritorio = db.session.get(Escritorio, usuario.escritorio_id)
        if not escritorio:
            return jsonify({"message": "Escritório não encontrado"}), 404

        if 'logo' not in request.files:
            return jsonify({"message": "Nenhum arquivo enviado"}), 400

        file = request.files['logo']
        if file.filename == '':
            return jsonify({"message": "Nenhum arquivo selecionado"}), 400

        if file and allowed_file(file.filename):
            # Criar diretório se não existir
            os.makedirs(UPLOAD_FOLDER, exist_ok=True)

            # Gerar nome seguro para o arquivo
            filename = secure_filename(file.filename)
            # Adicionar ID do escritório para evitar conflitos
            name, ext = os.path.splitext(filename)
            filename = f"escritorio_{escritorio.id}_{name}{ext}"

            file_path = os.path.join(UPLOAD_FOLDER, filename)
            file.save(file_path)

            # Atualizar caminho no banco
            escritorio.logo_path = f"/static/uploads/logos/{filename}"
            db.session.commit()

            return jsonify({
                "success": True,
                "message": "Logo enviado com sucesso",
                "logo_path": escritorio.logo_path
            }), 200
        else:
            return jsonify({"message": "Tipo de arquivo não permitido"}), 400

    except Exception as e:
        print(f"Erro ao fazer upload do logo: {str(e)}")
        db.session.rollback()
        return jsonify({"message": "Erro interno do servidor"}), 500

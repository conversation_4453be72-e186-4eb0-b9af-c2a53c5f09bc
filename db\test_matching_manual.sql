-- Script de teste para funcionalidade de Matching Manual
-- Execute este script após aplicar a migration principal

-- Verificar se as tabelas foram criadas corretamente
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'matching_manual_temporario'
ORDER BY ordinal_position;

-- Verificar índices criados
SELECT 
    indexname, 
    indexdef 
FROM pg_indexes 
WHERE tablename = 'matching_manual_temporario';

-- Verificar comentários das tabelas
SELECT 
    obj_description(c.oid) as table_comment
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE c.relname = 'matching_manual_temporario' 
AND n.nspname = 'public';

-- Verificar comentários das colunas
SELECT 
    a.attname as column_name,
    col_description(a.attrelid, a.attnum) as column_comment
FROM pg_attribute a
JOIN pg_class c ON a.attrelid = c.oid
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE c.relname = 'matching_manual_temporario' 
AND n.nspname = 'public'
AND a.attnum > 0
AND NOT a.attisdropped
ORDER BY a.attnum;

-- Exemplo de inserção de dados de teste (opcional)
/*
INSERT INTO matching_manual_temporario (
    empresa_id,
    escritorio_id,
    usuario_id,
    sped_item_id,
    sped_descricao,
    sped_codigo,
    xml_items_ids,
    xml_items_data,
    justificativa,
    confianca_usuario
) VALUES (
    1, -- empresa_id (ajustar conforme necessário)
    1, -- escritorio_id (ajustar conforme necessário)
    1, -- usuario_id (ajustar conforme necessário)
    1, -- sped_item_id (ajustar conforme necessário)
    'Produto de Teste SPED',
    'PROD001',
    ARRAY[1, 2], -- IDs dos itens XML
    '[
        {
            "id": 1,
            "descricao": "Produto XML 1",
            "codigo": "XML001",
            "quantidade": 10,
            "valor_total": 100.00,
            "chave_nf": "12345678901234567890123456789012345678901234",
            "numero_nf": "123"
        },
        {
            "id": 2,
            "descricao": "Produto XML 2", 
            "codigo": "XML002",
            "quantidade": 5,
            "valor_total": 50.00,
            "chave_nf": "12345678901234567890123456789012345678901234",
            "numero_nf": "123"
        }
    ]'::jsonb,
    'Teste de matching manual - produtos relacionados',
    4
);
*/

-- Verificar se a inserção funcionou (descomente se executou o INSERT acima)
/*
SELECT
    id,
    empresa_id,
    sped_item_id,
    array_length(xml_items_ids, 1) as qtd_xml_items,
    jsonb_array_length(xml_items_data) as qtd_xml_data,
    status,
    data_criacao
FROM matching_manual_temporario;
*/

-- Testar consulta de itens XML da mesma nota fiscal (ajustar conforme necessário)
/*
-- Primeiro, encontrar um item SPED para teste
SELECT
    ine.id as sped_item_id,
    ne.numero_nf,
    ne.chave_nf,
    pe.descricao as sped_descricao
FROM item_nota_entrada ine
JOIN nota_entrada ne ON ine.nota_entrada_id = ne.id
JOIN produto_entrada pe ON ine.produto_entrada_id = pe.id
WHERE ine.empresa_id = 1  -- Ajustar conforme necessário
LIMIT 5;

-- Depois, buscar itens XML da mesma nota (usar numero_nf do resultado acima)
SELECT DISTINCT
    nfi.id,
    p.codigo as codigo_produto,
    p.descricao as descricao_produto,
    nfi.unidade_comercial,
    nfi.ncm,
    nfi.quantidade,
    nfi.valor_unitario,
    nfi.valor_total,
    nfi.cfop,
    nfi.numero_nf,
    nfi.chave_nf,
    c.razao_social as cliente_nome
FROM nota_fiscal_item nfi
JOIN produto p ON nfi.produto_id = p.id
JOIN cliente c ON nfi.cliente_id = c.id
LEFT JOIN auditoria_comparativa_impostos aci ON nfi.id = aci.xml_item_id
WHERE nfi.empresa_id = 1  -- Ajustar conforme necessário
AND nfi.numero_nf = '217890'  -- Usar numero_nf do item SPED
AND nfi.tipo_nota = '0'  -- Apenas notas de entrada
AND (aci.id IS NULL OR aci.match_type = 'unmatched_xml')  -- Não pareados
ORDER BY p.descricao;
*/

-- Verificar estrutura da tabela nota_fiscal_item
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'nota_fiscal_item'
ORDER BY ordinal_position;

import api from './authService'
import type { Usuario, Empresa } from '@/types/usuarios'

export const usuariosService = {
  async getUsuarios(): Promise<{ usuarios: Usuario[] }> {
    const response = await api.get('/usuarios')
    return response.data
  },

  async getUsuario(id: number): Promise<Usuario> {
    const response = await api.get(`/usuarios/${id}`)
    return response.data.usuario
  },

  async createUsuario(
    data: Partial<Usuario> & { senha?: string; escritorio_id?: number }
  ): Promise<Usuario> {
    const response = await api.post('/usuarios', data)
    return response.data.usuario
  },

  async updateUsuario(
    id: number,
    data: Partial<Usuario> & { senha?: string }
  ): Promise<Usuario> {
    const response = await api.put(`/usuarios/${id}`, data)
    return response.data.usuario
  },

  async getEmpresas(): Promise<Empresa[]> {
    const response = await api.get('/empresas')
    return response.data.empresas || []
  },
}
#!/usr/bin/env python3
"""
Script para debugar os dados de tributos na auditoria comparativa
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'back'))

from models.escritorio import db
from models.tributo import Tributo
from models.auditoria_comparativa_impostos import AuditoriaComparativaImpostos
from models.nota_fiscal_item import NotaFiscalItem
from models.importacao_xml import ImportacaoXML
from sqlalchemy import func
from datetime import datetime

def debug_tributos_xml():
    """Debug dos dados de tributos XML"""
    print("=== DEBUG TRIBUTOS XML ===")
    
    # Buscar algumas notas de entrada recentes
    notas_entrada = db.session.query(ImportacaoXML).filter(
        ImportacaoXML.tipo_nota == '0',  # Entrada
        ImportacaoXML.data_entrada >= datetime(2024, 1, 1)
    ).limit(5).all()
    
    print(f"Encontradas {len(notas_entrada)} notas de entrada para análise")
    
    for nota in notas_entrada:
        print(f"\n--- NOTA: {nota.numero_nf} - Chave: {nota.chave_nf[:10]}... ---")
        
        # Buscar tributos relacionados a esta nota
        tributos = db.session.query(Tributo).filter(
            Tributo.empresa_id == nota.empresa_id,
            Tributo.numero_nf == nota.numero_nf,
            Tributo.data_emissao == nota.data_emissao
        ).all()
        
        print(f"Tributos encontrados: {len(tributos)}")
        
        for tributo in tributos:
            print(f"  Tributo ID: {tributo.id}")
            print(f"    ICMS - Valor: {tributo.icms_valor}, Alíquota: {tributo.icms_aliquota}, BC: {tributo.icms_vbc}")
            print(f"    IPI - Valor: {tributo.ipi_valor}, Alíquota: {tributo.ipi_aliquota}, BC: {tributo.ipi_vbc}")
            print(f"    ICMS-ST - Valor: {tributo.icms_st_valor}, Alíquota: {tributo.icms_st_aliquota}, BC: {tributo.icms_st_vbc}")
            print(f"    PIS - Valor: {tributo.pis_valor}, Alíquota: {tributo.pis_aliquota}, BC: {tributo.pis_vbc}")
            print(f"    COFINS - Valor: {tributo.cofins_valor}, Alíquota: {tributo.cofins_aliquota}, BC: {tributo.cofins_vbc}")
            print()

def debug_auditoria_comparativa():
    """Debug dos dados na auditoria comparativa"""
    print("\n=== DEBUG AUDITORIA COMPARATIVA ===")
    
    # Buscar alguns registros de auditoria
    auditorias = db.session.query(AuditoriaComparativaImpostos).limit(5).all()
    
    print(f"Encontrados {len(auditorias)} registros de auditoria para análise")
    
    for auditoria in auditorias:
        print(f"\n--- AUDITORIA ID: {auditoria.id} - Nota: {auditoria.numero_nf} ---")
        print(f"  XML ICMS - Valor: {auditoria.xml_icms_valor}, Alíquota: {auditoria.xml_icms_aliquota}, BC: {auditoria.xml_icms_bc}")
        print(f"  XML IPI - Valor: {auditoria.xml_ipi_valor}, Alíquota: {auditoria.xml_ipi_aliquota}, BC: {auditoria.xml_ipi_bc}")
        print(f"  XML ICMS-ST - Valor: {auditoria.xml_icms_st_valor}, Alíquota: {auditoria.xml_icms_st_aliquota}, BC: {auditoria.xml_icms_st_bc}")
        print(f"  XML PIS - Valor: {auditoria.xml_pis_valor}, Alíquota: {auditoria.xml_pis_aliquota}, BC: {auditoria.xml_pis_bc}")
        print(f"  XML COFINS - Valor: {auditoria.xml_cofins_valor}, Alíquota: {auditoria.xml_cofins_aliquota}, BC: {auditoria.xml_cofins_bc}")
        
        # Verificar se existe tributo relacionado
        if auditoria.xml_item_id:
            xml_item = db.session.get(NotaFiscalItem, auditoria.xml_item_id)
            if xml_item:
                tributo = db.session.query(Tributo).filter(
                    Tributo.empresa_id == xml_item.empresa_id,
                    Tributo.cliente_id == xml_item.cliente_id,
                    Tributo.produto_id == xml_item.produto_id,
                    Tributo.data_emissao == xml_item.data_emissao,
                    Tributo.numero_nf == xml_item.numero_nf
                ).first()
                
                if tributo:
                    print(f"  TRIBUTO RELACIONADO ID: {tributo.id}")
                    print(f"    ICMS - Valor: {tributo.icms_valor}, Alíquota: {tributo.icms_aliquota}")
                    print(f"    IPI - Valor: {tributo.ipi_valor}, Alíquota: {tributo.ipi_aliquota}")
                    print(f"    PIS - Valor: {tributo.pis_valor}, Alíquota: {tributo.pis_aliquota}")
                    print(f"    COFINS - Valor: {tributo.cofins_valor}, Alíquota: {tributo.cofins_aliquota}")
                else:
                    print("  NENHUM TRIBUTO RELACIONADO ENCONTRADO!")
        print()

def debug_metodo_get_xml_item_tributos():
    """Debug do método _get_xml_item_tributos"""
    print("\n=== DEBUG MÉTODO _get_xml_item_tributos ===")

    from services.auditoria_comparativa_service import AuditoriaComparativaService
    
    # Buscar um item XML para teste
    xml_item = db.session.query(NotaFiscalItem).first()
    if not xml_item:
        print("Nenhum item XML encontrado!")
        return
    
    print(f"Testando com XML Item ID: {xml_item.id}")
    
    # Simular o serviço
    service = AuditoriaComparativaService(xml_item.empresa_id, 1)  # usuario_id = 1
    
    # Chamar o método
    xml_tributos = service._get_xml_item_tributos(xml_item.id)
    
    if xml_tributos:
        print("Dados retornados pelo método:")
        for key, value in xml_tributos.items():
            print(f"  {key}: {value}")
    else:
        print("Método retornou None!")

if __name__ == "__main__":
    from app import create_app

    app_result = create_app()
    # Se create_app retorna uma tupla, pegar o primeiro elemento
    if isinstance(app_result, tuple):
        app = app_result[0]
    else:
        app = app_result

    with app.app_context():
        debug_tributos_xml()
        debug_auditoria_comparativa()
        debug_metodo_get_xml_item_tributos()

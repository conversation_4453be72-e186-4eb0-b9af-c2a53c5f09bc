from .escritorio import db
from sqlalchemy.sql import func

class Produto(db.Model):
    __tablename__ = 'produto'
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.<PERSON>, db.<PERSON>ey('escritorio.id'), nullable=False)
    codigo = db.Column(db.String(50), nullable=False)
    descricao = db.Column(db.String(255), nullable=False)
    # Colunas ncm e cfop removidas - agora estão apenas nas tabelas nota_fiscal_item e cenarios
    unidade_comercial = db.Column(db.String(10))
    unidade_tributavel = db.Column(db.String(10))
    codigo_ean = db.Column(db.String(50))  # Novo campo para cEAN
    codigo_ean_tributavel = db.Column(db.String(50))  # Novo campo para cEANTrib
    unidade_tributaria = db.Column(db.String(10))  # Novo campo para uTrib
    tipo_sped = db.Column(db.String(50))  # Novo campo para tipo SPED
    cest = db.Column(db.String(10))  # Código Especificador da Substituição Tributária
    data_cadastro = db.Column(db.DateTime, server_default=func.now())
    status = db.Column(db.String(20), default='novo')

    # Relacionamentos
    tributos = db.relationship('Tributo', backref='produto', lazy=True)

    def __repr__(self):
        return f"<Produto {self.codigo} - {self.descricao}>"

    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'codigo': self.codigo,
            'descricao': self.descricao,
            # Campos ncm e cfop removidos
            'unidade_comercial': self.unidade_comercial,
            'unidade_tributavel': self.unidade_tributavel,
            'codigo_ean': self.codigo_ean,
            'codigo_ean_tributavel': self.codigo_ean_tributavel,
            'unidade_tributaria': self.unidade_tributaria,
            'tipo_sped': self.tipo_sped,
            'cest': self.cest,
            'data_cadastro': self.data_cadastro.isoformat() if self.data_cadastro else None,
            'status': self.status
        }

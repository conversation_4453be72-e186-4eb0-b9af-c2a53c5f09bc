-- =====================================================
-- CORREÇÃO EMERGENCIAL DO BANCO DE DADOS
-- Corrige problemas de encoding em TODAS as tabelas
-- =====================================================

-- Definir encoding correto
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

-- Desabilitar verificações temporariamente
SET session_replication_role = replica;

-- Função para limpar texto com encoding problemático
CREATE OR REPLACE FUNCTION emergency_clean_text(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
    IF input_text IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Tentar converter para UTF-8 válido
    BEGIN
        -- Primeiro, tentar conversão direta
        RETURN convert_from(convert_to(input_text, 'UTF8'), 'UTF8');
    EXCEPTION
        WHEN OTHERS THEN
            -- Se falhar, limpar caracteres problemáticos
            BEGIN
                RETURN regexp_replace(input_text, '[^\x09\x0A\x0D\x20-\x7E\x80-\xFF]', '', 'g');
            EXCEPTION
                WHEN OTHERS THEN
                    -- Último recurso: manter apenas ASCII básico
                    RETURN regexp_replace(input_text, '[^\x20-\x7E]', '', 'g');
            END;
    END;
END;
$$ LANGUAGE plpgsql;

-- CORRIGIR TABELA USUARIO (problema no login)
UPDATE usuario SET 
    nome = emergency_clean_text(nome),
    email = emergency_clean_text(email),
    tipo_usuario = COALESCE(emergency_clean_text(tipo_usuario), 'usuario')
WHERE nome IS NOT NULL OR email IS NOT NULL;

-- Garantir que emails sejam válidos
UPDATE usuario SET email = '<EMAIL>' WHERE email IS NULL OR email = '' OR email !~ '^[^@]+@[^@]+\.[^@]+$';

-- CORRIGIR TABELA ESCRITORIO
UPDATE escritorio SET 
    nome = emergency_clean_text(nome),
    responsavel = emergency_clean_text(responsavel),
    endereco = emergency_clean_text(endereco),
    cidade = emergency_clean_text(cidade),
    estado = emergency_clean_text(estado),
    cep = emergency_clean_text(cep),
    telefone = emergency_clean_text(telefone),
    email = emergency_clean_text(email)
WHERE nome IS NOT NULL;

-- CORRIGIR TABELA EMPRESA
UPDATE empresa SET 
    nome = emergency_clean_text(nome),
    cnpj = emergency_clean_text(cnpj),
    inscricao_estadual = emergency_clean_text(inscricao_estadual),
    endereco = emergency_clean_text(endereco),
    cidade = emergency_clean_text(cidade),
    estado = emergency_clean_text(estado),
    cep = emergency_clean_text(cep),
    telefone = emergency_clean_text(telefone),
    email = emergency_clean_text(email),
    responsavel = emergency_clean_text(responsavel)
WHERE nome IS NOT NULL;

-- CORRIGIR TABELA CLIENTE
UPDATE cliente SET 
    nome = emergency_clean_text(nome),
    cnpj = emergency_clean_text(cnpj),
    cpf = emergency_clean_text(cpf),
    inscricao_estadual = emergency_clean_text(inscricao_estadual),
    endereco = emergency_clean_text(endereco),
    cidade = emergency_clean_text(cidade),
    uf = emergency_clean_text(uf),
    cep = emergency_clean_text(cep),
    telefone = emergency_clean_text(telefone),
    email = emergency_clean_text(email),
    atividade = emergency_clean_text(atividade),
    destino = emergency_clean_text(destino)
WHERE nome IS NOT NULL;

-- CORRIGIR TABELA PRODUTO
UPDATE produto SET 
    nome = emergency_clean_text(nome),
    ncm = emergency_clean_text(ncm),
    cest = emergency_clean_text(cest),
    unidade = emergency_clean_text(unidade),
    descricao = emergency_clean_text(descricao)
WHERE nome IS NOT NULL;

-- CORRIGIR TABELAS DE CENÁRIOS
UPDATE cenario_icms SET 
    status = COALESCE(emergency_clean_text(status), 'novo'),
    cst = emergency_clean_text(cst),
    cfop = emergency_clean_text(cfop),
    ncm = emergency_clean_text(ncm),
    origem = emergency_clean_text(origem),
    mod_bc = emergency_clean_text(mod_bc),
    direcao = COALESCE(emergency_clean_text(direcao), 'saida'),
    tipo_operacao = COALESCE(emergency_clean_text(tipo_operacao), '1')
WHERE id IS NOT NULL;

UPDATE cenario_icms_st SET 
    status = COALESCE(emergency_clean_text(status), 'novo'),
    cst = emergency_clean_text(cst),
    cfop = emergency_clean_text(cfop),
    ncm = emergency_clean_text(ncm),
    origem = emergency_clean_text(origem),
    mod_bc = emergency_clean_text(mod_bc),
    icms_st_mod_bc = emergency_clean_text(icms_st_mod_bc),
    direcao = COALESCE(emergency_clean_text(direcao), 'saida'),
    tipo_operacao = COALESCE(emergency_clean_text(tipo_operacao), '1')
WHERE id IS NOT NULL;

UPDATE cenario_ipi SET 
    status = COALESCE(emergency_clean_text(status), 'novo'),
    cst = emergency_clean_text(cst),
    cfop = emergency_clean_text(cfop),
    ncm = emergency_clean_text(ncm),
    codigo_enquadramento = emergency_clean_text(codigo_enquadramento),
    ex = emergency_clean_text(ex),
    direcao = COALESCE(emergency_clean_text(direcao), 'saida'),
    tipo_operacao = COALESCE(emergency_clean_text(tipo_operacao), '1')
WHERE id IS NOT NULL;

UPDATE cenario_pis SET 
    status = COALESCE(emergency_clean_text(status), 'novo'),
    cst = emergency_clean_text(cst),
    cfop = emergency_clean_text(cfop),
    ncm = emergency_clean_text(ncm),
    direcao = COALESCE(emergency_clean_text(direcao), 'saida'),
    tipo_operacao = COALESCE(emergency_clean_text(tipo_operacao), '1')
WHERE id IS NOT NULL;

UPDATE cenario_cofins SET 
    status = COALESCE(emergency_clean_text(status), 'novo'),
    cst = emergency_clean_text(cst),
    cfop = emergency_clean_text(cfop),
    ncm = emergency_clean_text(ncm),
    direcao = COALESCE(emergency_clean_text(direcao), 'saida'),
    tipo_operacao = COALESCE(emergency_clean_text(tipo_operacao), '1')
WHERE id IS NOT NULL;

UPDATE cenario_difal SET 
    status = COALESCE(emergency_clean_text(status), 'novo'),
    cst = emergency_clean_text(cst),
    cfop = emergency_clean_text(cfop),
    ncm = emergency_clean_text(ncm),
    origem = emergency_clean_text(origem),
    mod_bc = emergency_clean_text(mod_bc),
    direcao = COALESCE(emergency_clean_text(direcao), 'saida'),
    tipo_operacao = COALESCE(emergency_clean_text(tipo_operacao), '1')
WHERE id IS NOT NULL;

-- CORRIGIR TABELA NOTA_FISCAL_ITEM
UPDATE nota_fiscal_item SET 
    numero_nf = emergency_clean_text(numero_nf),
    chave_nf = emergency_clean_text(chave_nf),
    cfop = emergency_clean_text(cfop),
    ncm = emergency_clean_text(ncm),
    unidade = emergency_clean_text(unidade),
    tipo_operacao = COALESCE(emergency_clean_text(tipo_operacao), '1')
WHERE id IS NOT NULL;

-- CORRIGIR TABELA TRIBUTO
UPDATE tributo SET 
    icms_cst = emergency_clean_text(icms_cst),
    icms_origem = emergency_clean_text(icms_origem),
    icms_mod_bc = emergency_clean_text(icms_mod_bc),
    ipi_cst = emergency_clean_text(ipi_cst),
    ipi_codigo_enquadramento = emergency_clean_text(ipi_codigo_enquadramento),
    pis_cst = emergency_clean_text(pis_cst),
    cofins_cst = emergency_clean_text(cofins_cst)
WHERE id IS NOT NULL;

-- CORRIGIR TABELA AUDITORIA_RESULTADO
UPDATE auditoria_resultado SET 
    tipo_tributo = emergency_clean_text(tipo_tributo),
    status = COALESCE(emergency_clean_text(status), 'pendente')
WHERE id IS NOT NULL;

-- Garantir que todos os status sejam válidos
UPDATE cenario_icms SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_icms_st SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_ipi SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_pis SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_cofins SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_difal SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;

-- Garantir que tipos de usuário sejam válidos
UPDATE usuario SET tipo_usuario = 'usuario' WHERE tipo_usuario NOT IN ('admin', 'escritorio', 'usuario') OR tipo_usuario IS NULL;

-- Reabilitar verificações
SET session_replication_role = DEFAULT;

-- Remover função temporária
DROP FUNCTION IF EXISTS emergency_clean_text(TEXT);

-- VACUUM para limpar
VACUUM ANALYZE usuario;
VACUUM ANALYZE escritorio;
VACUUM ANALYZE empresa;
VACUUM ANALYZE cliente;
VACUUM ANALYZE produto;
VACUUM ANALYZE cenario_icms;
VACUUM ANALYZE cenario_icms_st;
VACUUM ANALYZE cenario_ipi;
VACUUM ANALYZE cenario_pis;
VACUUM ANALYZE cenario_cofins;
VACUUM ANALYZE cenario_difal;
VACUUM ANALYZE nota_fiscal_item;
VACUUM ANALYZE tributo;
VACUUM ANALYZE auditoria_resultado;

-- Commit final
COMMIT;

-- Mensagem de confirmação
DO $$
BEGIN
    RAISE NOTICE '=== BANCO DE DADOS CORRIGIDO ===';
    RAISE NOTICE 'Problemas de encoding foram corrigidos em todas as tabelas.';
    RAISE NOTICE 'Sistema deve estar funcionando normalmente agora.';
    RAISE NOTICE 'Tente fazer login novamente.';
END $$;

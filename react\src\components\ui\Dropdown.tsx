import { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'

interface DropdownOption {
  value: string
  label: string
  icon?: React.ReactNode
  disabled?: boolean
}

interface DropdownProps {
  options: DropdownOption[]
  value?: string | string[]
  placeholder?: string
  searchPlaceholder?: string
  onChange: (value: string | string[]) => void
  disabled?: boolean
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'modern'
  multiple?: boolean
  searchable?: boolean
  loading?: boolean
  onSearch?: (term: string) => void
}

export function Dropdown({
  options,
  value,
  placeholder = 'Selecione...',
  searchPlaceholder = 'Buscar...',
  onChange,
  disabled = false,
  className = '',
  size = 'md',
  variant = 'modern',
  multiple = false,
  searchable = false,
  loading = false,
  onSearch
}: DropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const selectedValues = Array.isArray(value) ? value : value ? [value] : []
  const selectedOptions = options.filter(opt => selectedValues.includes(opt.value))
  
  const filteredOptions = searchable 
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  useEffect(() => {
    if (onSearch) {
      const timeoutId = setTimeout(() => {
        onSearch(searchTerm)
      }, 300)
      
      return () => clearTimeout(timeoutId)
    }
  }, [searchTerm]) // Removido onSearch das dependências

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-sm',
    lg: 'px-5 py-4 text-base'
  }

  const handleSelect = (option: DropdownOption) => {
    if (option.disabled) return

    if (multiple) {
      const newValues = selectedValues.includes(option.value)
        ? selectedValues.filter(v => v !== option.value)
        : [...selectedValues, option.value]
      onChange(newValues)
    } else {
      onChange(option.value)
      setIsOpen(false)
      setSearchTerm('')
    }
  }

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen)
      if (!isOpen && searchable) {
        setTimeout(() => inputRef.current?.focus(), 100)
      }
    }
  }

  const getDisplayText = () => {
    if (selectedOptions.length === 0) {
      return placeholder
    }
    
    if (multiple) {
      if (selectedOptions.length === 1) {
        return selectedOptions[0].label
      }
      return `${selectedOptions.length} itens selecionados`
    }
    
    return selectedOptions[0]?.label || placeholder
  }

  if (variant === 'default') {
    return (
      <select
        value={Array.isArray(value) ? '' : value || ''}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className={`modern-select ${sizeClasses[size]} ${className}`}
        multiple={multiple}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value} disabled={option.disabled}>
            {option.label}
          </option>
        ))}
      </select>
    )
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        type="button"
        onClick={toggleDropdown}
        disabled={disabled || loading}
        className={`
          w-full ${sizeClasses[size]} text-left
          bg-white dark:bg-gray-800 
          border border-gray-300 dark:border-gray-600 
          rounded-lg shadow-sm
          hover:border-primary-400 dark:hover:border-primary-500
          focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500
          disabled:opacity-50 disabled:cursor-not-allowed
          transition-all duration-200
          flex items-center justify-between
          ${isOpen ? 'ring-2 ring-primary-500/20 border-primary-500' : ''}
        `}
      >
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {selectedOptions.length > 0 && selectedOptions[0]?.icon && !multiple && (
            <div className="flex-shrink-0 text-gray-600">
              {selectedOptions[0].icon}
            </div>
          )}
          <span className={`truncate ${selectedOptions.length > 0 ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}`}>
            {getDisplayText()}
          </span>
        </div>
        
        {loading ? (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
        ) : (
          <svg
            className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl overflow-hidden">
          {/* Search Input */}
          {searchable && (
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <input
                ref={inputRef}
                type="text"
                placeholder={searchPlaceholder}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500"
              />
            </div>
          )}

          {/* Select All / Clear All for multiple */}
          {multiple && filteredOptions.length > 0 && (
            <div className="p-2 border-b border-gray-200 dark:border-gray-700 flex gap-2">
              <button
                onClick={() => onChange(filteredOptions.map(opt => opt.value))}
                className="px-3 py-1 text-xs bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300 rounded hover:bg-primary-200 dark:hover:bg-primary-900/40 transition-colors"
              >
                Selecionar Todos
              </button>
              <button
                onClick={() => onChange([])}
                className="px-3 py-1 text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Limpar
              </button>
            </div>
          )}

          {/* Options */}
          <div className="max-h-60 overflow-y-auto">
            {filteredOptions.length === 0 ? (
              <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 text-center">
                Nenhuma opção encontrada
              </div>
            ) : (
              filteredOptions.map((option) => {
                const isSelected = selectedValues.includes(option.value)
                
                return (
                  <button
                    key={option.value}
                    onClick={() => handleSelect(option)}
                    disabled={option.disabled}
                    className={`
                      w-full px-4 py-3 text-left text-sm
                      hover:bg-gray-50 dark:hover:bg-gray-700
                      focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700
                      disabled:opacity-50 disabled:cursor-not-allowed
                      transition-colors duration-150
                      flex items-center gap-3
                      ${isSelected ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300' : 'text-gray-900 dark:text-white'}
                    `}
                  >
                    {multiple && (
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => {}} // Handled by button click
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                    )}
                    
                    {option.icon && (
                      <div className="flex-shrink-0 text-gray-400">
                        {option.icon}
                      </div>
                    )}
                    
                    <span className="flex-1 truncate">{option.label}</span>
                    
                    {!multiple && isSelected && (
                      <svg className="w-4 h-4 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </button>
                )
              })
            )}
          </div>
        </div>
      )}
    </div>
  )
}
import { useState } from 'react'
import {
  icmsStValidationService,
  ICMSSTValidationResult,
} from '@/services/icmsStValidationService'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'

interface Props {
  isOpen: boolean
  result: ICMSSTValidationResult | null
  onClose: () => void
  onApplied?: () => void
  onFilter?: (ids: number[]) => void
  onClearFilters?: () => void
}

export function ICMSSTValidationModal({
  isOpen,
  result,
  onClose,
  onApplied,
  onFilter,
  onClearFilters,
}: Props) {
  const [selected, setSelected] = useState<Set<number>>(new Set())
  const [loading, setLoading] = useState(false)

  if (!isOpen || !result) return null

  const toggle = (index: number, checked: boolean) => {
    const newSet = new Set(selected)
    if (checked) newSet.add(index)
    else newSet.delete(index)
    setSelected(newSet)
  }

  const toggleAll = (checked: boolean) => {
    if (checked) {
      setSelected(new Set(result.sugestoes.map((_, i) => i)))
    } else {
      setSelected(new Set())
    }
  }

  const applySuggestions = async (sugs: typeof result.sugestoes) => {
    setLoading(true)
    for (const s of sugs) {
      try {
        await icmsStValidationService.applySuggestion(
          s.cenario_id,
          s.divergencias || []
        )
      } catch (e) {
        console.error(e)
      }
    }
    setLoading(false)
    onApplied?.()
    onClose()
  }

  const hasCritical = (s: any) =>
    (s.divergencias || []).some((d: any) =>
      ['ncm', 'cest', 'vigencia'].includes(d.campo)
    )

  const handleApplyAll = () => {
    const applicable = result.sugestoes.filter((s) => !hasCritical(s))
    applySuggestions(applicable)
  }

  const handleApplySelected = () => {
    const applicable = result.sugestoes.filter(
      (_, i) => selected.has(i) && !hasCritical(result.sugestoes[i])
    )
    applySuggestions(applicable)
  }

  const handleFilterSelected = () => {
    if (!onFilter) return
    const ids = result.sugestoes
      .filter((_, i) => selected.has(i))
      .map((s) => s.cenario_id)
    if (ids.length > 0) {
      onFilter(ids)
      onClose()
    }
  }

  const getProblemaTexto = (s: any) => {
    const divergencias = s.divergencias || []
    const problemasCriticos = divergencias.filter((d: any) =>
      ['ncm', 'cest', 'vigencia'].includes(d.campo)
    )
    const problemasCorrigiveis = divergencias.filter(
      (d: any) => ['icms_st_aliquota', 'icms_st_p_mva'].includes(d.campo)
    )
    const sugestoesTexto = problemasCorrigiveis
      .map((d: any) => {
        if (d.campo === 'icms_st_aliquota')
          return `Alíq. ST: ${d.valor_atual}% → ${d.valor_sugerido}%`
        if (d.campo === 'icms_st_p_mva')
          return `MVA: ${d.valor_atual}% → ${d.valor_sugerido}%`
        return ''
      })
      .filter(Boolean)
      .join('; ')
    const problemasCriticosTexto = problemasCriticos
      .map((d: any) => {
        if (d.campo === 'ncm') return 'NCM não encontrado'
        if (d.campo === 'cest')
          return `CEST ${d.valor_atual || 'não informado'} ${
            d.valor_atual ? 'não encontrado' : 'obrigatório'
          }`
        if (d.campo === 'vigencia') return 'Vigência expirada'
        return d.motivo
      })
      .join('; ')

    if (problemasCriticos.length > 0) return problemasCriticosTexto
    if (sugestoesTexto) return sugestoesTexto
    return 'Nenhum problema'
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Validação ICMS-ST"
      size="2xl"
      footer={
        <Button
          variant="ghost"
          onClick={onClose}
        >
          Fechar
        </Button>
      }
    >
      <div className="space-y-6">
        <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          {result.message} Total de problemas: <span className="font-semibold">{result.total_sugestoes}</span>
        </div>

        {result.sugestoes.length > 0 && (
          <div className="flex flex-wrap gap-3">
            <div className="flex gap-2">
              <Button
                variant="success"
                size="sm"
                onClick={handleApplyAll}
                disabled={loading}
                loading={loading}
                icon={
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                }
              >
                Aplicar Todas
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={handleApplySelected}
                disabled={loading || selected.size === 0}
                loading={loading}
                icon={
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                }
              >
                Aplicar Selecionadas ({selected.size})
              </Button>
            </div>
            {onFilter && (
              <div className="flex gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleFilterSelected}
                  disabled={selected.size === 0}
                  icon={
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
                    </svg>
                  }
                >
                  Filtrar Selecionados
                </Button>
                {onClearFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClearFilters}
                  >
                    Limpar Filtros
                  </Button>
                )}
              </div>
            )}
          </div>
        )}

        <TableScrollContainer containerClassName="max-h-[60vh] overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900 sticky top-0">
              <tr>
                <th className="px-4 py-3">
                  <input
                    type="checkbox"
                    className="modern-checkbox"
                    checked={
                      selected.size === result.sugestoes.length &&
                      result.sugestoes.length > 0
                    }
                    ref={(input) => {
                      if (input) {
                        input.indeterminate =
                          selected.size > 0 &&
                          selected.size < result.sugestoes.length
                      }
                    }}
                    onChange={(e) => toggleAll(e.target.checked)}
                  />
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  NCM
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  CEST
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  CFOP
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  CST
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Alíq. ICMS
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Alíq. ST
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  MVA
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Tipo ST
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Problemas/Sugestões
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {result.sugestoes.map((s, i) => {
                const dados = s.dados_originais || {}
                return (
                  <tr key={i} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        className="modern-checkbox"
                        checked={selected.has(i)}
                        onChange={(e) => toggle(i, e.target.checked)}
                      />
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white font-mono">
                      {s.ncm || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white font-mono">
                      {s.cest || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white font-mono">
                      {s.cfop}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white font-mono">
                      {s.cst}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">
                      {dados.aliquota || 0}%
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">
                      {dados.icms_st_aliquota || 0}%
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">
                      {dados.icms_st_p_mva || 0}%
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        s.tipo_st === 'interna' 
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                          : 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
                      }`}>
                        {s.tipo_st === 'interna' ? 'Interna' : 'Interestadual'}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 dark:text-white max-w-xs">
                      <div className="truncate" title={getProblemaTexto(s)}>
                        {getProblemaTexto(s)}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-right">
                      <div className="flex gap-2 justify-end">
                        {onFilter && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              onFilter?.([s.cenario_id])
                              onClose()
                            }}
                            className="text-xs"
                          >
                            Filtrar
                          </Button>
                        )}
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => applySuggestions([s])}
                          disabled={loading || hasCritical(s)}
                          className="text-xs"
                        >
                          Aplicar
                        </Button>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </TableScrollContainer>
      </div>
    </Modal>
  )
}
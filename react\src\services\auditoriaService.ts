import api from './authService'

export interface ExecutarAuditoriaParams {
  empresaId: number
  tipoTributo: string
  tipoOperacao: 'entrada' | 'saida'
  year?: number
  month?: number
  forcarRecalculo?: boolean
}

export interface DashboardSumario {
  valor_total_notas: number
  total_notas: number
  total_produtos: number
  valor_inconsistente_maior: number
  valor_inconsistente_menor: number
  total_inconsistente: number
  total_conforme?: number
  notas_conformes?: number
  notas_inconsistentes?: number
  total_inconsistentes_vistas?: number
  total_inconsistentes_nao_vistas?: number
}

export interface SumarioAuditoria {
  id: number
  empresa_id: number
  escritorio_id: number
  ano: number
  mes: number
  tipo_tributo: string
  total_notas: number
  total_produtos: number
  valor_total_notas: number
  valor_total_cenarios: number
  valor_total_produtos: number
  valor_total_tributo: number
  total_conforme: number
  total_inconsistente: number
  notas_conformes: number
  notas_inconsistentes: number
  valor_inconsistente_maior: number
  valor_inconsistente_menor: number
  data_atualizacao?: string
}

export interface SumariosAuditoriaResponse {
  success: boolean
  sumarios: SumarioAuditoria[]
  message?: string
}

export interface ResultadoInconsistente {
  id: number
  origem: string
  numero: string
  cfop: string
  produto_numero: string
  produto_descricao: string
  ncm: string
  cest: string
  cst: string
  base_calculo: number
  aliquota: number
  valor: number
  status: string
  cenario_status?: string
  data_auditoria?: string
  analista_visualizou: boolean
  observacoes_analista?: string
  data_visualizacao?: string
  usuario_analista_id?: number
  cenario: {
    id: number
    origem: string
    numero: number
    cfop: string
    produto_numero: string
    produto_descricao: string
    ncm: string
    cest: string
    cst: string
    base_calculo: number
    aliquota: number
    valor: number
    status: string
  }
  diferenca: number
  comparacao: {
    base_calculo_diferente: boolean
    aliquota_diferente: boolean
    valor_diferente: boolean
  }
}

export interface DashboardResponse {
  sumario: DashboardSumario
  resultados_inconsistentes: ResultadoInconsistente[]
}

export interface FiltroOption {
  value: string
  related: {
    cfops?: string[]
    ncms?: string[]
    csts?: string[]
    aliquotas?: string[]
  }
}

export interface FiltroOptions {
  cfops: FiltroOption[]
  ncms: FiltroOption[]
  csts: FiltroOption[]
  aliquotas: FiltroOption[]
}

export const auditoriaService = {
  async executar({
    empresaId,
    tipoTributo,
    tipoOperacao,
    year,
    month,
    forcarRecalculo = true,
  }: ExecutarAuditoriaParams) {
    const response = await api.post('/auditoria/executar', {
      empresa_id: empresaId,
      tipo_tributo: tipoTributo,
      tipo_operacao: tipoOperacao === 'entrada' ? 0 : 1,
      year,
      month,
      forcar_recalculo: forcarRecalculo,
    })
    return response.data
  },
  
  async getDashboard({
    empresaId,
    tipoTributo,
    year,
    month,
  }: {
    empresaId: number
    tipoTributo: string
    year: number
    month: number
  }): Promise<DashboardResponse> {
    const response = await api.get('/auditoria/dashboard', {
      params: {
        empresa_id: empresaId,
        tipo_tributo: tipoTributo,
        year,
        month,
      },
    })
    return response.data.dashboard
  },

  async getDashboardFiltros({
    empresaId,
    tipoTributo,
    year,
    month,
  }: {
    empresaId: number
    tipoTributo: string
    year: number
    month: number
  }): Promise<FiltroOptions> {
    const response = await api.get('/auditoria/dashboard/filtros', {
      params: {
        empresa_id: empresaId,
        tipo_tributo: tipoTributo,
        year,
        month,
      },
    })
    return response.data.opcoes
  },

  async getDashboardDetalhamento({
    empresaId,
    tipoTributo,
    year,
    month,
    status = 'inconsistente',
    cfop,
    ncm,
    cst,
    aliquota,
    origem,
    numero,
    produto_numero,
    atividade,
    destinacao,
    analista_visualizou,
  }: {
    empresaId: number
    tipoTributo: string
    year: number
    month: number
    status?: string
    cfop?: string | string[]
    ncm?: string | string[]
    cst?: string | string[]
    aliquota?: string | string[]
    origem?: string
    numero?: string
    produto_numero?: string
    atividade?: string
    destinacao?: string
    analista_visualizou?: string
  }) {
    const params: any = {
      empresa_id: empresaId,
      tipo_tributo: tipoTributo,
      year,
      month,
      status,
    }

    // Adicionar filtros opcionais
    if (origem) params.origem = origem
    if (numero) params.numero = numero
    if (produto_numero) params.produto_numero = produto_numero
    if (atividade) params.atividade = atividade
    if (destinacao) params.destinacao = destinacao
    if (analista_visualizou) params.analista_visualizou = analista_visualizou

    // Adicionar arrays de filtros
    if (cfop) {
      if (Array.isArray(cfop)) {
        cfop.forEach(c => params[`cfop`] = c)
      } else {
        params.cfop = cfop
      }
    }
    if (ncm) {
      if (Array.isArray(ncm)) {
        ncm.forEach(n => params[`ncm`] = n)
      } else {
        params.ncm = ncm
      }
    }
    if (cst) {
      if (Array.isArray(cst)) {
        cst.forEach(c => params[`cst`] = c)
      } else {
        params.cst = cst
      }
    }
    if (aliquota) {
      if (Array.isArray(aliquota)) {
        aliquota.forEach(a => params[`aliquota`] = a)
      } else {
        params.aliquota = aliquota
      }
    }

    const response = await api.get('/auditoria/dashboard/detalhamento', {
      params,
      paramsSerializer: {
        indexes: null,
      },
    })
    return response.data
  },

  async marcarComoVista(resultadoId: number, observacoes?: string) {
    const response = await api.post(`/auditoria/marcar-vista/${resultadoId}`, {
      observacoes,
    })
    return response.data
  },

  async marcarSelecionadosComoVista(resultadoIds: number[], observacoes?: string) {
    const response = await api.post('/auditoria/marcar-vista-multiplo', {
      resultado_ids: resultadoIds,
      observacoes,
    })
    return response.data
  },

  async obterDetalhes(tipo: 'nota' | 'cenario', id: number, empresaId: number) {
    const response = await api.get(`/auditoria/detalhes/${tipo}/${id}`, {
      params: { empresa_id: empresaId },
    })
    return response.data.detalhes
  },

  async gerarRelatorio(resultadoId: number) {
    const response = await api.get(`/auditoria/relatorio/${resultadoId}`, {
      responseType: 'blob',
    })
    return response.data
  },

  async atualizarCliente(clienteId: number, dados: { atividade?: string; destinacao?: string }) {
    const response = await api.put(`/clientes/${clienteId}`, dados)
    return response.data
  },

  async gerarRelatorioCompleto(config: {
    empresaId: number
    tipoTributo: string
    year: number
    month: number
    tipo: 'completo' | 'inconsistencias' | 'resumo' | 'comparativo'
    formato: 'pdf' | 'excel'
    incluirDetalhes: boolean
    incluirGraficos: boolean
    filtros: any
  }) {
    const response = await api.post('/auditoria/relatorio/completo', config, {
      responseType: 'blob',
    })
    return response.data
  },

  async getAuditStatus(auditId: string) {
    try {
      const response = await api.get(`/auditoria/status/${auditId}`)
      return response.data
    } catch (error: any) {
      // Se der 404, a auditoria pode ter terminado
      if (error.response?.status === 404) {
        return null
      }
      throw error
    }
  },

  async obterSumarios(empresaId: number, year?: number, month?: number, tipoTributo?: string): Promise<SumariosAuditoriaResponse> {
    const response = await api.get('/api/auditoria/sumarios', {
      params: {
        empresa_id: empresaId,
        year,
        month,
        tipo_tributo: tipoTributo
      }
    })
    return response.data
  },
}
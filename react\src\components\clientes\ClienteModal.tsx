import { useState, useEffect } from 'react'
import { clientesService } from '@/services/clientesService'
import type { Cliente } from '@/types/clientes'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

interface ClienteModalProps {
  cliente?: Cliente | null
  isOpen: boolean
  onClose: () => void
  onSaved: () => void
  empresaId?: number
  atividades?: string[]
  destinacoes?: string[]
  ufs?: string[]
}

const DEFAULT_UFS = [
  'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MT', 'MS', 'MG',
  'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE',
  'TO'
]

export function ClienteModal({
  cliente,
  isOpen,
  onClose,
  onSaved,
  empresaId,
  atividades = [],
  destinacoes = [],
  ufs = DEFAULT_UFS
}: ClienteModalProps) {
  const [form, setForm] = useState<Partial<Cliente>>({})
  const [loading, setLoading] = useState(false)
  const [tab, setTab] = useState<'info' | 'fiscal' | 'endereco'>('info')

  useEffect(() => {
    if (cliente) {
      setForm(cliente)
    } else {
      setForm({})
    }
  }, [cliente])

  if (!isOpen) return null

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target
    setForm((prev) => ({ ...prev, [name]: type === 'checkbox' ? checked : value }))
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      if (cliente) {
        await clientesService.updateCliente(cliente.id, form)
      } else {
        await clientesService.createCliente({ ...form, empresa_id: empresaId })
      }
      onSaved()
      onClose()
    } catch (err: any) {
      alert(err.message || 'Erro ao salvar participante')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={cliente ? 'Editar Participante' : 'Novo Participante'}
      size="lg"
      footer={
        <div className="flex gap-3">
          <Button
            variant="ghost"
            onClick={onClose}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            loading={loading}
            glow
          >
            Salvar
          </Button>
        </div>
      }
    >
      {/* Modern Tabs */}
      <div className="mb-6">
        <div className="flex gap-2 p-1 bg-gray-100 dark:bg-gray-700 rounded-xl">
          <Button
            variant={tab === 'info' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setTab('info')}
            className="flex-1 justify-center"
            glow={tab === 'info'}
          >
            Informações Gerais
          </Button>
          <Button
            variant={tab === 'fiscal' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setTab('fiscal')}
            className="flex-1 justify-center"
            glow={tab === 'fiscal'}
          >
            Informações Fiscais
          </Button>
          <Button
            variant={tab === 'endereco' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setTab('endereco')}
            className="flex-1 justify-center"
            glow={tab === 'endereco'}
          >
            Endereço
          </Button>
        </div>
      </div>

      {tab === 'info' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              CNPJ / CPF*
            </label>
            <input
              type="text"
              name="cnpj"
              value={form.cnpj || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite o CNPJ ou CPF"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Razão Social*
            </label>
            <input
              type="text"
              name="razao_social"
              value={form.razao_social || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite a razão social"
            />
          </div>
          <div className="md:col-span-2 space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Inscrição Estadual
            </label>
            <input
              type="text"
              name="inscricao_estadual"
              value={form.inscricao_estadual || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite a inscrição estadual"
            />
          </div>
        </div>
      )}

      {tab === 'fiscal' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2 space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Natureza Jurídica
            </label>
            <input
              type="text"
              name="natureza_juridica"
              value={form.natureza_juridica || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite a natureza jurídica"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              CNAE
            </label>
            <input
              type="text"
              name="cnae"
              value={form.cnae || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite o CNAE"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Descrição
            </label>
            <input
              type="text"
              name="descricao"
              value={form.descricao || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite a descrição"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Atividade
            </label>
            <select
              name="atividade"
              value={form.atividade || ''}
              onChange={handleChange}
              className="modern-select"
            >
              <option value="">Selecione uma atividade</option>
              {atividades.map((a) => (
                <option key={a} value={a}>
                  {a}
                </option>
              ))}
            </select>
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Destinação
            </label>
            <select
              name="destinacao"
              value={form.destinacao || ''}
              onChange={handleChange}
              className="modern-select"
            >
              <option value="">Selecione uma destinação</option>
              {destinacoes.map((d) => (
                <option key={d} value={d}>
                  {d}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center space-x-3 md:col-span-2 mt-4">
            <input
              id="simples_nacional"
              type="checkbox"
              name="simples_nacional"
              checked={form.simples_nacional || false}
              onChange={handleChange}
              className="modern-checkbox"
            />
            <label
              htmlFor="simples_nacional"
              className="text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Simples Nacional
            </label>
          </div>
        </div>
      )}

      {tab === 'endereco' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              UF
            </label>
            <select
              name="uf"
              value={form.uf || ''}
              onChange={handleChange}
              className="modern-select"
            >
              <option value="">Selecione um estado</option>
              {ufs.map((uf) => (
                <option key={uf} value={uf}>
                  {uf}
                </option>
              ))}
            </select>
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Município
            </label>
            <input
              type="text"
              name="municipio"
              value={form.municipio || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite o município"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Logradouro
            </label>
            <input
              type="text"
              name="logradouro"
              value={form.logradouro || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite o logradouro"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Número
            </label>
            <input
              type="text"
              name="numero"
              value={form.numero || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite o número"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Bairro
            </label>
            <input
              type="text"
              name="bairro"
              value={form.bairro || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite o bairro"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              CEP
            </label>
            <input
              type="text"
              name="cep"
              value={form.cep || ''}
              onChange={handleChange}
              className="modern-input"
              placeholder="Digite o CEP"
            />
          </div>
        </div>
      )}
    </Modal>
  )
}
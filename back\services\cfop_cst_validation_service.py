"""
Serviço de Validação CFOP x CST x Alíquota para cenários IPI
Implementa regras de negócio para validar combinações CFOP/CST/Alíquota
"""

from models import db, CenarioIPI
from models.ipi_validation_result import IPIValidationResult
from sqlalchemy import and_
from decimal import Decimal
import logging
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class CFOPCSTValidationService:
    """Serviço para validação de combinações CFOP x CST x Alíquota"""
    
    # Regras configuráveis CFOP x CST x Alíquota
    # Estrutura: CFOP -> CST -> {'deve_ter_aliquota': bool, 'descricao': str}
    # Propriedades especiais por CFOP:
    # - 'csts_exclusivos': [lista] - Se definido, CFOP só pode ter esses CSTs
    # - 'cst_obrigatorio': 'CST' - Se definido, CFOP deve ter esse CST específico
    REGRAS_CFOP_CST = {
        '5101': {  # Venda de produção do estabelecimento
            # Exemplo: Este CFOP aceita todos os CSTs listados
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada'},
            '54': {'deve_ter_aliquota': False, 'descricao': 'Saída imune'},
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída com suspensão'},
            '99': {'deve_ter_aliquota': False, 'descricao': 'Outras saídas - geralmente sem alíquota'}
        },
        '5102': {  # Venda de mercadoria adquirida ou recebida de terceiros
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada'},
            '54': {'deve_ter_aliquota': False, 'descricao': 'Saída imune'},
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída com suspensão'},
            '99': {'deve_ter_aliquota': False, 'descricao': 'Outras saídas - geralmente sem alíquota'}
        },
        '5116': {  # Venda de produção do estabelecimento originada de encomenda para entrega futura
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada'},
            '54': {'deve_ter_aliquota': False, 'descricao': 'Saída imune'},
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída com suspensão'},
            '99': {'deve_ter_aliquota': False, 'descricao': 'Outras saídas - geralmente sem alíquota'}
        },
        '5118': {  # Venda de produção do estabelecimento entregue ao destinatário por conta e ordem do adquirente originário
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada'},
            '54': {'deve_ter_aliquota': False, 'descricao': 'Saída imune'},
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída com suspensão'},
            '99': {'deve_ter_aliquota': False, 'descricao': 'Outras saídas - geralmente sem alíquota'}
        },
        '5201': {  # Devolução de compra para industrialização
            # Exemplo: Este CFOP só pode ter CST 50 ou 51
            '_csts_exclusivos': ['50', '51'],  # Propriedade especial
            '_cst_obrigatorio': None,  # Nenhum CST obrigatório específico
            '50': {'deve_ter_aliquota': True, 'descricao': 'Entrada tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Entrada tributável com alíquota zero'}
        },
        '5901': {  # Remessa de bem por conta de contrato de comodato
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5902': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5903': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5905': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5908': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5909': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5910': {  # Remessa de mercadoria por conta e ordem de terceiros, em venda à ordem
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada'},
            '54': {'deve_ter_aliquota': False, 'descricao': 'Saída imune'},
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída com suspensão'},
            '99': {'deve_ter_aliquota': False, 'descricao': 'Outras saídas - geralmente sem alíquota'}
        },
        '5911': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '5912': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '5913': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '5914': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5915': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5916': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5917': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '5918': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '5919': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5920': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '53',  # CST obrigatório
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5921': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '53',  # CST obrigatório
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5922': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '5923': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5924': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '5949': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6101': {  # Venda de produção do estabelecimento (interestadual)
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada'},
            '54': {'deve_ter_aliquota': False, 'descricao': 'Saída imune'},
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída com suspensão'},
            '99': {'deve_ter_aliquota': False, 'descricao': 'Outras saídas - geralmente sem alíquota'}
        },
        '6102': {  # Venda de mercadoria adquirida ou recebida de terceiros (interestadual)
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada'},
            '54': {'deve_ter_aliquota': False, 'descricao': 'Saída imune'},
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída com suspensão'},
            '99': {'deve_ter_aliquota': False, 'descricao': 'Outras saídas - geralmente sem alíquota'}
        },
        '6118': {  # Venda de produção do estabelecimento entregue ao destinatário por conta e ordem do adquirente originário (interestadual)
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada'},
            '54': {'deve_ter_aliquota': False, 'descricao': 'Saída imune'},
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída com suspensão'},
            '99': {'deve_ter_aliquota': False, 'descricao': 'Outras saídas - geralmente sem alíquota'}
        },
        '6901': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6902': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6903': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6905': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6908': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6909': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6910': {  # Remessa de mercadoria por conta e ordem de terceiros, em venda à ordem
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
            '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada'},
            '54': {'deve_ter_aliquota': False, 'descricao': 'Saída imune'},
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída com suspensão'},
            '99': {'deve_ter_aliquota': False, 'descricao': 'Outras saídas - geralmente sem alíquota'}
        },
        '6911': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '5612': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '6913': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '6914': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6915': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6916': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6917': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '6918': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '6919': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6920': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6921': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6922': {  # Remessa de produção do estabelecimento, com fim específico de exportação
            # Exemplo: Exportação geralmente só pode ser CST 52 (isenta) ou 53 (não tributada)
            '_csts_exclusivos': ['50', '52', '53'],  # Só permite estes CSTs
            '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - exportação'},
            '52': {'deve_ter_aliquota': False, 'descricao': 'Saída isenta - exportação'},
            '53': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - exportação'}
        },
        '6923': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '99',  # CST obrigatório
            '99': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
        '6924': {  # Remessa de bem por conta de contrato de comodato
            # Exemplo: Este CFOP deve ter obrigatoriamente CST 53 (não tributada)
            '_cst_obrigatorio': '55',  # CST obrigatório
            '55': {'deve_ter_aliquota': False, 'descricao': 'Saída não tributada - comodato sempre não tributado'}
        },
    }
    
    def __init__(self):
        self.logger = logger

    def validar_cenarios_empresa(self, empresa_id: int, filtros: Dict = None) -> Dict:
        """
        Valida combinações CFOP x CST x Alíquota de uma empresa
        
        Args:
            empresa_id (int): ID da empresa
            filtros (Dict, optional): Filtros adicionais para os cenários
            
        Returns:
            Dict: Resultado da validação com sugestões
        """
        try:
            # Buscar cenários IPI de saída da empresa
            cenarios = self._buscar_cenarios_para_validacao(empresa_id, filtros)
            
            if not cenarios:
                return {
                    'success': True,
                    'total_cenarios': 0,
                    'cenarios_com_sugestoes': 0,
                    'sugestoes': [],
                    'message': 'Nenhum cenário encontrado para validação CFOP x CST'
                }
            
            # Validar cada cenário
            sugestoes = []
            for cenario in cenarios:
                sugestao = self._validar_cenario_individual(cenario)
                if sugestao:
                    sugestoes.append(sugestao)
            
            return {
                'success': True,
                'total_cenarios': len(cenarios),
                'cenarios_com_sugestoes': len(sugestoes),
                'sugestoes': sugestoes,
                'message': f'Validação CFOP x CST concluída. {len(sugestoes)} cenários com problemas de {len(cenarios)} analisados.'
            }
            
        except Exception as e:
            self.logger.error(f"Erro na validação CFOP x CST: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Erro interno na validação CFOP x CST'
            }

    def _buscar_cenarios_para_validacao(self, empresa_id: int, filtros: Dict = None) -> List[CenarioIPI]:
        """
        Busca cenários IPI que devem ser validados para CFOP x CST
        """
        # CFOPs que temos regras definidas
        cfops_validos = list(self.REGRAS_CFOP_CST.keys())
        
        query = CenarioIPI.query.filter(
            and_(
                CenarioIPI.empresa_id == empresa_id,
                CenarioIPI.direcao == 'saida',
                CenarioIPI.cfop.in_(cfops_validos)
            )
        )

        # Aplicar filtros adicionais se fornecidos
        if filtros:
            if filtros.get('status'):
                query = query.filter(CenarioIPI.status == filtros['status'])
            if filtros.get('ncm'):
                query = query.filter(CenarioIPI.ncm.like(f"%{filtros['ncm']}%"))
            if filtros.get('cliente_id'):
                query = query.filter(CenarioIPI.cliente_id == filtros['cliente_id'])
            if filtros.get('produto_id'):
                query = query.filter(CenarioIPI.produto_id == filtros['produto_id'])

        return query.all()

    def _validar_cenario_individual(self, cenario: CenarioIPI) -> Optional[Dict]:
        """
        Valida um cenário individual para CFOP x CST x Alíquota
        
        Args:
            cenario (CenarioIPI): Cenário a ser validado
            
        Returns:
            Optional[Dict]: Sugestão de correção ou None se não houver problemas
        """
        cfop = cenario.cfop
        cst = cenario.cst
        aliquota_atual = float(cenario.aliquota) if cenario.aliquota else 0.0
        
        # Verificar se temos regra para este CFOP
        if cfop not in self.REGRAS_CFOP_CST:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'CFOP_SEM_REGRA',
                'descricao': f'CFOP {cfop} não possui regras definidas para validação',
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': None,
                'pode_aplicar_automaticamente': False
            }
        
        # Obter regras do CFOP
        regras_cfop = self.REGRAS_CFOP_CST[cfop]
        
        # Verificar se existe restrição de CSTs exclusivos
        csts_exclusivos = regras_cfop.get('_csts_exclusivos')
        cst_obrigatorio = regras_cfop.get('_cst_obrigatorio')
        
        # Filtrar apenas as regras de CST (excluir propriedades especiais que começam com _)
        csts_validos = [k for k in regras_cfop.keys() if not k.startswith('_')]
        
        # Validação 1: CST obrigatório
        if cst_obrigatorio and cst != cst_obrigatorio:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'CST_OBRIGATORIO_INCORRETO',
                'descricao': f'CFOP {cfop} deve ter obrigatoriamente CST {cst_obrigatorio}, mas está com CST {cst}',
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': {
                    'cst_sugerido': cst_obrigatorio,
                    'motivo': f'CST obrigatório para CFOP {cfop}'
                },
                'pode_aplicar_automaticamente': True
            }
        
        # Validação 2: CSTs exclusivos
        if csts_exclusivos and cst not in csts_exclusivos:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'CST_NAO_PERMITIDO_PARA_CFOP',
                'descricao': f'CFOP {cfop} só permite CSTs: {", ".join(csts_exclusivos)}, mas está com CST {cst}',
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': {
                    'csts_permitidos': csts_exclusivos,
                    'cst_sugerido': self._sugerir_melhor_cst(csts_exclusivos, aliquota_atual),
                    'motivo': f'CSTs exclusivos para CFOP {cfop}'
                },
                'pode_aplicar_automaticamente': len(csts_exclusivos) == 1  # Só aplica automaticamente se há apenas 1 opção
            }
        
        # Validação 3: CST não existe nas regras
        if cst not in csts_validos:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'CST_INVALIDO_PARA_CFOP',
                'descricao': f'CST {cst} não é válido para CFOP {cfop}. CSTs válidos: {", ".join(csts_validos)}',
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': {
                    'csts_validos': csts_validos,
                    'cst_sugerido': self._sugerir_melhor_cst(csts_validos, aliquota_atual)
                },
                'pode_aplicar_automaticamente': False  # Requer decisão manual
            }
        
        # Verificar regra de alíquota para a combinação CFOP x CST
        regra = regras_cfop[cst]
        deve_ter_aliquota = regra['deve_ter_aliquota']
        
        problema = None
        
        if deve_ter_aliquota and aliquota_atual == 0.0:
            problema = {
                'tipo_problema': 'ALIQUOTA_ZERADA_INCORRETA',
                'descricao': f'CFOP {cfop} + CST {cst} deve ter alíquota, mas está zerada. {regra["descricao"]}',
                'sugestao_tipo': 'DEFINIR_ALIQUOTA'
            }
        elif not deve_ter_aliquota and aliquota_atual > 0.0:
            problema = {
                'tipo_problema': 'ALIQUOTA_PREENCHIDA_INCORRETA',
                'descricao': f'CFOP {cfop} + CST {cst} não deve ter alíquota, mas está com {aliquota_atual}%. {regra["descricao"]}',
                'sugestao_tipo': 'ZERAR_ALIQUOTA'
            }
        
        if problema:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': problema['tipo_problema'],
                'descricao': problema['descricao'],
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': {
                    'aliquota_sugerida': 0.0 if problema['sugestao_tipo'] == 'ZERAR_ALIQUOTA' else None,
                    'deve_ter_aliquota': deve_ter_aliquota,
                    'regra_aplicada': regra['descricao']
                },
                'pode_aplicar_automaticamente': problema['sugestao_tipo'] == 'ZERAR_ALIQUOTA'
            }
        
        return None

    def _sugerir_cst_padrao(self, cfop: str, aliquota_atual: float) -> str:
        """
        Sugere um CST padrão baseado no CFOP e alíquota atual
        """
        if aliquota_atual > 0.0:
            return '50'  # Tributado
        else:
            return '51'  # Tributável com alíquota zero

    def _sugerir_melhor_cst(self, csts_disponiveis: List[str], aliquota_atual: float) -> str:
        """
        Sugere o melhor CST baseado na alíquota atual e CSTs disponíveis
        
        Args:
            csts_disponiveis (List[str]): Lista de CSTs válidos
            aliquota_atual (float): Alíquota atual do cenário
            
        Returns:
            str: CST sugerido
        """
        # Se há apenas uma opção, retornar ela
        if len(csts_disponiveis) == 1:
            return csts_disponiveis[0]
        
        # Priorizar baseado na alíquota atual
        if aliquota_atual > 0.0:
            # Se tem alíquota, priorizar CST 50 (tributado)
            if '50' in csts_disponiveis:
                return '50'
            # Senão, pegar o primeiro CST que deve ter alíquota
            for cst in csts_disponiveis:
                if cst in ['50', '99']:  # CSTs que podem ter alíquota
                    return cst
        else:
            # Se não tem alíquota, priorizar CSTs que não devem ter alíquota
            prioridade_sem_aliquota = ['51', '52', '53', '54', '55']
            for cst_prioritario in prioridade_sem_aliquota:
                if cst_prioritario in csts_disponiveis:
                    return cst_prioritario
        
        # Fallback: retornar o primeiro disponível
        return csts_disponiveis[0]

    def _cenario_to_dict(self, cenario: CenarioIPI) -> Dict:
        """
        Converte cenário para dicionário com informações relevantes
        """
        return {
            'id': cenario.id,
            'ncm': cenario.ncm,
            'cfop': cenario.cfop,
            'cst': cenario.cst,
            'aliquota': float(cenario.aliquota) if cenario.aliquota else 0.0,
            'ex': cenario.ex,
            'cliente_id': cenario.cliente_id,
            'produto_id': cenario.produto_id,
            'status': cenario.status
        }

    def aplicar_sugestao(self, cenario_id: int, sugestao: Dict, usuario: str = None) -> Dict:
        """
        Aplica uma sugestão de correção CFOP x CST a um cenário
        """
        try:
            cenario = CenarioIPI.query.get(cenario_id)
            if not cenario:
                return {
                    'success': False,
                    'message': 'Cenário não encontrado'
                }

            # Salvar dados originais para histórico
            dados_originais = self._cenario_to_dict(cenario)

            # Aplicar correções
            if 'cst_sugerido' in sugestao:
                cenario.cst = sugestao['cst_sugerido']

            if 'aliquota_sugerida' in sugestao:
                cenario.aliquota = Decimal(str(sugestao['aliquota_sugerida']))

            # Salvar histórico da validação
            self._salvar_historico_validacao(
                cenario,
                dados_originais,
                sugestao,
                usuario,
                aplicada=True
            )

            db.session.commit()

            return {
                'success': True,
                'message': 'Sugestão CFOP x CST aplicada com sucesso',
                'cenario_atualizado': self._cenario_to_dict(cenario)
            }

        except Exception as e:
            db.session.rollback()
            self.logger.error(f"Erro ao aplicar sugestão CFOP x CST: {str(e)}")
            return {
                'success': False,
                'message': f'Erro ao aplicar sugestão: {str(e)}'
            }

    def _salvar_historico_validacao(self, cenario: CenarioIPI, dados_originais: Dict,
                                   sugestao: Dict, usuario: str = None, aplicada: bool = False):
        """
        Salva o histórico da validação CFOP x CST
        """
        try:
            # Criar registro de validação
            validacao = IPIValidationResult.criar_validacao(
                empresa_id=cenario.empresa_id,
                escritorio_id=cenario.escritorio_id,
                cenario_id=cenario.id,
                tipo_problema='CFOP_CST_ALIQUOTA_INCORRETA',
                descricao_problema=f"Correção CFOP x CST aplicada: {dados_originais.get('cfop')}/{dados_originais.get('cst')} → Alíquota {dados_originais.get('aliquota')}% → {sugestao.get('aliquota_sugerida', 'N/A')}%",
                dados_originais=dados_originais,
                sugestao=sugestao,
                tipi_data={'tipo_validacao': 'CFOP_CST_ALIQUOTA'}
            )

            if aplicada:
                validacao.marcar_como_aplicada(usuario, automatica=True)

        except Exception as e:
            self.logger.error(f"Erro ao salvar histórico de validação CFOP x CST: {str(e)}")

    def obter_regras_configuradas(self) -> Dict:
        """
        Retorna as regras CFOP x CST configuradas para edição
        """
        return self.REGRAS_CFOP_CST

    def atualizar_regras(self, novas_regras: Dict) -> Dict:
        """
        Atualiza as regras CFOP x CST (para futuras implementações de edição)
        """
        # Por enquanto, as regras são estáticas no código
        # Futuramente pode ser implementado salvamento em banco de dados
        return {
            'success': False,
            'message': 'Edição de regras não implementada. Edite o arquivo cfop_cst_validation_service.py'
        }
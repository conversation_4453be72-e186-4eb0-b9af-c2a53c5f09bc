"""
Modelo para armazenar resultados de validação ICMS-ST
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, ForeignKey
from sqlalchemy.sql import func
from .escritorio import db

class ICMSSTValidationResultModel(db.Model):
    __tablename__ = 'icms_st_validation_results'
    
    id = Column(Integer, primary_key=True)
    empresa_id = Column(Integer, ForeignKey('empresa.id'), nullable=False)
    cenario_id = Column(Integer, nullable=False)
    
    # Dados originais do cenário
    dados_originais = Column(JSON, nullable=False)
    
    # Sugestões de correção
    sugestoes = Column(JSON, nullable=False)
    
    # Dados da API consultada
    dados_api = Column(JSON, nullable=True)
    
    # Tipo de validação
    tipo_validacao = Column(String(50), default='ICMS_ST_COMPLETA')  # ICMS_ST_COMPLETA, CFOP_CST, etc.
    
    # Status da validação
    status = Column(String(20), default='pendente')  # pendente, aplicado, ignorado
    
    # Metadados
    data_validacao = Column(DateTime, default=func.now())
    aplicado_em = Column(DateTime, nullable=True)
    aplicado_por = Column(String(100), nullable=True)
    
    # Observações
    observacoes = Column(Text, nullable=True)
    
    def to_dict(self):
        """
        Converte o modelo para dicionário
        """
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'cenario_id': self.cenario_id,
            'dados_originais': self.dados_originais,
            'sugestoes': self.sugestoes,
            'dados_api': self.dados_api,
            'tipo_validacao': self.tipo_validacao,
            'status': self.status,
            'data_validacao': self.data_validacao.isoformat() if self.data_validacao else None,
            'aplicado_em': self.aplicado_em.isoformat() if self.aplicado_em else None,
            'aplicado_por': self.aplicado_por,
            'observacoes': self.observacoes
        }
-- Migração para sistema de notas faltantes com detecção de pulos de numeração
-- Execute este script após implementar as mudanças no sistema

-- 1. Adicionar novos campos à tabela notas_faltantes se não existirem
DO $$
BEGIN
    -- Adicionar campo tipo_faltante se não existir
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'notas_faltantes' AND column_name = 'tipo_faltante') THEN
        ALTER TABLE notas_faltantes ADD COLUMN tipo_faltante VARCHAR(20) DEFAULT 'xml_vs_sped';
    END IF;
    
    -- Adicionar campo numero_inicio se não existir (para pulos de numeração)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'notas_faltantes' AND column_name = 'numero_inicio') THEN
        ALTER TABLE notas_faltantes ADD COLUMN numero_inicio INTEGER;
    END IF;
    
    -- Adicionar campo numero_fim se não existir (para pulos de numeração)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'notas_faltantes' AND column_name = 'numero_fim') THEN
        ALTER TABLE notas_faltantes ADD COLUMN numero_fim INTEGER;
    END IF;
    
    -- Adicionar campo tipo_nota se não existir
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'notas_faltantes' AND column_name = 'tipo_nota') THEN
        ALTER TABLE notas_faltantes ADD COLUMN tipo_nota VARCHAR(1) DEFAULT '0';
    END IF;
END $$;

-- 2. Atualizar registros existentes
UPDATE notas_faltantes 
SET tipo_faltante = 'xml_vs_sped' 
WHERE tipo_faltante IS NULL;

-- 3. Adicionar comentários às colunas
COMMENT ON COLUMN notas_faltantes.tipo_faltante IS 'Tipo de nota faltante: xml_vs_sped, pulo_numeracao';
COMMENT ON COLUMN notas_faltantes.numero_inicio IS 'Número inicial do pulo (para tipo pulo_numeracao)';
COMMENT ON COLUMN notas_faltantes.numero_fim IS 'Número final do pulo (para tipo pulo_numeracao)';
COMMENT ON COLUMN notas_faltantes.tipo_nota IS 'Tipo da nota: 0=entrada, 1=saída';

-- 4. Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_notas_faltantes_tipo_faltante 
ON notas_faltantes(tipo_faltante);

CREATE INDEX IF NOT EXISTS idx_notas_faltantes_tipo_nota 
ON notas_faltantes(tipo_nota);

CREATE INDEX IF NOT EXISTS idx_notas_faltantes_empresa_tipo_mes_ano 
ON notas_faltantes(empresa_id, tipo_nota, mes_referencia, ano_referencia);

-- 5. Criar índices para melhor performance nas consultas de numeração
CREATE INDEX IF NOT EXISTS idx_importacao_xml_empresa_tipo_data_numero 
ON importacao_xml(empresa_id, tipo_nota, data_entrada, numero_nf);

CREATE INDEX IF NOT EXISTS idx_nota_entrada_empresa_tipo_data_numero 
ON nota_entrada(empresa_id, ind_oper, data_entrada_saida, numero_nf);

-- 6. Atualizar constraint única para incluir tipo_faltante
DO $$
BEGIN
    -- Remover constraint antiga se existir
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE table_name = 'notas_faltantes' 
               AND constraint_name = 'uq_notas_faltantes_empresa_chave_origem') THEN
        ALTER TABLE notas_faltantes DROP CONSTRAINT uq_notas_faltantes_empresa_chave_origem;
    END IF;
    
    -- Adicionar nova constraint única
    ALTER TABLE notas_faltantes ADD CONSTRAINT uq_notas_faltantes_empresa_chave_origem_tipo
    UNIQUE (empresa_id, chave_nf, origem, tipo_faltante);
END $$;

-- 7. Limpar dados inconsistentes (opcional - execute com cuidado)
-- DELETE FROM notas_faltantes WHERE status = 'encontrado' AND data_resolucao < NOW() - INTERVAL '30 days';

-- 8. Atualizar estatísticas das tabelas
ANALYZE notas_faltantes;
ANALYZE importacao_xml;
ANALYZE nota_entrada;

-- 9. Verificar integridade dos dados
DO $$
DECLARE
    total_notas_faltantes INTEGER;
    total_xml_sem_chave INTEGER;
    total_sped_sem_chave INTEGER;
BEGIN
    -- Contar total de notas faltantes
    SELECT COUNT(*) INTO total_notas_faltantes FROM notas_faltantes;
    
    -- Contar XMLs sem chave
    SELECT COUNT(*) INTO total_xml_sem_chave 
    FROM importacao_xml 
    WHERE chave_nf IS NULL OR chave_nf = '';
    
    -- Contar notas SPED sem chave
    SELECT COUNT(*) INTO total_sped_sem_chave 
    FROM nota_entrada 
    WHERE chave_nf IS NULL OR chave_nf = '';
    
    -- Exibir relatório
    RAISE NOTICE 'Relatório de Migração:';
    RAISE NOTICE 'Total de notas faltantes: %', total_notas_faltantes;
    RAISE NOTICE 'XMLs sem chave NF: %', total_xml_sem_chave;
    RAISE NOTICE 'Notas SPED sem chave NF: %', total_sped_sem_chave;
    
    IF total_xml_sem_chave > 0 OR total_sped_sem_chave > 0 THEN
        RAISE WARNING 'Existem notas sem chave NF que podem afetar a identificação de faltantes';
    END IF;
END $$;

-- 10. Criar função para limpeza automática de notas faltantes antigas
CREATE OR REPLACE FUNCTION limpar_notas_faltantes_antigas()
RETURNS INTEGER AS $$
DECLARE
    registros_removidos INTEGER;
BEGIN
    -- Remove notas faltantes marcadas como encontradas há mais de 60 dias
    DELETE FROM notas_faltantes 
    WHERE status = 'encontrado' 
    AND data_resolucao < NOW() - INTERVAL '60 days';
    
    GET DIAGNOSTICS registros_removidos = ROW_COUNT;
    
    RETURN registros_removidos;
END;
$$ LANGUAGE plpgsql;

-- 11. Comentário final
COMMENT ON TABLE notas_faltantes IS 'Tabela para controle de notas faltantes - XML vs SPED e pulos de numeração';

-- Fim da migração
-- Execute este script em ambiente de produção após testar em desenvolvimento

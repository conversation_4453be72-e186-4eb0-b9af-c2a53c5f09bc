import { useState, useEffect, useCallback, useRef } from 'react'
import { auditoriaComparativaService, AuditoriaComparativaItem } from '@/services/auditoriaComparativaService'

export interface AuditoriaComparativaFilterState {
  parceiros: string[]
  cfops_sped: string[]
  cfops_xml: string[]
  csts_sped: string[]
  csts_xml: string[]
  csts_sped_pis: string[]
  csts_sped_cofins: string[]
  csts_xml_pis: string[]
  csts_xml_cofins: string[]
  ncms_sped: string[]
  ncms_xml: string[]
  ufs: string[]
  regimes: string[]
  tipos: string[]
  origens_sped: string[]
  origens_xml: string[]
  csosns: string[]
  reducoes_sped: string[]
  reducoes_xml: string[]
  aliquotas_sped: string[]
  aliquotas_xml: string[]
  aliquotas_sped_pis: string[]
  aliquotas_sped_cofins: string[]
  aliquotas_xml_pis: string[]
  aliquotas_xml_cofins: string[]
  status: string[]
  match_types: string[]
  mvas: string[]
  numero_nf?: string
  data_emissao?: string
  produto_codigo_sped?: string
  produto_codigo_nota?: string
  produto_descricao?: string
  descricao_tipo?: string
  produto_nota?: string
}

export interface AuditoriaComparativaFilterOptions {
  parceiros: Array<{ value: string; label: string }>
  cfops_sped: Array<{ value: string; label: string }>
  cfops_xml: Array<{ value: string; label: string }>
  csts_sped: Array<{ value: string; label: string }>
  csts_xml: Array<{ value: string; label: string }>
  csts_sped_pis: Array<{ value: string; label: string }>
  csts_sped_cofins: Array<{ value: string; label: string }>
  csts_xml_pis: Array<{ value: string; label: string }>
  csts_xml_cofins: Array<{ value: string; label: string }>
  ncms_sped: Array<{ value: string; label: string }>
  ncms_xml: Array<{ value: string; label: string }>
  ufs: Array<{ value: string; label: string }>
  regimes: Array<{ value: string; label: string }>
  tipos: Array<{ value: string; label: string }>
  origens_sped: Array<{ value: string; label: string }>
  origens_xml: Array<{ value: string; label: string }>
  csosns: Array<{ value: string; label: string }>
  reducoes_sped: Array<{ value: string; label: string }>
  reducoes_xml: Array<{ value: string; label: string }>
  aliquotas_sped: Array<{ value: string; label: string }>
  aliquotas_xml: Array<{ value: string; label: string }>
  aliquotas_sped_pis: Array<{ value: string; label: string }>
  aliquotas_sped_cofins: Array<{ value: string; label: string }>
  aliquotas_xml_pis: Array<{ value: string; label: string }>
  aliquotas_xml_cofins: Array<{ value: string; label: string }>
  status: Array<{ value: string; label: string }>
  match_types: Array<{ value: string; label: string }>
  mvas: Array<{ value: string; label: string }>
}

const initialFilterState: AuditoriaComparativaFilterState = {
  parceiros: [],
  cfops_sped: [],
  cfops_xml: [],
  csts_sped: [],
  csts_xml: [],
  csts_sped_pis: [],
  csts_sped_cofins: [],
  csts_xml_pis: [],
  csts_xml_cofins: [],
  ncms_sped: [],
  ncms_xml: [],
  ufs: [],
  regimes: [],
  tipos: [],
  origens_sped: [],
  origens_xml: [],
  csosns: [],
  reducoes_sped: [],
  reducoes_xml: [],
  aliquotas_sped: [],
  aliquotas_xml: [],
  aliquotas_sped_pis: [],
  aliquotas_sped_cofins: [],
  aliquotas_xml_pis: [],
  aliquotas_xml_cofins: [],
  status: [],
  match_types: [],
  mvas: [],
  numero_nf: '',
  data_emissao: '',
  produto_codigo_sped: '',
  produto_codigo_nota: '',
  produto_descricao: '',
  descricao_tipo: '',
  produto_nota: '',
}

const initialFilterOptions: AuditoriaComparativaFilterOptions = {
  parceiros: [],
  cfops_sped: [],
  cfops_xml: [],
  csts_sped: [],
  csts_xml: [],
  csts_sped_pis: [],
  csts_sped_cofins: [],
  csts_xml_pis: [],
  csts_xml_cofins: [],
  ncms_sped: [],
  ncms_xml: [],
  ufs: [],
  regimes: [],
  tipos: [],
  origens_sped: [],
  origens_xml: [],
  csosns: [],
  reducoes_sped: [],
  reducoes_xml: [],
  aliquotas_sped: [],
  aliquotas_xml: [],
  aliquotas_sped_pis: [],
  aliquotas_sped_cofins: [],
  aliquotas_xml_pis: [],
  aliquotas_xml_cofins: [],
  status: [],
  match_types: [],
  mvas: [],
}

interface UseAuditoriaComparativaFiltersProps {
  empresaId?: number
  mes: number
  ano: number
  tributo: string
  initialData?: AuditoriaComparativaItem[]
  initialTotal?: number
  onDataChange?: (data: AuditoriaComparativaItem[], total: number) => void
}

export function useAuditoriaComparativaFilters({
  empresaId,
  mes,
  ano,
  tributo,
  initialData = [],
  initialTotal,
  onDataChange,
}: UseAuditoriaComparativaFiltersProps) {
  const [filters, setFilters] = useState<AuditoriaComparativaFilterState>(initialFilterState)
  const [options, setOptions] = useState<AuditoriaComparativaFilterOptions>(initialFilterOptions)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(false)
  const [data, setData] = useState<AuditoriaComparativaItem[]>(initialData)
  const initialTotalCount = initialTotal ?? initialData.length
  const [totalCount, setTotalCount] = useState<number>(initialTotalCount)
  const pageSize = 50
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(initialTotalCount > pageSize)
  const [allData, setAllData] = useState<AuditoriaComparativaItem[]>([])

  // Carregar dados iniciais na primeira execução
  const isFirstLoad = useRef(true)

  const hasActiveFilters = Object.values(filters).some(v =>
    Array.isArray(v) ? v.length > 0 : v
  )
  const textFilterTimeout = useRef<NodeJS.Timeout | null>(null)

  const loadFilterOptions = useCallback(
    async (currentFilters: AuditoriaComparativaFilterState) => {
      if (!empresaId) return
      setIsLoading(true)
      try {
        const response = await auditoriaComparativaService.obterOpcoesFiltro({
          empresaId,
          mes,
          ano,
          tributo,
          filtros: currentFilters,
        })
      const opcoes = response.opcoes || {}
      
      // Verificar formato das alíquotas
      if (opcoes.aliquotas_sped) {
        console.log('Aliquotas SPED disponíveis:', opcoes.aliquotas_sped)
      }
      if (opcoes.aliquotas_xml) {
        console.log('Aliquotas XML disponíveis:', opcoes.aliquotas_xml)
      }
      
      setOptions({
        parceiros: opcoes.parceiros || [],
        cfops_sped: opcoes.cfops_sped || [],
        cfops_xml: opcoes.cfops_xml || [],
        csts_sped: opcoes.csts_sped || [],
        csts_xml: opcoes.csts_xml || [],
        csts_sped_pis: opcoes.csts_sped_pis || [],
        csts_sped_cofins: opcoes.csts_sped_cofins || [],
        csts_xml_pis: opcoes.csts_xml_pis || [],
        csts_xml_cofins: opcoes.csts_xml_cofins || [],
        ncms_sped: opcoes.ncms_sped || [],
        ncms_xml: opcoes.ncms_xml || [],
        ufs: opcoes.ufs || [],
        regimes: opcoes.regimes || [],
        tipos: opcoes.tipos || [],
        origens_sped: opcoes.origens_sped || [],
        origens_xml: opcoes.origens_xml || [],
        csosns: opcoes.csosns || [],
        reducoes_sped: opcoes.reducoes_sped || [],
        reducoes_xml: opcoes.reducoes_xml || [],
        aliquotas_sped: opcoes.aliquotas_sped || [],
        aliquotas_xml: opcoes.aliquotas_xml || [],
        aliquotas_sped_pis: opcoes.aliquotas_sped_pis || [],
        aliquotas_sped_cofins: opcoes.aliquotas_sped_cofins || [],
        aliquotas_xml_pis: opcoes.aliquotas_xml_pis || [],
        aliquotas_xml_cofins: opcoes.aliquotas_xml_cofins || [],
        status: opcoes.status || [],
        match_types: opcoes.match_types || [],
        mvas: opcoes.mvas || [],
      })
    } catch (err) {
      console.error('Erro ao carregar opções de filtros', err)
      setOptions(initialFilterOptions)
    } finally {
      setIsLoading(false)
    }
  }, [empresaId, mes, ano, tributo])

  const loadAllData = useCallback(async () => {
    if (!empresaId) return
    setIsLoadingData(true)
    try {
      const response = await auditoriaComparativaService.buscarPorPeriodo({
        empresaId,
        mes,
        ano,
        tributo,
        filtros: filters,
      })
      const items = response.auditorias || []
      const total = response.total_registros || 0
      setAllData(items)
      setTotalCount(total)
      
      // Aplicar paginação no frontend para a primeira página
      const paginatedItems = items.slice(0, pageSize)
      setData(paginatedItems)
      setHasMore(pageSize < total)
      setCurrentPage(1)
      onDataChange?.(paginatedItems, total)
    } catch (err) {
      console.error('Erro ao carregar todos os dados', err)
      setData([])
      setAllData([])
      setTotalCount(0)
      setHasMore(false)
      setCurrentPage(1)
    } finally {
      setIsLoadingData(false)
    }
  }, [empresaId, mes, ano, tributo, filters, onDataChange, pageSize])

  const loadFilteredData = useCallback(
    async (
      currentFilters: AuditoriaComparativaFilterState,
      page = 1,
      append = false
    ) => {
      // Verificar se há filtros ativos
      const hasActiveFilters = Object.values(currentFilters).some(v => 
        Array.isArray(v) ? v.length > 0 : v
      );
      
      // Se for para carregar dados filtrados, buscar do backend
      if (hasActiveFilters) {
        if (!empresaId) return
        setIsLoadingData(true)
        try {
          const response = await auditoriaComparativaService.buscarPorPeriodo({
            empresaId,
            mes,
            ano,
            tributo,
            filtros: currentFilters,
            page,
            pageSize,
          })
          const items = response.auditorias || []
          const total = response.total_registros || items.length

          // Alguns ambientes podem ignorar parâmetros de paginação e retornar
          // todos os registros. Garante que apenas os itens da página atual
          // sejam exibidos e calcula corretamente se há mais páginas.
          const startIndex = (page - 1) * pageSize
          const paginatedItems =
            items.length > pageSize
              ? items.slice(startIndex, startIndex + pageSize)
              : items

          setTotalCount(total)
          setHasMore(startIndex + paginatedItems.length < total)
          setCurrentPage(page)
          if (append) {
            setData(prev => {
              const newData = [...prev, ...paginatedItems]
              onDataChange?.(newData, total)
              return newData
            })
          } else {
            setData(paginatedItems)
            onDataChange?.(paginatedItems, total)
          }
        } catch (err) {
          console.error('Erro ao carregar dados filtrados', err)
          if (!append) {
            setData([])
            setTotalCount(0)
            setHasMore(false)
            setCurrentPage(1)
          }
        } finally {
          setIsLoadingData(false)
        }
      } else {
        // Se não houver filtros, usar os dados já carregados
        setHasMore(currentPage * pageSize < totalCount)
        if (!append) {
          // Para paginação sem filtros, usar os dados do allData
          const startIndex = (page - 1) * pageSize
          const paginatedItems = allData.slice(startIndex, startIndex + pageSize)
          setData(paginatedItems)
          onDataChange?.(paginatedItems, totalCount)
          setCurrentPage(page)
          setHasMore(startIndex + paginatedItems.length < totalCount)
        }
        setIsLoadingData(false)
      }
    },
    [empresaId, mes, ano, tributo, onDataChange, pageSize, allData, totalCount, currentPage]
  )

  const updateFilter = useCallback(
    async (filterType: keyof AuditoriaComparativaFilterState, values: string[]) => {
      // Para filtros de alíquota, garantir formato com 2 casas decimais apenas quando necessário
      let finalValues = values;
      if (filterType.includes('aliquota')) {
        finalValues = values.map(value => {
          // Verificar se o valor já está formatado corretamente
          if (value.includes('.') && value.split('.')[1]?.length === 2) {
            return value;
          }
          // Garantir que o valor tenha 2 casas decimais
          const num = parseFloat(value);
          return isNaN(num) ? value : num.toFixed(2);
        });
      }
      
      const newFilters = { ...filters, [filterType]: finalValues }
      setFilters(newFilters)
      await loadFilteredData(newFilters, 1, false)
      
    },
    [filters, loadFilteredData]
  )

  const updateTextFilter = useCallback(
    (filterType: keyof AuditoriaComparativaFilterState, value: string) => {
      const newFilters = { ...filters, [filterType]: value }
      setFilters(newFilters)
      if (textFilterTimeout.current) clearTimeout(textFilterTimeout.current)
      textFilterTimeout.current = setTimeout(() => {
        loadFilteredData(newFilters, 1, false)
      }, 300) // Reduzido para 300ms para resposta mais rápida
    },
    [filters, loadFilteredData]
  )

  const clearAllFilters = useCallback(() => {
    setFilters(initialFilterState)
    loadFilteredData(initialFilterState, 1, false)
  }, [loadFilteredData])

  const loadMore = useCallback(() => {
    if (hasMore && !isLoadingData) {
      const nextPage = currentPage + 1
      loadFilteredData(filters, nextPage, true)
    }
  }, [hasMore, isLoadingData, currentPage, filters, loadFilteredData])

  // Carregar dados iniciais com paginação quando os parâmetros mudarem
  useEffect(() => {
    if (empresaId && isFirstLoad.current) {
      isFirstLoad.current = false
      
      if (initialData.length) {
        const total = initialTotal ?? initialData.length
        setAllData(initialData)
        const paginatedItems = initialData.slice(0, pageSize)
        setData(paginatedItems)
        setTotalCount(total)
        setHasMore(paginatedItems.length < total)
        onDataChange?.(paginatedItems, total)
      } else {
        loadAllData()
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [empresaId, mes, ano, tributo, initialData, initialTotal])

  useEffect(() => {
    loadFilterOptions(filters)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [empresaId, mes, ano, tributo, filters])
  useEffect(() => {
    if (initialData.length && !hasActiveFilters) {
      const total = initialTotal ?? initialData.length
      setAllData(initialData)
      setData(initialData.slice(0, pageSize))
      setTotalCount(total)
      setHasMore(initialData.length > pageSize)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialData, initialTotal, hasActiveFilters])

  return {
    filters,
    options,
    data,
    totalCount,
    isLoading,
    isLoadingData,
    hasActiveFilters,
    hasMore,
    loadMore,
    updateFilter,
    updateTextFilter,
    clearAllFilters,
  }
}
"""
Script para inicializar o banco de dados e criar um usuário administrador
"""
import os
import sys
import bcrypt
from dotenv import load_dotenv
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Configurar a codificação padrão para UTF-8
if sys.version_info[0] < 3:
    reload(sys)
    sys.setdefaultencoding('utf-8')
else:
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# Carregar variáveis de ambiente
load_dotenv()

# Configurações do banco de dados - usando variáveis de ambiente diretamente
DB_CONFIG = {
    'dbname': os.getenv('DB_NAME', 'fiscal_bruno2'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', '123!asd'),
    'host': os.getenv('DB_HOST', '**************'),
    'port': os.getenv('DB_PORT', '5432')
}

def get_connection(dbname=None):
    """Estabelece conexão com o banco de dados"""
    params = DB_CONFIG.copy()
    if dbname:
        params['dbname'] = dbname
    
    # Remover valores None
    params = {k: v for k, v in params.items() if v is not None}
    
    print(f"Conectando com parâmetros: { {k: v if k != 'password' else '***'} for k, v in params.items() }")
    
    return psycopg2.connect(**params)

def criar_banco_dados():
    """Cria o banco de dados se não existir"""
    conn = None
    try:
        # Conectar ao banco de dados 'postgres' (banco padrão)
        conn = get_connection('postgres')
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        db_name = DB_CONFIG['dbname']
        
        # Verificar se o banco de dados já existe
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
        exists = cursor.fetchone()
        
        if not exists:
            print(f"Criando banco de dados '{db_name}'...")
            cursor.execute(f'CREATE DATABASE "{db_name}" ENCODING \'UTF8\'')
            print(f"Banco de dados '{db_name}' criado com sucesso!")
        else:
            print(f"Banco de dados '{db_name}' já existe.")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"Erro ao criar banco de dados: {e}")
        return False
    finally:
        if conn is not None:
            conn.close()

def executar_schema():
    """Executa o script de schema para criar as tabelas"""
    conn = None
    try:
        # Conectar ao banco de dados
        conn = get_connection()
        cursor = conn.cursor()
        
        # Obter o caminho absoluto para o arquivo schema.sql
        script_dir = os.path.dirname(os.path.abspath(__file__))
        schema_path = os.path.join(os.path.dirname(script_dir), 'db', 'schema.sql')
        
        print(f"Lendo schema de: {schema_path}")
        
        # Ler e executar o script de schema
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
            # Executar cada comando separadamente para evitar problemas com pontos e vírgulas
            for command in schema_sql.split(';'):
                if command.strip():
                    cursor.execute(command)
        
        conn.commit()
        print("Schema executado com sucesso!")
        
        return conn, cursor
        
    except Exception as e:
        if conn is not None:
            conn.rollback()
        print(f"Erro ao executar schema: {e}")
        import traceback
        traceback.print_exc()
        return None, None
    # Não fechamos a conexão aqui, pois ela será usada pela função que chama

def gerar_hash_senha(senha):
    """Gera um hash bcrypt para a senha"""
    return bcrypt.hashpw(senha.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def criar_usuario_admin(conn, cursor):
    """Cria um usuário administrador"""
    try:
        # Verificar se já existe um usuário admin
        cursor.execute("SELECT id FROM usuario WHERE tipo_usuario = 'admin'")
        admin_existente = cursor.fetchone()
        
        if admin_existente:
            print(f"Usuário administrador já existe (ID: {admin_existente[0]})")
            return
        
        # Dados do usuário admin
        nome = "Administrador"
        portal_user_id = "admin_portal"
        
        # Inserir o usuário admin
        cursor.execute(
            """
            INSERT INTO usuario
            (nome, portal_user_id, is_admin, tipo_usuario, empresas_permitidas)
            VALUES (%s, %s, %s, %s, %s)
            RETURNING id
            """,
            (nome, portal_user_id, True, 'admin', '[]')
        )
        
        usuario_id = cursor.fetchone()[0]
        conn.commit()
        
        print("\n=== USUÁRIO ADMINISTRADOR CRIADO ===")
        print(f"ID: {usuario_id}")
        print("Usuário criado com portal_user_id 'admin_portal'")
        print("==================================\n")
        
    except Exception as e:
        if conn is not None:
            conn.rollback()
        print(f"Erro ao criar usuário administrador: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Função principal"""
    print("=== INICIALIZAÇÃO DO BANCO DE DADOS ===")
    
    try:
        # Criar banco de dados
        if not criar_banco_dados():
            print("Falha ao criar o banco de dados.")
            return
        
        # Executar schema
        conn, cursor = executar_schema()
        if not conn or not cursor:
            print("Falha ao executar o schema do banco de dados.")
            return
        
        # Criar usuário administrador
        criar_usuario_admin(conn, cursor)
        
        print("\n✅ Inicialização concluída com sucesso!")
        
    except Exception as e:
        print(f"\n❌ Erro durante a inicialização: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Garantir que as conexões são fechadas
        try:
            if 'cursor' in locals() and cursor is not None:
                cursor.close()
            if 'conn' in locals() and conn is not None:
                conn.close()
        except Exception as e:
            print(f"Aviso ao fechar conexões: {e}")

if __name__ == "__main__":
    main()


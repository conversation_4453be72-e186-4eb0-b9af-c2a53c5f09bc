import { useQuery } from '@tanstack/react-query'
import {
  dashboardService,
  EmpresaGraficos,
} from '@/services/dashboardService'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Area,
  AreaChart,
} from 'recharts'

interface InconsistenciasGraficosProps {
  empresaId: number
  year?: number
  month?: number
}

interface ChartItem {
  label: string
  valor: number
  total: number
}

const tributosOrder = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'] as const
const tributoLabels: Record<string, string> = {
  icms: 'ICMS',
  icms_st: 'ICMS-ST',
  ipi: 'IPI',
  pis: 'PIS',
  cofins: 'COFINS',
  difal: 'DIFAL',
}
const tributoColors = ['#0d6efd', '#198754', '#ffc107', '#dc3545', '#0dcaf0', '#6c757d']

export function InconsistenciasGraficos({
  empresaId,
  year,
  month,
}: InconsistenciasGraficosProps) {
  const { data, isLoading, error } = useQuery({
    queryKey: ['empresa-graficos', empresaId, year, month],
    queryFn: () => dashboardService.getEmpresaGraficos(empresaId, year, month),
  }) as {
    data: EmpresaGraficos | undefined
    isLoading: boolean
    error: unknown
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error || !data?.success) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex">
          <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clipRule="evenodd"
            />
          </svg>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              Erro ao carregar gráficos de inconsistências
            </h3>
            <p className="text-sm text-red-700 dark:text-red-300 mt-1">
              {data?.message || 'Tente novamente mais tarde.'}
            </p>
          </div>
        </div>
      </div>
    )
  }

  const labels = data.dados.labels
  const geral: ChartItem[] = labels.map((label, idx) => ({
    label,
    valor: data.dados.geral.valores_inconsistentes[idx] || 0,
    total: data.dados.geral.total_inconsistencias[idx] || 0,
  }))

  const tributos = tributosOrder
    .filter((t) => data.dados.tributos[t])
    .map((t, index) => ({
      key: t,
      label: tributoLabels[t],
      color: tributoColors[index],
      data: labels.map((label, idx) => ({
        label,
        valor: data.dados.tributos[t].valores_inconsistentes[idx] || 0,
        total: data.dados.tributos[t].total_inconsistencias[idx] || 0,
      })),
    }))

  const formatCurrency = (value: number) =>
    `R$ ${value.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`

  return (
    <div className="space-y-8">
      <div>
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
          </svg>
          Análise Temporal de Inconsistências
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Evolução dos valores inconsistentes nos últimos 12 meses
        </p>
      </div>

      {/* Gráfico Geral - Transformado em Área */}
      <div className="card p-4">
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={geral}>
            <defs>
              <linearGradient id="colorGeral" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="rgb(7, 161, 66)" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="rgb(7, 161, 66)" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="label" stroke="#9ca3af" />
            <YAxis
              stroke="#9ca3af"
              tickFormatter={(v) => `R$ ${v.toLocaleString('pt-BR')}`}
            />
            <Tooltip 
              formatter={(v: number) => formatCurrency(v)} 
              labelFormatter={(label) => `Período: ${label}`}
            />
            <Area
              type="monotone"
              dataKey="valor"
              stroke="rgb(7, 161, 66)"
              strokeWidth={2}
              fillOpacity={1}
              fill="url(#colorGeral)"
              name="Valor Inconsistente"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      <div>
        <h5 className="text-md font-semibold text-gray-900 dark:text-white flex items-center">
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 9a3 3 0 106 0 3 3 0 00-6 0zM2 9a3 3 0 016 0 3 3 0 01-6 0zM14 9a3 3 0 116 0 3 3 0 01-6 0z" />
          </svg>
          Análise por Tributo
        </h5>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {tributos.map((t) => (
          <div key={t.key} className="card p-4">
            <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t.label}
            </h6>
            <ResponsiveContainer width="100%" height={200}>
              <AreaChart data={t.data}>
                <defs>
                  <linearGradient id={`color${t.key}`} x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={t.color} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={t.color} stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="label" stroke="#9ca3af" />
                <YAxis
                  stroke="#9ca3af"
                  tickFormatter={(v) => `R$ ${v.toLocaleString('pt-BR')}`}
                />
                <Tooltip 
                  formatter={(v: number) => formatCurrency(v)} 
                  labelFormatter={(label) => `Período: ${label}`}
                />
                <Area 
                  type="monotone" 
                  dataKey="valor" 
                  stroke={t.color} 
                  strokeWidth={2}
                  fillOpacity={1} 
                  fill={`url(#color${t.key})`}
                  name="Valor Inconsistente"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        ))}
      </div>
    </div>
  )
}

export default InconsistenciasGraficos

"""
Serviço de importação em lote com controle de concorrência melhorado e suporte assíncrono
"""
from typing import List, Dict
from models import db, Empresa
from models.importacao_async import ImportacaoAsync
from .xml_import_service import XMLImportService
from utils import XMLProcessor
import traceback
from services.transactional import transactional_session

class BatchXMLImportService:
    """
    Serviço para importação em lote de arquivos XML
    """

    def __init__(self, escritorio_id: int, usuario_id: int, max_workers: int = 4, max_files_per_batch: int = 50, progress_callback=None, import_id=None, app=None):
        """
        Inicializa o serviço de importação em lote

        Args:
            escritorio_id (int): ID do escritório
            usuario_id (int): ID do usuário que está realizando a importação
            max_workers (int): Número máximo de threads para processamento paralelo
            max_files_per_batch (int): Número máximo de arquivos por lote para evitar sobrecarga
            progress_callback (callable): Função callback para reportar progresso
            import_id (str): ID da importação assíncrona (opcional)
            app (Flask): Instância da aplicação para uso em threads paralelas
        """
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id
        self.max_workers = max_workers
        self.max_files_per_batch = max_files_per_batch
        self.progress_callback = progress_callback
        self.import_id = import_id
        self.app = app
        self.importacao_async = None
        self.total_files = 0
        self.processed_files = 0
        self.success_count = 0
        self.error_count = 0

        # Se temos um import_id, buscar ou criar o registro de importação assíncrona
        if self.import_id:
            self.importacao_async = ImportacaoAsync.query.get(self.import_id)
            if not self.importacao_async:
                with transactional_session():
                    self.importacao_async = ImportacaoAsync(
                        id=self.import_id,
                        usuario_id=self.usuario_id,
                        escritorio_id=self.escritorio_id,
                        tipo='xml_batch',
                        status='iniciando'
                    )
                    db.session.add(self.importacao_async)

    def process_xml_batch(self, xml_files: List[Dict]) -> Dict:
        """
        Processa um lote de arquivos XML com controle de concorrência e suporte assíncrono

        Args:
            xml_files (List[Dict]): Lista de dicionários contendo {filename, content} dos XMLs

        Returns:
            Dict: Resultado do processamento em lote
        """

        # Resultados do processamento
        self.total_files = len(xml_files)
        self.processed_files = 0
        self.success_count = 0
        self.error_count = 0
        results = {
            'success': [],
            'errors': [],
            'total': self.total_files,
            'processed': 0
        }

        try:
            # Atualizar importação assíncrona se disponível
            if self.importacao_async:
                with transactional_session():
                    self.importacao_async.total_arquivos = len(xml_files)
                    self.importacao_async.status = 'processando'
                    self.importacao_async.mensagem_atual = 'Iniciando processamento dos arquivos'

            # Agrupar XMLs por empresa para otimizar o processamento
            xml_by_company = self._group_by_company(xml_files)

            # Processar cada empresa sequencialmente para evitar problemas de contexto
            for empresa_id, empresa_xmls in xml_by_company.items():

                try:
                    batch_result = self._process_company_batch(empresa_id, empresa_xmls)
                    results['success'].extend(batch_result['success'])
                    results['errors'].extend(batch_result['errors'])
                    results['processed'] += len(batch_result['success']) + len(batch_result['errors'])
                except Exception as e:
                    error_trace = traceback.format_exc()
                    results['errors'].append({
                        'message': f'Erro no processamento da empresa {empresa_id}: {str(e)}',
                        'stack_trace': error_trace
                    })

            # Marcar como concluído se temos importação assíncrona
            if self.importacao_async:
                with transactional_session():
                    self.importacao_async.marcar_concluido(results)

        except Exception as e:
            error_trace = traceback.format_exc()
            error_msg = f'Erro geral no processamento em lote: {str(e)}'

            results['errors'].append({
                'message': error_msg,
                'stack_trace': error_trace
            })

            # Marcar como erro se temos importação assíncrona
            if self.importacao_async:
                with transactional_session():
                    self.importacao_async.marcar_erro(error_msg)

        finally:
            return results

    def _group_by_company(self, xml_files: List[Dict]) -> Dict[int, List[Dict]]:
        """
        Agrupa os XMLs por empresa baseado no CNPJ do emitente ou destinatário
        Implementa fallback para XMLs de entrada onde a empresa pode ser o destinatário

        Args:
            xml_files (List[Dict]): Lista de arquivos XML

        Returns:
            Dict[int, List[Dict]]: XMLs agrupados por ID da empresa
        """
        xml_by_company = {}

        for xml_file in xml_files:
            try:
                # Extrair informações do XML
                processor = XMLProcessor(xml_file['content'])
                emitente = processor.get_emitente()
                destinatario = processor.get_destinatario()

                # Extrair CNPJs/CPFs
                cnpj_emitente = emitente.get('cnpj') or emitente.get('cpf')
                cnpj_destinatario = destinatario.get('cnpj') or destinatario.get('cpf')

                empresa_encontrada = None

                # 1. Tentar encontrar empresa como emitente
                if cnpj_emitente:
                    cnpj_limpo = self._clean_document(cnpj_emitente)
                    empresa_encontrada = self._find_empresa_by_document(cnpj_limpo)

                # 2. Se não encontrou como emitente, tentar como destinatário (fallback para XMLs de entrada)
                if not empresa_encontrada and cnpj_destinatario:
                    cnpj_limpo = self._clean_document(cnpj_destinatario)
                    empresa_encontrada = self._find_empresa_by_document(cnpj_limpo)

                    if empresa_encontrada:
                        print(f"[BATCH] Fallback ativado: Empresa {empresa_encontrada.razao_social} encontrada como destinatário para {xml_file['filename']}")

                if not empresa_encontrada:
                    print(f"[BATCH] Empresa não encontrada para o arquivo {xml_file['filename']}")
                    continue

                # Agrupar por empresa
                if empresa_encontrada.id not in xml_by_company:
                    xml_by_company[empresa_encontrada.id] = []

                xml_by_company[empresa_encontrada.id].append(xml_file)
                print(f"[BATCH] Arquivo {xml_file['filename']} agrupado para empresa {empresa_encontrada.razao_social}")

            except Exception as e:
                print(f"[BATCH] Erro ao processar arquivo {xml_file['filename']}: {str(e)}")
                continue

        print(f"[BATCH] Agrupamento concluído: {len(xml_by_company)} empresas, {sum(len(files) for files in xml_by_company.values())} arquivos")
        return xml_by_company

    def _clean_document(self, document):
        """
        Limpa um documento (CNPJ/CPF) removendo caracteres especiais
        """
        if not document:
            return None
        return ''.join(filter(str.isdigit, str(document)))

    def _find_empresa_by_document(self, document_limpo):
        """
        Busca empresa por documento limpo (CNPJ/CPF)
        """
        if not document_limpo:
            return None

        return Empresa.query.filter(
            db.func.replace(
                db.func.replace(
                    db.func.replace(Empresa.cnpj, '.', ''),
                    '/', ''
                ),
                '-', ''
            ) == document_limpo
        ).first()

    def _process_company_batch(self, empresa_id: int, xml_files: List[Dict]) -> Dict:
        """
        Processa um lote de XMLs de uma empresa específica com controle de transações

        Args:
            empresa_id (int): ID da empresa
            xml_files (List[Dict]): Lista de arquivos XML da empresa

        Returns:
            Dict: Resultado do processamento
        """
        results = {
            'success': [],
            'errors': []
        }

        # Buscar empresa para obter o escritório_id correto
        empresa = Empresa.query.get(empresa_id)
        if not empresa:
            raise ValueError(f'Empresa com ID {empresa_id} não encontrada')

        # Criar serviço de importação para a empresa
        service = XMLImportService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,  # Usar o escritório da empresa
            usuario_id=self.usuario_id
        )

        # Dividir os arquivos em lotes menores para evitar sobrecarga
        total_files = len(xml_files)

        # Dividir em lotes menores
        for i in range(0, total_files, self.max_files_per_batch):
            batch = xml_files[i:i + self.max_files_per_batch]

            import gc
            import time
            from concurrent.futures import ThreadPoolExecutor, as_completed

            # Processar XMLs do lote em paralelo
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:

                def process_single(xml):
                    if self.app:
                        with self.app.app_context():
                            return service.import_xml(
                                xml_content=xml['content'],
                                filename=xml['filename']
                            )
                    return service.import_xml(
                        xml_content=xml['content'],
                        filename=xml['filename']
                    )

                future_map = {
                    executor.submit(process_single, xml_file): xml_file
                    for xml_file in batch
                }

                for future in as_completed(future_map):
                    xml_file = future_map[future]
                    try:
                        start_time = time.time()
                        result = future.result()

                        # Registrar tempo de processamento
                        processing_time = time.time() - start_time

                        if result['success']:
                            results['success'].append({
                                'filename': xml_file['filename'],
                                'importacao_id': result.get('importacao_id'),
                                'message': 'Importado com sucesso',
                                'processing_time': processing_time
                            })
                            self.success_count += 1
                        else:
                            results['errors'].append({
                                'filename': xml_file['filename'],
                                'message': result.get('message'),
                                'error': result.get('error')
                            })
                            self.error_count += 1

                        # Reportar progresso
                        self.processed_files += 1

                        # Atualizar importação assíncrona se disponível
                        if self.importacao_async:
                            with transactional_session():
                                self.importacao_async.atualizar_progresso(
                                    arquivos_processados=self.processed_files,
                                    arquivos_sucesso=self.success_count,
                                    arquivos_erro=self.error_count,
                                    mensagem=f'Processando: {xml_file["filename"]}'
                                )

                        # Callback tradicional se disponível
                        if self.progress_callback:
                            progress_data = {
                                'processed': self.processed_files,
                                'total': self.total_files,
                                'current_file': xml_file['filename'],
                                'success_count': self.success_count,
                                'error_count': self.error_count,
                                'empresa_id': empresa_id
                            }
                            self.progress_callback(progress_data)

                        gc.collect()

                    except Exception as e:
                        error_trace = traceback.format_exc()

                        results['errors'].append({
                            'filename': xml_file['filename'],
                            'message': f'Erro ao processar XML: {str(e)}',
                            'stack_trace': error_trace
                        })
                        self.error_count += 1

                        # Reportar progresso mesmo em caso de erro
                        self.processed_files += 1

                        # Atualizar importação assíncrona se disponível
                        if self.importacao_async:
                            with transactional_session():
                                self.importacao_async.atualizar_progresso(
                                    arquivos_processados=self.processed_files,
                                    arquivos_sucesso=self.success_count,
                                    arquivos_erro=self.error_count,
                                    mensagem=f'Erro ao processar: {xml_file["filename"]}'
                                )

                        # Callback tradicional se disponível
                        if self.progress_callback:
                            progress_data = {
                                'processed': self.processed_files,
                                'total': self.total_files,
                                'current_file': xml_file['filename'],
                                'success_count': self.success_count,
                                'error_count': self.error_count,
                                'empresa_id': empresa_id,
                                'error': str(e)
                            }
                            self.progress_callback(progress_data)

                        # Forçar coleta de lixo após erro
                        gc.collect()

            # Pausa entre lotes para permitir que o sistema se recupere
            if i + self.max_files_per_batch < total_files:
                time.sleep(0.5)  # Pausa reduzida entre lotes

        return results
# Processo de Matching da Auditoria Comparativa

Este documento descreve o fluxo atual de matching entre os itens do XML e do SPED na página **Auditoria Comparativa** (tributos).

## 1. Etapas do Algoritmo

O serviço `ItemMatchingService` executa o matching em múltiplas fases. O método `find_matches_for_note` prepara os itens e chama `_execute_matching_algorithm`:

```python
# back/services/item_matching_service.py
learned_matches = self.learning_service.aplicar_aprendizado_automatico(itens_xml, itens_sped)
for match in learned_matches:
    matches.append(match)
    xml_matched.add(match['xml_item']['id'])
    sped_matched.add(match['sped_item']['id'])
```

1. **Fase 0 – Aprendizado automático**: aplica matches previamente aprovados e registrados em `historico_matching_aprendizado`.
2. **Fase 1 – Matching direto**: compara valores, quantidades, CFOP, unidade e descrição.
3. **Fase 2 – Matching por embeddings**: usa OpenAI para itens restantes.
A Fase 0 deve vir aqui, após a fase 1 ou 2, pois aqui, iremos comparar os matches previamente aprovados, com os dados dos matches que acabamos de encontrar.
4. **Fase 3 – Itens não pareados**: registra itens sem correspondência.

O algoritmo retorna os matches com `match_type` e `confidence_score` para criação de registros `AuditoriaComparativaImpostos`.

## 2. Salvando Aprendizado

Quando o usuário aprova um match (mesmo após editar dados do SPED), o backend grava um histórico para uso futuro. A rota `/api/auditoria-comparativa/aprovar-match` chama `salvar_historico_matching_com_dados_atualizados`:

```python
# back/services/auditoria_comparativa_service.py
historico = HistoricoMatchingAprendizado()
historico.empresa_id = auditoria.empresa_id
historico.escritorio_id = auditoria.escritorio_id
historico.xml_item_id = auditoria.xml_item_id
historico.sped_item_id = auditoria.sped_item_id
historico.cliente_id = auditoria.cliente_id
historico.xml_codigo_produto = xml_item.codigo_produto if xml_item else None
historico.sped_codigo_produto = sped_item.cod_item if sped_item else None
historico.match_type_original = auditoria.match_type
historico.confidence_score_original = auditoria.confidence_score
historico.acao_usuario = f'{acao}_{tributo}'
historico.usuario_id = usuario_id
historico.feedback_usuario = f'[{tributo.upper()}] {feedback}' if feedback else f'[{tributo.upper()}] {acao.title()}'
```

Além dos campos acima, são armazenadas características do match, incluindo snapshot dos tributos SPED:

```python
caracteristicas = {
    'tributo': tributo,
    'timestamp': datetime.now().isoformat(),
    'confidence_score': float(auditoria.confidence_score) if auditoria.confidence_score else None,
    'match_type': auditoria.match_type,
    'xml_codigo_produto': historico.xml_codigo_produto,
    'sped_codigo_produto': historico.sped_codigo_produto,
    'cliente_id': auditoria.cliente_id,
    'dados_sped_atualizados': self._get_sped_data_snapshot(auditoria),
    'sped_foi_alterado': auditoria.sped_alterado,
    'historico_alteracoes': auditoria.historico_alteracoes
}
```

## 3. Utilização do Histórico

Na geração de auditorias de meses futuros, o método `_execute_matching_algorithm` utiliza `MatchingLearningService` para consultar aprendizados e sugerir matches imediatamente:

```python
# back/services/matching_learning_service.py
match_aprendido = self.consultar_match_aprendido(xml_codigo, sped_codigo, cliente_id)
```

Se um match aprendido é encontrado, ele é retornado com `match_type='learned'` e alta confiança, acelerando o processo de matching para a empresa.

## 4. Auto‑aprovação

O serviço `SugestoesInteligentesService` analisa o histórico para verificar se um item pode ser auto‑aprovado. Caso exista match exato aprovado e os tributos atuais sejam compatíveis, o endpoint `/api/auditoria-comparativa/auto-aprovar/<auditoria_id>` executa:

```python
# back/services/sugestoes_inteligentes_service.py
pode_auto_aprovar, motivo = self._verificar_auto_aprovacao(auditoria, tributo, sugestoes)
if pode_auto_aprovar:
    auditoria.set_status_tributo(tributo, 'aprovado', usuario_id, f'Auto-aprovado: {motivo}')
    self._registrar_auto_aprovacao(auditoria, tributo, usuario_id, sugestoes)
```

Assim, somente após confirmação do histórico é que o status do tributo passa para **aprovado** automaticamente. No cadastro inicial, mesmo com `confidence_score` alto, o status começa como `em_analise`.

## 5. Resumo

- **Aprendizado gravado**: sempre que o usuário aprova ou rejeita um match, registramos IDs, códigos dos produtos, tipo de match, score original, usuário e snapshot dos dados SPED.
- **Uso futuro**: esses registros alimentam o `MatchingLearningService`, que na fase 0 do algoritmo sugere matches automáticos para novas auditorias.
- **Auto-aprovação**: quando um item atual coincide 100% com o histórico e os tributos são iguais (dentro da tolerância), o sistema pode aprovar automaticamente via endpoint específico.
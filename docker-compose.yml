version: '3.8'

services:
  web:
    build: .
    container_name: auditoria_fiscal_web
    command: python wsgi.py
    volumes:
      - .:/app
    ports:
      - "127.0.0.1:5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://audittei:#<EMAIL>/fiscal?sslmode=require
      - FLASK_DEBUG=0
      - CNPJ_WS_TOKEN=DAsEVpQfzFs0egLMxAYORJdnlKqVE9u9HBgdyxpUB7c7
      - OPENAI_API_KEY=********************************************************************************************************************************************************************
      - OPENAI_MODEL=gpt-4o-mini
      - OPENAI_MAX_TOKENS=1500
      - OPENAI_TEMPERATURE=0.3
      - PORTAL_JWT_PUBLIC_KEY_FILE=/run/secrets/portal_public_key
    secrets:
      - portal_public_key
    restart: unless-stopped

secrets:
  portal_public_key:
    file: ./docker/secrets/portal_public_key

# Filtros Avançados - Sistema de Cenários

Este documento descreve o sistema de filtros avançados implementado para as tabelas de cenários no frontend React.

## Visão Geral

O sistema de filtros avançados permite aos usuários filtrar cenários de forma intuitiva usando dropdowns com checkboxes, similar ao sistema funcional do frontend antigo. Os filtros são aplicados em cascata, onde a seleção de um filtro afeta as opções disponíveis nos outros filtros.

## Componentes Principais

### 1. useAdvancedFilters Hook

Hook personalizado que gerencia o estado dos filtros avançados.

```typescript
import { useAdvancedFilters } from '@/hooks/useAdvancedFilters'

const {
  filters,           // Estado atual dos filtros
  options,           // Opções disponíveis para cada filtro
  isLoading,         // Estado de carregamento
  hasActiveFilters,  // Se há filtros ativos
  updateFilter,      // Atualizar um filtro específico
  clearAllFilters,   // Limpar todos os filtros
  getFilteredOptions // Obter opções filtradas por relacionamentos
} = useAdvancedFilters({
  tipoTributo: 'icms',
  empresaId: 123,
  status: 'novo',
  onFiltersChange: (newFilters) => {
    // Callback quando filtros mudam
  }
})
```

### 2. AdvancedFilterDropdown Component

Componente dropdown reutilizável com funcionalidades avançadas:

- Busca por texto
- Seleção múltipla com checkboxes
- Botões "Selecionar Todos" e "Limpar"
- Aplicação e cancelamento de filtros

```typescript
<AdvancedFilterDropdown
  options={filteredOptions}
  selectedValues={selectedValues}
  placeholder="Filtrar CFOP..."
  onApply={(values) => updateFilter('cfops', values)}
  onClear={() => updateFilter('cfops', [])}
  isLoading={isLoading}
/>
```

### 3. FilterRow Component

Componente que renderiza uma linha de filtros abaixo dos cabeçalhos da tabela:

```typescript
<FilterRow
  tipoTributo="icms"
  filters={filters}
  options={options}
  isLoading={isLoading}
  hasActiveFilters={hasActiveFilters}
  onUpdateFilter={updateFilter}
  onClearAllFilters={clearAllFilters}
  getFilteredOptions={getFilteredOptions}
/>
```

## Tipos de Filtros Suportados

### Filtros Comuns (todos os tributos)
- **CFOP**: Código Fiscal de Operações e Prestações
- **NCM**: Nomenclatura Comum do Mercosul
- **CST**: Código de Situação Tributária
- **Estados**: UF dos clientes
- **Atividades**: Atividade dos clientes
- **Destinações**: Destinação dos clientes

### Filtros Específicos por Tributo

#### ICMS
- **% ICMS**: Alíquota de ICMS
- **% Red. BC**: Percentual de redução da base de cálculo

#### ICMS-ST
- **% ICMS**: Alíquota de ICMS
- **% ICMS ST**: Alíquota de ICMS-ST
- **% Red. BC ICMS**: Redução da base de cálculo ICMS
- **% Red. BC ICMS ST**: Redução da base de cálculo ICMS-ST
- **% MVA**: Margem de Valor Agregado

#### IPI
- **% IPI**: Alíquota de IPI
- **% Red. IPI**: Redução da base de cálculo IPI

#### PIS/COFINS
- **% PIS/COFINS**: Alíquotas respectivas
- **% Red. PIS/COFINS**: Reduções respectivas

## Funcionalidades

### 1. Filtros em Cascata

Quando um filtro é aplicado, as opções dos outros filtros são automaticamente atualizadas para mostrar apenas valores compatíveis com a seleção atual.

### 2. Busca Inteligente

Cada dropdown possui um campo de busca que permite encontrar rapidamente valores específicos.

### 3. Seleção Múltipla

Usuários podem selecionar múltiplos valores em cada filtro usando checkboxes.

### 4. Controles de Seleção

- **Selecionar Todos**: Seleciona todos os itens visíveis (após busca)
- **Limpar**: Remove todas as seleções do filtro atual
- **Limpar Todos os Filtros**: Remove todos os filtros ativos

### 5. Indicadores Visuais

- Contador de itens selecionados no input principal
- Indicador de quantos cenários estão sendo exibidos vs total
- Estados de carregamento durante busca de opções

## Integração com Tabelas

### Implementação em Tabelas Existentes

Para integrar os filtros em uma tabela existente:

1. **Adicionar props necessárias**:
```typescript
interface TableProps {
  // ... props existentes
  status?: 'novo' | 'producao' | 'inconsistente'
  onFilteredDataChange?: (filteredCenarios: CenarioTributo[]) => void
}
```

2. **Usar o hook useAdvancedFilters**:
```typescript
const {
  filters,
  options,
  isLoading: filtersLoading,
  hasActiveFilters,
  updateFilter,
  clearAllFilters,
  getFilteredOptions
} = useAdvancedFilters({
  tipoTributo: 'icms',
  empresaId: empresaId || undefined,
  status,
  onFiltersChange: (newFilters) => {
    const filteredData = applyFiltersToData(cenarios, newFilters)
    onFilteredDataChange?.(filteredData)
  }
})
```

3. **Adicionar FilterRow na estrutura da tabela**:
```typescript
<thead>
  {/* Cabeçalhos normais */}
  <tr>
    {/* ... cabeçalhos das colunas */}
  </tr>
  
  {/* Linha de filtros */}
  <FilterRow
    tipoTributo="icms"
    filters={filters}
    options={options}
    isLoading={filtersLoading}
    hasActiveFilters={hasActiveFilters}
    onUpdateFilter={updateFilter}
    onClearAllFilters={clearAllFilters}
    getFilteredOptions={getFilteredOptions}
  />
</thead>
```

## Backend Integration

O sistema utiliza o endpoint existente:
```
GET /api/cenarios/{tipo_tributo}/filter-options
```

### Parâmetros de Query:
- `empresa_id`: ID da empresa
- `direcao`: Direção dos cenários ('entrada' ou 'saida')
- `status`: Status dos cenários ('novo', 'producao', 'inconsistente')
- `year`: Ano (opcional)
- `month`: Mês (opcional)

### Resposta Esperada:
```json
{
  "success": true,
  "opcoes": {
    "cfops": [
      {
        "value": "5101",
        "label": "5101",
        "count": 150,
        "related": {
          "ncms": ["12345678", "87654321"],
          "csts": ["00", "10"]
        }
      }
    ],
    "ncms": [...],
    "csts": [...],
    // ... outros filtros
  }
}
```

## Considerações de Performance

1. **Carregamento Lazy**: Opções são carregadas apenas quando necessário
2. **Cache Local**: Opções são mantidas em cache durante a sessão
3. **Filtros Otimizados**: Aplicação de filtros é feita no frontend para responsividade
4. **Relacionamentos**: Sistema de cascata reduz opções desnecessárias

## Próximos Passos

1. Implementar filtros nas demais tabelas (ICMS-ST, IPI, PIS, COFINS, DIFAL)
2. Adicionar persistência de filtros no localStorage
3. Implementar filtros de data/período
4. Adicionar exportação de dados filtrados

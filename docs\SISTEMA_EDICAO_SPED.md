# Sistema de Edição SPED e Histórico de Matching Inteligente

## Visão Geral

Este sistema permite que analistas fiscais editem dados SPED incorretos diretamente no sistema de auditoria comparativa, mantendo um histórico inteligente de aprendizado para futuras auditorias.

## Funcionalidades Implementadas

### 1. Edição Individual de Dados SPED

**Localização:** Auditoria Comparativa > Tabs de Tributos (ICMS, ICMS-ST, IPI, PIS/COFINS)

**Como usar:**
- Clique no botão "Editar" (ícone de lápis) ao lado de qualquer registro
- Modal de edição será aberto com campos específicos do tributo
- Preencha os campos que precisam ser corrigidos
- Adicione um motivo para a alteração
- Clique em "Salvar Alterações"

**Campos editáveis por tributo:**
- **ICMS:** CFOP, NCM, Origem, CST, Base de Cálculo, Alíquota, Valor, Redução, CSOSN, Crédito ICMS
- **ICMS-ST:** Base de Cálculo ST, Alíquota ST, Valor ST, MVA, Redução
- **IPI:** CST, Base de Cálculo, Alíquota, Valor, Redução
- **PIS:** CST, Base de Cálculo, Alíquota, Valor
- **COFINS:** CST, Base de Cálculo, Alíquota, Valor

### 2. Edição em Massa de Dados SPED

**Como usar:**
- Selecione múltiplos registros usando os checkboxes
- Clique no botão "Editar SPED em Massa"
- Preencha apenas os campos que deseja alterar em todos os registros
- Campos em branco não serão alterados
- Adicione um motivo para a alteração em massa
- Clique em "Salvar Alterações em Massa"

### 3. Histórico de Matching Inteligente

**Funcionalidade:**
- Quando um match é aprovado após edição de dados SPED, o sistema salva:
  - Dados SPED originais e atualizados
  - Códigos dos produtos XML e SPED
  - Cliente associado
  - Tributo específico
  - Motivo da alteração

**Benefícios:**
- Futuras auditorias podem identificar automaticamente produtos já corrigidos
- Sistema aprende com correções anteriores
- Reduz trabalho manual repetitivo

### 4. Atualização de Tabelas Originais

**Tabelas atualizadas automaticamente:**
- `auditoria_comparativa_impostos` - Dados da auditoria
- `item_nota_entrada` - Dados originais do SPED
- `produto_entrada` - Informações do produto (NCM, tipo)

**Vantagem:** Quando o SPED for exportado, conterá os dados corrigidos.

## Fluxo de Trabalho Recomendado

### Para Correção Individual:
1. Acesse Auditoria Comparativa
2. Selecione empresa e período
3. Vá para a tab do tributo desejado (ICMS, ICMS-ST, etc.)
4. Identifique registros com problemas
5. Clique em "Editar" no registro
6. Corrija os dados incorretos
7. Adicione motivo da correção
8. Salve as alterações
9. Aprove o match se estiver correto

### Para Correção em Massa:
1. Identifique um padrão de erro comum
2. Selecione todos os registros com o mesmo problema
3. Clique em "Editar SPED em Massa"
4. Preencha apenas os campos que precisam ser corrigidos
5. Adicione motivo da correção em massa
6. Salve as alterações
7. Aprove os matches em massa se estiverem corretos

## Controles de Segurança

### Permissões:
- Apenas usuários autorizados podem editar dados SPED
- Verificação de empresa/escritório antes de qualquer alteração

### Auditoria:
- Todas as alterações são registradas com timestamp
- Usuário responsável pela alteração é identificado
- Motivo da alteração é obrigatório
- Histórico completo de alterações é mantido

### Rastreabilidade:
- Campo `sped_alterado` indica se dados foram modificados
- Campo `historico_alteracoes` contém log completo das mudanças
- Histórico de matching preserva dados originais e alterados

## Impacto na Exportação SPED

**Importante:** Todas as alterações feitas através deste sistema serão refletidas na exportação do SPED, garantindo que:
- Dados corrigidos sejam exportados
- Inconsistências sejam eliminadas
- Arquivo SPED final esteja conforme as correções

## Monitoramento e Relatórios

### Campos de Controle:
- `data_atualizacao` - Última modificação
- `usuario_atualizacao` - Usuário que fez a última alteração
- `sped_alterado` - Flag indicando alteração manual
- `historico_alteracoes` - Log JSON das alterações

### Consultas Úteis:
```sql
-- Registros alterados por usuário
SELECT COUNT(*) FROM auditoria_comparativa_impostos 
WHERE sped_alterado = true AND usuario_atualizacao = [user_id];

-- Histórico de aprendizado por cliente
SELECT * FROM historico_matching_aprendizado 
WHERE cliente_id = [client_id] AND acao_usuario LIKE 'aprovado_%';
```

## Troubleshooting

### Problemas Comuns:

1. **Erro ao salvar alterações:**
   - Verificar permissões do usuário
   - Confirmar se empresa pertence ao escritório do usuário
   - Validar formato dos dados (números, datas, etc.)

2. **Botões desabilitados:**
   - Verificar se registros estão selecionados
   - Confirmar se usuário tem permissão de edição

3. **Alterações não refletidas:**
   - Recarregar a página
   - Verificar se commit foi realizado no banco
   - Consultar logs de erro no console

### Logs Importantes:
- Console do navegador para erros JavaScript
- Logs do Flask para erros de backend
- Logs do PostgreSQL para erros de banco de dados

## Próximos Passos

### Funcionalidades Futuras:
1. **Sugestões Inteligentes:** Sistema sugerirá correções baseado no histórico
2. **Auto-aprovação:** Matches 100% idênticos ao histórico serão aprovados automaticamente
3. **Relatórios de Produtividade:** Métricas de correções por usuário/período
4. **Exportação SPED:** Funcionalidade completa de exportação com dados corrigidos

### Melhorias Planejadas:
1. **Interface:** Melhorar UX do modal de edição
2. **Performance:** Otimizar consultas para grandes volumes
3. **Validações:** Adicionar mais validações de negócio
4. **Integração:** Conectar com outros módulos do sistema

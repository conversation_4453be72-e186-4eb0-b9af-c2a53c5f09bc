import { useFilterStore } from '@/store/filterStore'

export function useFilters() {
  const store = useFilterStore()

  return {
    ...store,
    // Helpers para facilitar o uso
    hasCompanySelected: store.selectedCompany !== null,
    currentPeriod: `${store.selectedMonth}/${store.selectedYear}`,
    
    // Reset filters
    resetFilters: () => {
      store.setFilters({
        company: null,
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
      })
    },
  }
}
"""
Serviço de validação PIS/COFINS usando API externa
"""
import requests
import hashlib
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from sqlalchemy import and_

from models import db
from models.pis_cofins_cache import PisCofinsCacheModel
from models.pis_cofins_validation_result import PisCofinValidationResultModel
from models.cenario import CenarioPIS, CenarioCOFINS
from models.empresa import Empresa

class PisCofinValidationService:
    
    # Configurações da API
    API_BASE_URL = "https://www.legisweb.com.br/api/piscofins/"
    API_TOKEN = "8741902a3f20e527c0b76c5b3a918b3f"  # Token fornecido
    CLIENTE_ID = "65597"  # ID do cliente fornecido
    
    # Mapeamento de regimes tributários
    REGIME_MAPPING = {
        'Lucro Presumido': '1',
        'Lucro Real': '2', 
        'Simples Nacional': '3'
    }
    
    # Mapeamento de atividades
    ATIVIDADE_MAPPING = {
        'Atacadista': '1',
        'Distribuidor': '2',
        'Fabricante': '3',
        'Varejista': '4'
    }
    
    # CFOPs válidos para validação (saída)
    CFOPS_VALIDOS = ['5101', '5401', '6101', '6401', '5118', '5122']
    
    def __init__(self):
        self.session = requests.Session()
        # o requests.Session não respeita timeout padrão, por isso
        # definimos explicitamente em cada chamada
        self.session.timeout = 30

    # ------------------------------------------------------------------
    # Helpers de normalização / mapeamento
    # ------------------------------------------------------------------

    def _norm_ncm(self, ncm: str) -> str:
        """Remove qualquer caractere não numérico do NCM."""
        if not ncm:
            return ""
        return "".join(ch for ch in ncm if ch.isdigit())

    def _parse_cst(self, cst: str) -> Tuple[str, str]:
        """Separa o código CST da descrição."""
        if not cst:
            return "", ""
        partes = cst.split("-", 1)
        codigo = partes[0].strip()
        descricao = partes[1].strip() if len(partes) > 1 else ""
        return codigo, descricao

    def map_regime_sistema_para_api(self, regime: str) -> Optional[str]:
        """Mapeia a string de regime utilizada no sistema para o código da API."""
        return self.REGIME_MAPPING.get(regime)

    def map_atividade_sistema_para_api(self, atividade: str) -> Optional[str]:
        """Mapeia a string de atividade utilizada no sistema para o código da API."""
        if not atividade:
            return None

        atividade_lower = atividade.lower()
        for nome, codigo in self.ATIVIDADE_MAPPING.items():
            if nome.lower() in atividade_lower:
                return codigo
        return self.ATIVIDADE_MAPPING.get(atividade)
    
    def _gerar_hash_consulta(self, ncm: str, regime_tributario: str, atividade: str) -> str:
        """Gera hash único para a consulta baseado no NCM normalizado."""
        ncm_norm = self._norm_ncm(ncm)
        dados = f"{ncm_norm}_{regime_tributario}_{atividade}"
        return hashlib.sha256(dados.encode()).hexdigest()
    
    def _consultar_api(self, ncm: str, regime_tributario: str, atividade: str) -> Optional[Dict]:
        """Realiza a chamada para a API da LegisWeb."""
        try:
            regime_codigo = self.map_regime_sistema_para_api(regime_tributario)
            atividade_codigo = self.map_atividade_sistema_para_api(atividade)

            if not regime_codigo or not atividade_codigo:
                raise ValueError(
                    f"Regime tributário ou atividade inválidos: {regime_tributario}, {atividade}"
                )

            ncm_norm = self._norm_ncm(ncm)

            params = {
                "t": self.API_TOKEN,
                "c": self.CLIENTE_ID,
                "ncm": ncm_norm,
                "regime_tributario_origem": regime_codigo,
                "atividade_origem": atividade_codigo,
            }

            response = self.session.get(self.API_BASE_URL, params=params, timeout=30)
            response.raise_for_status()

            return response.json()
        except Exception as e:
            print(f"Erro ao consultar API PIS/COFINS: {e}")
            return None
    
    def _processar_resposta_tipo1(self, resposta: Dict, ncm: str = "") -> List[Dict]:
        """Processa resposta tipo 1 (alíquotas padrão)"""
        resultados: List[Dict] = []
        ncm_norm = self._norm_ncm(ncm)

        for item in resposta.get("resposta", []):
            tributo = item.get("tributo", "").strip().upper()
            cst_code, cst_desc = self._parse_cst(item.get("cst", ""))
            valor = float(item.get("valor", 0) or 0)
            tipo_valor = item.get("tipo_valor") or "Porcentagem"
            regime_cod = self.map_regime_sistema_para_api(item.get("regime_tributario"))

            registro = {
                    "ncm_original": ncm,
                    "ncm_norm": ncm_norm,
                    "regime_tributario_origem_code": regime_cod or "",
                    "atividade_origem_code": "",
                    "atividade_destino_code": None,
                    "tributo": tributo,
                    "cst_code": cst_code,
                    "cst_desc": cst_desc,
                    "valor": valor,
                    "tipo_valor": tipo_valor,
                    "regra": "Alíquota Padrão",
                    "aplicabilidade": "",
                    "descricao_produto": "",
                    "id_base_legal": item.get("id_base_legal", ""),
                    "desc_base_legal": item.get("desc_base_legal", ""),
                    "exibir_aliquota_padrao": "1",
                    "aliquota_padrao_json": None,
                }

            if tributo == "PIS":
                registro.update(
                    {
                        "cst_pis": f"{cst_code} - {cst_desc}" if cst_code else "",
                        "aliquota_pis": valor,
                        "cst_cofins": "",
                        "aliquota_cofins": None,
                    }
                )
            else:  # COFINS
                registro.update(
                    {
                        "cst_pis": "",
                        "aliquota_pis": None,
                        "cst_cofins": f"{cst_code} - {cst_desc}" if cst_code else "",
                        "aliquota_cofins": valor,
                    }
                )

            resultados.append(registro)

        return resultados
    
    def _processar_resposta_tipo2_3(self, resposta: Dict) -> List[Dict]:
        """Processa resposta tipo 2/3 (com bloco trib)"""

        resultados: List[Dict] = []

        for item_resposta in resposta.get("resposta", []):
            regra = item_resposta.get("regra", "")
            aplicabilidade = item_resposta.get("aplicabilidade", "")

            for trib in item_resposta.get("trib", []):
                tributo = trib.get("tributo", "").strip().upper()

                ncm_original = trib.get("ncm", "")
                ncm_norm = self._norm_ncm(ncm_original)

                if tributo == "PIS":
                    cst = trib.get("cst_pis") or trib.get("cst")
                else:
                    cst = trib.get("cst_cofins") or trib.get("cst")

                cst_code, cst_desc = self._parse_cst(cst)

                resultado = {
                    "ncm_original": ncm_original,
                    "ncm_norm": ncm_norm,
                    "regime_tributario_origem_code": self.map_regime_sistema_para_api(
                        trib.get("regime_tributario_origem")
                    )
                    or "",
                    "atividade_origem_code": self.map_atividade_sistema_para_api(
                        trib.get("atividade_origem")
                    )
                    or "",
                    "atividade_destino_code": self.map_atividade_sistema_para_api(
                        trib.get("atividade_destino")
                    ),
                    "tributo": tributo,
                    "cst_code": cst_code,
                    "cst_desc": cst_desc,
                    "valor": float(trib.get("valor", 0) or 0),
                    "tipo_valor": trib.get("tipo_valor"),
                    "regra": regra,
                    "aplicabilidade": aplicabilidade,
                    "descricao_produto": trib.get("descricao", ""),
                    "id_base_legal": trib.get("id_base_legal", ""),
                    "desc_base_legal": trib.get("descricao_base_legal", ""),
                    "exibir_aliquota_padrao": trib.get(
                        "exibir_aliquota_padrao",
                        item_resposta.get("exibir_aliquota_padrao"),
                    ),
                    "aliquota_padrao_json": trib.get("aliquota_padrao")
                    or item_resposta.get("aliquota_padrao"),
                }

                if tributo == "PIS":
                    resultado.update(
                        {
                            "cst_pis": f"{cst_code} - {cst_desc}" if cst_code else "",
                            "aliquota_pis": float(trib.get("valor", 0) or 0),
                            "cst_cofins": "",
                            "aliquota_cofins": None,
                        }
                    )
                else:
                    resultado.update(
                        {
                            "cst_pis": "",
                            "aliquota_pis": None,
                            "cst_cofins": f"{cst_code} - {cst_desc}" if cst_code else "",
                            "aliquota_cofins": float(trib.get("valor", 0) or 0),
                        }
                    )

                resultados.append(resultado)

                # Adiciona registros de alíquota padrão como entradas independentes
                aliquota_padrao = trib.get("aliquota_padrao") or item_resposta.get(
                    "aliquota_padrao"
                )
                if aliquota_padrao:
                    for ap in aliquota_padrao:
                        ap_tributo = ap.get("tributo", "").strip().upper()
                        ap_cst_code, ap_cst_desc = self._parse_cst(ap.get("cst", ""))
                        resultados.append(
                            {
                                "ncm_original": ncm_original,
                                "ncm_norm": ncm_norm,
                                "regime_tributario_origem_code": self.map_regime_sistema_para_api(
                                    ap.get("regime_tributario")
                                )
                                or "",
                                "atividade_origem_code": self.map_atividade_sistema_para_api(
                                    trib.get("atividade_origem")
                                )
                                or "",
                                # Alíquota padrão não é específica para um destino
                                "atividade_destino_code": None,
                                "tributo": ap_tributo,
                                "cst_code": ap_cst_code,
                                "cst_desc": ap_cst_desc,
                                "valor": float(ap.get("valor", 0) or 0),
                                "tipo_valor": ap.get("tipo_valor") or "Porcentagem",
                                "regra": "Alíquota Padrão",
                                "aplicabilidade": "",
                                "descricao_produto": trib.get("descricao", ""),
                                "id_base_legal": ap.get("id_base_legal", ""),
                                "desc_base_legal": ap.get("desc_base_legal", ""),
                                "exibir_aliquota_padrao": "1",
                                "aliquota_padrao_json": None,
                            }
                        )

        return resultados
    
    def _salvar_no_cache(self, dados: List[Dict], hash_consulta: str):
        """Persiste os registros processados no cache local."""

        try:
            for item in dados:
                cache_item = PisCofinsCacheModel(
                    ncm_original=item.get("ncm_original", ""),
                    ncm_norm=item.get("ncm_norm", ""),
                    regime_tributario_origem_code=item.get(
                        "regime_tributario_origem_code", ""
                    ),
                    atividade_origem_code=item.get("atividade_origem_code", ""),
                    atividade_destino_code=item.get("atividade_destino_code"),
                    tributo=item.get("tributo"),
                    cst_code=item.get("cst_code"),
                    cst_desc=item.get("cst_desc"),
                    valor=item.get("valor"),
                    tipo_valor=item.get("tipo_valor"),
                    exibir_aliquota_padrao=item.get("exibir_aliquota_padrao"),
                    aliquota_padrao_json=item.get("aliquota_padrao_json"),
                    regra=item.get("regra"),
                    aplicabilidade=item.get("aplicabilidade"),
                    descricao_produto=item.get("descricao_produto"),
                    id_base_legal=item.get("id_base_legal"),
                    desc_base_legal=item.get("desc_base_legal"),
                    hash_consulta=hash_consulta,
                )

                db.session.add(cache_item)

            db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"Erro ao salvar no cache: {e}")
    
    def obter_dados_pis_cofins(self, ncm: str, regime_tributario: str, atividade: str, forcar_api: bool = False) -> List[Dict]:
        """
        Obtém dados PIS/COFINS, primeiro do cache, depois da API se necessário
        """
        ncm_norm = self._norm_ncm(ncm)
        hash_consulta = self._gerar_hash_consulta(ncm, regime_tributario, atividade)

        # Verificar cache primeiro (se não forçar API)
        if not forcar_api:
            dados_cache = PisCofinsCacheModel.buscar_por_hash(hash_consulta)

            # Verificar se o cache não está muito antigo (30 dias)
            if dados_cache:
                data_limite = datetime.now() - timedelta(days=30)
                if dados_cache[0].data_consulta > data_limite:
                    return [item.to_dict() for item in dados_cache]

        # Consultar API
        resposta_api = self._consultar_api(ncm, regime_tributario, atividade)

        if not resposta_api:
            return []

        # Processar resposta baseado no tipo
        if resposta_api.get('resposta') and isinstance(resposta_api['resposta'], list) and resposta_api['resposta']:
            if 'trib' in resposta_api['resposta'][0]:
                dados_processados = self._processar_resposta_tipo2_3(resposta_api)
            else:
                dados_processados = self._processar_resposta_tipo1(resposta_api, ncm)
        else:
            dados_processados = []

        # Salvar no cache
        if dados_processados:
            self._salvar_no_cache(dados_processados, hash_consulta)

        return dados_processados
    
    def validar_cenarios_pis_cofins(
        self,
        empresa_id: int,
        cfop_filtro: str = None,
        tributo_tipo: str = None,
        status_filtro: str = None,
    ) -> Dict:
        """Valida cenários PIS e/ou COFINS de uma empresa"""
        try:
            empresa = Empresa.query.get(empresa_id)
            if not empresa:
                return {"erro": "Empresa não encontrada"}

            regime_tributario = empresa.tributacao
            atividade = empresa.atividade

            if not regime_tributario or not atividade:
                return {"erro": "Empresa sem regime tributário ou atividade definidos"}

            sugestoes = []

            # Buscar cenários PIS
            if tributo_tipo in (None, "PIS"):
                query_pis = CenarioPIS.query.filter(CenarioPIS.empresa_id == empresa_id)
                if cfop_filtro:
                    query_pis = query_pis.filter(CenarioPIS.cfop == cfop_filtro)
                else:
                    query_pis = query_pis.filter(CenarioPIS.cfop.in_(self.CFOPS_VALIDOS))
                if status_filtro:
                    query_pis = query_pis.filter(CenarioPIS.status == status_filtro)

                cenarios_pis = query_pis.all()
                print(
                    f"[DEBUG] Encontrados {len(cenarios_pis)} cenários PIS para empresa {empresa_id}"
                )

                for cenario in cenarios_pis:
                    sugestao = self._validar_cenario_individual(
                        cenario, "PIS", regime_tributario, atividade
                    )
                    if sugestao:
                        sugestoes.append(sugestao)

            # Buscar cenários COFINS
            if tributo_tipo in (None, "COFINS"):
                query_cofins = CenarioCOFINS.query.filter(
                    CenarioCOFINS.empresa_id == empresa_id
                )
                if cfop_filtro:
                    query_cofins = query_cofins.filter(CenarioCOFINS.cfop == cfop_filtro)
                else:
                    query_cofins = query_cofins.filter(
                        CenarioCOFINS.cfop.in_(self.CFOPS_VALIDOS)
                    )
                if status_filtro:
                    query_cofins = query_cofins.filter(
                        CenarioCOFINS.status == status_filtro
                    )

                cenarios_cofins = query_cofins.all()
                print(
                    f"[DEBUG] Encontrados {len(cenarios_cofins)} cenários COFINS para empresa {empresa_id}"
                )

                for cenario in cenarios_cofins:
                    sugestao = self._validar_cenario_individual(
                        cenario, "COFINS", regime_tributario, atividade
                    )
                    if sugestao:
                        sugestoes.append(sugestao)

            return {
                "empresa_id": empresa_id,
                "total_sugestoes": len(sugestoes),
                "sugestoes": sugestoes,
            }

        except Exception as e:
            return {"erro": f"Erro na validação: {str(e)}"}
    
    def _validar_cenario_individual(self, cenario, tributo_tipo: str, regime_tributario: str, atividade: str) -> Optional[Dict]:
        """
        Valida um cenário individual (PIS ou COFINS)
        """
        try:
            ncm = cenario.ncm
            if not ncm:
                return None

            # Obter dados da API/cache
            dados_api = self.obter_dados_pis_cofins(ncm, regime_tributario, atividade)

            if not dados_api:
                return None

            # Determina atividade destino do cliente
            atividade_cliente = getattr(getattr(cenario, "cliente", None), "atividade", None)
            atividade_destino_code = self.map_atividade_sistema_para_api(atividade_cliente)

            cst_cenario = cenario.cst or ""
            aliquota_cenario = float(cenario.aliquota or 0)

            # Filtra registros específicos para a atividade do cliente, quando houver
            candidatos = []
            if atividade_destino_code:
                candidatos = [
                    d
                    for d in dados_api
                    if d.get("tributo") == tributo_tipo
                    and d.get("atividade_destino_code") == atividade_destino_code
                ]

            registro = None
            if cst_cenario:
                registro = next(
                    (d for d in candidatos if d.get("cst_code") == cst_cenario), None
                )
            if not registro and candidatos:
                registro = candidatos[0]

            # Fallback para alíquota padrão
            if not registro:
                padrao = [
                    d
                    for d in dados_api
                    if d.get("atividade_destino_code") is None
                    and d.get("tributo") == tributo_tipo
                    and d.get("regra") == "Alíquota Padrão"
                    and d.get("regime_tributario_origem_code")
                    == self.map_regime_sistema_para_api(regime_tributario)
                ]
                if padrao:
                    registro = padrao[0]

            if not registro:
                return None

            aliquota_api = registro.get("valor", 0)
            cst_api = registro.get("cst_code", "")

            divergencias = []
            if abs(aliquota_api - aliquota_cenario) > 0.01:
                divergencias.append(
                    {
                        "campo": "aliquota",
                        "valor_atual": aliquota_cenario,
                        "valor_sugerido": aliquota_api,
                        "motivo": f"Alíquota {tributo_tipo} divergente da tabela oficial",
                        "regra": registro.get("regra"),
                        "aplicabilidade": registro.get("aplicabilidade"),
                        "base_legal": registro.get("desc_base_legal"),
                    }
                )

            if cst_api and cst_api != cst_cenario:
                divergencias.append(
                    {
                        "campo": "cst",
                        "valor_atual": (cst_cenario or "").split("-")[0].strip(),
                        "valor_sugerido": cst_api,
                        "motivo": f"CST {tributo_tipo} divergente da tabela oficial",
                        "regra": registro.get("regra"),
                        "aplicabilidade": registro.get("aplicabilidade"),
                        "base_legal": registro.get("desc_base_legal"),
                    }
                )

            if not divergencias:
                return None

            empresa = getattr(cenario, "empresa", None)
            cliente = getattr(cenario, "cliente", None)
            produto = getattr(cenario, "produto", None)

            return {
                "cenario_id": cenario.id,
                "tributo": tributo_tipo,
                "ncm": ncm,
                "cfop": cenario.cfop,
                "codigo_produto": getattr(produto, "codigo", None),
                "estado": getattr(cliente, "uf", None),
                "destinacao_empresa": getattr(
                    empresa, "destinacao", getattr(empresa, "atividade", None)
                ),
                "regime_empresa": getattr(empresa, "tributacao", None),
                "destinacao_cliente": getattr(cliente, "destinacao", None),
                "divergencias": divergencias,
                "dados_api": registro,
                "dados_originais": {"aliquota": aliquota_cenario, "cst": cst_cenario},
            }

        except Exception as e:
            print(f"Erro ao validar cenário {cenario.id}: {str(e)}")
            return None
    
    def aplicar_sugestao(self, cenario_id: int, tributo_tipo: str, sugestoes: List[Dict], usuario: str = None) -> Dict:
        """
        Aplica sugestões de correção em um cenário
        """
        try:
            # Buscar cenário
            if tributo_tipo == 'PIS':
                cenario = CenarioPIS.query.get(cenario_id)
            else:
                cenario = CenarioCOFINS.query.get(cenario_id)
            
            if not cenario:
                return {'erro': 'Cenário não encontrado'}
            
            # Salvar dados originais
            dados_originais = {
                'aliquota': float(cenario.aliquota or 0),
                'cst': cenario.cst or ''
            }
            
            # Aplicar sugestões
            for sugestao in sugestoes:
                campo = sugestao.get('campo')
                valor_sugerido = sugestao.get('valor_sugerido')

                # Ignorar campos sem valor sugerido
                if valor_sugerido in [None, '']:
                    continue

                if campo == 'aliquota':
                    # Converter para float quando informado, senão manter nulo
                    cenario.aliquota = float(valor_sugerido)
                elif campo == 'cst':
                    cenario.cst = valor_sugerido
            
            # Salvar alterações
            db.session.commit()
            
            # Registrar no histórico
            resultado = PisCofinValidationResultModel(
                empresa_id=cenario.empresa_id,
                cenario_id=cenario_id,
                dados_originais=dados_originais,
                sugestoes=sugestoes,
                status='aplicado',
                aplicado_em=datetime.now(),
                aplicado_por=usuario
            )
            
            db.session.add(resultado)
            db.session.commit()
            
            return {'sucesso': True, 'cenario_id': cenario_id}
            
        except Exception as e:
            db.session.rollback()
            return {'erro': f'Erro ao aplicar sugestão: {str(e)}'}
    
    def obter_historico_validacoes(self, empresa_id: int) -> List[Dict]:
        """
        Obtém histórico de validações de uma empresa
        """
        try:
            resultados = PisCofinValidationResultModel.query.filter(
                PisCofinValidationResultModel.empresa_id == empresa_id
            ).order_by(PisCofinValidationResultModel.data_validacao.desc()).all()
            
            return [resultado.to_dict() for resultado in resultados]
            
        except Exception as e:
            print(f"Erro ao obter histórico: {str(e)}")
            return []
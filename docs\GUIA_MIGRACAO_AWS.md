# Guia de Migração da Aplicação para a AWS

Este documento descreve o plano de ação para migrar a aplicação de Auditoria Fiscal para uma arquitetura escalável, resiliente e custo-efetiva na AWS, baseada em contêineres e serviços gerenciados.

## 1. Análise da Estrutura Atual e Mudanças Necessárias

A estrutura atual do projeto é bem organizada e o uso de `Dockerfile` e `docker-compose.yml` facilita a migração. No entanto, para operar em um ambiente de nuvem distribuído como o ECS, algumas mudanças críticas são necessárias:

### 1.1. Conexão com o Banco de Dados
- **Problema:** Atualmente, as credenciais do banco de dados provavelmente estão fixas em algum arquivo de configuração ou no `docker-compose.yml`.
- **Solução:** A aplicação (`back/app.py` e `init_db.py`) deve ser modificada para ler as informações de conexão (host, porta, usuário, senha, nome do banco) a partir de **variáveis de ambiente**. O serviço ECS injetará essas variáveis no contêiner em tempo de execução, conectando-se à instância do Amazon RDS.

### 1.2. Armazenamento de Arquivos (Uploads)
- **Problema:** A pasta `uploads/` é usada para armazenar arquivos enviados pelos usuários. Em um ambiente com múltiplos contêineres (auto-scaling), este diretório local não é compartilhado e é efêmero (descartado quando um contêiner para).
- **Solução:** O fluxo de upload de arquivos deve ser reescrito para usar o **Amazon S3**.
    1. Remover completamente o uso do diretório `uploads/`.
    2. Adicionar o SDK da AWS para Python (`boto3`) ao `requirements.txt`.
    3. Modificar a rota de importação (`back/routes/importacao_routes.py`) para, em vez de salvar o arquivo localmente, fazer o upload diretamente para um bucket S3.
    4. Após o upload para o S3 ser bem-sucedido, a rota deve publicar uma mensagem na fila **Amazon SQS** com as informações do arquivo (ex: `{"bucket": "meu-bucket-de-uploads", "key": "caminho/para/o/arquivo.zip", "cliente_id": 123}`).

### 1.3. Processamento Assíncrono (Auditoria)
- **Problema:** O processamento pesado (auditoria de arquivos SPED/XML) provavelmente é executado na mesma thread ou processo que atende à requisição HTTP, bloqueando a aplicação.
- **Solução:** Criaremos um novo script Python, que chamaremos de `worker.py`, na raiz do diretório `back/`.
    - Este `worker.py` será o ponto de entrada para as tasks Fargate de curta duração.
    - Sua função será:
        1. Entrar em um loop para consultar a fila SQS.
        2. Ao receber uma mensagem, baixar o arquivo correspondente do S3.
        3. Chamar os serviços de auditoria existentes (ex: `auditoria_service.py`) para processar o arquivo.
        4. Gravar os resultados no banco de dados (Amazon RDS).
        5. Se o processamento for bem-sucedido, remover a mensagem da fila SQS.
    - Os serviços como `auditoria_service.py` não precisarão de grandes mudanças, apenas receberão o caminho do arquivo baixado do S3 em vez de um caminho local.

### 1.4. Dockerfile
- **Problema:** O `Dockerfile` atual pode estar otimizado para desenvolvimento.
- **Solução:** Vamos revisá-lo para produção. O `Dockerfile` precisará copiar o novo `worker.py` e todas as dependências. O `CMD` ou `ENTRYPOINT` do Dockerfile será o comando para iniciar o servidor web (ex: `gunicorn wsgi:app`). Para as tasks do worker, vamos **sobrescrever** este comando na definição da tarefa do ECS para executar `python back/worker.py`.

---

## 2. Plano de Ação Detalhado (Passo a Passo)

### Passo 1: Configurar a Infraestrutura Base na AWS
1.  **Amazon RDS:** Criar uma instância PostgreSQL no RDS. Guarde as credenciais (endpoint, usuário, senha). Configure os Security Groups para permitir o acesso a partir do serviço ECS.
2.  **Amazon S3:** Criar um bucket S3 para receber os arquivos `.zip` e `.xml`.
3.  **Amazon SQS:** Criar uma fila Standard no SQS. Esta será a fila de tarefas de importação.
4.  **Amazon ECR:** Criar um repositório no Elastic Container Registry para hospedar a imagem Docker da aplicação.

### Passo 2: Adaptar o Código da Aplicação
1.  **Adicionar Dependência:**
    ```bash
    # Adicionar ao seu requirements.txt
    boto3
    ```
2.  **Parametrizar Conexão com BD:**
    - Em `back/app.py` (ou onde a conexão do SQLAlchemy/Flask é criada), substitua os valores fixos por `os.environ.get('DB_HOST')`, `os.environ.get('DB_USER')`, etc.
3.  **Refatorar Upload para S3 e SQS:**
    - Na sua rota de upload, implemente uma lógica parecida com esta:
    ```python
    # Exemplo em back/routes/importacao_routes.py
    import boto3
    import json
    import os

    s3_client = boto3.client('s3')
    sqs_client = boto3.client('sqs')
    
    # ... dentro da sua rota de upload ...
    file = request.files['file']
    cliente_id = request.form['cliente_id']
    s3_bucket = os.environ.get('S3_BUCKET')
    sqs_queue_url = os.environ.get('SQS_QUEUE_URL')

    # 1. Upload para S3
    s3_key = f"uploads/{cliente_id}/{file.filename}"
    s3_client.upload_fileobj(file, s3_bucket, s3_key)

    # 2. Enviar mensagem para SQS
    message_body = json.dumps({
        "s3_bucket": s3_bucket,
        "s3_key": s3_key,
        "cliente_id": cliente_id
    })
    sqs_client.send_message(
        QueueUrl=sqs_queue_url,
        MessageBody=message_body
    )
    
    return {"status": "Importação iniciada com sucesso!"}, 202
    ```
4.  **Criar o `back/worker.py`:**
    ```python
    # Exemplo de estrutura para back/worker.py
    import boto3
    import json
    import os
    import time
    
    # Importe seus serviços de auditoria
    # from services.auditoria_service import processar_auditoria
    
    sqs_client = boto3.client('sqs')
    s3_client = boto3.client('s3')
    
    SQS_QUEUE_URL = os.environ.get('SQS_QUEUE_URL')

    def main():
        while True:
            response = sqs_client.receive_message(
                QueueUrl=SQS_QUEUE_URL,
                MaxNumberOfMessages=1,
                WaitTimeSeconds=20
            )
            
            if 'Messages' in response:
                message = response['Messages'][0]
                receipt_handle = message['ReceiptHandle']
                
                try:
                    body = json.loads(message['Body'])
                    s3_bucket = body['s3_bucket']
                    s3_key = body['s3_key']
                    
                    # Baixar arquivo do S3 para um local temporário no contêiner
                    local_filename = f"/tmp/{os.path.basename(s3_key)}"
                    s3_client.download_file(s3_bucket, s3_key, local_filename)
                    
                    print(f"Processando arquivo: {local_filename}")
                    # Chame sua lógica de negócio principal aqui
                    # processar_auditoria(local_filename, body['cliente_id'])
                    
                    # Se tudo deu certo, delete a mensagem da fila
                    sqs_client.delete_message(
                        QueueUrl=SQS_QUEUE_URL,
                        ReceiptHandle=receipt_handle
                    )
                    print("Processamento concluído com sucesso.")

                except Exception as e:
                    print(f"Erro ao processar mensagem: {e}")
                    # Em um cenário real, considere mover a mensagem para uma Dead-Letter Queue (DLQ)
            else:
                print("Nenhuma mensagem na fila. Aguardando...")
                # Opcional: sair do script se não houver mais mensagens,
                # já que a task Fargate é de curta duração.
                break 

    if __name__ == '__main__':
        main()
    ```

### Passo 3: Construir e Publicar a Imagem Docker
1.  Faça login no ECR.
2.  Construa a imagem Docker.
3.  Marque a imagem com o URI do repositório ECR.
4.  Envie a imagem para o ECR.

```bash
# Exemplo de comandos
aws ecr get-login-password --region seu-region | docker login --username AWS --password-stdin SEU_ACCOUNT_ID.dkr.ecr.seu-region.amazonaws.com
docker build -t auditoria-fiscal .
docker tag auditoria-fiscal:latest SEU_ACCOUNT_ID.dkr.ecr.seu-region.amazonaws.com/auditoria-fiscal:latest
docker push SEU_ACCOUNT_ID.dkr.ecr.seu-region.amazonaws.com/auditoria-fiscal:latest
```

### Passo 4: Configurar e Implantar no ECS com Fargate
1.  **Criar um Cluster ECS.**
2.  **Criar Definições de Tarefa (Task Definitions):**
    - **`auditoria-fiscal-web`:**
        - Apontar para a imagem no ECR.
        - Mapeamento de porta (ex: 8000:8000).
        - Definir as variáveis de ambiente (credenciais do RDS, etc.).
        - Atribuir uma IAM Role com permissões para S3, SQS e RDS.
    - **`auditoria-fiscal-worker`:**
        - Usar a **mesma imagem** do ECR.
        - **Sobrescrever o Comando (Command Override):** `python,back/worker.py`.
        - Definir as mesmas variáveis de ambiente.
        - Atribuir a mesma IAM Role.
3.  **Criar Serviços:**
    - **Serviço Web:**
        - Criar um serviço usando a task definition `auditoria-fiscal-web`.
        - Configurar para usar um Application Load Balancer (ALB).
        - Configurar o Auto Scaling (ex: escalar se o uso de CPU > 75%).
    - **Serviço do Worker (ou Tarefas Agendadas):**
        - Criar um serviço usando a task definition `auditoria-fiscal-worker`.
        - Configurar o número desejado de tarefas para 1 (ou mais, se a fila for grande).
        - Configurar o Auto Scaling para este serviço baseado no tamanho da fila SQS (ex: se `ApproximateNumberOfMessagesVisible` > 10, adicionar mais uma task).

### Passo 5: Testar o Fluxo Completo
1.  Acesse a URL do Load Balancer.
2.  Faça o upload de um arquivo pela interface.
3.  Verifique no S3 se o arquivo apareceu.
4.  Verifique no CloudWatch Logs se a task do worker foi iniciada e processou o arquivo.
5.  Verifique no banco de dados (RDS) se os resultados da auditoria foram salvos.

---

## 3. Próximos Passos e Considerações
- **CI/CD:** Automatize o processo de build e deploy usando **AWS CodePipeline** ou **GitHub Actions**.
- **Logs e Monitoramento:** Centralize todos os logs dos contêineres no **Amazon CloudWatch Logs** para facilitar a depuração.
- **Segurança:** Utilize **IAM Roles** em vez de chaves de acesso fixas. Refine os Security Groups para restringir o tráfego ao mínimo necessário.
- **Gestão de Segredos:** Para as credenciais do banco de dados, considere usar o **AWS Secrets Manager** em vez de variáveis de ambiente simples para uma segurança ainda maior.

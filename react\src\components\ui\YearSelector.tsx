import { useFilterStore } from '@/store/filterStore'
import { Dropdown } from '@/components/ui/Dropdown'

export function YearSelector() {
  const { selectedYear, setYear } = useFilterStore()

  // Gera anos de 2020 até ano atual + 1
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: currentYear - 2019 }, (_, i) => 2020 + i)

  const options = years.map((year) => ({
    value: year.toString(),
    label: year.toString(),
  }))

  const handleChange = (value: string | string[]) => {
    if (Array.isArray(value)) return
    setYear(parseInt(value))
  }

  return (
    <Dropdown
      options={options}
      value={selectedYear.toString()}
      onChange={handleChange}
      className="w-24"
    />
  )
}
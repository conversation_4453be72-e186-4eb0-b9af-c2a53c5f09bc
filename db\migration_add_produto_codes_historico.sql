-- Migração para adicionar campos de códigos de produto na tabela historico_matching_aprendizado
-- Data: 2025-06-16

-- Adicionar campos de códigos de produto se não existirem
DO $$
BEGIN
    -- Adicionar xml_codigo_produto
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'historico_matching_aprendizado' 
        AND column_name = 'xml_codigo_produto'
    ) THEN
        ALTER TABLE historico_matching_aprendizado 
        ADD COLUMN xml_codigo_produto VARCHAR(50);
    END IF;
    
    -- Adicionar sped_codigo_produto
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'historico_matching_aprendizado' 
        AND column_name = 'sped_codigo_produto'
    ) THEN
        ALTER TABLE historico_matching_aprendizado 
        ADD COLUMN sped_codigo_produto VARCHAR(50);
    END IF;
    
    -- Adicionar cliente_id se não existir
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'historico_matching_aprendizado' 
        AND column_name = 'cliente_id'
    ) THEN
        ALTER TABLE historico_matching_aprendizado 
        ADD COLUMN cliente_id INTEGER REFERENCES cliente(id);
    END IF;
END $$;

-- Criar índices para os novos campos
CREATE INDEX IF NOT EXISTS idx_historico_matching_cliente ON historico_matching_aprendizado(cliente_id);
CREATE INDEX IF NOT EXISTS idx_historico_matching_produtos ON historico_matching_aprendizado(empresa_id, cliente_id, xml_codigo_produto, sped_codigo_produto);
CREATE INDEX IF NOT EXISTS idx_historico_matching_xml_codigo ON historico_matching_aprendizado(xml_codigo_produto);
CREATE INDEX IF NOT EXISTS idx_historico_matching_sped_codigo ON historico_matching_aprendizado(sped_codigo_produto);

-- Comentários para documentação
COMMENT ON COLUMN historico_matching_aprendizado.xml_codigo_produto IS 'Código do produto no XML para aprendizado futuro';
COMMENT ON COLUMN historico_matching_aprendizado.sped_codigo_produto IS 'Código do produto no SPED para aprendizado futuro';
COMMENT ON COLUMN historico_matching_aprendizado.cliente_id IS 'ID do cliente para aprendizado específico por cliente';

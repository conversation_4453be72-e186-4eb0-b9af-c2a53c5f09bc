import { useState, useRef, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import type { FilterOption } from '@/hooks/useAdvancedFilters'

interface AdvancedFilterDropdownProps {
  options: FilterOption[]
  selectedValues: string[]
  placeholder: string
  onApply: (values: string[]) => void
  onClear: () => void
  isLoading?: boolean
  disabled?: boolean
  className?: string
}

export function AdvancedFilterDropdown({
  options,
  selectedValues,
  placeholder,
  onApply,
  onClear,
  isLoading = false,
  disabled = false,
  className = ''
}: AdvancedFilterDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [tempSelectedValues, setTempSelectedValues] = useState<string[]>(selectedValues)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Sincronizar valores temporários quando selectedValues mudar
  useEffect(() => {
    setTempSelectedValues(selectedValues)
  }, [selectedValues])

  // Fechar dropdown quando clicar fora
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  // Filtrar opções baseado no termo de busca
  const filteredOptions = options.filter(option =>
    option.value.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.label?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Texto do input baseado na seleção
  const getInputText = useCallback(() => {
    if (selectedValues.length === 0) return ''
    if (selectedValues.length === 1) return selectedValues[0]
    return `${selectedValues.length} selecionados`
  }, [selectedValues])

  // Manipular seleção de checkbox
  const handleCheckboxChange = useCallback((value: string, checked: boolean) => {
    if (checked) {
      setTempSelectedValues((prev: string[]) => [...prev, value])
    } else {
      setTempSelectedValues((prev: string[]) => prev.filter((v: string) => v !== value))
    }
  }, [])

  // Selecionar todos os itens visíveis
  const handleSelectAll = useCallback(() => {
    const visibleValues = filteredOptions.map((option: FilterOption) => option.value)
    const newValues = [...new Set([...tempSelectedValues, ...visibleValues])]
    setTempSelectedValues(newValues)
  }, [filteredOptions, tempSelectedValues])

  // Limpar seleção
  const handleClearSelection = useCallback(() => {
    setTempSelectedValues([])
  }, [])

  // Aplicar filtro
  const handleApply = useCallback(() => {
    onApply(tempSelectedValues)
    setIsOpen(false)
    setSearchTerm('')
  }, [onApply, tempSelectedValues])

  // Cancelar e fechar
  const handleCancel = useCallback(() => {
    setTempSelectedValues(selectedValues)
    setIsOpen(false)
    setSearchTerm('')
  }, [selectedValues])

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Input principal */}
      <input
        ref={inputRef}
        type="text"
        className="w-full px-3 py-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded-md 
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                   focus:ring-2 focus:ring-primary-500 focus:border-primary-500
                   disabled:bg-gray-100 dark:disabled:bg-gray-700 disabled:cursor-not-allowed
                   cursor-pointer"
        placeholder={placeholder}
        value={getInputText()}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        readOnly
        disabled={disabled || isLoading}
      />

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-40 mt-1 bg-white dark:bg-gray-800
                        border border-gray-300 dark:border-gray-600 rounded-md shadow-lg
                        max-h-80 overflow-hidden min-w-[250px]">
          
          {/* Campo de busca */}
          <div className="p-2 border-b border-gray-200 dark:border-gray-700">
            <input
              type="text"
              className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                         focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              placeholder={`Buscar ${placeholder.toLowerCase()}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              autoFocus
            />
          </div>

          {/* Botões de controle */}
          <div className="p-2 border-b border-gray-200 dark:border-gray-700 flex justify-between gap-2">
            <Button
              variant="ghost"
              size="xs"
              onClick={handleSelectAll}
              disabled={filteredOptions.length === 0}
            >
              Todos
            </Button>
            <Button
              variant="ghost"
              size="xs"
              onClick={handleClearSelection}
              disabled={tempSelectedValues.length === 0}
            >
              Limpar
            </Button>
          </div>

          {/* Lista de opções */}
          <div className="max-h-48 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                Carregando opções...
              </div>
            ) : filteredOptions.length === 0 ? (
              <div className="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                {searchTerm ? 'Nenhuma opção encontrada' : 'Nenhuma opção disponível'}
              </div>
            ) : (
              filteredOptions.map((option) => (
                <label
                  key={option.value}
                  className="flex items-center px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    checked={tempSelectedValues.includes(option.value)}
                    onChange={(e) => handleCheckboxChange(option.value, e.target.checked)}
                  />
                  <span className="ml-2 text-xs text-gray-900 dark:text-white flex-1">
                    {option.label || option.value}
                  </span>
                  {option.count && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      ({option.count})
                    </span>
                  )}
                </label>
              ))
            )}
          </div>

          {/* Botões de ação */}
          <div className="p-2 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-2">
            <Button
              variant="ghost"
              size="xs"
              onClick={handleCancel}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              size="xs"
              onClick={handleApply}
              disabled={isLoading}
            >
              Aplicar
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

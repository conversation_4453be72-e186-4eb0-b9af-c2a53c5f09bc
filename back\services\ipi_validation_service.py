"""
Serviço de Validação IPI baseado na tabela TIPI
Implementa regras de negócio para validar cenários IPI contra a tabela TIPI
"""

from models import db, CenarioIPI, TIPI
from models.ipi_validation_result import IPIValidationResult
from sqlalchemy import and_, or_
from decimal import Decimal, InvalidOperation
import logging
from typing import List, Dict, Optional, Tuple

logger = logging.getLogger(__name__)

class IPIValidationService:
    """Serviço para validação de cenários IPI baseado na tabela TIPI"""
    
    # CFOPs que devem ser validados
    CFOPS_VALIDOS = ['5101', '5401', '6101', '6401', '5118', '5122']
    
    # CSTs que podem ter alíquota
    CSTS_COM_ALIQUOTA = ['50', '99']
    
    # CST que sempre deve ter alíquota positiva
    CST_ALIQUOTA_OBRIGATORIA = '50'

    # CSTs que não devem ter alíquota (Saída Isenta, Imune, Suspensão, etc.)
    CSTS_SEM_ALIQUOTA = ['51', '52', '53', '54', '55']
    
    def __init__(self):
        self.logger = logger

    def validar_cenarios_empresa(self, empresa_id: int, filtros: Dict = None) -> Dict:
        """
        Valida todos os cenários IPI de saída de uma empresa
        
        Args:
            empresa_id (int): ID da empresa
            filtros (Dict, optional): Filtros adicionais para os cenários
            
        Returns:
            Dict: Resultado da validação com sugestões
        """
        try:
            # Buscar cenários IPI de saída da empresa
            cenarios = self._buscar_cenarios_para_validacao(empresa_id, filtros)
            
            if not cenarios:
                return {
                    'success': True,
                    'total_cenarios': 0,
                    'cenarios_com_sugestoes': 0,
                    'sugestoes': [],
                    'message': 'Nenhum cenário encontrado para validação'
                }
            
            # Validar cada cenário
            sugestoes = []
            for cenario in cenarios:
                sugestao = self._validar_cenario_individual(cenario)
                if sugestao:
                    sugestoes.append(sugestao)
            
            return {
                'success': True,
                'total_cenarios': len(cenarios),
                'cenarios_com_sugestoes': len(sugestoes),
                'sugestoes': sugestoes,
                'message': f'Validação concluída. {len(sugestoes)} cenários com sugestões de {len(cenarios)} analisados.'
            }
            
        except Exception as e:
            self.logger.error(f"Erro na validação de cenários IPI: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Erro interno na validação'
            }

    def _buscar_cenarios_para_validacao(self, empresa_id: int, filtros: Dict = None) -> List[CenarioIPI]:
        """
        Busca cenários IPI que devem ser validados

        Args:
            empresa_id (int): ID da empresa
            filtros (Dict, optional): Filtros adicionais

        Returns:
            List[CenarioIPI]: Lista de cenários para validação
        """
        # Se for validação de cenário específico
        if filtros and filtros.get('cenario_id'):
            cenario = CenarioIPI.query.filter(
                and_(
                    CenarioIPI.id == filtros['cenario_id'],
                    CenarioIPI.empresa_id == empresa_id,
                    CenarioIPI.direcao == 'saida'
                )
            ).first()
            return [cenario] if cenario else []

        # Validação geral da empresa
        query = CenarioIPI.query.filter(
            and_(
                CenarioIPI.empresa_id == empresa_id,
                CenarioIPI.direcao == 'saida',
                CenarioIPI.cfop.in_(self.CFOPS_VALIDOS)
            )
        )

        # Aplicar filtros adicionais se fornecidos
        if filtros:
            if filtros.get('status'):
                query = query.filter(CenarioIPI.status == filtros['status'])
            if filtros.get('ncm'):
                query = query.filter(CenarioIPI.ncm.like(f"%{filtros['ncm']}%"))
            if filtros.get('cliente_id'):
                query = query.filter(CenarioIPI.cliente_id == filtros['cliente_id'])
            if filtros.get('produto_id'):
                query = query.filter(CenarioIPI.produto_id == filtros['produto_id'])

        return query.all()

    def _validar_cenario_individual(self, cenario: CenarioIPI) -> Optional[Dict]:
        """
        Valida um cenário individual contra a tabela TIPI
        
        Args:
            cenario (CenarioIPI): Cenário a ser validado
            
        Returns:
            Optional[Dict]: Sugestão de correção ou None se não houver problemas
        """
        if not cenario.ncm:
            return None
            
        # Buscar dados na TIPI
        tipi_data = TIPI.buscar_por_ncm(cenario.ncm, cenario.ex)
        
        if not tipi_data:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'NCM_NAO_ENCONTRADO',
                'descricao': f'NCM {cenario.ncm} não encontrado na tabela TIPI',
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': None,
                'pode_aplicar_automaticamente': False
            }
        
        # Obter alíquota recomendada da TIPI
        aliquota_tipi = tipi_data.get_aliquota_numerica()
        aliquota_cenario = float(cenario.aliquota) if cenario.aliquota else 0.0
        
        # Validar regras de CST vs alíquota
        problemas = self._identificar_problemas_cst_aliquota(cenario, aliquota_tipi)
        
        if problemas:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'CST_ALIQUOTA_INCORRETA',
                'descricao': problemas['descricao'],
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': {
                    'cst_sugerido': problemas['cst_sugerido'],
                    'aliquota_sugerida': aliquota_tipi,
                    'aliquota_tipi': aliquota_tipi,
                    'tipi_descricao': tipi_data.descricao
                },
                'pode_aplicar_automaticamente': True
            }
        
        return None

    def _identificar_problemas_cst_aliquota(self, cenario: CenarioIPI, aliquota_tipi: float) -> Optional[Dict]:
        """
        Identifica problemas de CST vs alíquota baseado nas regras de negócio
        
        Args:
            cenario (CenarioIPI): Cenário sendo validado
            aliquota_tipi (float): Alíquota recomendada pela TIPI
            
        Returns:
            Optional[Dict]: Problema identificado ou None
        """
        cst_atual = cenario.cst
        aliquota_atual = float(cenario.aliquota) if cenario.aliquota else 0.0
        
        # Regra 1: CST 50 (tributado) deve ter alíquota se a TIPI indicar.
        if cst_atual == self.CST_ALIQUOTA_OBRIGATORIA and aliquota_atual == 0.0 and aliquota_tipi > 0.0:
            return {
                'descricao': f'CST {self.CST_ALIQUOTA_OBRIGATORIA} deve ter alíquota positiva. TIPI indica {aliquota_tipi}%',
                'cst_sugerido': self.CST_ALIQUOTA_OBRIGATORIA,
                'aliquota_sugerida': aliquota_tipi
            }

        # Regra 2: CSTs que não devem ter alíquota (51 a 55) com alíquota > 0.
        if cst_atual in self.CSTS_SEM_ALIQUOTA and aliquota_atual > 0.0:
            if aliquota_tipi > 0.0:
                # O CST está errado, pois a TIPI indica que há alíquota.
                return {
                    'descricao': f'CST {cst_atual} é para operações sem alíquota, mas a TIPI indica {aliquota_tipi}%. Sugestão: usar CST {self.CST_ALIQUOTA_OBRIGATORIA}.',
                    'cst_sugerido': self.CST_ALIQUOTA_OBRIGATORIA,
                    'aliquota_sugerida': aliquota_tipi
                }
            else:
                # A alíquota está errada, deveria ser zero.
                return {
                    'descricao': f'CST {cst_atual} não deve ter alíquota, mas está com {aliquota_atual}%. Sugestão: zerar a alíquota.',
                    'cst_sugerido': cst_atual,
                    'aliquota_sugerida': 0.0
                }
        
        # Regra 3: Se cenário tem CST 50 com alíquota 0, mas TIPI indica 0, deveria ser CST 51
        if cst_atual == self.CST_ALIQUOTA_OBRIGATORIA and aliquota_atual == 0.0 and aliquota_tipi == 0.0:
            return {
                'descricao': f'Cenário com alíquota 0% deveria usar CST 51 (TIPI indica NT/0%)',
                'cst_sugerido': '51',
                'aliquota_sugerida': 0.0
            }
        
        # Regra 4: CSTs que podem ter alíquota (50, 99) - verificar se alíquota está correta conforme TIPI
        if cst_atual in self.CSTS_COM_ALIQUOTA and abs(aliquota_atual - aliquota_tipi) > 0.01:
            return {
                'descricao': f'Alíquota atual {aliquota_atual}% difere da TIPI {aliquota_tipi}% para CST {cst_atual}',
                'cst_sugerido': cst_atual,
                'aliquota_sugerida': aliquota_tipi
            }
        
        return None

    def _cenario_to_dict(self, cenario: CenarioIPI) -> Dict:
        """
        Converte cenário para dicionário com informações relevantes
        
        Args:
            cenario (CenarioIPI): Cenário a ser convertido
            
        Returns:
            Dict: Dados do cenário
        """
        return {
            'id': cenario.id,
            'ncm': cenario.ncm,
            'cfop': cenario.cfop,
            'cst': cenario.cst,
            'aliquota': float(cenario.aliquota) if cenario.aliquota else 0.0,
            'ex': cenario.ex,
            'cliente_id': cenario.cliente_id,
            'produto_id': cenario.produto_id,
            'status': cenario.status
        }

    def aplicar_sugestao(self, cenario_id: int, sugestao: Dict, usuario: str = None) -> Dict:
        """
        Aplica uma sugestão de correção a um cenário

        Args:
            cenario_id (int): ID do cenário
            sugestao (Dict): Dados da sugestão a ser aplicada
            usuario (str, optional): Nome do usuário que aplicou a sugestão

        Returns:
            Dict: Resultado da aplicação
        """
        try:
            cenario = CenarioIPI.query.get(cenario_id)
            if not cenario:
                return {
                    'success': False,
                    'message': 'Cenário não encontrado'
                }

            # Salvar dados originais para histórico
            dados_originais = self._cenario_to_dict(cenario)

            # Aplicar correções
            if 'cst_sugerido' in sugestao:
                cenario.cst = sugestao['cst_sugerido']

            if 'aliquota_sugerida' in sugestao:
                cenario.aliquota = Decimal(str(sugestao['aliquota_sugerida']))

            # Salvar histórico da validação
            self._salvar_historico_validacao(
                cenario,
                dados_originais,
                sugestao,
                usuario,
                aplicada=True
            )

            db.session.commit()

            return {
                'success': True,
                'message': 'Sugestão aplicada com sucesso',
                'cenario_atualizado': self._cenario_to_dict(cenario)
            }

        except Exception as e:
            db.session.rollback()
            self.logger.error(f"Erro ao aplicar sugestão: {str(e)}")
            return {
                'success': False,
                'message': f'Erro ao aplicar sugestão: {str(e)}'
            }

    def _salvar_historico_validacao(self, cenario: CenarioIPI, dados_originais: Dict,
                                   sugestao: Dict, usuario: str = None, aplicada: bool = False):
        """
        Salva o histórico da validação IPI

        Args:
            cenario (CenarioIPI): Cenário validado
            dados_originais (Dict): Dados originais do cenário
            sugestao (Dict): Sugestão aplicada
            usuario (str, optional): Usuário que aplicou
            aplicada (bool): Se a sugestão foi aplicada
        """
        try:
            # Buscar dados da TIPI para o histórico
            tipi_data = None
            if cenario.ncm:
                tipi = TIPI.buscar_por_ncm(cenario.ncm, cenario.ex)
                if tipi:
                    tipi_data = tipi.to_dict()

            # Criar registro de validação
            validacao = IPIValidationResult.criar_validacao(
                empresa_id=cenario.empresa_id,
                escritorio_id=cenario.escritorio_id,
                cenario_id=cenario.id,
                tipo_problema='CST_ALIQUOTA_INCORRETA',
                descricao_problema=f"Correção aplicada: CST {dados_originais.get('cst')} → {sugestao.get('cst_sugerido')}, Alíquota {dados_originais.get('aliquota')}% → {sugestao.get('aliquota_sugerida')}%",
                dados_originais=dados_originais,
                sugestao=sugestao,
                tipi_data=tipi_data
            )

            if aplicada:
                validacao.marcar_como_aplicada(usuario, automatica=True)

        except Exception as e:
            self.logger.error(f"Erro ao salvar histórico de validação: {str(e)}")
            # Não falhar a operação principal por erro no histórico

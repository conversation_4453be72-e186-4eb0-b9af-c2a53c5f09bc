"""
Modelo para armazenar resultados de validações IPI
"""

from .escritorio import db
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import JSONB
from datetime import datetime

class IPIValidationResult(db.Model):
    """Modelo para armazenar histórico de validações IPI baseadas na tabela TIPI"""
    __tablename__ = 'ipi_validation_results'

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.<PERSON>ey('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('escritorio.id'), nullable=False)
    cenario_id = db.Column(db.Integer, db.<PERSON>ey('cenario_ipi.id'), nullable=False)
    
    # Dados da validação
    tipo_problema = db.Column(db.String(50), nullable=False)  # NCM_NAO_ENCONTRADO, CST_ALIQUOTA_INCORRETA
    descricao_problema = db.Column(db.Text, nullable=False)
    
    # Estado antes da correção
    dados_originais = db.Column(JSONB, nullable=False)  # NCM, CFOP, CST, alíquota originais
    
    # Sugestão aplicada
    sugestao_aplicada = db.Column(JSONB, nullable=True)  # CST sugerido, alíquota sugerida, etc.
    
    # Dados da TIPI consultada
    tipi_data = db.Column(JSONB, nullable=True)  # NCM, EX, descrição, alíquota da TIPI
    
    # Status da validação
    status = db.Column(db.String(20), default='pendente')  # pendente, aplicada, rejeitada
    aplicada_automaticamente = db.Column(db.Boolean, default=False)
    
    # Metadados
    data_validacao = db.Column(db.DateTime, server_default=func.now())
    data_aplicacao = db.Column(db.DateTime, nullable=True)
    usuario_aplicacao = db.Column(db.String(100), nullable=True)
    
    # Relacionamentos
    empresa = db.relationship('Empresa', backref='ipi_validations')
    escritorio = db.relationship('Escritorio', backref='ipi_validations')
    cenario = db.relationship('CenarioIPI', backref='ipi_validations')

    def __repr__(self):
        return f"<IPIValidationResult {self.id} - Cenário {self.cenario_id} - {self.tipo_problema}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cenario_id': self.cenario_id,
            'tipo_problema': self.tipo_problema,
            'descricao_problema': self.descricao_problema,
            'dados_originais': self.dados_originais,
            'sugestao_aplicada': self.sugestao_aplicada,
            'tipi_data': self.tipi_data,
            'status': self.status,
            'aplicada_automaticamente': self.aplicada_automaticamente,
            'data_validacao': self.data_validacao.isoformat() if self.data_validacao else None,
            'data_aplicacao': self.data_aplicacao.isoformat() if self.data_aplicacao else None,
            'usuario_aplicacao': self.usuario_aplicacao
        }

    @classmethod
    def criar_validacao(cls, empresa_id, escritorio_id, cenario_id, tipo_problema, 
                       descricao_problema, dados_originais, sugestao=None, tipi_data=None):
        """
        Cria um novo registro de validação IPI
        
        Args:
            empresa_id (int): ID da empresa
            escritorio_id (int): ID do escritório
            cenario_id (int): ID do cenário validado
            tipo_problema (str): Tipo do problema identificado
            descricao_problema (str): Descrição do problema
            dados_originais (dict): Dados originais do cenário
            sugestao (dict, optional): Sugestão de correção
            tipi_data (dict, optional): Dados da TIPI consultada
            
        Returns:
            IPIValidationResult: Nova instância criada
        """
        validacao = cls(
            empresa_id=empresa_id,
            escritorio_id=escritorio_id,
            cenario_id=cenario_id,
            tipo_problema=tipo_problema,
            descricao_problema=descricao_problema,
            dados_originais=dados_originais,
            sugestao_aplicada=sugestao,
            tipi_data=tipi_data
        )
        
        db.session.add(validacao)
        return validacao

    def marcar_como_aplicada(self, usuario=None, automatica=False):
        """
        Marca a validação como aplicada
        
        Args:
            usuario (str, optional): Nome do usuário que aplicou
            automatica (bool): Se foi aplicada automaticamente
        """
        self.status = 'aplicada'
        self.data_aplicacao = datetime.utcnow()
        self.usuario_aplicacao = usuario
        self.aplicada_automaticamente = automatica

    def marcar_como_rejeitada(self, usuario=None):
        """
        Marca a validação como rejeitada
        
        Args:
            usuario (str, optional): Nome do usuário que rejeitou
        """
        self.status = 'rejeitada'
        self.data_aplicacao = datetime.utcnow()
        self.usuario_aplicacao = usuario

    @classmethod
    def buscar_por_empresa(cls, empresa_id, status=None, limit=100):
        """
        Busca validações por empresa
        
        Args:
            empresa_id (int): ID da empresa
            status (str, optional): Filtrar por status
            limit (int): Limite de registros
            
        Returns:
            List[IPIValidationResult]: Lista de validações
        """
        query = cls.query.filter(cls.empresa_id == empresa_id)
        
        if status:
            query = query.filter(cls.status == status)
            
        return query.order_by(cls.data_validacao.desc()).limit(limit).all()

    @classmethod
    def buscar_por_cenario(cls, cenario_id):
        """
        Busca validações por cenário
        
        Args:
            cenario_id (int): ID do cenário
            
        Returns:
            List[IPIValidationResult]: Lista de validações do cenário
        """
        return cls.query.filter(cls.cenario_id == cenario_id).order_by(cls.data_validacao.desc()).all()

    @classmethod
    def estatisticas_empresa(cls, empresa_id):
        """
        Retorna estatísticas de validações para uma empresa
        
        Args:
            empresa_id (int): ID da empresa
            
        Returns:
            Dict: Estatísticas das validações
        """
        total = cls.query.filter(cls.empresa_id == empresa_id).count()
        pendentes = cls.query.filter(cls.empresa_id == empresa_id, cls.status == 'pendente').count()
        aplicadas = cls.query.filter(cls.empresa_id == empresa_id, cls.status == 'aplicada').count()
        rejeitadas = cls.query.filter(cls.empresa_id == empresa_id, cls.status == 'rejeitada').count()
        
        return {
            'total': total,
            'pendentes': pendentes,
            'aplicadas': aplicadas,
            'rejeitadas': rejeitadas,
            'taxa_aplicacao': (aplicadas / total * 100) if total > 0 else 0
        }

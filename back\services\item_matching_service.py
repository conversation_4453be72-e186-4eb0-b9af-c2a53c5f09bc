"""
Serviço de Matching Inteligente entre Itens XML e SPED
Utiliza embeddings da OpenAI e algoritmos híbridos para encontrar correspondências
"""

import os
import json
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import openai
from sqlalchemy import text, and_, or_
from models import db
from models.nota_fiscal_item import NotaFiscalItem
from models.item_nota_entrada import ItemNotaEntrada
from models.produto import Produto
from models.produto_entrada import ProdutoEntrada
from models.nota_entrada import NotaEntrada
from models.importacao_xml import ImportacaoXML
from utils.cfop_pair_utils import load_cfop_pair_rules

class ItemMatchingService:
    def __init__(self, empresa_id: int, escritorio_id: int):
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.embedding_model = "text-embedding-3-large"
        self.cfop_pairs = load_cfop_pair_rules()
        
        # Pesos para cálculo de score final
        self.weights = {
            'embedding_similarity': 0.1,
            'value_similarity': 0.5,
            'quantity_similarity': 0.15,
            'unit_similarity': 0.1,
            'ncm_similarity': 0.1,
            'cfop_similarity': 0.3
        }
        
        # Tolerâncias para comparações
        self.tolerances = {
            'value_percent': 0.02,  # 2% de tolerância para valores
            'quantity_percent': 0.05,  # 5% de tolerância para quantidades
        }

    def find_matches_for_note(self, chave_nf: str, force_recalculate: bool = False) -> Dict[str, Any]:
        """
        Encontra matches entre itens XML e SPED para uma nota fiscal específica
        
        Args:
            chave_nf: Chave da nota fiscal
            force_recalculate: Se True, força recálculo mesmo se já existir
            
        Returns:
            Dict com matches encontrados e estatísticas
        """
        try:
            # Buscar itens XML da nota
            itens_xml = self._get_xml_items(chave_nf)
            if not itens_xml:
                return {
                    'success': False,
                    'message': 'Nenhum item XML encontrado para esta nota'
                }
            
            # Buscar itens SPED da nota
            itens_sped = self._get_sped_items(chave_nf)
            if not itens_sped:
                return {
                    'success': False,
                    'message': 'Nenhum item SPED encontrado para esta nota'
                }
            
            # Executar algoritmo de matching
            matches = self._execute_matching_algorithm(itens_xml, itens_sped)
            
            # Calcular estatísticas
            stats = self._calculate_matching_stats(matches, itens_xml, itens_sped)
            
            return {
                'success': True,
                'chave_nf': chave_nf,
                'matches': matches,
                'statistics': stats,
                'total_xml_items': len(itens_xml),
                'total_sped_items': len(itens_sped)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao processar matching: {str(e)}'
            }

    def _get_xml_items(self, chave_nf: str) -> List[Dict]:
        """Busca itens XML da nota fiscal"""
        query = db.session.query(
            NotaFiscalItem,
            Produto.descricao.label('produto_descricao'),
            Produto.codigo.label('produto_codigo')
        ).outerjoin(
            Produto, NotaFiscalItem.produto_id == Produto.id
        ).filter(
            NotaFiscalItem.empresa_id == self.empresa_id,
            NotaFiscalItem.chave_nf == chave_nf,
            NotaFiscalItem.tipo_nota == '0'  # Entrada
        ).all()
        
        items = []
        for item, produto_desc, produto_codigo in query:
            items.append({
                'id': item.id,
                'produto_id': item.produto_id,
                'cliente_id': item.cliente_id,  # Incluir cliente_id
                'codigo_produto': produto_codigo,
                'descricao': produto_desc or 'Sem descrição',
                'quantidade': float(item.quantidade) if item.quantidade else 0,
                'valor_unitario': float(item.valor_unitario) if item.valor_unitario else 0,
                'valor_total': float(item.valor_total) if item.valor_total else 0,
                'unidade': item.unidade_comercial or '',
                'ncm': item.ncm or '',
                'cfop': item.cfop or '',
                'numero_nf': item.numero_nf
            })
        
        return items

    def _get_sped_items(self, chave_nf: str) -> List[Dict]:
        """Busca itens SPED da nota fiscal"""
        query = db.session.query(
            ItemNotaEntrada,
            ProdutoEntrada.descricao.label('produto_descricao'),
            ProdutoEntrada.ncm.label('produto_ncm'),
            NotaEntrada.numero_nf
        ).join(
            NotaEntrada, ItemNotaEntrada.nota_entrada_id == NotaEntrada.id
        ).outerjoin(
            ProdutoEntrada, ItemNotaEntrada.produto_entrada_id == ProdutoEntrada.id
        ).filter(
            ItemNotaEntrada.empresa_id == self.empresa_id,
            NotaEntrada.chave_nf == chave_nf
        ).all()

        items = []
        for item, produto_desc, produto_ncm, numero_nf in query:
            items.append({
                'id': item.id,
                'produto_entrada_id': item.produto_entrada_id,
                'descricao': produto_desc or item.descricao_complementar or 'Sem descrição',
                'quantidade': float(item.quantidade) if item.quantidade else 0,
                'valor_item': float(item.valor_item) if item.valor_item else 0,
                'unidade': item.unidade or '',
                'cfop': item.cfop or '',
                'cod_item': item.cod_item,
                'num_item': item.num_item,
                'numero_nf': numero_nf,
                'ncm': produto_ncm or '',  # NCM do produto SPED
                # Dados tributários SPED
                'cst_icms': item.cst_icms,
                'valor_bc_icms': float(item.valor_bc_icms) if item.valor_bc_icms else 0,
                'aliquota_icms': float(item.aliquota_icms) if item.aliquota_icms else 0,
                'valor_icms': float(item.valor_icms) if item.valor_icms else 0,
                'valor_bc_icms_st': float(item.valor_bc_icms_st) if item.valor_bc_icms_st else 0,
                'aliquota_st': float(item.aliquota_st) if item.aliquota_st else 0,
                'valor_icms_st': float(item.valor_icms_st) if item.valor_icms_st else 0,
                'origem_icms': item.origem_icms,
                'cst_ipi': item.cst_ipi,
                'valor_bc_ipi': float(item.valor_bc_ipi) if item.valor_bc_ipi else 0,
                'aliquota_ipi': float(item.aliquota_ipi) if item.aliquota_ipi else 0,
                'valor_ipi': float(item.valor_ipi) if item.valor_ipi else 0,
                'cst_pis': item.cst_pis,
                'valor_bc_pis': float(item.valor_bc_pis) if item.valor_bc_pis else 0,
                'aliquota_pis': float(item.aliquota_pis) if item.aliquota_pis else 0,
                'valor_pis': float(item.valor_pis) if item.valor_pis else 0,
                'cst_cofins': item.cst_cofins,
                'valor_bc_cofins': float(item.valor_bc_cofins) if item.valor_bc_cofins else 0,
                'aliquota_cofins': float(item.aliquota_cofins) if item.aliquota_cofins else 0,
                'valor_cofins': float(item.valor_cofins) if item.valor_cofins else 0
            })

        return items

    def _execute_matching_algorithm(self, itens_xml: List[Dict], itens_sped: List[Dict]) -> List[Dict]:
        """
        Executa o algoritmo híbrido de matching
        1. Tentativa de match direto (valor + quantidade + descrição)
        2. Match por embeddings para itens não pareados
        3. Agrupamento para casos complexos
        """
        matches = []
        xml_matched = set()
        sped_matched = set()

        # FASE 1: Match direto por critérios exatos (apenas itens não pareados)
        itens_xml_restantes = [item for item in itens_xml if item['id'] not in xml_matched]
        itens_sped_restantes = [item for item in itens_sped if item['id'] not in sped_matched]

        direct_matches = self._direct_matching(itens_xml_restantes, itens_sped_restantes)
        for match in direct_matches:
            matches.append(match)
            xml_matched.add(match['xml_item']['id'])
            sped_matched.add(match['sped_item']['id'])
        
        # FASE 2: Match por embeddings para itens restantes
        remaining_xml = [item for item in itens_xml if item['id'] not in xml_matched]
        remaining_sped = [item for item in itens_sped if item['id'] not in sped_matched]
        
        if remaining_xml and remaining_sped:
            embedding_matches = self._embedding_matching(remaining_xml, remaining_sped)
            for match in embedding_matches:
                matches.append(match)
                xml_matched.add(match['xml_item']['id'])
                sped_matched.add(match['sped_item']['id'])
        
        # FASE 3: Identificar itens não pareados para possível agrupamento
        unmatched_xml = [item for item in itens_xml if item['id'] not in xml_matched]
        unmatched_sped = [item for item in itens_sped if item['id'] not in sped_matched]
        
        # Adicionar itens não pareados como matches com score baixo
        for xml_item in unmatched_xml:
            matches.append({
                'xml_item': xml_item,
                'sped_item': None,
                'match_type': 'unmatched_xml',
                'confidence_score': 0.0,
                'details': {
                    'reason': 'Item XML sem correspondência no SPED'
                }
            })
        
        for sped_item in unmatched_sped:
            matches.append({
                'xml_item': None,
                'sped_item': sped_item,
                'match_type': 'unmatched_sped',
                'confidence_score': 0.0,
                'details': {
                    'reason': 'Item SPED sem correspondência no XML'
                }
            })
        
        return matches

    def _direct_matching(self, itens_xml: List[Dict], itens_sped: List[Dict]) -> List[Dict]:
        matches = []
        matched_xml_indices = set()
        matched_sped_indices = set()

        for i, xml_item in enumerate(itens_xml):
            best_match_idx = None
            best_score = 0.6
            best_position_diff = float('inf')
                        
            for j, sped_item in enumerate(itens_sped):
                if j in matched_sped_indices:
                    continue

                score = self._calculate_direct_match_score(xml_item, sped_item)

                if score >= best_score:
                    position_diff = abs(i - j)
                    if (score > best_score) or (score == best_score and position_diff < best_position_diff):
                        best_score = score
                        best_match_idx = j
                        best_position_diff = position_diff

            if best_match_idx is not None:
                matches.append({
                    'xml_item': xml_item,
                    'sped_item': itens_sped[best_match_idx],
                    'match_type': 'direct',
                    'confidence_score': best_score,
                    'details': {
                        'method': 'Direct matching by value, quantity, description and position',
                        'position_diff': best_position_diff
                    }
                })
                matched_xml_indices.add(i)
                matched_sped_indices.add(best_match_idx)
            else:
                pass

        return matches

    def _calculate_direct_match_score(self, xml_item: Dict, sped_item: Dict) -> float:
        """
        Calcula score para match direto baseado em critérios objetivos
        """
        scores = []

        # 1. Comparação de valores (peso alto)
        xml_value = xml_item.get('valor_total', 0)
        sped_value = sped_item.get('valor_item', 0)

        if xml_value > 0 and sped_value > 0:
            value_diff = abs(xml_value - sped_value) / max(xml_value, sped_value)
            value_score = max(0, 1 - (value_diff / self.tolerances['value_percent']))
            scores.append(('value', value_score, 0.4))

        # 2. Comparação de quantidades
        xml_qty = xml_item.get('quantidade', 0)
        sped_qty = sped_item.get('quantidade', 0)

        if xml_qty > 0 and sped_qty > 0:
            qty_diff = abs(xml_qty - sped_qty) / max(xml_qty, sped_qty)
            qty_score = max(0, 1 - (qty_diff / self.tolerances['quantity_percent']))
            scores.append(('quantity', qty_score, 0.3))

        # 3. Comparação de unidades
        cfop_xml = str(xml_item.get('cfop', '')).strip()
        cfop_sped = str(sped_item.get('cfop', '')).strip()
        if cfop_xml and cfop_sped:
            if cfop_xml == cfop_sped:
                cfop_score = 0.0
            elif cfop_sped in self.cfop_pairs.get(cfop_xml, []):
                cfop_score = 1.5
            else:
                cfop_score = 0.7
            scores.append(('cfop', cfop_score, 0.2))

        # 4. Comparação de unidades
        xml_unit = xml_item.get('unidade', '').upper().strip()
        sped_unit = sped_item.get('unidade', '').upper().strip()

        if xml_unit and sped_unit:
            unit_score = 1.0 if xml_unit == sped_unit else 0.0
            scores.append(('unit', unit_score, 0.1))

        # 5. Comparação básica de descrição (palavras-chave)
        xml_desc = xml_item.get('descricao', '').lower()
        sped_desc = sped_item.get('descricao', '').lower()

        if xml_desc and sped_desc:
            desc_score = self._calculate_description_similarity(xml_desc, sped_desc)
            scores.append(('description', desc_score, 0.2))

        # Calcular score ponderado
        if not scores:
            return 0.0

        total_weight = sum(weight for _, _, weight in scores)
        weighted_score = sum(score * weight for _, score, weight in scores) / total_weight

        # Garantir que retorna Python float
        return float(weighted_score)

    def _calculate_description_similarity(self, desc1: str, desc2: str) -> float:
        """
        Calcula similaridade básica entre descrições usando palavras-chave
        """
        if not desc1 or not desc2:
            return 0.0

        # Remover palavras muito comuns
        stop_words = {'de', 'da', 'do', 'das', 'dos', 'e', 'ou', 'com', 'para', 'por', 'em', 'a', 'o'}

        words1 = set(word.strip() for word in desc1.split() if len(word.strip()) > 2 and word.strip() not in stop_words)
        words2 = set(word.strip() for word in desc2.split() if len(word.strip()) > 2 and word.strip() not in stop_words)

        if not words1 or not words2:
            return 0.0

        # Calcular Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        return intersection / union if union > 0 else 0.0

    def _embedding_matching(self, itens_xml: List[Dict], itens_sped: List[Dict]) -> List[Dict]:
        """
        Matching usando embeddings da OpenAI para itens que não tiveram match direto
        """
        if not os.getenv('OPENAI_API_KEY'):
            return []

        try:
            # Preparar textos para embeddings
            xml_texts = []
            sped_texts = []

            for xml_item in itens_xml:
                text = self._prepare_text_for_embedding(xml_item, 'xml')
                xml_texts.append(text)

            for sped_item in itens_sped:
                text = self._prepare_text_for_embedding(sped_item, 'sped')
                sped_texts.append(text)

            # Gerar embeddings
            xml_embeddings = self._get_embeddings(xml_texts)
            sped_embeddings = self._get_embeddings(sped_texts)

            if not xml_embeddings or not sped_embeddings:
                return []

            # Calcular similaridades e encontrar matches
            matches = []
            used_sped_indices = set()

            for i, xml_embedding in enumerate(xml_embeddings):
                best_match_idx = None
                best_similarity = 0.0

                for j, sped_embedding in enumerate(sped_embeddings):
                    if j in used_sped_indices:
                        continue

                    # Calcular similaridade coseno
                    similarity = self._cosine_similarity(xml_embedding, sped_embedding)

                    # Combinar com outros fatores
                    combined_score = self._calculate_combined_score(
                        itens_xml[i], itens_sped[j], similarity
                    )

                    if combined_score > best_similarity and combined_score >= 0.6:
                        best_similarity = combined_score
                        best_match_idx = j

                if best_match_idx is not None:
                    matches.append({
                        'xml_item': itens_xml[i],
                        'sped_item': itens_sped[best_match_idx],
                        'match_type': 'embedding',
                        'confidence_score': best_similarity,
                        'details': {
                            'method': 'OpenAI embedding similarity',
                            'embedding_similarity': self._cosine_similarity(
                                xml_embeddings[i], sped_embeddings[best_match_idx]
                            )
                        }
                    })
                    used_sped_indices.add(best_match_idx)

            return matches

        except Exception as e:
            print(f"Erro no matching por embeddings: {str(e)}")
            return []

    def _prepare_text_for_embedding(self, item: Dict, source: str) -> str:
        """
        Prepara texto do item para gerar embedding
        """
        parts = []

        # Descrição do produto
        desc = item.get('descricao', '')
        if desc:
            parts.append(f"Produto: {desc}")

        # Valores
        if source == 'xml':
            valor = item.get('valor_total', 0)
            qty = item.get('quantidade', 0)
        else:  # sped
            valor = item.get('valor_item', 0)
            qty = item.get('quantidade', 0)

        if valor > 0:
            parts.append(f"Valor: R$ {valor:.2f}")

        if qty > 0:
            parts.append(f"Quantidade: {qty}")

        # Unidade
        unidade = item.get('unidade', '')
        if unidade:
            parts.append(f"Unidade: {unidade}")

        # NCM (se disponível)
        ncm = item.get('ncm', '')
        if ncm:
            parts.append(f"NCM: {ncm}")

        return " | ".join(parts)

    def _get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Gera embeddings usando OpenAI
        """
        try:
            response = self.client.embeddings.create(
                model=self.embedding_model,
                input=texts
            )

            return [data.embedding for data in response.data]

        except Exception as e:
            print(f"Erro ao gerar embeddings: {str(e)}")
            return []

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        Calcula similaridade coseno entre dois vetores
        """
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)

            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            similarity = dot_product / (norm1 * norm2)

            # Converter numpy scalar para Python float
            if hasattr(similarity, 'item'):
                return float(similarity.item())
            return float(similarity)

        except Exception:
            return 0.0

    def _calculate_combined_score(self, xml_item: Dict, sped_item: Dict, embedding_similarity: float) -> float:
        """
        Combina similaridade de embedding com outros fatores
        """
        scores = {
            'embedding_similarity': embedding_similarity
        }

        # Adicionar score de valor se disponível
        xml_value = xml_item.get('valor_total', 0)
        sped_value = sped_item.get('valor_item', 0)

        if xml_value > 0 and sped_value > 0:
            value_diff = abs(xml_value - sped_value) / max(xml_value, sped_value)
            scores['value_similarity'] = max(0, 1 - value_diff)

        # Adicionar score de quantidade
        xml_qty = xml_item.get('quantidade', 0)
        sped_qty = sped_item.get('quantidade', 0)

        if xml_qty > 0 and sped_qty > 0:
            qty_diff = abs(xml_qty - sped_qty) / max(xml_qty, sped_qty)
            scores['quantity_similarity'] = max(0, 1 - qty_diff)

        # Adicionar score de unidade
        xml_unit = xml_item.get('unidade', '').upper().strip()
        sped_unit = sped_item.get('unidade', '').upper().strip()

        if xml_unit and sped_unit:
            scores['unit_similarity'] = 1.0 if xml_unit == sped_unit else 0.0

         # Adicionar score de CFOP considerando regras de pareamento
        cfop_xml = str(xml_item.get('cfop', '')).strip()
        cfop_sped = str(sped_item.get('cfop', '')).strip()
        if cfop_xml and cfop_sped:
            if cfop_xml == cfop_sped:
                scores['cfop_similarity'] = 0.0
            elif cfop_sped in self.cfop_pairs.get(cfop_xml, []):
                scores['cfop_similarity'] = 1.5
            else:
                scores['cfop_similarity'] = 0.7

        # Calcular score final ponderado
        total_weight = 0
        weighted_sum = 0

        for key, score in scores.items():
            if key in self.weights:
                weight = self.weights[key]
                weighted_sum += score * weight
                total_weight += weight

        final_score = weighted_sum / total_weight if total_weight > 0 else embedding_similarity

        # Garantir que retorna Python float
        if hasattr(final_score, 'item'):
            return float(final_score.item())
        return float(final_score)

    def _calculate_matching_stats(self, matches: List[Dict], itens_xml: List[Dict], itens_sped: List[Dict]) -> Dict:
        """
        Calcula estatísticas do matching
        """
        total_matches = len([m for m in matches if m['xml_item'] and m['sped_item']])
        direct_matches = len([m for m in matches if m['match_type'] == 'direct'])
        embedding_matches = len([m for m in matches if m['match_type'] == 'embedding'])
        unmatched_xml = len([m for m in matches if m['match_type'] == 'unmatched_xml'])
        unmatched_sped = len([m for m in matches if m['match_type'] == 'unmatched_sped'])

        # Calcular score médio dos matches
        matched_scores = [m['confidence_score'] for m in matches if m['xml_item'] and m['sped_item']]
        avg_confidence = sum(matched_scores) / len(matched_scores) if matched_scores else 0.0

        return {
            'total_xml_items': len(itens_xml),
            'total_sped_items': len(itens_sped),
            'total_matches': total_matches,
            'direct_matches': direct_matches,
            'embedding_matches': embedding_matches,
            'unmatched_xml_items': unmatched_xml,
            'unmatched_sped_items': unmatched_sped,
            'match_rate': (total_matches / max(len(itens_xml), len(itens_sped))) * 100 if itens_xml or itens_sped else 0,
            'average_confidence': avg_confidence,
            'high_confidence_matches': len([s for s in matched_scores if s >= 0.6]),
            'medium_confidence_matches': len([s for s in matched_scores if 0.4 <= s < 0.6]),
            'low_confidence_matches': len([s for s in matched_scores if s < 0.4])
        }
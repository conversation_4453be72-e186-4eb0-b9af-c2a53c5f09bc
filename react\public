<svg width="180" height="170" viewBox="0 0 180 170" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_45_84)">
<mask id="path-1-inside-1_45_84" fill="white">
<path d="M170.697 80.5479C170.697 125.033 134.635 161.096 90.1495 161.096C45.6641 161.096 9.60156 125.033 9.60156 80.5479C9.60156 36.0625 45.6641 0 90.1495 0C134.635 0 170.697 36.0625 170.697 80.5479Z"/>
</mask>
<path d="M170.697 80.5479C170.697 125.033 134.635 161.096 90.1495 161.096C45.6641 161.096 9.60156 125.033 9.60156 80.5479C9.60156 36.0625 45.6641 0 90.1495 0C134.635 0 170.697 36.0625 170.697 80.5479Z" fill="#F5F5F5"/>
<g clip-path="url(#paint0_angular_45_84_clip_path)" data-figma-skip-parse="true" mask="url(#path-1-inside-1_45_84)"><g transform="matrix(-0.0109353 -0.0778808 0.0778808 -0.0109353 90.1495 80.548)"><foreignObject x="-1328.98" y="-1328.98" width="2657.96" height="2657.96"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(152, 152, 152, 1) 0deg,rgba(125, 125, 125, 1) 36.5659deg,rgba(84, 84, 84, 1) 87.9407deg,rgba(0, 0, 0, 1) 141.017deg,rgba(55, 55, 55, 1) 217.933deg,rgba(84, 84, 84, 1) 270.563deg,rgba(209, 209, 209, 1) 285.732deg,rgba(152, 152, 152, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M170.697 80.5479H158.697C158.697 118.406 128.007 149.096 90.1495 149.096V161.096V173.096C141.262 173.096 182.697 131.661 182.697 80.5479H170.697ZM90.1495 161.096V149.096C52.2915 149.096 21.6016 118.406 21.6016 80.5479H9.60156H-2.39844C-2.39844 131.661 39.0367 173.096 90.1495 173.096V161.096ZM9.60156 80.5479H21.6016C21.6016 42.69 52.2915 12 90.1495 12V0V-12C39.0367 -12 -2.39844 29.4351 -2.39844 80.5479H9.60156ZM90.1495 0V12C128.007 12 158.697 42.69 158.697 80.5479H170.697H182.697C182.697 29.4351 141.262 -12 90.1495 -12V0Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.49038460850715637,&#34;g&#34;:0.49038460850715637,&#34;b&#34;:0.49038460850715637,&#34;a&#34;:1.0},&#34;position&#34;:0.10157206654548645},{&#34;color&#34;:{&#34;r&#34;:0.33173078298568726,&#34;g&#34;:0.33173078298568726,&#34;b&#34;:0.33173078298568726,&#34;a&#34;:1.0},&#34;position&#34;:0.24427975714206696},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39171445369720459},{&#34;color&#34;:{&#34;r&#34;:0.21634615957736969,&#34;g&#34;:0.21634615957736969,&#34;b&#34;:0.21634615957736969,&#34;a&#34;:1.0},&#34;position&#34;:0.60537004470825195},{&#34;color&#34;:{&#34;r&#34;:0.33173078298568726,&#34;g&#34;:0.33173078298568726,&#34;b&#34;:0.33173078298568726,&#34;a&#34;:1.0},&#34;position&#34;:0.75156289339065552},{&#34;color&#34;:{&#34;r&#34;:0.82211536169052124,&#34;g&#34;:0.82211536169052124,&#34;b&#34;:0.82211536169052124,&#34;a&#34;:1.0},&#34;position&#34;:0.79370021820068359}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.49038460850715637,&#34;g&#34;:0.49038460850715637,&#34;b&#34;:0.49038460850715637,&#34;a&#34;:1.0},&#34;position&#34;:0.10157206654548645},{&#34;color&#34;:{&#34;r&#34;:0.33173078298568726,&#34;g&#34;:0.33173078298568726,&#34;b&#34;:0.33173078298568726,&#34;a&#34;:1.0},&#34;position&#34;:0.24427975714206696},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39171445369720459},{&#34;color&#34;:{&#34;r&#34;:0.21634615957736969,&#34;g&#34;:0.21634615957736969,&#34;b&#34;:0.21634615957736969,&#34;a&#34;:1.0},&#34;position&#34;:0.60537004470825195},{&#34;color&#34;:{&#34;r&#34;:0.33173078298568726,&#34;g&#34;:0.33173078298568726,&#34;b&#34;:0.33173078298568726,&#34;a&#34;:1.0},&#34;position&#34;:0.75156289339065552},{&#34;color&#34;:{&#34;r&#34;:0.82211536169052124,&#34;g&#34;:0.82211536169052124,&#34;b&#34;:0.82211536169052124,&#34;a&#34;:1.0},&#34;position&#34;:0.79370021820068359}],&#34;transform&#34;:{&#34;m00&#34;:-21.870626449584961,&#34;m01&#34;:155.76156616210938,&#34;m02&#34;:23.204034805297852,&#34;m10&#34;:-155.76156616210938,&#34;m11&#34;:-21.870626449584961,&#34;m12&#34;:169.36404418945312},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-1-inside-1_45_84)"/>
</g>
<mask id="path-3-inside-2_45_84" fill="white">
<path d="M147.76 80.0143C147.76 111.832 121.967 137.625 90.1495 137.625C58.3321 137.625 32.5391 111.832 32.5391 80.0143C32.5391 48.1969 58.3321 22.4038 90.1495 22.4038C121.967 22.4038 147.76 48.1969 147.76 80.0143Z"/>
</mask>
<path d="M147.76 80.0143C147.76 111.832 121.967 137.625 90.1495 137.625C58.3321 137.625 32.5391 111.832 32.5391 80.0143C32.5391 48.1969 58.3321 22.4038 90.1495 22.4038C121.967 22.4038 147.76 48.1969 147.76 80.0143Z" fill="#F5F5F5"/>
<g clip-path="url(#paint1_angular_45_84_clip_path)" data-figma-skip-parse="true" mask="url(#path-3-inside-2_45_84)"><g transform="matrix(-0.0450748 -0.0338728 0.0338728 -0.0450748 90.1495 80.0143)"><foreignObject x="-1728.65" y="-1728.65" width="3457.31" height="3457.31"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(102, 102, 102, 1) 0deg,rgba(84, 84, 84, 1) 39.9822deg,rgba(56, 56, 56, 1) 103.872deg,rgba(67, 67, 67, 1) 164.491deg,rgba(0, 0, 0, 1) 233.327deg,rgba(140, 140, 140, 1) 295.66deg,rgba(102, 102, 102, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M147.76 80.0143H135.76C135.76 105.204 115.339 125.625 90.1495 125.625V137.625V149.625C128.594 149.625 159.76 118.459 159.76 80.0143H147.76ZM90.1495 137.625V125.625C64.9596 125.625 44.5391 105.204 44.5391 80.0143H32.5391H20.5391C20.5391 118.459 51.7047 149.625 90.1495 149.625V137.625ZM32.5391 80.0143H44.5391C44.5391 54.8243 64.9596 34.4038 90.1495 34.4038V22.4038V10.4038C51.7047 10.4038 20.5391 41.5695 20.5391 80.0143H32.5391ZM90.1495 22.4038V34.4038C115.339 34.4038 135.76 54.8243 135.76 80.0143H147.76H159.76C159.76 41.5695 128.594 10.4038 90.1495 10.4038V22.4038Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.33173078298568726,&#34;g&#34;:0.33173078298568726,&#34;b&#34;:0.33173078298568726,&#34;a&#34;:1.0},&#34;position&#34;:0.11106154322624207},{&#34;color&#34;:{&#34;r&#34;:0.22192806005477905,&#34;g&#34;:0.22192806005477905,&#34;b&#34;:0.22192806005477905,&#34;a&#34;:1.0},&#34;position&#34;:0.28853413462638855},{&#34;color&#34;:{&#34;r&#34;:0.26442307233810425,&#34;g&#34;:0.26442307233810425,&#34;b&#34;:0.26442307233810425,&#34;a&#34;:1.0},&#34;position&#34;:0.45691832900047302},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.64812946319580078},{&#34;color&#34;:{&#34;r&#34;:0.55288463830947876,&#34;g&#34;:0.55288463830947876,&#34;b&#34;:0.55288463830947876,&#34;a&#34;:1.0},&#34;position&#34;:0.82127755880355835},{&#34;color&#34;:{&#34;r&#34;:0.40000000596046448,&#34;g&#34;:0.40000000596046448,&#34;b&#34;:0.40000000596046448,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.33173078298568726,&#34;g&#34;:0.33173078298568726,&#34;b&#34;:0.33173078298568726,&#34;a&#34;:1.0},&#34;position&#34;:0.11106154322624207},{&#34;color&#34;:{&#34;r&#34;:0.22192806005477905,&#34;g&#34;:0.22192806005477905,&#34;b&#34;:0.22192806005477905,&#34;a&#34;:1.0},&#34;position&#34;:0.28853413462638855},{&#34;color&#34;:{&#34;r&#34;:0.26442307233810425,&#34;g&#34;:0.26442307233810425,&#34;b&#34;:0.26442307233810425,&#34;a&#34;:1.0},&#34;position&#34;:0.45691832900047302},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.64812946319580078},{&#34;color&#34;:{&#34;r&#34;:0.55288463830947876,&#34;g&#34;:0.55288463830947876,&#34;b&#34;:0.55288463830947876,&#34;a&#34;:1.0},&#34;position&#34;:0.82127755880355835},{&#34;color&#34;:{&#34;r&#34;:0.40000000596046448,&#34;g&#34;:0.40000000596046448,&#34;b&#34;:0.40000000596046448,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-90.149681091308594,&#34;m01&#34;:67.745620727539062,&#34;m02&#34;:101.35153198242188,&#34;m10&#34;:-67.745620727539062,&#34;m11&#34;:-90.149681091308594,&#34;m12&#34;:158.96189880371094},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-3-inside-2_45_84)"/>
<mask id="path-5-inside-3_45_84" fill="white">
<path d="M124.822 80.0143C124.822 99.1636 109.299 114.687 90.1495 114.687C71.0002 114.687 55.4766 99.1636 55.4766 80.0143C55.4766 60.8649 71.0002 45.3413 90.1495 45.3413C109.299 45.3413 124.822 60.8649 124.822 80.0143Z"/>
</mask>
<path d="M124.822 80.0143C124.822 99.1636 109.299 114.687 90.1495 114.687C71.0002 114.687 55.4766 99.1636 55.4766 80.0143C55.4766 60.8649 71.0002 45.3413 90.1495 45.3413C109.299 45.3413 124.822 60.8649 124.822 80.0143Z" fill="#F5F5F5"/>
<g clip-path="url(#paint2_angular_45_84_clip_path)" data-figma-skip-parse="true" mask="url(#path-5-inside-3_45_84)"><g transform="matrix(-0.0301409 0.0171388 -0.0171388 -0.0301409 90.1495 80.0143)"><foreignObject x="-1835.52" y="-1835.52" width="3671.03" height="3671.03"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(1, 1, 1, 1) 0deg,rgba(0, 0, 0, 1) 0.668836deg,rgba(64, 64, 64, 1) 40.7312deg,rgba(0, 0, 0, 1) 90.7115deg,rgba(149, 146, 146, 1) 134.954deg,rgba(190, 190, 190, 1) 179.859deg,rgba(171, 171, 171, 1) 217.424deg,rgba(205, 205, 205, 1) 269.441deg,rgba(115, 115, 115, 1) 301.858deg,rgba(1, 1, 1, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M124.822 80.0143H112.822C112.822 92.5362 102.671 102.687 90.1495 102.687V114.687V126.687C115.926 126.687 136.822 105.791 136.822 80.0143H124.822ZM90.1495 114.687V102.687C77.6276 102.687 67.4766 92.5362 67.4766 80.0143H55.4766H43.4766C43.4766 105.791 64.3728 126.687 90.1495 126.687V114.687ZM55.4766 80.0143H67.4766C67.4766 67.4923 77.6276 57.3413 90.1495 57.3413V45.3413V33.3413C64.3728 33.3413 43.4766 54.2375 43.4766 80.0143H55.4766ZM90.1495 45.3413V57.3413C102.671 57.3413 112.822 67.4923 112.822 80.0143H124.822H136.822C136.822 54.2375 115.926 33.3413 90.1495 33.3413V45.3413Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0018578766612336040},{&#34;color&#34;:{&#34;r&#34;:0.25480768084526062,&#34;g&#34;:0.25480768084526062,&#34;b&#34;:0.25480768084526062,&#34;a&#34;:1.0},&#34;position&#34;:0.1131422296166420},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.25197637081146240},{&#34;color&#34;:{&#34;r&#34;:0.58653843402862549,&#34;g&#34;:0.57525885105133057,&#34;b&#34;:0.57525885105133057,&#34;a&#34;:1.0},&#34;position&#34;:0.37487357854843140},{&#34;color&#34;:{&#34;r&#34;:0.74519228935241699,&#34;g&#34;:0.74519228935241699,&#34;b&#34;:0.74519228935241699,&#34;a&#34;:1.0},&#34;position&#34;:0.49960744380950928},{&#34;color&#34;:{&#34;r&#34;:0.67307692766189575,&#34;g&#34;:0.67307692766189575,&#34;b&#34;:0.67307692766189575,&#34;a&#34;:1.0},&#34;position&#34;:0.60395467281341553},{&#34;color&#34;:{&#34;r&#34;:0.80769228935241699,&#34;g&#34;:0.80769228935241699,&#34;b&#34;:0.80769228935241699,&#34;a&#34;:1.0},&#34;position&#34;:0.74844801425933838},{&#34;color&#34;:{&#34;r&#34;:0.45192307233810425,&#34;g&#34;:0.45192307233810425,&#34;b&#34;:0.45192307233810425,&#34;a&#34;:1.0},&#34;position&#34;:0.83849525451660156}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0018578766612336040},{&#34;color&#34;:{&#34;r&#34;:0.25480768084526062,&#34;g&#34;:0.25480768084526062,&#34;b&#34;:0.25480768084526062,&#34;a&#34;:1.0},&#34;position&#34;:0.1131422296166420},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.25197637081146240},{&#34;color&#34;:{&#34;r&#34;:0.58653843402862549,&#34;g&#34;:0.57525885105133057,&#34;b&#34;:0.57525885105133057,&#34;a&#34;:1.0},&#34;position&#34;:0.37487357854843140},{&#34;color&#34;:{&#34;r&#34;:0.74519228935241699,&#34;g&#34;:0.74519228935241699,&#34;b&#34;:0.74519228935241699,&#34;a&#34;:1.0},&#34;position&#34;:0.49960744380950928},{&#34;color&#34;:{&#34;r&#34;:0.67307692766189575,&#34;g&#34;:0.67307692766189575,&#34;b&#34;:0.67307692766189575,&#34;a&#34;:1.0},&#34;position&#34;:0.60395467281341553},{&#34;color&#34;:{&#34;r&#34;:0.80769228935241699,&#34;g&#34;:0.80769228935241699,&#34;b&#34;:0.80769228935241699,&#34;a&#34;:1.0},&#34;position&#34;:0.74844801425933838},{&#34;color&#34;:{&#34;r&#34;:0.45192307233810425,&#34;g&#34;:0.45192307233810425,&#34;b&#34;:0.45192307233810425,&#34;a&#34;:1.0},&#34;position&#34;:0.83849525451660156}],&#34;transform&#34;:{&#34;m00&#34;:-60.281894683837891,&#34;m01&#34;:-34.277519226074219,&#34;m02&#34;:137.42922973632812,&#34;m10&#34;:34.277519226074219,&#34;m11&#34;:-60.281894683837891,&#34;m12&#34;:93.016456604003906},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-5-inside-3_45_84)"/>
<rect y="70.6846" width="27.9452" height="19.726" fill="#F5F5F5"/>
<rect x="153" y="71" width="27" height="19" fill="#F5F5F5"/>
<circle cx="89.8835" cy="79.7477" r="13.069" fill="url(#paint3_linear_45_84)"/>
<defs>
<filter id="filter0_d_45_84" x="5.60156" y="0" width="169.096" height="169.096" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45_84"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45_84" result="shape"/>
</filter>
<clipPath id="paint0_angular_45_84_clip_path"><path d="M170.697 80.5479H158.697C158.697 118.406 128.007 149.096 90.1495 149.096V161.096V173.096C141.262 173.096 182.697 131.661 182.697 80.5479H170.697ZM90.1495 161.096V149.096C52.2915 149.096 21.6016 118.406 21.6016 80.5479H9.60156H-2.39844C-2.39844 131.661 39.0367 173.096 90.1495 173.096V161.096ZM9.60156 80.5479H21.6016C21.6016 42.69 52.2915 12 90.1495 12V0V-12C39.0367 -12 -2.39844 29.4351 -2.39844 80.5479H9.60156ZM90.1495 0V12C128.007 12 158.697 42.69 158.697 80.5479H170.697H182.697C182.697 29.4351 141.262 -12 90.1495 -12V0Z" mask="url(#path-1-inside-1_45_84)"/></clipPath><clipPath id="paint1_angular_45_84_clip_path"><path d="M147.76 80.0143H135.76C135.76 105.204 115.339 125.625 90.1495 125.625V137.625V149.625C128.594 149.625 159.76 118.459 159.76 80.0143H147.76ZM90.1495 137.625V125.625C64.9596 125.625 44.5391 105.204 44.5391 80.0143H32.5391H20.5391C20.5391 118.459 51.7047 149.625 90.1495 149.625V137.625ZM32.5391 80.0143H44.5391C44.5391 54.8243 64.9596 34.4038 90.1495 34.4038V22.4038V10.4038C51.7047 10.4038 20.5391 41.5695 20.5391 80.0143H32.5391ZM90.1495 22.4038V34.4038C115.339 34.4038 135.76 54.8243 135.76 80.0143H147.76H159.76C159.76 41.5695 128.594 10.4038 90.1495 10.4038V22.4038Z" mask="url(#path-3-inside-2_45_84)"/></clipPath><clipPath id="paint2_angular_45_84_clip_path"><path d="M124.822 80.0143H112.822C112.822 92.5362 102.671 102.687 90.1495 102.687V114.687V126.687C115.926 126.687 136.822 105.791 136.822 80.0143H124.822ZM90.1495 114.687V102.687C77.6276 102.687 67.4766 92.5362 67.4766 80.0143H55.4766H43.4766C43.4766 105.791 64.3728 126.687 90.1495 126.687V114.687ZM55.4766 80.0143H67.4766C67.4766 67.4923 77.6276 57.3413 90.1495 57.3413V45.3413V33.3413C64.3728 33.3413 43.4766 54.2375 43.4766 80.0143H55.4766ZM90.1495 45.3413V57.3413C102.671 57.3413 112.822 67.4923 112.822 80.0143H124.822H136.822C136.822 54.2375 115.926 33.3413 90.1495 33.3413V45.3413Z" mask="url(#path-5-inside-3_45_84)"/></clipPath><linearGradient id="paint3_linear_45_84" x1="89.8835" y1="66.6787" x2="90.1502" y2="92.8168" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF68CD"/>
<stop offset="1" stop-color="#FF2E79"/>
</linearGradient>
</defs>
</svg>

# Importação de Dados da TIPI

Este diretório contém scripts para importar a tabela TIPI (Tabela de Incidência do Imposto sobre Produtos Industrializados) para o banco de dados do sistema de Auditoria Fiscal.

## Estrutura da Tabela TIPI

A tabela `tipi` possui a seguinte estrutura:

| Coluna | Tipo | Descrição |
|--------|------|-----------|
| id | SERIAL | Chave primária |
| ncm | VARCHAR(10) | Código NCM (Nomenclatura Comum do Mercosul) |
| ex | VARCHAR(2) | Código EX da NCM (quando aplicável) |
| descricao | TEXT | Descrição do produto conforme TIPI |
| aliquota | VARCHAR(20) | Alíquota do IPI (pode ser um número ou texto como "NT") |
| data_criacao | TIMESTAMP | Data de criação do registro |
| data_atualizacao | TIMESTAMP | Data da última atualização do registro |

## Como usar o script de importação

### Pré-requisitos

- Python 3.6 ou superior
- Bibliotecas Python:
  - pandas
  - psycopg2-binary
  - python-dotenv
  - openpyxl (para ler arquivos .xlsx)

### Instalação das dependências

```bash
pip install pandas psycopg2-binary python-dotenv openpyxl
```

### Executando a importação

1. Certifique-se de que o arquivo `.env` na raiz do projeto contém as configurações corretas do banco de dados.

2. Execute o script de importação com o seguinte comando:

```bash
python -m back.scripts.importar_tipi caminho/para/seu/arquivo_tipi.xlsx --criar-tabela
```

Onde:
- `caminho/para/seu/arquivo_tipi.xlsx` é o caminho para o arquivo Excel com os dados da TIPI
- A flag `--criar-tabela` é opcional e deve ser usada apenas na primeira execução para criar a tabela

### Formato do arquivo Excel

O arquivo Excel deve conter as seguintes colunas:
- NCM: Código NCM (será tratado como texto)
- EX: Código EX (opcional, será tratado como texto)
- DESCRIÇÃO: Descrição do produto
- ALÍQUOTA: Alíquota do IPI (pode ser número ou texto como "NT")

### Exemplo de uso

```bash
# Primeira execução - cria a tabela e importa os dados
python -m back.scripts.importar_tipi dados/tabela_tipi.xlsx --criar-tabela

# Execuções posteriores - apenas atualiza os dados
python -m back.scripts.importar_tipi dados/tabela_tipi_atualizada.xlsx
```

## Tratamento de Dados

O script realiza as seguintes operações nos dados:

1. **NCM**:
   - Remove caracteres não numéricos, exceto pontos
   - Remove pontos
   - Completa com zeros à direita até 8 dígitos

2. **EX**:
   - Remove caracteres não numéricos
   - Limita a 2 dígitos

3. **Descrição**:
   - Remove espaços em branco no início e no final

4. **Alíquota**:
   - Converte para maiúsculas
   - Mantém como texto para suportar valores como "NT"

## Dicas

- O script é idempotente, ou seja, pode ser executado várias vezes sem duplicar registros
- Registros com o mesmo NCM+EX serão atualizados
- O script exibe estatísticas de importação ao final da execução
- Em caso de erro, todas as alterações são revertidas (transação atômica)

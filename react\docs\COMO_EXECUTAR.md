# 🚀 Como Executar o Sistema React

## Pré-requisitos

- Node.js 18+ instalado
- Flask backend rodando na porta 5000
- Banco de dados PostgreSQL configurado

## 1. Instal<PERSON><PERSON>

```bash
cd react
npm install
```

## 2. Executar em Desenvolvimento

```bash
# Terminal 1: Backend Flask
cd back
python wsgi.py

# Terminal 2: Frontend React
cd react
npm run dev
```

## 3. Acessar o Sistema

### 🧪 **Página de Teste (Recomendado para desenvolvimento)**
```
http://localhost:3000/test-auth
```

### 🔐 **Fluxo Normal de Login**
```
http://localhost:3000/login
```

### 📊 **Dashboard (se já autenticado)**
```
http://localhost:3000/dashboard
```

## 4. Fluxo de Teste Rápido

1. **Acesse**: http://localhost:3000/test-auth
2. **Clique**: "Login Direto" 
3. **Resultado**: Redirecionamento automático para dashboard
4. **Usuário**: <PERSON> (admin)

## 5. Estrutura Atual Implementada

### ✅ **Autenticação Completa**
- Login via token JWT do portal
- Validação com backend Flask
- Gerenciamento de estado (Zustand)
- Compatibilidade total com sistema atual

### ✅ **Layout Base**
- Header com logo e menu de usuário
- Sidebar com navegação
- Dashboard responsivo
- Tema claro/escuro preparado

### ✅ **Componentes Base**
- LoadingSpinner
- Botões padronizados
- Cards informativos
- Layout grid responsivo

## 6. Próximos Passos de Desenvolvimento

### **Fase Atual: Autenticação ✅**
- [x] Setup do projeto React
- [x] Sistema de autenticação
- [x] Layout base
- [x] Dashboard inicial

### **Próxima Fase: Dashboard Funcional**
- [ ] Integração com APIs de dados
- [ ] Seletores de empresa/ano/mês
- [ ] Cards com dados reais
- [ ] Gráficos e estatísticas

### **Futuras Fases**
- [ ] Módulo de Importação
- [ ] Módulo de Auditoria
- [ ] Módulo de Cenários
- [ ] Gestão de Clientes/Produtos

## 7. Comandos Úteis

```bash
# Desenvolvimento
npm run dev

# Build para produção
npm run build

# Testes
npm run test

# Linting
npm run lint

# Preview do build
npm run preview
```

## 8. Debugging

### **Console do Navegador**
```javascript
// Verificar autenticação
localStorage.getItem('token')
localStorage.getItem('currentUser')

// Verificar cookies
document.cookie
```

### **Network Tab**
- Verificar requisições para `/fiscal/api/portal-login`
- Verificar headers de autorização
- Verificar cookies sendo enviados

### **React DevTools**
- Verificar estado do Zustand
- Verificar props dos componentes
- Verificar re-renders

## 9. Arquitetura

```
react/
├── src/
│   ├── components/
│   │   ├── dev/           # Componentes de desenvolvimento
│   │   ├── layout/        # Layout (Header, Sidebar)
│   │   └── ui/            # Componentes base (Button, Loading)
│   ├── hooks/             # Custom hooks (useAuth)
│   ├── pages/             # Páginas (Login, Dashboard)
│   ├── services/          # API calls (authService)
│   ├── store/             # Estado global (Zustand)
│   └── utils/             # Utilitários
```

## 10. Tecnologias Utilizadas

- **React 18** - Framework
- **TypeScript** - Type safety
- **Vite** - Build tool
- **Tailwind CSS** - Styling
- **Zustand** - Estado global
- **Axios** - HTTP client
- **React Router** - Roteamento

## 11. Compatibilidade

### **100% Compatível com Sistema Atual**
- ✅ Mesmas APIs Flask
- ✅ Mesmo sistema de autenticação
- ✅ Mesmo localStorage
- ✅ Mesmos cookies
- ✅ Mesma estrutura de dados

### **Melhorias Implementadas**
- ✅ Type safety com TypeScript
- ✅ Componentes reutilizáveis
- ✅ Estado global organizado
- ✅ Hot reload para desenvolvimento
- ✅ Build otimizado para produção

## 12. Troubleshooting

### **Erro: Cannot connect to Flask**
```bash
# Verificar se Flask está rodando
curl http://localhost:5000/fiscal/api/portal-login
```

### **Erro: Token inválido**
- Verificar se a chave SSH está configurada no Flask
- Verificar se o token não expirou
- Usar token atualizado no TestAuthPage

### **Erro: CORS**
- Verificar proxy no `vite.config.ts`
- Verificar `withCredentials: true` no axios

## 13. Deploy

### **Desenvolvimento**
```bash
npm run dev
```

### **Produção**
```bash
npm run build
# Arquivos gerados em: dist/
```

### **Integração com Flask**
- Build do React vai para pasta `dist/`
- Flask serve arquivos estáticos
- APIs continuam funcionando normalmente

---

## 🎯 Status Atual

**✅ PRONTO PARA DESENVOLVIMENTO**

O sistema React está funcionando com:
- Autenticação completa
- Layout responsivo  
- Navegação funcional
- Compatibilidade total
- Ambiente de desenvolvimento otimizado

**Próximo passo**: Implementar dashboard com dados reais das APIs Flask.
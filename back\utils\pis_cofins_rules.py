"""Validação de regras de PIS/COFINS para Auditoria Comparativa."""
from typing import List, Dict


def check_pis_cofins_rules(empresa: Dict, auditoria: Dict) -> List[str]:
    """Retorna lista de códigos de regras violadas.

    Args:
        empresa: Dicionário com dados da empresa (deve conter ``tributacao``).
        auditoria: Dados do item de auditoria já convertidos para ``dict``.

    Returns:
        Lista de identificadores das regras violadas.
    """
    violacoes: List[str] = []

    regime = (empresa or {}).get("tributacao")
    if regime not in ("Lucro Presumido", "Lucro Real"):
        return violacoes

    cfop_sped = (auditoria.get("sped_cfop") or "").strip()
    cfop_xml = (auditoria.get("xml_cfop") or "").strip()
    cst_pis = (auditoria.get("tributos", {}).get("pis", {}) or {}).get("cst") or ""
    tipo_produto = auditoria.get("tipo_produto") or auditoria.get("sped_tipo_item")
    fin_nfe = auditoria.get("fin_nfe")
    ind_oper = auditoria.get("ind_oper")
    ind_emit = auditoria.get("ind_emit")
    pis_aliq = float(auditoria.get("tributos", {}).get("pis", {}).get("aliquota") or 0)
    cofins_aliq = float(auditoria.get("tributos", {}).get("cofins", {}).get("aliquota") or 0)
    descricao_produto = (auditoria.get("xml_produto_descricao") or "").lower()

    devolucao_cfops = {"1201", "1202", "1410", "1411"}

    if regime == "Lucro Presumido":
        if cfop_sped.startswith("19") or cfop_sped in devolucao_cfops:
            if cst_pis != "98":
                violacoes.append("LP_CFOP_98")
        if cfop_xml == "5123" and cfop_sped == "1922" and cst_pis != "70":
            violacoes.append("LP_5123_1922_70")
        if not cfop_sped.startswith("19") and cfop_sped not in devolucao_cfops and cst_pis != "70":
            violacoes.append("LP_CST_70")
    elif regime == "Lucro Real":
        if tipo_produto in {"00", "01", "02", "04"} and cst_pis not in {"50", "56"}:
            violacoes.append("LR_CST_50_56")

# Aqui devemos utilizar IA para ler a descrição e dizer se é EPI ou Manutenção, se não for utilizado IA, deve verificar manualmente
        if tipo_produto == "07":
            if ("epi" in descricao_produto or "manut" in descricao_produto) and cst_pis not in {"50", "56"}:
                violacoes.append("LR_EPI_50_56")
            elif "epi" not in descricao_produto and "manut" not in descricao_produto and cst_pis != "70":
                violacoes.append("LR_USO_CONSUMO_70")

        if tipo_produto in {"07", "08"} and cst_pis != "70":
            violacoes.append("LR_CST_70")

        if cst_pis in {"50", "56"} and tipo_produto in {"00", "01", "02", "04"}:
            if not (abs(pis_aliq - 1.65) < 0.01 and abs(cofins_aliq - 7.6) < 0.01):
                violacoes.append("LR_ALIQ_50_56")

        if cfop_sped in devolucao_cfops and fin_nfe == "4":
            if cst_pis in {"50", "56"}:
                valid_aliqs = {
                    (1.65, 7.6),
                    (2.0, 9.6),
                    (2.3, 10.8),
                }
                if (round(pis_aliq, 2), round(cofins_aliq, 2)) not in valid_aliqs:
                    violacoes.append("LR_DEVOLUCAO_ALIQ")
            if ind_oper == "0" and ind_emit == "0":
                if cst_pis != "98" or (pis_aliq != 0 or cofins_aliq != 0):
                    violacoes.append("LR_DEVOLUCAO_98")

        if cfop_sped.startswith("19") and cst_pis != "98":
            violacoes.append("LR_CFOP_98")

        if cfop_xml == "5123" and cfop_sped == "1922":
            if cst_pis not in {"50", "56"} or not (abs(pis_aliq - 1.65) < 0.01 and abs(cofins_aliq - 7.6) < 0.01):
                violacoes.append("LR_5123_1922")

        if cfop_sped == "1252":
            if cst_pis not in {"50", "56"} or not (abs(pis_aliq - 1.65) < 0.01 and abs(cofins_aliq - 7.6) < 0.01):
                violacoes.append("LR_1252_ALIQ")

    return violacoes
"""
Rotas para Auditoria Comparativa de Impostos
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Empresa
from services.auditoria_comparativa_service import AuditoriaComparativaService
from services.item_matching_service import ItemMatchingService
from services.sugestoes_inteligentes_service import SugestoesInteligentesService
from services.transactional import transactional_session
from services.memory_monitor_service import MemoryMonitorService
import logging

logger = logging.getLogger(__name__)

auditoria_comparativa_bp = Blueprint('auditoria_comparativa', __name__)


def normalize_cst(value):
    """Normaliza valores de CST para strings de dois dígitos."""
    if value is None:
        return ''
    value_str = str(value).strip()
    return value_str.zfill(2) if value_str.isdigit() and len(value_str) < 2 else value_str


def format_decimal(value):
    """Format numeric values consistently with two decimal places."""
    try:
        return f"{float(value):.2f}"
    except (TypeError, ValueError):
        return ''


def has_company_access(usuario, empresa):
    """Verifica se o usuário tem permissão para acessar a empresa."""
    if not usuario or not empresa:
        return False

    if usuario.is_admin or usuario.tipo_usuario == 'admin':
        return True

    if usuario.escritorio_id == empresa.escritorio_id:
        return True

    empresas_perm = usuario.empresas_permitidas or []
    return empresa.id in empresas_perm

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/gerar', methods=['POST'])
@jwt_required()
def gerar_auditoria_comparativa():
    """
    Gera auditoria comparativa para uma nota fiscal específica
    """
    try:
        data = request.get_json()
        usuario_id = get_jwt_identity()
        
        # Validar parâmetros
        chave_nf = data.get('chave_nf')
        empresa_id = data.get('empresa_id')
        force_recalculate = data.get('force_recalculate', False)
        
        if not chave_nf or not empresa_id:
            return jsonify({
                'success': False,
                'message': 'Chave NF e empresa_id são obrigatórios'
            }), 400
        
        # Verificar permissão do usuário
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404
        
        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403
        
        # Criar serviço e gerar auditoria
        service = AuditoriaComparativaService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )
        
        resultado = service.gerar_auditoria_comparativa(chave_nf, force_recalculate)
        
        if resultado['success']:
            return jsonify(resultado), 200
        else:
            return jsonify(resultado), 400
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/nota/<chave_nf>', methods=['GET'])
@jwt_required()
def get_auditoria_por_nota(chave_nf):
    """
    Busca auditoria comparativa de uma nota específica
    """
    try:
        usuario_id = get_jwt_identity()
        empresa_id = request.args.get('empresa_id', type=int)
        tributo_filter = request.args.get('tributo')  # icms, icms_st, ipi, pis, cofins
        
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400
        
        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404
        
        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403
        
        # Buscar auditoria
        service = AuditoriaComparativaService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )
        
        resultado = service.get_auditoria_by_nota(chave_nf, tributo_filter)
        
        if resultado['success']:
            return jsonify(resultado), 200
        else:
            return jsonify(resultado), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/gerar-periodo', methods=['POST'])
@jwt_required()
def gerar_auditoria_comparativa_periodo():
    """
    Gera auditoria comparativa para todas as notas de um período (mês/ano) com websocket
    """
    try:
        import threading
        import uuid
        from services.websocket_service import get_websocket_service

        data = request.get_json()
        usuario_id = get_jwt_identity()

        # Validar parâmetros
        empresa_id = data.get('empresa_id')
        mes = data.get('mes')
        ano = data.get('ano')
        force_recalculate = data.get('force_recalculate', False)

        if not all([empresa_id, mes, ano]):
            return jsonify({
                'success': False,
                'message': 'empresa_id, mes e ano são obrigatórios'
            }), 400

        # Verificar permissão do usuário
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Gerar ID único para esta auditoria
        audit_id = str(uuid.uuid4())

        # Obter serviço websocket
        websocket_service = get_websocket_service()

        # Capturar o contexto da aplicação atual
        from flask import current_app
        app = current_app._get_current_object()

        def executar_auditoria():
            """Executa auditoria em thread separada"""
            # Usar contexto da aplicação na thread
            with app.app_context():
                try:
                    # Criar serviço
                    service = AuditoriaComparativaService(
                        empresa_id=empresa_id,
                        escritorio_id=empresa.escritorio_id,
                        usuario_id=usuario_id
                    )

                    # Criar callback de progresso
                    def progress_callback(progress_data):
                        if websocket_service:
                            websocket_service.send_audit_progress(audit_id, progress_data)

                    # Executar auditoria com callback
                    resultado = service.gerar_auditoria_comparativa_periodo(
                        mes, ano, force_recalculate, progress_callback
                    )

                    # Enviar resultado final
                    if websocket_service:
                        if resultado['success']:
                            websocket_service.send_audit_complete(audit_id, resultado)
                        else:
                            websocket_service.send_audit_error(audit_id, {
                                'message': resultado['message'],
                                'error_type': 'processing_error'
                            })

                except Exception as e:
                    import traceback
                    error_msg = f"Erro na thread de auditoria: {str(e)}"
                    print(error_msg)
                    print(traceback.format_exc())
                    if websocket_service:
                        websocket_service.send_audit_error(audit_id, {
                            'message': f'Erro interno: {str(e)}',
                            'error_type': 'internal_error'
                        })

        # Iniciar processamento em thread separada
        thread = threading.Thread(target=executar_auditoria)
        thread.daemon = True
        thread.start()

        # Retornar imediatamente com ID da auditoria
        return jsonify({
            'success': True,
            'message': 'Auditoria iniciada com sucesso',
            'audit_id': audit_id,
            'periodo': f'{mes:02d}/{ano}'
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/periodo', methods=['GET'])
@jwt_required()
def get_auditoria_por_periodo():
    """
    Busca auditoria comparativa de um período (mês/ano) com suporte a carregamento completo otimizado
    """
    try:
        usuario_id = get_jwt_identity()
        empresa_id = request.args.get('empresa_id', type=int)
        mes = request.args.get('mes', type=int)
        ano = request.args.get('ano', type=int)
        tributo_filter = request.args.get('tributo')  # icms, icms_st, ipi, pis, cofins

        def parse_list(name: str):
            value = request.args.get(name)
            return [v for v in value.split(',') if v] if value else []

        filtros = {
            'parceiros': parse_list('parceiros'),
            'ufs': parse_list('ufs'),
            'regimes': parse_list('regimes'),
            'ncms_sped': parse_list('ncms_sped'),
            'ncms_xml': parse_list('ncms_xml'),
            'tipos': parse_list('tipos'),
            'origens_sped': parse_list('origens_sped'),
            'origens_xml': parse_list('origens_xml'),
            'cfops_sped': parse_list('cfops_sped'),
            'cfops_xml': parse_list('cfops_xml'),
            'csts_sped': parse_list('csts_sped'),
            'csts_xml': parse_list('csts_xml'),
            'csts_sped_pis': parse_list('csts_sped_pis'),
            'csts_sped_cofins': parse_list('csts_sped_cofins'),
            'csts_xml_pis': parse_list('csts_xml_pis'),
            'csts_xml_cofins': parse_list('csts_xml_cofins'),
            'csosns': parse_list('csosns'),
            'reducoes_sped': parse_list('reducoes_sped'),
            'reducoes_xml': parse_list('reducoes_xml'),
            'aliquotas_sped': parse_list('aliquotas_sped'),
            'aliquotas_xml': parse_list('aliquotas_xml'),
            'aliquotas_sped_pis': parse_list('aliquotas_sped_pis'),
            'aliquotas_sped_cofins': parse_list('aliquotas_sped_cofins'),
            'aliquotas_xml_pis': parse_list('aliquotas_xml_pis'),
            'aliquotas_xml_cofins': parse_list('aliquotas_xml_cofins'),
            'match_types': parse_list('match_types'),
            'status': parse_list('status'),
            'mvas': parse_list('mvas'),
            'numero_nf': request.args.get('numero_nf', '').strip(),
            'data_emissao': request.args.get('data_emissao', '').strip(),
            'produto_codigo_sped': request.args.get('produto_codigo_sped', '').strip(),
            'produto_codigo_nota': request.args.get('produto_codigo_nota', '').strip(),
            'produto_descricao': request.args.get('produto_descricao', '').strip(),
            'descricao_tipo': request.args.get('descricao_tipo', '').strip(),
            'produto_nota': request.args.get('produto_nota', '').strip(),
        }
        load_all = request.args.get('load_all', 'false').lower() == 'true'
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 100, type=int)

        if not all([empresa_id, mes, ano]):
            return jsonify({
                'success': False,
                'message': 'empresa_id, mes e ano são obrigatórios'
            }), 400

        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Buscar auditoria
        service = AuditoriaComparativaService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )

        # Se load_all for solicitado, verificar restrições de memória primeiro
        if load_all:
            # Fazer uma consulta rápida para contar registros
            count_result = service.count_auditoria_by_periodo(mes, ano, tributo_filter)
            
            if count_result.get('success') and count_result.get('count', 0) > 0:
                record_count = count_result['count']
                
                # Verificar restrições de memória
                memory_monitor = MemoryMonitorService()
                memory_check = memory_monitor.check_memory_constraints(record_count)
                
                if not memory_check['can_load_all']:
                    logger.warning(f"Memory constraint violation for auditoria comparativa: {memory_check['message']}")
                    return jsonify({
                        'success': False,
                        'error': 'memory_constraint',
                        'message': memory_check['message'],
                        'suggested_action': memory_check['suggested_action'],
                        'total_count': record_count,
                        'memory_stats': memory_check['memory_stats']
                    }), 413  # Payload Too Large

        # Buscar dados com parâmetros de paginação ou carregamento completo
        if load_all:
            resultado = service.get_auditoria_by_periodo(mes, ano, tributo_filter, load_all=True)
        else:
            # Limitar per_page para paginação normal
            if per_page > 1000:
                per_page = 1000
            resultado = service.get_auditoria_by_periodo(mes, ano, tributo_filter, 
                                                       page=page, per_page=per_page)

        if resultado['success']:
            auditorias = resultado.get('auditorias', [])

            def matches(aud):
                parceiro = aud.get('parceiro_nome') or aud.get('cliente_nome') or ''
                if filtros['parceiros'] and parceiro not in filtros['parceiros']:
                    return False
                if filtros['ufs'] and (aud.get('cliente_uf') or aud.get('participante_uf') or '') not in filtros['ufs']:
                    return False
                if filtros['regimes'] and (aud.get('regime_parceiro') or '') not in filtros['regimes']:
                    return False
                if filtros['ncms_sped'] and (aud.get('sped_ncm') or '') not in filtros['ncms_sped']:
                    return False
                if filtros['ncms_xml'] and (aud.get('xml_ncm') or '') not in filtros['ncms_xml']:
                    return False
                if filtros['tipos'] and (aud.get('tipo_produto') or '') not in filtros['tipos']:
                    return False
                if filtros['origens_sped'] and (aud.get('sped_origem') or '') not in filtros['origens_sped']:
                    return False
                if filtros['origens_xml'] and (aud.get('xml_origem') or '') not in filtros['origens_xml']:
                    return False
                if filtros['cfops_sped'] and (aud.get('sped_cfop') or '') not in filtros['cfops_sped']:
                    return False
                if filtros['cfops_xml'] and (aud.get('xml_cfop') or '') not in filtros['cfops_xml']:
                    return False
                if filtros['csts_sped']:
                    cst_sped = aud.get('tributos', {}).get(tributo_filter or '', {}).get('cst')
                    if not cst_sped:
                        cst_sped = aud.get('cst_sped')
                    cst_sped = normalize_cst(cst_sped)
                    if cst_sped not in filtros['csts_sped']:
                        return False
                if filtros['csts_xml']:
                    xml_data = aud.get('xml_data', {})
                    xml_cst_map = {
                        'pis': 'pis_cst',
                        'cofins': 'cofins_cst',
                        'ipi': 'ipi_cst',
                        'icms_st': 'icms_st_cst',
                        'icms': 'cst',
                    }
                    cst_xml = xml_data.get(xml_cst_map.get(tributo_filter, 'cst'))
                    if not cst_xml:
                        cst_xml = aud.get('cst_nota')
                    cst_xml = normalize_cst(cst_xml)
                    if cst_xml not in filtros['csts_xml']:
                        return False
                if filtros['csosns'] and (aud.get('csosn_nota') or aud.get('xml_csosn') or '') not in filtros['csosns']:
                    return False
                if filtros['reducoes_sped']:
                    red = aud.get('reducao_sped')
                    formatted = format_decimal(red)
                    if formatted not in filtros['reducoes_sped']:
                        return False
                if filtros['reducoes_xml']:
                    red = aud.get('reducao_nota')
                    formatted = format_decimal(red)
                    if formatted not in filtros['reducoes_xml']:
                        return False
                if filtros['aliquotas_sped']:
                    aliq = aud.get('aliquota_sped')
                    formatted = format_decimal(aliq)
                    if formatted not in filtros['aliquotas_sped']:
                        return False
                if filtros['aliquotas_xml']:
                    aliq = aud.get('aliquota_nota')
                    formatted = format_decimal(aliq)
                    if formatted not in filtros['aliquotas_xml']:
                        return False
                if filtros['aliquotas_sped_pis']:
                    aliq = aud.get('tributos', {}).get('pis', {}).get('aliquota')
                    if format_decimal(aliq) not in filtros['aliquotas_sped_pis']:
                        return False
                if filtros['aliquotas_sped_cofins']:
                    aliq = aud.get('tributos', {}).get('cofins', {}).get('aliquota')
                    if format_decimal(aliq) not in filtros['aliquotas_sped_cofins']:
                        return False
                if filtros['aliquotas_xml_pis']:
                    aliq = aud.get('xml_data', {}).get('pis_aliquota')
                    if format_decimal(aliq) not in filtros['aliquotas_xml_pis']:
                        return False
                if filtros['aliquotas_xml_cofins']:
                    aliq = aud.get('xml_data', {}).get('cofins_aliquota')
                    if format_decimal(aliq) not in filtros['aliquotas_xml_cofins']:
                        return False
                if filtros['mvas']:
                    mva = aud.get('mva_nota')
                    if format_decimal(mva) not in filtros['mvas']:
                        return False
                if filtros['match_types'] and (aud.get('match_type') or '') not in filtros['match_types']:
                    return False
                if filtros['status'] and (aud.get('status') or '') not in filtros['status']:
                    return False
                if filtros['numero_nf'] and filtros['numero_nf'] not in (aud.get('numero_nf') or ''):
                    return False
                if filtros['data_emissao'] and filtros['data_emissao'] not in (aud.get('data_emissao') or ''):
                    return False
                if filtros['produto_codigo_sped'] and filtros['produto_codigo_sped'].lower() not in (aud.get('sped_codigo_produto') or '').lower():
                    return False
                if filtros['produto_codigo_nota'] and filtros['produto_codigo_nota'].lower() not in (aud.get('xml_codigo_produto') or '').lower():
                    return False
                if filtros['produto_descricao'] and filtros['produto_descricao'].lower() not in ((aud.get('sped_produto_descricao') or '') + (aud.get('xml_produto_descricao') or '')).lower():
                    return False
                if filtros['descricao_tipo'] and filtros['descricao_tipo'].lower() not in (aud.get('descricao_tipo_produto') or aud.get('descricao_tipo') or '').lower():
                    return False
                if filtros['produto_nota'] and filtros['produto_nota'].lower() not in (aud.get('produto_nota') or '').lower():
                    return False
                return True

            auditorias = [a for a in auditorias if matches(a)]
            resultado['auditorias'] = auditorias
            resultado['total_registros'] = len(auditorias)

            # Adicionar informações de carregamento se load_all foi usado
            if load_all and 'auditorias' in resultado:
                resultado['load_info'] = {
                    'total_count': len(resultado['auditorias']),
                    'loaded_count': len(resultado['auditorias']),
                    'load_all': True
                }
                
                # Adicionar informações de memória
                memory_monitor = MemoryMonitorService()
                resultado['memory_stats'] = memory_monitor.get_memory_stats()
            
            return jsonify(resultado), 200
        else:
            return jsonify(resultado), 404

    except Exception as e:
        logger.error(f"Error in get_auditoria_por_periodo: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/periodo/batch', methods=['GET'])
@jwt_required()
def get_auditoria_por_periodo_batch():
    """
    Busca auditoria comparativa de um período em lotes para carregamento progressivo
    """
    try:
        usuario_id = get_jwt_identity()
        empresa_id = request.args.get('empresa_id', type=int)
        mes = request.args.get('mes', type=int)
        ano = request.args.get('ano', type=int)
        tributo_filter = request.args.get('tributo')  # icms, icms_st, ipi, pis, cofins
        offset = request.args.get('offset', 0, type=int)
        batch_size = request.args.get('batch_size', 1000, type=int)

        if not all([empresa_id, mes, ano]):
            return jsonify({
                'success': False,
                'message': 'empresa_id, mes e ano são obrigatórios'
            }), 400

        # Limitar batch_size para evitar sobrecarga
        if batch_size > 5000:
            batch_size = 5000

        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Buscar auditoria em lote
        service = AuditoriaComparativaService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )

        resultado = service.get_auditoria_by_periodo_batch(
            mes, ano, tributo_filter, offset, batch_size
        )

        if resultado['success']:
            return jsonify({
                'success': True,
                'auditorias': resultado['auditorias'],
                'batch_info': {
                    'offset': offset,
                    'batch_size': batch_size,
                    'loaded_count': len(resultado['auditorias']),
                    'has_more': resultado.get('has_more', False)
                }
            }), 200
        else:
            return jsonify(resultado), 500

    except Exception as e:
        logger.error(f"Error in get_auditoria_por_periodo_batch: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/filter-options', methods=['GET'])
@jwt_required()
def obter_opcoes_filtro_auditoria_comparativa():
    """
    Obtém as opções para os filtros de auditoria comparativa com relacionamentos
    """
    try:
        from collections import defaultdict

        usuario_id = get_jwt_identity()
        empresa_id = request.args.get('empresa_id', type=int)
        mes = request.args.get('mes', type=int)
        ano = request.args.get('ano', type=int)
        tributo_filter = request.args.get('tributo')  # icms, icms_st, ipi, pis, cofins

        def parse_list(name: str):
            value = request.args.get(name)
            return [v for v in value.split(',') if v] if value else []

        filtros = {
            'parceiros': parse_list('parceiros'),
            'ufs': parse_list('ufs'),
            'regimes': parse_list('regimes'),
            'ncms_sped': parse_list('ncms_sped'),
            'ncms_xml': parse_list('ncms_xml'),
            'tipos': parse_list('tipos'),
            'origens_sped': parse_list('origens_sped'),
            'origens_xml': parse_list('origens_xml'),
            'cfops_sped': parse_list('cfops_sped'),
            'cfops_xml': parse_list('cfops_xml'),
            'csts_sped': parse_list('csts_sped'),
            'csts_xml': parse_list('csts_xml'),
            'csts_sped_pis': parse_list('csts_sped_pis'),
            'csts_sped_cofins': parse_list('csts_sped_cofins'),
            'csts_xml_pis': parse_list('csts_xml_pis'),
            'csts_xml_cofins': parse_list('csts_xml_cofins'),
            'csosns': parse_list('csosns'),
            'reducoes_sped': parse_list('reducoes_sped'),
            'reducoes_xml': parse_list('reducoes_xml'),
            'aliquotas_sped': parse_list('aliquotas_sped'),
            'aliquotas_xml': parse_list('aliquotas_xml'),
            'aliquotas_sped_pis': parse_list('aliquotas_sped_pis'),
            'aliquotas_sped_cofins': parse_list('aliquotas_sped_cofins'),
            'aliquotas_xml_pis': parse_list('aliquotas_xml_pis'),
            'aliquotas_xml_cofins': parse_list('aliquotas_xml_cofins'),
            'mvas': parse_list('mvas'),
            'match_types': parse_list('match_types'),
            'status': parse_list('status'),
            'numero_nf': request.args.get('numero_nf', '').strip(),
            'data_emissao': request.args.get('data_emissao', '').strip(),
            'produto_codigo_sped': request.args.get('produto_codigo_sped', '').strip(),
            'produto_codigo_nota': request.args.get('produto_codigo_nota', '').strip(),
            'produto_descricao': request.args.get('produto_descricao', '').strip(),
            'descricao_tipo': request.args.get('descricao_tipo', '').strip(),
            'produto_nota': request.args.get('produto_nota', '').strip(),
        }

        if not all([empresa_id, mes, ano]):
            return jsonify({
                'success': False,
                'message': 'empresa_id, mes e ano são obrigatórios'
            }), 400

        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Buscar dados da auditoria para extrair opções de filtros
        service = AuditoriaComparativaService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )

        resultado = service.get_auditoria_by_periodo(mes, ano, tributo_filter)

        if not resultado['success'] or not resultado.get('auditorias'):
            return jsonify({
                'success': True,
                'opcoes': {
                    'parceiros': [],
                    'ufs': [],
                    'cfops_sped': [],
                    'cfops_xml': [],
                    'csts_sped': [],
                    'csts_xml': [],
                    'csts_sped_pis': [],
                    'csts_sped_cofins': [],
                    'csts_xml_pis': [],
                    'csts_xml_cofins': [],
                    'ncms_sped': [],
                    'ncms_xml': [],
                    'status': [],
                    'origens_sped': [],
                    'origens_xml': [],
                    'aliquotas_sped': [],
                    'aliquotas_xml': [],
                    'aliquotas_sped_pis': [],
                    'aliquotas_sped_cofins': [],
                    'aliquotas_xml_pis': [],
                    'aliquotas_xml_cofins': [],
                    'match_types': [],
                    'regimes': [],
                    'tipos': [],
                    'csosns': [],
                    'reducoes_sped': [],
                    'reducoes_xml': [],
                    'reducoes': [],
                    'mvas': []
                }
            }), 200

        auditorias = resultado['auditorias']

        def matches(aud):
            parceiro = aud.get('parceiro_nome') or aud.get('cliente_nome') or ''
            if filtros['parceiros'] and parceiro not in filtros['parceiros']:
                return False
            if filtros['ufs'] and (aud.get('cliente_uf') or aud.get('participante_uf') or '') not in filtros['ufs']:
                return False
            if filtros['regimes'] and (aud.get('regime_parceiro') or '') not in filtros['regimes']:
                return False
            if filtros['ncms_sped'] and (aud.get('sped_ncm') or '') not in filtros['ncms_sped']:
                return False
            if filtros['ncms_xml'] and (aud.get('xml_ncm') or '') not in filtros['ncms_xml']:
                return False
            if filtros['tipos'] and (aud.get('tipo_produto') or '') not in filtros['tipos']:
                return False
            if filtros['origens_sped'] and (aud.get('sped_origem') or '') not in filtros['origens_sped']:
                return False
            if filtros['origens_xml'] and (aud.get('xml_origem') or '') not in filtros['origens_xml']:
                return False
            if filtros['cfops_sped'] and (aud.get('sped_cfop') or '') not in filtros['cfops_sped']:
                return False
            if filtros['cfops_xml'] and (aud.get('xml_cfop') or '') not in filtros['cfops_xml']:
                return False
            xml_data = aud.get('xml_data', {})
            if filtros['csts_sped']:
                cst_sped = aud.get('tributos', {}).get(tributo_filter or '', {}).get('cst')
                cst_sped = normalize_cst(cst_sped)
                if cst_sped not in filtros['csts_sped']:
                    return False
            if filtros['csts_xml']:
                xml_cst_map = {
                    'pis': 'pis_cst',
                    'cofins': 'cofins_cst',
                    'ipi': 'ipi_cst',
                    'icms_st': 'icms_st_cst',
                    'icms': 'cst',
                }
                cst_xml = xml_data.get(xml_cst_map.get(tributo_filter, 'cst'))
                cst_xml = normalize_cst(cst_xml)
                if cst_xml not in filtros['csts_xml']:
                    return False
            if filtros['csts_sped_pis'] and normalize_cst(
                aud.get('tributos', {}).get('pis', {}).get('cst')
            ) not in filtros['csts_sped_pis']:
                return False
            if filtros['csts_sped_cofins'] and normalize_cst(
                aud.get('tributos', {}).get('cofins', {}).get('cst')
            ) not in filtros['csts_sped_cofins']:
                return False
            if filtros['csts_xml_pis'] and normalize_cst(
                aud.get('xml_data', {}).get('pis_cst')
            ) not in filtros['csts_xml_pis']:
                return False
            if filtros['csts_xml_cofins'] and normalize_cst(
                aud.get('xml_data', {}).get('cofins_cst')
            ) not in filtros['csts_xml_cofins']:
                return False
            if filtros['csosns'] and (aud.get('csosn_nota') or aud.get('xml_csosn') or '') not in filtros['csosns']:
                return False
            if filtros['reducoes_sped']:
                reducao = aud.get('tributos', {}).get(tributo_filter or '', {}).get('reducao')
                formatted = f"{float(reducao):.2f}" if reducao is not None else ''
                if formatted not in filtros['reducoes_sped']:
                    return False
            if filtros['reducoes_xml']:
                xml_red_map = {
                    'pis': 'pis_reducao',
                    'cofins': 'cofins_reducao',
                    'ipi': 'ipi_reducao',
                    'icms_st': 'icms_st_reducao',
                    'icms': 'icms_reducao',
                }
                reducao_xml = xml_data.get(xml_red_map.get(tributo_filter, 'reducao'))
                formatted = f"{float(reducao_xml):.2f}" if reducao_xml is not None else ''
                if formatted not in filtros['reducoes_xml']:
                    return False
            if filtros['aliquotas_sped']:
                aliq = aud.get('tributos', {}).get(tributo_filter or '', {}).get('aliquota')
                formatted = f"{float(aliq):.2f}" if aliq is not None else ''
                if formatted not in filtros['aliquotas_sped']:
                    return False
            if filtros['aliquotas_xml']:
                xml_aliq_map = {
                    'pis': 'pis_aliquota',
                    'cofins': 'cofins_aliquota',
                    'ipi': 'ipi_aliquota',
                    'icms_st': 'icms_st_aliquota',
                    'icms': 'icms_aliquota',
                }
                aliq_xml = xml_data.get(xml_aliq_map.get(tributo_filter, 'aliquota'))
                formatted = f"{float(aliq_xml):.2f}" if aliq_xml is not None else ''
                if formatted not in filtros['aliquotas_xml']:
                    return False
            if filtros['aliquotas_sped_pis']:
                aliq = aud.get('tributos', {}).get('pis', {}).get('aliquota')
                formatted = f"{float(aliq):.2f}" if aliq is not None else ''
                if formatted not in filtros['aliquotas_sped_pis']:
                    return False
            if filtros['aliquotas_sped_cofins']:
                aliq = aud.get('tributos', {}).get('cofins', {}).get('aliquota')
                formatted = f"{float(aliq):.2f}" if aliq is not None else ''
                if formatted not in filtros['aliquotas_sped_cofins']:
                    return False
            if filtros['aliquotas_xml_pis']:
                aliq = aud.get('xml_data', {}).get('pis_aliquota')
                formatted = f"{float(aliq):.2f}" if aliq is not None else ''
                if formatted not in filtros['aliquotas_xml_pis']:
                    return False
            if filtros['aliquotas_xml_cofins']:
                aliq = aud.get('xml_data', {}).get('cofins_aliquota')
                formatted = f"{float(aliq):.2f}" if aliq is not None else ''
                if formatted not in filtros['aliquotas_xml_cofins']:
                    return False
            if filtros['match_types'] and (aud.get('match_type') or '') not in filtros['match_types']:
                return False
            if filtros['status']:
                if tributo_filter == 'pis_cofins':
                    status_val = 'aprovado' if (
                        aud.get('tributos', {}).get('pis', {}).get('status') == 'aprovado'
                        and aud.get('tributos', {}).get('cofins', {}).get('status') == 'aprovado'
                    ) else 'pendente'
                else:
                    status_val = aud.get('tributos', {}).get(tributo_filter or '', {}).get('status') or ''
                if status_val not in filtros['status']:
                    return False
            if filtros['mvas']:
                mva = aud.get('mva_nota')
                formatted = f"{float(mva):.2f}" if mva is not None else ''
                if formatted not in filtros['mvas']:
                    return False
            if filtros['numero_nf'] and filtros['numero_nf'] not in (aud.get('numero_nf') or ''):
                return False
            if filtros['data_emissao'] and filtros['data_emissao'] not in (aud.get('data_emissao') or ''):
                return False
            if filtros['produto_codigo_sped'] and filtros['produto_codigo_sped'].lower() not in (aud.get('sped_codigo_produto') or '').lower():
                return False
            if filtros['produto_codigo_nota'] and filtros['produto_codigo_nota'].lower() not in (aud.get('xml_codigo_produto') or '').lower():
                return False
            if filtros['produto_descricao'] and filtros['produto_descricao'].lower() not in ((aud.get('sped_produto_descricao') or '') + (aud.get('xml_produto_descricao') or '')).lower():
                return False
            if filtros['descricao_tipo'] and filtros['descricao_tipo'].lower() not in (aud.get('descricao_tipo_produto') or aud.get('descricao_tipo') or '').lower():
                return False
            if filtros['produto_nota'] and filtros['produto_nota'].lower() not in (aud.get('produto_nota') or '').lower():
                return False
            return True

        auditorias = [a for a in auditorias if matches(a)]

        # Estruturas para armazenar relacionamentos - separando XML e SPED
        parceiros = defaultdict(lambda: {'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})

        # Filtros separados para SPED e XML
        cfops_sped = defaultdict(lambda: {'parceiros': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})
        cfops_xml = defaultdict(lambda: {'parceiros': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})

        csts_sped = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})
        csts_xml = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})

        ncms_sped = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})
        ncms_xml = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})

        origens_sped = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})
        origens_xml = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})

        aliquotas_sped = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})
        aliquotas_xml = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})

        ufs_set = set()
        reducoes_sped_set = set()
        reducoes_xml_set = set()

        # Conjuntos específicos para PIS e COFINS
        csts_sped_pis_set = set()
        csts_sped_cofins_set = set()
        csts_xml_pis_set = set()
        csts_xml_cofins_set = set()

        aliquotas_sped_pis_set = set()
        aliquotas_sped_cofins_set = set()
        aliquotas_xml_pis_set = set()
        aliquotas_xml_cofins_set = set()

        mvas_set = set()

        # Filtros específicos
        status = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})
        match_types = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'regimes': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})
        regimes = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'tipos': set(), 'csosns': set(), 'reducoes': set()})
        tipos = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'csosns': set(), 'reducoes': set()})
        csosns = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'reducoes': set()})
        reducoes = defaultdict(lambda: {'parceiros': set(), 'cfops_sped': set(), 'cfops_xml': set(), 'csts_sped': set(), 'csts_xml': set(), 'ncms_sped': set(), 'ncms_xml': set(), 'status': set(), 'origens_sped': set(), 'origens_xml': set(), 'aliquotas_sped': set(), 'aliquotas_xml': set(), 'match_types': set(), 'regimes': set(), 'tipos': set(), 'csosns': set()})

        # Processar cada auditoria para extrair valores e relacionamentos
        for auditoria in auditorias:
            # Extrair valores básicos
            parceiro = auditoria.get('parceiro_nome') or auditoria.get('cliente_nome') or ''
            cfop_sped = auditoria.get('sped_cfop') or ''
            cfop_xml = auditoria.get('xml_cfop') or ''
            ncm_sped = auditoria.get('sped_ncm') or ''
            ncm_xml = auditoria.get('xml_ncm') or ''
            origem_sped = auditoria.get('sped_origem')
            origem_xml = auditoria.get('xml_origem')
            match_type = auditoria.get('match_type') or ''

            ufs_set.add(auditoria.get('cliente_uf') or auditoria.get('participante_uf') or '')

            # Extrair dados específicos adicionais
            regime_parceiro = auditoria.get('regime_parceiro') or ''
            tipo_produto = auditoria.get('tipo_produto') or ''

            # Extrair dados do tributo específico
            tributos_data = auditoria.get('tributos', {})
            xml_data = auditoria.get('xml_data', {})

            if tributo_filter == 'pis_cofins':
                # Ignorar itens onde PIS e COFINS já foram aprovados
                if (
                    tributos_data.get('pis', {}).get('status') == 'aprovado' and
                    tributos_data.get('cofins', {}).get('status') == 'aprovado'
                ):
                    continue
                tributos_to_process = ['pis', 'cofins']
            elif tributo_filter and tributo_filter in tributos_data:
                # Ignorar itens aprovados para o tributo filtrado
                if tributos_data.get(tributo_filter, {}).get('status') == 'aprovado':
                    continue
                tributos_to_process = [tributo_filter]
            else:
                tributos_to_process = [next(iter(tributos_data.keys()))] if tributos_data else []

            # Extrair CSOSN do XML (sempre baseado em ICMS)
            csosn_xml = auditoria.get('xml_csosn') or xml_data.get('csosn') or ''

            for trib_key in tributos_to_process:
                tributo_data = tributos_data.get(trib_key, {})
                cst_sped = normalize_cst(tributo_data.get('cst'))
                aliquota_sped = tributo_data.get('aliquota') or ''
                reducao_sped = tributo_data.get('reducao') or ''
                if reducao_sped:
                    try:
                        reducoes_sped_set.add(f"{float(reducao_sped):.2f}")
                    except ValueError:
                        reducoes_sped_set.add(str(reducao_sped))
                mva_xml = auditoria.get('mva_nota') or ''
                if mva_xml:
                    try:
                        mvas_set.add(f"{float(mva_xml):.2f}")
                    except ValueError:
                        pass
                status_tributo = tributo_data.get('status') or 'pendente'
                cst_xml = normalize_cst(xml_data.get(f'{trib_key}_cst') or xml_data.get('cst'))
                aliquota_xml = xml_data.get(f'{trib_key}_aliquota') or ''
                reducao_xml = xml_data.get(f'{trib_key}_reducao') or xml_data.get('reducao') or ''
                if reducao_xml:
                    try:
                        reducoes_xml_set.add(f"{float(reducao_xml):.2f}")
                    except ValueError:
                        reducoes_xml_set.add(str(reducao_xml))

                if trib_key == 'pis':
                    if cst_sped:
                        csts_sped_pis_set.add(cst_sped)
                    if cst_xml:
                        csts_xml_pis_set.add(cst_xml)
                    if aliquota_sped:
                        aliquotas_sped_pis_set.add(f"{float(aliquota_sped):.2f}")
                    if aliquota_xml:
                        aliquotas_xml_pis_set.add(f"{float(aliquota_xml):.2f}")
                elif trib_key == 'cofins':
                    if cst_sped:
                        csts_sped_cofins_set.add(cst_sped)
                    if cst_xml:
                        csts_xml_cofins_set.add(cst_xml)
                    if aliquota_sped:
                        aliquotas_sped_cofins_set.add(f"{float(aliquota_sped):.2f}")
                    if aliquota_xml:
                        aliquotas_xml_cofins_set.add(f"{float(aliquota_xml):.2f}")

                # Filtrar registros sem informações mínimas
                if not match_type or not status_tributo:
                    continue

                # Construir relacionamentos bidirecionais separando SPED e XML
                if parceiro:
                    if cfop_sped:
                        parceiros[parceiro]['cfops_sped'].add(cfop_sped)
                    if cfop_xml:
                        parceiros[parceiro]['cfops_xml'].add(cfop_xml)
                    if cst_sped:
                        parceiros[parceiro]['csts_sped'].add(cst_sped)
                    if cst_xml:
                        parceiros[parceiro]['csts_xml'].add(cst_xml)
                    if ncm_sped:
                        parceiros[parceiro]['ncms_sped'].add(ncm_sped)
                    if ncm_xml:
                        parceiros[parceiro]['ncms_xml'].add(ncm_xml)
                    if origem_sped is not None and str(origem_sped) != '':
                        parceiros[parceiro]['origens_sped'].add(origem_sped)
                    if origem_xml is not None and str(origem_xml) != '':
                        parceiros[parceiro]['origens_xml'].add(origem_xml)
                    if aliquota_sped:
                        formatted = f"{float(aliquota_sped):.2f}"
                        parceiros[parceiro]['aliquotas_sped'].add(formatted)
                    if aliquota_xml:
                        formatted_xml = f"{float(aliquota_xml):.2f}"
                        parceiros[parceiro]['aliquotas_xml'].add(formatted_xml)
                    parceiros[parceiro]['status'].add(status_tributo)
                    parceiros[parceiro]['match_types'].add(match_type)
                    if regime_parceiro:
                        parceiros[parceiro]['regimes'].add(regime_parceiro)
                    if tipo_produto:
                        parceiros[parceiro]['tipos'].add(tipo_produto)
                    if csosn_xml:
                        parceiros[parceiro]['csosns'].add(csosn_xml)
                    if reducao_sped:
                        parceiros[parceiro]['reducoes'].add(str(reducao_sped))

                # Processar CFOP SPED
                if cfop_sped:
                    cfops_sped[cfop_sped]['parceiros'].add(parceiro)
                    if cst_sped:
                        cfops_sped[cfop_sped]['csts_sped'].add(cst_sped)
                    if cst_xml:
                        cfops_sped[cfop_sped]['csts_xml'].add(cst_xml)
                    if ncm_sped:
                        cfops_sped[cfop_sped]['ncms_sped'].add(ncm_sped)
                    if ncm_xml:
                        cfops_sped[cfop_sped]['ncms_xml'].add(ncm_xml)
                    cfops_sped[cfop_sped]['status'].add(status_tributo)
                    cfops_sped[cfop_sped]['match_types'].add(match_type)
                    if regime_parceiro:
                        cfops_sped[cfop_sped]['regimes'].add(regime_parceiro)

                # Processar CFOP XML
                if cfop_xml:
                    cfops_xml[cfop_xml]['parceiros'].add(parceiro)
                    if cst_sped:
                        cfops_xml[cfop_xml]['csts_sped'].add(cst_sped)
                    if cst_xml:
                        cfops_xml[cfop_xml]['csts_xml'].add(cst_xml)
                    if ncm_sped:
                        cfops_xml[cfop_xml]['ncms_sped'].add(ncm_sped)
                    if ncm_xml:
                        cfops_xml[cfop_xml]['ncms_xml'].add(ncm_xml)
                    cfops_xml[cfop_xml]['status'].add(status_tributo)
                    cfops_xml[cfop_xml]['match_types'].add(match_type)
                    if regime_parceiro:
                        cfops_xml[cfop_xml]['regimes'].add(regime_parceiro)

                # Processar CST SPED
                if cst_sped:
                    csts_sped[cst_sped]['parceiros'].add(parceiro)
                    if cfop_sped:
                        csts_sped[cst_sped]['cfops_sped'].add(cfop_sped)
                    if cfop_xml:
                        csts_sped[cst_sped]['cfops_xml'].add(cfop_xml)
                    if ncm_sped:
                        csts_sped[cst_sped]['ncms_sped'].add(ncm_sped)
                    if ncm_xml:
                        csts_sped[cst_sped]['ncms_xml'].add(ncm_xml)
                    csts_sped[cst_sped]['status'].add(status_tributo)
                    csts_sped[cst_sped]['match_types'].add(match_type)

                # Processar CST XML
                if cst_xml:
                    csts_xml[cst_xml]['parceiros'].add(parceiro)
                    if cfop_sped:
                        csts_xml[cst_xml]['cfops_sped'].add(cfop_sped)
                    if cfop_xml:
                        csts_xml[cst_xml]['cfops_xml'].add(cfop_xml)
                    if ncm_sped:
                        csts_xml[cst_xml]['ncms_sped'].add(ncm_sped)
                    if ncm_xml:
                        csts_xml[cst_xml]['ncms_xml'].add(ncm_xml)
                    csts_xml[cst_xml]['status'].add(status_tributo)
                    csts_xml[cst_xml]['match_types'].add(match_type)

                # Processar NCM SPED
                if ncm_sped:
                    ncms_sped[ncm_sped]['parceiros'].add(parceiro)
                    if cfop_sped:
                        ncms_sped[ncm_sped]['cfops_sped'].add(cfop_sped)
                    if cfop_xml:
                        ncms_sped[ncm_sped]['cfops_xml'].add(cfop_xml)
                    if cst_sped:
                        ncms_sped[ncm_sped]['csts_sped'].add(cst_sped)
                    if cst_xml:
                        ncms_sped[ncm_sped]['csts_xml'].add(cst_xml)
                    ncms_sped[ncm_sped]['status'].add(status_tributo)
                    ncms_sped[ncm_sped]['match_types'].add(match_type)

                # Processar NCM XML
                if ncm_xml:
                    ncms_xml[ncm_xml]['parceiros'].add(parceiro)
                    if cfop_sped:
                        ncms_xml[ncm_xml]['cfops_sped'].add(cfop_sped)
                    if cfop_xml:
                        ncms_xml[ncm_xml]['cfops_xml'].add(cfop_xml)
                    if cst_sped:
                        ncms_xml[ncm_xml]['csts_sped'].add(cst_sped)
                    if cst_xml:
                        ncms_xml[ncm_xml]['csts_xml'].add(cst_xml)
                    ncms_xml[ncm_xml]['status'].add(status_tributo)
                    ncms_xml[ncm_xml]['match_types'].add(match_type)

                # Processar ORIGEM SPED
                if origem_sped:
                    origens_sped[origem_sped]['parceiros'].add(parceiro)
                    if cfop_sped:
                        origens_sped[origem_sped]['cfops_sped'].add(cfop_sped)
                    if cfop_xml:
                        origens_sped[origem_sped]['cfops_xml'].add(cfop_xml)
                    if cst_sped:
                        origens_sped[origem_sped]['csts_sped'].add(cst_sped)
                    if cst_xml:
                        origens_sped[origem_sped]['csts_xml'].add(cst_xml)
                    if ncm_sped:
                        origens_sped[origem_sped]['ncms_sped'].add(ncm_sped)
                    if ncm_xml:
                        origens_sped[origem_sped]['ncms_xml'].add(ncm_xml)
                    if aliquota_sped:
                        formatted = f"{float(aliquota_sped):.2f}"
                        origens_sped[origem_sped]['aliquotas_sped'].add(formatted)
                    if aliquota_xml:
                        formatted_xml = f"{float(aliquota_xml):.2f}"
                        origens_sped[origem_sped]['aliquotas_xml'].add(formatted_xml)
                    origens_sped[origem_sped]['status'].add(status_tributo)
                    origens_sped[origem_sped]['match_types'].add(match_type)
                    if regime_parceiro:
                        origens_sped[origem_sped]['regimes'].add(regime_parceiro)
                    if tipo_produto:
                        origens_sped[origem_sped]['tipos'].add(tipo_produto)
                    if csosn_xml:
                        origens_sped[origem_sped]['csosns'].add(csosn_xml)
                    if reducao_sped:
                        origens_sped[origem_sped]['reducoes'].add(str(reducao_sped))

                # Processar ORIGEM XML
                if origem_xml:
                    origens_xml[origem_xml]['parceiros'].add(parceiro)
                    if cfop_sped:
                        origens_xml[origem_xml]['cfops_sped'].add(cfop_sped)
                    if cfop_xml:
                        origens_xml[origem_xml]['cfops_xml'].add(cfop_xml)
                    if cst_sped:
                        origens_xml[origem_xml]['csts_sped'].add(cst_sped)
                    if cst_xml:
                        origens_xml[origem_xml]['csts_xml'].add(cst_xml)
                    if ncm_sped:
                        origens_xml[origem_xml]['ncms_sped'].add(ncm_sped)
                    if ncm_xml:
                        origens_xml[origem_xml]['ncms_xml'].add(ncm_xml)
                    if aliquota_sped:
                        formatted = f"{float(aliquota_sped):.2f}"
                        origens_xml[origem_xml]['aliquotas_sped'].add(formatted)
                    if aliquota_xml:
                        formatted_xml = f"{float(aliquota_xml):.2f}"
                        origens_xml[origem_xml]['aliquotas_xml'].add(formatted_xml)
                    origens_xml[origem_xml]['status'].add(status_tributo)
                    origens_xml[origem_xml]['match_types'].add(match_type)
                    if regime_parceiro:
                        origens_xml[origem_xml]['regimes'].add(regime_parceiro)
                    if tipo_produto:
                        origens_xml[origem_xml]['tipos'].add(tipo_produto)
                    if csosn_xml:
                        origens_xml[origem_xml]['csosns'].add(csosn_xml)
                    if reducao_sped:
                        origens_xml[origem_xml]['reducoes'].add(str(reducao_sped))

                # Processar outros campos específicos
                if regime_parceiro:
                    regimes[regime_parceiro]['parceiros'].add(parceiro)
                    regimes[regime_parceiro]['status'].add(status_tributo)
                    regimes[regime_parceiro]['match_types'].add(match_type)

                if tipo_produto:
                    tipos[tipo_produto]['parceiros'].add(parceiro)
                    tipos[tipo_produto]['status'].add(status_tributo)
                    tipos[tipo_produto]['match_types'].add(match_type)

                if csosn_xml:
                    csosns[csosn_xml]['parceiros'].add(parceiro)
                    csosns[csosn_xml]['status'].add(status_tributo)
                    csosns[csosn_xml]['match_types'].add(match_type)

                if reducao_sped:
                    reducoes[str(reducao_sped)]['parceiros'].add(parceiro)
                    reducoes[str(reducao_sped)]['status'].add(status_tributo)
                    reducoes[str(reducao_sped)]['match_types'].add(match_type)

                if aliquota_sped:
                    formatted = f"{float(aliquota_sped):.2f}"
                    aliquotas_sped[formatted]['parceiros'].add(parceiro)
                    aliquotas_sped[formatted]['status'].add(status_tributo)
                    aliquotas_sped[formatted]['match_types'].add(match_type)

                if aliquota_xml:
                    formatted_xml = f"{float(aliquota_xml):.2f}"
                    aliquotas_xml[formatted_xml]['parceiros'].add(parceiro)
                    aliquotas_xml[formatted_xml]['status'].add(status_tributo)
                    aliquotas_xml[formatted_xml]['match_types'].add(match_type)

                # Processar status e match_types
                if status_tributo:
                    status[status_tributo]['parceiros'].add(parceiro)
                    status[status_tributo]['match_types'].add(match_type)

                if match_type:
                    match_types[match_type]['parceiros'].add(parceiro)
                    match_types[match_type]['status'].add(status_tributo)

            # Construir relacionamentos bidirecionais separando SPED e XML
            if parceiro:
                if cfop_sped:
                    parceiros[parceiro]['cfops_sped'].add(cfop_sped)
                if cfop_xml:
                    parceiros[parceiro]['cfops_xml'].add(cfop_xml)
                if cst_sped:
                    parceiros[parceiro]['csts_sped'].add(cst_sped)
                if cst_xml:
                    parceiros[parceiro]['csts_xml'].add(cst_xml)
                if ncm_sped:
                    parceiros[parceiro]['ncms_sped'].add(ncm_sped)
                if ncm_xml:
                    parceiros[parceiro]['ncms_xml'].add(ncm_xml)
                if origem_sped is not None and str(origem_sped) != '':
                    parceiros[parceiro]['origens_sped'].add(origem_sped)
                if origem_xml is not None and str(origem_xml) != '':
                    parceiros[parceiro]['origens_xml'].add(origem_xml)
                if aliquota_sped:
                   formatted = f"{float(aliquota_sped):.2f}"
                   parceiros[parceiro]['aliquotas_sped'].add(formatted)
                if aliquota_xml:
                   formatted_xml = f"{float(aliquota_xml):.2f}"
                   parceiros[parceiro]['aliquotas_xml'].add(formatted_xml)
                parceiros[parceiro]['status'].add(status_tributo)
                parceiros[parceiro]['match_types'].add(match_type)
                if regime_parceiro:
                    parceiros[parceiro]['regimes'].add(regime_parceiro)
                if tipo_produto:
                    parceiros[parceiro]['tipos'].add(tipo_produto)
                if csosn_xml:
                    parceiros[parceiro]['csosns'].add(csosn_xml)
                if reducao_sped:
                    parceiros[parceiro]['reducoes'].add(str(reducao_sped))

            # Processar CFOP SPED
            if cfop_sped:
                cfops_sped[cfop_sped]['parceiros'].add(parceiro)
                if cst_sped:
                    cfops_sped[cfop_sped]['csts_sped'].add(cst_sped)
                if cst_xml:
                    cfops_sped[cfop_sped]['csts_xml'].add(cst_xml)
                if ncm_sped:
                    cfops_sped[cfop_sped]['ncms_sped'].add(ncm_sped)
                if ncm_xml:
                    cfops_sped[cfop_sped]['ncms_xml'].add(ncm_xml)
                cfops_sped[cfop_sped]['status'].add(status_tributo)
                cfops_sped[cfop_sped]['match_types'].add(match_type)
                if regime_parceiro:
                    cfops_sped[cfop_sped]['regimes'].add(regime_parceiro)

            # Processar CFOP XML
            if cfop_xml:
                cfops_xml[cfop_xml]['parceiros'].add(parceiro)
                if cst_sped:
                    cfops_xml[cfop_xml]['csts_sped'].add(cst_sped)
                if cst_xml:
                    cfops_xml[cfop_xml]['csts_xml'].add(cst_xml)
                if ncm_sped:
                    cfops_xml[cfop_xml]['ncms_sped'].add(ncm_sped)
                if ncm_xml:
                    cfops_xml[cfop_xml]['ncms_xml'].add(ncm_xml)
                cfops_xml[cfop_xml]['status'].add(status_tributo)
                cfops_xml[cfop_xml]['match_types'].add(match_type)
                if regime_parceiro:
                    cfops_xml[cfop_xml]['regimes'].add(regime_parceiro)

            # Processar CST SPED
            if cst_sped:
                csts_sped[cst_sped]['parceiros'].add(parceiro)
                if cfop_sped:
                    csts_sped[cst_sped]['cfops_sped'].add(cfop_sped)
                if cfop_xml:
                    csts_sped[cst_sped]['cfops_xml'].add(cfop_xml)
                if ncm_sped:
                    csts_sped[cst_sped]['ncms_sped'].add(ncm_sped)
                if ncm_xml:
                    csts_sped[cst_sped]['ncms_xml'].add(ncm_xml)
                csts_sped[cst_sped]['status'].add(status_tributo)
                csts_sped[cst_sped]['match_types'].add(match_type)

            # Processar CST XML
            if cst_xml:
                csts_xml[cst_xml]['parceiros'].add(parceiro)
                if cfop_sped:
                    csts_xml[cst_xml]['cfops_sped'].add(cfop_sped)
                if cfop_xml:
                    csts_xml[cst_xml]['cfops_xml'].add(cfop_xml)
                if ncm_sped:
                    csts_xml[cst_xml]['ncms_sped'].add(ncm_sped)
                if ncm_xml:
                    csts_xml[cst_xml]['ncms_xml'].add(ncm_xml)
                csts_xml[cst_xml]['status'].add(status_tributo)
                csts_xml[cst_xml]['match_types'].add(match_type)

            # Processar NCM SPED
            if ncm_sped:
                ncms_sped[ncm_sped]['parceiros'].add(parceiro)
                if cfop_sped:
                    ncms_sped[ncm_sped]['cfops_sped'].add(cfop_sped)
                if cfop_xml:
                    ncms_sped[ncm_sped]['cfops_xml'].add(cfop_xml)
                if cst_sped:
                    ncms_sped[ncm_sped]['csts_sped'].add(cst_sped)
                if cst_xml:
                    ncms_sped[ncm_sped]['csts_xml'].add(cst_xml)
                ncms_sped[ncm_sped]['status'].add(status_tributo)
                ncms_sped[ncm_sped]['match_types'].add(match_type)

            # Processar NCM XML
            if ncm_xml:
                ncms_xml[ncm_xml]['parceiros'].add(parceiro)
                if cfop_sped:
                    ncms_xml[ncm_xml]['cfops_sped'].add(cfop_sped)
                if cfop_xml:
                    ncms_xml[ncm_xml]['cfops_xml'].add(cfop_xml)
                if cst_sped:
                    ncms_xml[ncm_xml]['csts_sped'].add(cst_sped)
                if cst_xml:
                    ncms_xml[ncm_xml]['csts_xml'].add(cst_xml)
                ncms_xml[ncm_xml]['status'].add(status_tributo)
                ncms_xml[ncm_xml]['match_types'].add(match_type)

            # Processar ORIGEM SPED
            if origem_sped:
                origens_sped[origem_sped]['parceiros'].add(parceiro)
                if cfop_sped:
                    origens_sped[origem_sped]['cfops_sped'].add(cfop_sped)
                if cfop_xml:
                    origens_sped[origem_sped]['cfops_xml'].add(cfop_xml)
                if cst_sped:
                    origens_sped[origem_sped]['csts_sped'].add(cst_sped)
                if cst_xml:
                    origens_sped[origem_sped]['csts_xml'].add(cst_xml)
                if ncm_sped:
                    origens_sped[origem_sped]['ncms_sped'].add(ncm_sped)
                if ncm_xml:
                    origens_sped[origem_sped]['ncms_xml'].add(ncm_xml)
                if aliquota_sped:
                    formatted = f"{float(aliquota_sped):.2f}"
                    origens_sped[origem_sped]['aliquotas_sped'].add(formatted)
                if aliquota_xml:
                    formatted_xml = f"{float(aliquota_xml):.2f}"
                    origens_sped[origem_sped]['aliquotas_xml'].add(formatted_xml)
                origens_sped[origem_sped]['status'].add(status_tributo)
                origens_sped[origem_sped]['match_types'].add(match_type)
                if regime_parceiro:
                    origens_sped[origem_sped]['regimes'].add(regime_parceiro)
                if tipo_produto:
                    origens_sped[origem_sped]['tipos'].add(tipo_produto)
                if csosn_xml:
                    origens_sped[origem_sped]['csosns'].add(csosn_xml)
                if reducao_sped:
                    origens_sped[origem_sped]['reducoes'].add(str(reducao_sped))

            # Processar ORIGEM XML
            if origem_xml:
                origens_xml[origem_xml]['parceiros'].add(parceiro)
                if cfop_sped:
                    origens_xml[origem_xml]['cfops_sped'].add(cfop_sped)
                if cfop_xml:
                    origens_xml[origem_xml]['cfops_xml'].add(cfop_xml)
                if cst_sped:
                    origens_xml[origem_xml]['csts_sped'].add(cst_sped)
                if cst_xml:
                    origens_xml[origem_xml]['csts_xml'].add(cst_xml)
                if ncm_sped:
                    origens_xml[origem_xml]['ncms_sped'].add(ncm_sped)
                if ncm_xml:
                    origens_xml[origem_xml]['ncms_xml'].add(ncm_xml)
                if aliquota_sped:
                    formatted = f"{float(aliquota_sped):.2f}"
                    origens_xml[origem_xml]['aliquotas_sped'].add(formatted)
                if aliquota_xml:
                    formatted_xml = f"{float(aliquota_xml):.2f}"
                    origens_xml[origem_xml]['aliquotas_xml'].add(formatted_xml)
                origens_xml[origem_xml]['status'].add(status_tributo)
                origens_xml[origem_xml]['match_types'].add(match_type)
                if regime_parceiro:
                    origens_xml[origem_xml]['regimes'].add(regime_parceiro)
                if tipo_produto:
                    origens_xml[origem_xml]['tipos'].add(tipo_produto)
                if csosn_xml:
                    origens_xml[origem_xml]['csosns'].add(csosn_xml)
                if reducao_sped:
                    origens_xml[origem_xml]['reducoes'].add(str(reducao_sped))

            # Processar outros campos específicos
            if regime_parceiro:
                regimes[regime_parceiro]['parceiros'].add(parceiro)
                regimes[regime_parceiro]['status'].add(status_tributo)
                regimes[regime_parceiro]['match_types'].add(match_type)

            if tipo_produto:
                tipos[tipo_produto]['parceiros'].add(parceiro)
                tipos[tipo_produto]['status'].add(status_tributo)
                tipos[tipo_produto]['match_types'].add(match_type)

            if csosn_xml:
                csosns[csosn_xml]['parceiros'].add(parceiro)
                csosns[csosn_xml]['status'].add(status_tributo)
                csosns[csosn_xml]['match_types'].add(match_type)

            if reducao_sped:
                reducoes[str(reducao_sped)]['parceiros'].add(parceiro)
                reducoes[str(reducao_sped)]['status'].add(status_tributo)
                reducoes[str(reducao_sped)]['match_types'].add(match_type)

            if aliquota_sped:
                formatted = f"{float(aliquota_sped):.2f}"
                aliquotas_sped[formatted]['parceiros'].add(parceiro)
                aliquotas_sped[formatted]['status'].add(status_tributo)
                aliquotas_sped[formatted]['match_types'].add(match_type)

            if aliquota_xml:
                formatted_xml = f"{float(aliquota_xml):.2f}"
                aliquotas_xml[formatted_xml]['parceiros'].add(parceiro)
                aliquotas_xml[formatted_xml]['status'].add(status_tributo)
                aliquotas_xml[formatted_xml]['match_types'].add(match_type)
            # Processar status e match_types
            if status_tributo:
                status[status_tributo]['parceiros'].add(parceiro)
                status[status_tributo]['match_types'].add(match_type)

            if match_type:
                match_types[match_type]['parceiros'].add(parceiro)
                match_types[match_type]['status'].add(status_tributo)

        # Converter sets para listas e formatar resposta
        opcoes = {
            'parceiros': [{'value': parceiro, 'related': {}} for parceiro in sorted(parceiros.keys()) if parceiro],
            'ufs': [{'value': uf, 'related': {}} for uf in sorted(ufs_set) if uf],

            'cfops_sped': [{'value': cfop, 'related': {}} for cfop in sorted(cfops_sped.keys()) if cfop],
            'cfops_xml': [{'value': cfop, 'related': {}} for cfop in sorted(cfops_xml.keys()) if cfop],

            'csts_sped': [{'value': cst, 'related': {}} for cst in sorted(csts_sped.keys()) if cst],
            'csts_xml': [{'value': cst, 'related': {}} for cst in sorted(csts_xml.keys()) if cst],

            'ncms_sped': [{'value': ncm, 'related': {}} for ncm in sorted(ncms_sped.keys()) if ncm],
            'ncms_xml': [{'value': ncm, 'related': {}} for ncm in sorted(ncms_xml.keys()) if ncm],

            'origens_sped': [
                {'value': origem, 'related': {}}
                for origem in sorted(origens_sped.keys())
                if origem is not None and str(origem) != ''
            ],
            'origens_xml': [
                {'value': origem, 'related': {}}
                for origem in sorted(origens_xml.keys())
                if origem is not None and str(origem) != ''
            ],

            'csts_sped_pis': [{'value': v, 'related': {}} for v in sorted(csts_sped_pis_set) if v],
            'csts_sped_cofins': [{'value': v, 'related': {}} for v in sorted(csts_sped_cofins_set) if v],
            'csts_xml_pis': [{'value': v, 'related': {}} for v in sorted(csts_xml_pis_set) if v],
            'csts_xml_cofins': [{'value': v, 'related': {}} for v in sorted(csts_xml_cofins_set) if v],

            'aliquotas_sped_pis': [{'value': v, 'related': {}} for v in sorted(aliquotas_sped_pis_set, key=lambda x: float(x)) if v],
            'aliquotas_sped_cofins': [{'value': v, 'related': {}} for v in sorted(aliquotas_sped_cofins_set, key=lambda x: float(x)) if v],
            'aliquotas_xml_pis': [{'value': v, 'related': {}} for v in sorted(aliquotas_xml_pis_set, key=lambda x: float(x)) if v],
            'aliquotas_xml_cofins': [{'value': v, 'related': {}} for v in sorted(aliquotas_xml_cofins_set, key=lambda x: float(x)) if v],

            'aliquotas_sped': [{'value': aliquota, 'related': {}} for aliquota in sorted(aliquotas_sped.keys(), key=lambda x: float(x) if x and x.replace('.', '').replace(',', '').isdigit() else 0) if aliquota],
            'aliquotas_xml': [{'value': aliquota, 'related': {}} for aliquota in sorted(aliquotas_xml.keys(), key=lambda x: float(x) if x and x.replace('.', '').replace(',', '').isdigit() else 0) if aliquota],

            'status': [{'value': stat, 'related': {}} for stat in sorted(status.keys()) if stat],
            'match_types': [
                {
                    'value': match_type,
                    'label': {
                        'direct': 'Direto',
                        'embedding': 'por IA',
                        'unmatched_sped': 'SPED sem par',
                        'unmatched_xml': 'XML sem par',
                        'manual': 'Manual'
                    }.get(match_type, match_type),
                    'related': {}
                }
                for match_type in sorted(match_types.keys()) if match_type
            ],
            'regimes': [{'value': regime, 'related': {}} for regime in sorted(regimes.keys()) if regime],
            'tipos': [{'value': tipo, 'related': {}} for tipo in sorted(tipos.keys()) if tipo],
            'csosns': [{'value': csosn, 'related': {}} for csosn in sorted(csosns.keys()) if csosn],
            'reducoes_sped': [{'value': r, 'related': {}} for r in sorted(reducoes_sped_set, key=lambda x: float(x) if x and x.replace('.', '').replace(',', '').isdigit() else 0) if r],
            'reducoes_xml': [{'value': r, 'related': {}} for r in sorted(reducoes_xml_set, key=lambda x: float(x) if x and x.replace('.', '').replace(',', '').isdigit() else 0) if r],
            'reducoes': [{'value': reducao, 'related': {}} for reducao in sorted(reducoes.keys(), key=lambda x: float(x) if x and x.replace('.', '').replace(',', '').isdigit() else 0) if reducao],
            'mvas': [{'value': v, 'related': {}} for v in sorted(mvas_set, key=lambda x: float(x)) if v]
        }

        return jsonify({
            'success': True,
            'opcoes': opcoes
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao obter opções de filtro: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/matching/<chave_nf>', methods=['GET'])
@jwt_required()
def get_matching_details(chave_nf):
    """
    Busca detalhes do matching para uma nota específica
    """
    try:
        usuario_id = get_jwt_identity()
        empresa_id = request.args.get('empresa_id', type=int)
        
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400
        
        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404
        
        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403
        
        # Executar matching
        matching_service = ItemMatchingService(empresa_id, empresa.escritorio_id)
        resultado = matching_service.find_matches_for_note(chave_nf)
        
        return jsonify(resultado), 200
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/aprovar-match', methods=['POST'])
@jwt_required()
def aprovar_match():
    """
    Aprova um match sugerido pelo algoritmo
    """
    try:
        data = request.get_json()
        usuario_id = get_jwt_identity()

        xml_item_id = data.get('xml_item_id')
        sped_item_id = data.get('sped_item_id')
        empresa_id = data.get('empresa_id')
        tributo = data.get('tributo', 'icms')  # Obter tributo específico da requisição
        feedback = data.get('feedback', '')

        if not all([xml_item_id, sped_item_id, empresa_id]):
            return jsonify({
                'success': False,
                'message': 'xml_item_id, sped_item_id e empresa_id são obrigatórios'
            }), 400

        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Buscar dados dos itens XML e SPED
        from models.nota_fiscal_item import NotaFiscalItem
        from models.item_nota_entrada import ItemNotaEntrada
        from models.produto import Produto
        from models.produto_entrada import ProdutoEntrada

        xml_item = db.session.get(NotaFiscalItem, xml_item_id)
        sped_item = db.session.get(ItemNotaEntrada, sped_item_id)

        if not xml_item or not sped_item:
            return jsonify({
                'success': False,
                'message': 'Item XML ou SPED não encontrado'
            }), 404

        # Buscar códigos dos produtos
        xml_produto = db.session.get(Produto, xml_item.produto_id) if xml_item.produto_id else None
        sped_produto = db.session.get(ProdutoEntrada, sped_item.produto_entrada_id) if sped_item.produto_entrada_id else None

        xml_codigo_produto = xml_produto.codigo if xml_produto else None
        sped_codigo_produto = sped_produto.cod_item if sped_produto else sped_item.cod_item

        # Buscar registro de auditoria
        from models.auditoria_comparativa_impostos import AuditoriaComparativaImpostos
        auditoria = db.session.query(AuditoriaComparativaImpostos).filter(
            AuditoriaComparativaImpostos.xml_item_id == xml_item_id,
            AuditoriaComparativaImpostos.sped_item_id == sped_item_id,
            AuditoriaComparativaImpostos.empresa_id == empresa_id
        ).first()

        if not auditoria:
            return jsonify({
                'success': False,
                'message': 'Registro de auditoria não encontrado'
            }), 404

        # Verificar se já foi aprovado para este tributo
        if auditoria.is_tributo_aprovado(tributo):
            return jsonify({
                'success': False,
                'message': f'Match já foi aprovado para {tributo.upper()}'
            }), 400

        # Aprovar para o tributo específico
        auditoria.set_status_tributo(tributo, 'aprovado', usuario_id, feedback)

        # Criar serviço para salvar histórico com dados atualizados
        service = AuditoriaComparativaService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )

        # Salvar histórico de matching com dados SPED atualizados
        with transactional_session():
            resultado_historico = service.salvar_historico_matching_com_dados_atualizados(
                auditoria, tributo, 'aprovado', usuario_id, feedback
            )

            if not resultado_historico['success']:
                print(f"Aviso: Erro ao salvar histórico de matching: {resultado_historico['message']}")

        return jsonify({
            'success': True,
            'message': 'Match aprovado com sucesso',
            'historico_id': resultado_historico.get('historico_id') if resultado_historico['success'] else None
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500
    finally:
        db.session.close()

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/sugestoes/<int:auditoria_id>', methods=['GET'])
@jwt_required()
def obter_sugestoes_inteligentes(auditoria_id):
    """
    Obtém sugestões inteligentes para um item específico
    """
    try:
        usuario_id = get_jwt_identity()
        tributo = request.args.get('tributo', 'icms')
        empresa_id = request.args.get('empresa_id', type=int)

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400

        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Criar serviço e obter sugestões
        service = SugestoesInteligentesService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id
        )

        resultado = service.obter_sugestoes_para_item(auditoria_id, tributo)

        if resultado['success']:
            return jsonify(resultado), 200
        else:
            return jsonify(resultado), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/auto-aprovar/<int:auditoria_id>', methods=['POST'])
@jwt_required()
def auto_aprovar_item(auditoria_id):
    """
    Processa auto-aprovação de um item baseado nas sugestões
    """
    try:
        data = request.get_json()
        usuario_id = get_jwt_identity()

        tributo = data.get('tributo', 'icms')
        empresa_id = data.get('empresa_id')

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400

        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Criar serviço e processar auto-aprovação
        service = SugestoesInteligentesService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id
        )

        resultado = service.processar_auto_aprovacao(auditoria_id, tributo, usuario_id)

        if resultado['success']:
            return jsonify(resultado), 200
        else:
            return jsonify(resultado), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/rejeitar-match', methods=['POST'])
@jwt_required()
def rejeitar_match():
    """
    Rejeita um match sugerido pelo algoritmo
    """
    try:
        data = request.get_json()
        usuario_id = get_jwt_identity()

        xml_item_id = data.get('xml_item_id')
        sped_item_id = data.get('sped_item_id')
        empresa_id = data.get('empresa_id')
        tributo = data.get('tributo', 'icms')  # Obter tributo específico da requisição
        motivo = data.get('motivo', '')

        if not all([xml_item_id, sped_item_id, empresa_id]):
            return jsonify({
                'success': False,
                'message': 'xml_item_id, sped_item_id e empresa_id são obrigatórios'
            }), 400

        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Buscar dados dos itens XML e SPED
        from models.nota_fiscal_item import NotaFiscalItem
        from models.item_nota_entrada import ItemNotaEntrada
        from models.produto import Produto
        from models.produto_entrada import ProdutoEntrada

        xml_item = db.session.get(NotaFiscalItem, xml_item_id)
        sped_item = db.session.get(ItemNotaEntrada, sped_item_id)

        if not xml_item or not sped_item:
            return jsonify({
                'success': False,
                'message': 'Item XML ou SPED não encontrado'
            }), 404

        # Buscar códigos dos produtos
        xml_produto = db.session.get(Produto, xml_item.produto_id) if xml_item.produto_id else None
        sped_produto = db.session.get(ProdutoEntrada, sped_item.produto_entrada_id) if sped_item.produto_entrada_id else None

        xml_codigo_produto = xml_produto.codigo if xml_produto else None
        sped_codigo_produto = sped_produto.cod_item if sped_produto else sped_item.cod_item

        # Registrar rejeição no histórico de aprendizado
        from models.auditoria_comparativa_impostos import HistoricoMatchingAprendizado

        historico = HistoricoMatchingAprendizado()
        historico.empresa_id = empresa_id
        historico.escritorio_id = empresa.escritorio_id
        historico.xml_item_id = xml_item_id
        historico.sped_item_id = sped_item_id
        historico.cliente_id = xml_item.cliente_id
        historico.xml_codigo_produto = xml_codigo_produto
        historico.sped_codigo_produto = sped_codigo_produto
        historico.acao_usuario = f'rejeitado_{tributo}'  # Incluir tributo específico
        historico.usuario_id = usuario_id
        historico.feedback_usuario = f'[{tributo.upper()}] {motivo}' if motivo else f'[{tributo.upper()}] Rejeitado'

        # Adicionar características do match para aprendizado
        historico.match_type_original = data.get('match_type')
        historico.confidence_score_original = data.get('confidence_score')
        historico.caracteristicas_match = {
            'tributo': tributo,  # Adicionar tributo específico
            'timestamp': data.get('timestamp'),
            'confidence_score': data.get('confidence_score'),
            'match_type': data.get('match_type'),
            'motivo_rejeicao': motivo,
            'xml_codigo_produto': xml_codigo_produto,
            'sped_codigo_produto': sped_codigo_produto,
            'cliente_id': xml_item.cliente_id
        }



        # Buscar registro de auditoria
        from models.auditoria_comparativa_impostos import AuditoriaComparativaImpostos
        auditoria = db.session.query(AuditoriaComparativaImpostos).filter(
            AuditoriaComparativaImpostos.xml_item_id == xml_item_id,
            AuditoriaComparativaImpostos.sped_item_id == sped_item_id,
            AuditoriaComparativaImpostos.empresa_id == empresa_id
        ).first()

        if auditoria:
            # Rejeitar para o tributo específico
            auditoria.set_status_tributo(tributo, 'rejeitado', usuario_id, motivo)
        else:
            return jsonify({
                'success': False,
                'message': 'Registro de auditoria não encontrado'
            }), 404

        with transactional_session():
            db.session.add(historico)

        return jsonify({
            'success': True,
            'message': 'Match rejeitado com sucesso',
            'historico_id': historico.id
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500
    finally:
        db.session.close()

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/aprovar-matches-massa', methods=['POST'])
@jwt_required()
def aprovar_matches_massa():
    """
    Aprova múltiplos matches em massa
    """
    try:
        data = request.get_json()
        usuario_id = get_jwt_identity()

        matches = data.get('matches', [])  # Lista de {xml_item_id, sped_item_id}
        empresa_id = data.get('empresa_id')
        tributo = data.get('tributo', 'icms')  # Tributo específico
        feedback = data.get('feedback', 'Aprovação em massa')

        if not matches or not empresa_id:
            return jsonify({
                'success': False,
                'message': 'Lista de matches e empresa_id são obrigatórios'
            }), 400

        # Verificar permissão
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        aprovados = []
        erros = []

        with transactional_session():
            for match in matches:
                try:
                    xml_item_id = match.get('xml_item_id')
                    sped_item_id = match.get('sped_item_id')

                    if not xml_item_id or not sped_item_id:
                        erros.append(f"Match inválido: {match}")
                        continue

                    # Buscar dados dos itens
                    from models.nota_fiscal_item import NotaFiscalItem
                    from models.item_nota_entrada import ItemNotaEntrada
                    from models.produto import Produto
                    from models.produto_entrada import ProdutoEntrada

                    xml_item = db.session.get(NotaFiscalItem, xml_item_id)
                    sped_item = db.session.get(ItemNotaEntrada, sped_item_id)

                    if not xml_item or not sped_item:
                        erros.append(f"Item não encontrado: XML {xml_item_id}, SPED {sped_item_id}")
                        continue

                    # Buscar códigos dos produtos
                    xml_produto = db.session.get(Produto, xml_item.produto_id) if xml_item.produto_id else None
                    sped_produto = db.session.get(ProdutoEntrada, sped_item.produto_entrada_id) if sped_item.produto_entrada_id else None

                    xml_codigo_produto = xml_produto.codigo if xml_produto else None
                    sped_codigo_produto = sped_produto.cod_item if sped_produto else sped_item.cod_item

                    # Registrar aprovação no histórico utilizando o mesmo
                    # mecanismo da aprovação individual para garantir que
                    # todas as características do match sejam salvas.
                    service = AuditoriaComparativaService(
                        empresa_id=empresa_id,
                        escritorio_id=empresa.escritorio_id,
                        usuario_id=usuario_id
                    )

                    # Buscar registro de auditoria
                    from models.auditoria_comparativa_impostos import AuditoriaComparativaImpostos
                    auditoria = db.session.query(AuditoriaComparativaImpostos).filter(
                        AuditoriaComparativaImpostos.xml_item_id == xml_item_id,
                        AuditoriaComparativaImpostos.sped_item_id == sped_item_id,
                        AuditoriaComparativaImpostos.empresa_id == empresa_id
                    ).first()

                    if auditoria:
                        if auditoria.is_tributo_aprovado(tributo):
                            erros.append(
                                f"Match XML {xml_item_id}, SPED {sped_item_id} já aprovado para {tributo.upper()}"
                            )
                            continue

                        auditoria.set_status_tributo(tributo, 'aprovado', usuario_id, feedback)

                        resultado_historico = service.salvar_historico_matching_com_dados_atualizados(
                            auditoria,
                            tributo,
                            'aprovado',
                            usuario_id,
                            feedback,
                            aprovacao_massa=True
                        )

                        if not resultado_historico['success']:
                            erros.append(
                                f"Erro ao salvar histórico: {resultado_historico['message']}"
                            )
                        else:
                            aprovados.append({
                                'xml_item_id': xml_item_id,
                                'sped_item_id': sped_item_id,
                                'historico_id': resultado_historico.get('historico_id')
                            })
                    else:
                        erros.append(
                            f"Registro de auditoria não encontrado: XML {xml_item_id}, SPED {sped_item_id}"
                        )
                        continue

                except Exception as e:
                    erros.append(
                        f"Erro no match XML {xml_item_id}, SPED {sped_item_id}: {str(e)}"
                    )

        return jsonify({
            'success': True,
            'message': f'{len(aprovados)} matches aprovados com sucesso',
            'aprovados': len(aprovados),
            'erros': len(erros),
            'detalhes_erros': erros
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500
    finally:
        db.session.close()

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/editar-sped/<int:auditoria_id>', methods=['PUT'])
@jwt_required()
def editar_dados_sped(auditoria_id):
    """
    Edita dados SPED de um registro de auditoria comparativa
    """
    try:
        data = request.get_json()
        usuario_id = get_jwt_identity()

        print(f"DEBUG - Editando SPED auditoria_id: {auditoria_id}")
        print(f"DEBUG - Dados recebidos: {data}")
        print(f"DEBUG - Usuario ID: {usuario_id}")

        empresa_id = data.get('empresa_id')
        motivo_alteracao = data.get('motivo_alteracao', '')

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400

        # Verificar permissão do usuário
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Criar serviço e editar dados
        service = AuditoriaComparativaService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )

        print(f"DEBUG FRONTEND - Dados recebidos: {data}")

        # Extrair dados SPED dos tributos
        dados_sped = {}

        # Primeiro, adicionar campos básicos diretamente
        campos_basicos = ['cfop', 'ncm', 'origem_icms', 'tipo_item']
        for campo in campos_basicos:
            if campo in data:
                dados_sped[campo] = data[campo]
                print(f"DEBUG - Campo básico {campo}: {data[campo]}")

        # Depois, adicionar dados dos tributos
        for key, value in data.items():
            if key not in ['empresa_id', 'motivo_alteracao'] + campos_basicos and isinstance(value, dict):
                print(f"DEBUG - Processando tributo {key}: {value}")
                dados_sped[key] = value

        # Adicionar motivo da alteração
        dados_sped['motivo_alteracao'] = motivo_alteracao

        print(f"DEBUG - Dados SPED processados finais: {dados_sped}")

        resultado = service.editar_dados_sped(auditoria_id, dados_sped)

        print(f"DEBUG - Resultado do serviço: {resultado}")

        if resultado['success']:
            return jsonify(resultado), 200
        else:
            return jsonify(resultado), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@auditoria_comparativa_bp.route('/api/auditoria-comparativa/editar-sped-massa', methods=['PUT'])
@jwt_required()
def editar_dados_sped_massa():
    """
    Edita dados SPED de múltiplos registros de auditoria comparativa
    """
    try:
        data = request.get_json()
        usuario_id = get_jwt_identity()

        auditoria_ids = data.get('auditoria_ids', [])
        empresa_id = data.get('empresa_id')
        motivo_alteracao = data.get('motivo_alteracao', '')

        if not auditoria_ids or not empresa_id:
            return jsonify({
                'success': False,
                'message': 'auditoria_ids e empresa_id são obrigatórios'
            }), 400

        # Verificar permissão do usuário
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not has_company_access(usuario, empresa):
            return jsonify({'message': 'Acesso negado'}), 403

        # Criar serviço
        service = AuditoriaComparativaService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )

        print(f"DEBUG MASSA - Dados recebidos: {data}")
        print(f"DEBUG MASSA - Auditoria IDs: {auditoria_ids}")

        # Extrair dados SPED dos tributos
        dados_sped = {}

        # Primeiro, adicionar campos básicos diretamente
        campos_basicos = ['cfop', 'ncm', 'origem_icms', 'tipo_item']
        for campo in campos_basicos:
            if campo in data and data[campo]:
                dados_sped[campo] = data[campo]
                print(f"DEBUG MASSA - Campo básico {campo}: {data[campo]}")

        # Depois, adicionar dados dos tributos
        for key, value in data.items():
            if key not in ['auditoria_ids', 'empresa_id', 'motivo_alteracao'] + campos_basicos and isinstance(value, dict):
                print(f"DEBUG MASSA - Processando tributo {key}: {value}")
                dados_sped[key] = value

        # Adicionar motivo da alteração
        dados_sped['motivo_alteracao'] = motivo_alteracao

        print(f"DEBUG MASSA - Dados SPED processados finais: {dados_sped}")

        # Processar cada auditoria
        sucessos = []
        erros = []

        for auditoria_id in auditoria_ids:
            try:
                resultado = service.editar_dados_sped(auditoria_id, dados_sped)
                if resultado['success']:
                    sucessos.append(auditoria_id)
                else:
                    erros.append(f"ID {auditoria_id}: {resultado['message']}")
            except Exception as e:
                erros.append(f"ID {auditoria_id}: {str(e)}")

        return jsonify({
            'success': len(sucessos) > 0,
            'message': f'{len(sucessos)} registros atualizados com sucesso',
            'sucessos': sucessos,
            'erros': erros,
            'total_processados': len(auditoria_ids),
            'total_sucessos': len(sucessos),
            'total_erros': len(erros)
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500
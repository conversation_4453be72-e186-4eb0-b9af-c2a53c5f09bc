import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useFilterStore } from '@/store/filterStore'
import { dashboardService } from '@/services/dashboardService'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { DashboardCards } from '@/components/dashboard/DashboardCards'
import { EmpresasList } from '@/components/dashboard/EmpresasList'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

type TabType = 'saida' | 'entrada'

export function DashboardPage() {
  const [activeTab, setActiveTab] = useState<TabType>('saida')
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card')
  const { selectedYear, selectedMonth } = useFilterStore()

  // Queries para dados de saída
  const {
    data: statsSaida,
    isLoading: loadingStatsSaida,
    error: errorStatsSaida,
  } = useQuery({
    queryKey: ['dashboard-stats-saida', selectedYear, selectedMonth],
    queryFn: () => dashboardService.getStats(selectedYear, selectedMonth),
    enabled: activeTab === 'saida',
  })

  const {
    data: empresasSaida,
    isLoading: loadingEmpresasSaida,
    error: errorEmpresasSaida,
  } = useQuery({
    queryKey: ['dashboard-empresas-saida', selectedYear, selectedMonth],
    queryFn: () => dashboardService.getEmpresas(selectedYear, selectedMonth),
    enabled: activeTab === 'saida',
  })

  // Queries para dados de entrada
  const {
    data: statsEntrada,
    isLoading: loadingStatsEntrada,
    error: errorStatsEntrada,
  } = useQuery({
    queryKey: ['dashboard-stats-entrada', selectedYear, selectedMonth],
    queryFn: () => dashboardService.getStatsEntrada(selectedYear, selectedMonth),
    enabled: activeTab === 'entrada',
  })

  const {
    data: empresasEntrada,
    isLoading: loadingEmpresasEntrada,
    error: errorEmpresasEntrada,
  } = useQuery({
    queryKey: ['dashboard-empresas-entrada', selectedYear, selectedMonth],
    queryFn: () => dashboardService.getEmpresasEntrada(selectedYear, selectedMonth),
    enabled: activeTab === 'entrada',
  })

  // Dados atuais baseados na tab ativa
  const currentStats = activeTab === 'saida' ? statsSaida : statsEntrada
  const currentEmpresas = activeTab === 'saida' ? empresasSaida : empresasEntrada
  const isLoadingStats = activeTab === 'saida' ? loadingStatsSaida : loadingStatsEntrada
  const isLoadingEmpresas = activeTab === 'saida' ? loadingEmpresasSaida : loadingEmpresasEntrada
  const hasError = activeTab === 'saida' ? errorStatsSaida || errorEmpresasSaida : errorStatsEntrada || errorEmpresasEntrada

  return (
    <div className="space-y-6">
      {/* Header Hero */}
      <div className="relative overflow-hidden bg-gradient-to-br  rounded-3xl text-white">
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3 mb-3">
                <div>
                  <h1 className="text-4xl text-black font-semibold mb-1">
                    Dashboard
                  </h1>
                  <p className="text-gray-400 text-lg">
                    Visão geral do sistema de auditoria fiscal
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2 text-gray-500">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">Período: {selectedMonth}/{selectedYear}</span>
              </div>
            </div>
            
            <div className="hidden md:block">
              <div className="w-32 h-32 bg-white/10 rounded-full animate-float"></div>
            </div>
          </div>
        </div>
        
        {/* Background decorations */}
        <div className="absolute top-0 right-0 -mt-8 -mr-8 w-40 h-40 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 -mb-12 -ml-12 w-48 h-48 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 right-1/4 w-24 h-24 bg-white/10 rounded-full blur-2xl"></div>
      </div>

      {/* Modern Tabs */}
      <Card className="p-2 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex gap-2">
          <Button
            variant={activeTab === 'saida' ? 'primary' : 'ghost'}
            size="md"
            onClick={() => setActiveTab('saida')}
            className="flex-1 justify-center"
            icon={
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            }
            glow={activeTab === 'saida'}
          >
            Saída
          </Button>
          
          <Button
            variant={activeTab === 'entrada' ? 'primary' : 'ghost'}
            size="md"
            onClick={() => setActiveTab('entrada')}
            className="flex-1 justify-center"
            icon={
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            }
            glow={activeTab === 'entrada'}
          >
            Entrada
          </Button>
        </div>
      </Card>

      {/* Error State */}
      {hasError && (
        <Card className="border-error-200 dark:border-error-800 bg-gradient-to-r from-error-50 to-error-100 dark:from-error-900/20 dark:to-error-800/20">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-error-100 dark:bg-error-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
              <svg className="w-6 h-6 text-error-600 dark:text-error-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-error-800 dark:text-error-200 mb-1">
                ⚠️ Erro ao carregar dados do dashboard
              </h3>
              <p className="text-error-700 dark:text-error-300 mb-4">
                Verifique sua conexão e tente novamente. Se o problema persistir, entre em contato com o suporte.
              </p>
              <Button
                variant="error"
                size="sm"
                onClick={() => window.location.reload()}
                icon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                }
              >
                Tentar Novamente
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Loading State */}
      {isLoadingStats && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <LoadingSpinner size="lg" className="mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              Carregando dados do dashboard...
            </p>
          </div>
        </div>
      )}

      {/* Dashboard Content */}
      {!isLoadingStats && !hasError && currentStats && (
        <>
          {/* Stats Cards */}
          <DashboardCards stats={currentStats} />

          {/* Companies List */}
          <Card className="overflow-hidden">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 p-6 border-b border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-xl flex items-center justify-center">
                    <svg className="w-5 h-5 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4zM3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm1 5a1 1 0 100 2h12a1 1 0 100-2H4z" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white gradient-text">
                      Suas Empresas
                    </h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Clique em "Ver Detalhes" para acessar o dashboard específico da empresa
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant={viewMode === 'card' ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('card')}
                    icon={
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1V4a1 1 0 00-1-1H4zm0 6a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1v-3a1 1 0 00-1-1H4zm6-6a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1V4zm1 6a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-3z" />
                      </svg>
                    }
                    aria-label="Exibir em cards"
                  />
                  <Button
                    variant={viewMode === 'list' ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    icon={
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm1 4a1 1 0 100 2h12a1 1 0 100-2H4z" clipRule="evenodd" />
                      </svg>
                    }
                    aria-label="Exibir em lista"
                  />
                </div>
              </div>
            </div>
            
            {isLoadingEmpresas ? (
              <div className="p-12 text-center">
                <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <LoadingSpinner className="w-8 h-8" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Carregando empresas...
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Aguarde enquanto buscamos suas empresas
                </p>
              </div>
            ) : (
              <EmpresasList empresas={currentEmpresas || []} tipo={activeTab} viewMode={viewMode} />
            )}
          </Card>
        </>
      )}
    </div>
  )
}
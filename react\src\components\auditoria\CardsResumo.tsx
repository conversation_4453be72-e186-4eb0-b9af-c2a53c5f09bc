import React from 'react'
import { Card, StatsCard } from '@/components/ui/Card'
import { DashboardSumario } from '@/services/auditoriaService'

interface CardsResumoProps {
  sumario: DashboardSumario
  onInconsistenciaClick?: () => void
}

export function CardsResumo({ sumario, onInconsistenciaClick }: CardsResumoProps) {
  const formatCurrency = (value?: number) => {
    if (value === undefined || value === null) return 'R$ 0,00'
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  // Calcular valores derivados
  const valorConforme = sumario.valor_total_notas - sumario.valor_inconsistente_maior - sumario.valor_inconsistente_menor
  const totalInconsistente = sumario.valor_inconsistente_maior + sumario.valor_inconsistente_menor
  
  const porcentagemConforme = sumario.valor_total_notas > 0 
    ? (valorConforme / sumario.valor_total_notas) * 100 
    : 0
  const porcentagemInconsistente = sumario.valor_total_notas > 0 
    ? (totalInconsistente / sumario.valor_total_notas) * 100 
    : 0

  const notasConformes = sumario.notas_conformes !== undefined
    ? sumario.notas_conformes
    : sumario.total_notas - Math.min(sumario.total_inconsistente, sumario.total_notas)

  const notasInconsistentes = sumario.notas_inconsistentes !== undefined
    ? sumario.notas_inconsistentes
    : Math.min(sumario.total_inconsistente, sumario.total_notas)

  return (
    <div className="space-y-6">
      {/* Cards principais */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 hover:scale-105 transition-transform duration-200" hover>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                TOTAL
              </h3>
              <p className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {formatCurrency(sumario.valor_total_notas)}
              </p>
              <div className="space-y-1">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Total de notas: <span className="font-semibold">{sumario.total_notas.toLocaleString('pt-BR')}</span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Total de operações: <span className="font-semibold">{sumario.total_produtos.toLocaleString('pt-BR')}</span>
                </div>
              </div>
            </div>
            <div className="w-12 h-12 rounded-xl flex items-center justify-center text-white bg-gradient-to-r from-primary-500 to-primary-600">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="p-6 hover:scale-105 transition-transform duration-200" hover>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Conformidade
              </h3>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
                {formatCurrency(valorConforme)}
              </p>
              <div className="flex items-center gap-2 mb-2">
                <div className="flex items-center gap-1 text-xs font-medium px-2 py-1 rounded-full text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/20">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                  {formatPercentage(porcentagemConforme)}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Notas conforme: <span className="font-semibold text-green-600">{notasConformes.toLocaleString('pt-BR')}</span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Operações conforme: <span className="font-semibold text-green-600">{(sumario.total_conforme || 0).toLocaleString('pt-BR')}</span>
                </div>
              </div>
            </div>
            <div className="w-12 h-12 rounded-xl flex items-center justify-center text-white bg-gradient-to-r from-green-500 to-green-600">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </Card>

        <Card 
          className="p-6 hover:scale-105 transition-transform duration-200 cursor-pointer" 
          hover
          onClick={onInconsistenciaClick}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Inconsistências
              </h3>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400 mb-2">
                {formatCurrency(totalInconsistente)}
              </p>
              <div className="flex items-center gap-2 mb-2">
                <div className="flex items-center gap-1 text-xs font-medium px-2 py-1 rounded-full text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/20">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  {formatPercentage(porcentagemInconsistente)}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Notas inconsistentes: <span className="font-semibold text-red-600">{notasInconsistentes.toLocaleString('pt-BR')}</span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Operações inconsistentes: <span className="font-semibold text-red-600">{sumario.total_inconsistente.toLocaleString('pt-BR')}</span>
                </div>
              </div>
            </div>
            <div className="w-12 h-12 rounded-xl flex items-center justify-center text-white bg-gradient-to-r from-red-500 to-red-600">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </Card>
      </div>

      {/* Cards de valores a maior e a menor */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6 hover:shadow-lg transition-all duration-200" hover>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Valores a Maior
              </h3>
              <p className={`text-2xl font-bold mb-2 ${
                sumario.valor_inconsistente_maior > 0 
                  ? 'text-red-600 dark:text-red-400' 
                  : 'text-gray-900 dark:text-white'
              }`}>
                {formatCurrency(sumario.valor_inconsistente_maior)}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Valores calculados maiores que os da nota
              </p>
            </div>
            <div className="w-12 h-12 rounded-xl flex items-center justify-center text-white bg-gradient-to-r from-red-500 to-red-600">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="p-6 hover:shadow-lg transition-all duration-200" hover>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Valores a Menor
              </h3>
              <p className={`text-2xl font-bold mb-2 ${
                sumario.valor_inconsistente_menor > 0 
                  ? 'text-red-600 dark:text-red-400' 
                  : 'text-gray-900 dark:text-white'
              }`}>
                {formatCurrency(sumario.valor_inconsistente_menor)}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Valores calculados menores que os da nota
              </p>
            </div>
            <div className="w-12 h-12 rounded-xl flex items-center justify-center text-white bg-gradient-to-r from-orange-500 to-orange-600">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 13l-5 5m0 0l-5-5m5 5V6" />
              </svg>
            </div>
          </div>
        </Card>
      </div>

      {/* Resumo de inconsistências */}
      {sumario.total_inconsistente > 0 && (
        <Card className="p-6 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900/50 flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                Resumo de Inconsistências
              </h4>
              <p className="text-yellow-700 dark:text-yellow-300 mb-4">
                Foram encontradas <span className="font-bold">{sumario.total_inconsistente.toLocaleString('pt-BR')}</span> inconsistências neste período.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center">
                    <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-green-700 dark:text-green-300">
                      {(sumario.total_inconsistentes_vistas || 0).toLocaleString('pt-BR')} inconsistências analisadas
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900/50 flex items-center justify-center">
                    <svg className="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-orange-700 dark:text-orange-300">
                      {(sumario.total_inconsistentes_nao_vistas || 0).toLocaleString('pt-BR')} aguardam análise
                    </p>
                  </div>
                </div>
              </div>
              
              <p className="text-sm text-yellow-600 dark:text-yellow-400 mt-4">
                Acesse a aba <span className="font-semibold">Detalhamento</span> para visualizar e filtrar os dados completos.
              </p>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
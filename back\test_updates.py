#!/usr/bin/env python3
"""
Script de teste para verificar as atualizações implementadas
"""

import sys
import os

# Adicionar o diretório back ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'back'))

def test_xml_processor_cpf():
    """Testa se o XMLProcessor consegue extrair CPF corretamente"""
    print("🧪 Testando XMLProcessor com CPF...")
    
    try:
        from utils.xml_processor import XMLProcessor
        
        # XML de teste com CPF
        xml_test = """<?xml version="1.0" encoding="UTF-8"?>
        <nfeProc xmlns="http://www.portalfiscal.inf.br/nfe">
            <NFe>
                <infNFe Id="NFe35200714200166000187550010000000046550000046">
                    <emit>
                        <CPF>12345678901</CPF>
                        <xNome><PERSON></xNome>
                    </emit>
                    <dest>
                        <CNPJ>12345678000195</CNPJ>
                        <xNome>Empresa Teste</xNome>
                    </dest>
                    <ide>
                        <tpNF>0</tpNF>
                    </ide>
                </infNFe>
            </NFe>
        </nfeProc>"""
        
        processor = XMLProcessor(xml_test)
        emitente = processor.get_emitente()
        destinatario = processor.get_destinatario()
        info_nfe = processor.get_info_nfe()
        
        print(f"✅ Emitente CPF: {emitente.get('cpf')}")
        print(f"✅ Emitente tipo: {emitente.get('tipo_pessoa')}")
        print(f"✅ Destinatário CNPJ: {destinatario.get('cnpj')}")
        print(f"✅ Tipo operação: {info_nfe.get('tipo_operacao')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste XMLProcessor: {str(e)}")
        return False

def test_safe_clean_document():
    """Testa a função de limpeza segura de documentos"""
    print("\n🧪 Testando limpeza segura de documentos...")
    
    try:
        from services.xml_import_service import XMLImportService
        
        # Criar instância temporária para testar o método
        service = XMLImportService(1, 1, 1)
        
        # Testes
        test_cases = [
            ("12.345.678/0001-95", "12345678000195"),
            ("123.456.789-01", "12345678901"),
            (None, None),
            ("", None),
            ("   ", None),
        ]
        
        for input_doc, expected in test_cases:
            result = service._safe_clean_document(input_doc)
            if result == expected:
                print(f"✅ '{input_doc}' -> '{result}'")
            else:
                print(f"❌ '{input_doc}' -> '{result}' (esperado: '{expected}')")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de limpeza: {str(e)}")
        return False

def test_importacao_async_model():
    """Testa se o modelo ImportacaoAsync está funcionando"""
    print("\n🧪 Testando modelo ImportacaoAsync...")
    
    try:
        from models.importacao_async import ImportacaoAsync
        
        # Criar instância de teste
        importacao = ImportacaoAsync(
            id="test-123",
            usuario_id=1,
            escritorio_id=1,
            tipo="xml_batch",
            total_arquivos=10
        )
        
        # Testar métodos
        importacao.atualizar_progresso(5, 4, 1, "Processando arquivo teste.xml")
        
        print(f"✅ Progresso: {importacao.porcentagem}%")
        print(f"✅ Processados: {importacao.arquivos_processados}")
        print(f"✅ Sucesso: {importacao.arquivos_sucesso}")
        print(f"✅ Erro: {importacao.arquivos_erro}")
        
        # Testar to_dict
        data = importacao.to_dict()
        print(f"✅ Serialização: {len(data)} campos")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste ImportacaoAsync: {str(e)}")
        return False

def main():
    """Executa todos os testes"""
    print("🚀 Iniciando testes das atualizações do sistema XML\n")
    
    tests = [
        test_xml_processor_cpf,
        test_safe_clean_document,
        test_importacao_async_model,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Resultado: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 Todas as atualizações estão funcionando corretamente!")
        return 0
    else:
        print("⚠️  Algumas atualizações precisam de ajustes.")
        return 1

if __name__ == "__main__":
    sys.exit(main())

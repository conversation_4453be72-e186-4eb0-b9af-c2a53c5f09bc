from flask import Blueprint, request, jsonify, current_app
from functools import wraps
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, ImportacaoXML, ImportacaoSped, Empresa
from services import XMLImportService
from services.batch_xml_import_service import BatchXMLImportService
from services.sped_import_service import SPEDImportService
from services.optimized_xml_import_service import OptimizedXMLImportService
from utils.xml_processor import XMLProcessor
from utils.sped_processor import SPEDProcessor
import os
import time
from werkzeug.utils import secure_filename
from typing import List
import re

importacao_bp = Blueprint('importacao_bp', __name__)

def normalizar_cnpj(cnpj):
    """Remove formatação do CNPJ, mantendo apenas números"""
    if not cnpj:
        return None
    return re.sub(r'[^0-9]', '', cnpj)

def buscar_empresa_por_cnpj(cnpj, usuario):
    """
    Busca empresa por CNPJ considerando formatação e contexto do usuário
    """
    if not cnpj:
        return None
    
    cnpj_normalizado = normalizar_cnpj(cnpj)
    
    # Buscar por CNPJ normalizado e formatado
    cnpjs_busca = [cnpj, cnpj_normalizado]
    
    # Adicionar versão formatada se não estiver na lista
    if len(cnpj_normalizado) == 14:
        cnpj_formatado = f"{cnpj_normalizado[:2]}.{cnpj_normalizado[2:5]}.{cnpj_normalizado[5:8]}/{cnpj_normalizado[8:12]}-{cnpj_normalizado[12:]}"
        if cnpj_formatado not in cnpjs_busca:
            cnpjs_busca.append(cnpj_formatado)
    
    for cnpj_teste in cnpjs_busca:
        if usuario.tipo_usuario == 'escritorio':
            # Usuário de escritório: buscar apenas empresas do seu escritório
            empresa = Empresa.query.filter_by(cnpj=cnpj_teste, escritorio_id=usuario.escritorio_id).first()
            if empresa:
                return empresa
        else:
            # Admin: priorizar empresa do próprio escritório, senão buscar globalmente
            empresa = Empresa.query.filter_by(cnpj=cnpj_teste, escritorio_id=usuario.escritorio_id).first()
            if empresa:
                return empresa
            
            # Se não encontrou no próprio escritório, buscar globalmente
            empresa = Empresa.query.filter_by(cnpj=cnpj_teste).first()
            if empresa:
                return empresa
    
    return None

# Configuração para upload de arquivos
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'uploads')
ALLOWED_EXTENSIONS = {'xml', 'txt', 'zip'}

# Criar diretório de uploads se não existir
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    """
    Verifica se o arquivo tem uma extensão permitida
    """
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@importacao_bp.route('/api/importacoes/batch', methods=['POST'])
@jwt_required()
def importar_xml_batch():
    """
    Importa múltiplos arquivos XML em lote
    """
    try:
        print("[BATCH] Iniciando importação em lote...")
        print(f"[BATCH] Headers da requisição: {dict(request.headers)}")
        print(f"[BATCH] Files na requisição: {request.files.keys()}")

        # Verificar se foram enviados arquivos
        if 'arquivos' not in request.files:
            print("[BATCH] Erro: Campo 'arquivos' não encontrado na requisição")
            return jsonify({"message": "Nenhum arquivo enviado"}), 400

        arquivos = request.files.getlist('arquivos')
        print(f"[BATCH] Número de arquivos recebidos: {len(arquivos)}")
        print(f"[BATCH] Nomes dos arquivos: {[a.filename for a in arquivos]}")

        if not arquivos or all(arquivo.filename == '' for arquivo in arquivos):
            print("[BATCH] Erro: Nenhum arquivo válido selecionado")
            return jsonify({"message": "Nenhum arquivo selecionado"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar e processar arquivos (XML e ZIP)
        xml_files = []
        invalid_files = []

        for arquivo in arquivos:
            filename = arquivo.filename.lower()
            
            if filename.endswith('.zip'):
                # Processar arquivo ZIP
                try:
                    zip_content = arquivo.read()
                    extracted_xmls = _extract_xmls_from_zip_batch(zip_content, arquivo.filename)
                    xml_files.extend(extracted_xmls)
                    print(f"[BATCH] Extraídos {len(extracted_xmls)} XMLs do arquivo ZIP: {arquivo.filename}")
                except Exception as e:
                    invalid_files.append({
                        'filename': arquivo.filename,
                        'message': f'Erro ao processar arquivo ZIP: {str(e)}'
                    })
                continue
                
            elif not filename.endswith('.xml'):
                invalid_files.append({
                    'filename': arquivo.filename,
                    'message': 'Arquivo deve ser XML ou ZIP'
                })
                continue

            try:
                # Ler conteúdo do arquivo como string
                xml_content = arquivo.read().decode('utf-8')
                if not xml_content:
                    invalid_files.append({
                        'filename': arquivo.filename,
                        'message': 'Arquivo vazio'
                    })
                    continue

                # Validar XML
                processor = XMLProcessor(xml_content)
                emitente = processor.get_emitente()
                destinatario = processor.get_destinatario()

                # Extrair documentos com tratamento seguro
                cnpj_emitente = emitente.get('cnpj') or emitente.get('cpf')
                cnpj_destinatario = destinatario.get('cnpj') or destinatario.get('cpf')

                if not cnpj_emitente and not cnpj_destinatario:
                    invalid_files.append({
                        'filename': arquivo.filename,
                        'reason': 'CNPJ/CPF do emitente e destinatário não encontrados'
                    })
                    continue

                # Verificar se empresa existe (emitente ou destinatário)
                empresa = None
                if cnpj_emitente:
                    empresa = buscar_empresa_por_cnpj(cnpj_emitente, usuario)

                if not empresa and cnpj_destinatario:
                    empresa = buscar_empresa_por_cnpj(cnpj_destinatario, usuario)

                if not empresa:
                    invalid_files.append({
                        'filename': arquivo.filename,
                        'reason': f'Empresa não cadastrada (Emitente: {cnpj_emitente}, Destinatário: {cnpj_destinatario})'
                    })
                    continue

                # Verificar permissões para a empresa
                if not (usuario.is_admin or
                        usuario.tipo_usuario == 'admin' or
                        (usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id) or
                        (usuario.empresas_permitidas and empresa.id in usuario.empresas_permitidas)):
                    invalid_files.append({
                        'filename': arquivo.filename,
                        'reason': f'Sem permissão para importar para empresa {empresa.razao_social}'
                    })
                    continue

                # Adicionar à lista de XMLs válidos
                xml_files.append({
                    'filename': arquivo.filename,
                    'content': xml_content
                })

            except Exception as e:
                invalid_files.append({
                    'filename': arquivo.filename,
                    'reason': f'Erro ao processar XML: {str(e)}'
                })
                continue

        if not xml_files:
            return jsonify({
                "message": "Nenhum XML válido para importação",
                "invalid_files": invalid_files
            }), 400

        # Processar XMLs válidos em lote
        # Determinar o escritório_id com base no tipo de usuário e permissões
        if usuario.tipo_usuario == 'escritorio':
            escritorio_id = usuario.escritorio_id
        else:
            # Para usuários comuns, pegar o escritório da primeira empresa válida
            if len(xml_files) > 0:
                primeiro_xml = xml_files[0]
                processor = XMLProcessor(primeiro_xml['content'])
                emitente = processor.get_emitente()
                destinatario = processor.get_destinatario()

                cnpj_emitente = emitente.get('cnpj') or emitente.get('cpf')
                cnpj_destinatario = destinatario.get('cnpj') or destinatario.get('cpf')

                empresa = None
                if cnpj_emitente:
                    empresa = buscar_empresa_por_cnpj(cnpj_emitente, usuario)
                if not empresa and cnpj_destinatario:
                    empresa = buscar_empresa_por_cnpj(cnpj_destinatario, usuario)

                escritorio_id = empresa.escritorio_id if empresa else None
            else:
                escritorio_id = None

        # Gerar ou utilizar ID único para esta importação
        import uuid
        import_id = request.form.get('import_id') or str(uuid.uuid4())

        # Obter serviço WebSocket
        from services.websocket_service import get_websocket_service
        websocket_service = get_websocket_service()

        # Criar callback de progresso
        progress_callback = None
        if websocket_service:
            progress_callback = websocket_service.create_progress_callback(import_id)

        # Importar sistema de filas e importação assíncrona
        from services.queue_manager import get_queue_manager, Task, TaskStatus
        from models.importacao_async import ImportacaoAsync
        queue_manager = get_queue_manager()

        # Criar registro de importação assíncrona
        importacao_async = ImportacaoAsync(
            id=import_id,
            usuario_id=usuario_id,
            escritorio_id=escritorio_id,
            tipo='xml_batch',
            status='iniciando',
            total_arquivos=len(xml_files)
        )
        db.session.add(importacao_async)
        db.session.commit()

        # Criar tarefa para a fila
        task = Task(
            id=import_id,
            type='batch_import',
            user_id=usuario_id,
            empresa_id=0,  # Múltiplas empresas
            data={
                'xml_files': xml_files,
                'escritorio_id': escritorio_id,
                'usuario_id': usuario_id,
                'import_id': import_id,
                'progress_callback': progress_callback
            },
            priority=1  # Prioridade normal
        )

        # Definir callback para processamento
        def process_batch_import(data):
            service = BatchXMLImportService(
                escritorio_id=data['escritorio_id'],
                usuario_id=data['usuario_id'],
                max_workers=2,  # Reduzido para evitar sobrecarga
                progress_callback=data['progress_callback'],
                import_id=data['import_id'],
                app=current_app._get_current_object()
            )
            return service.process_xml_batch(data['xml_files'])

        task.callback = process_batch_import

        # Submeter tarefa para a fila
        if not queue_manager.submit_import_task(task):
            # Marcar importação como erro se não conseguir submeter
            importacao_async.marcar_erro("Sistema sobrecarregado")
            db.session.commit()

            return jsonify({
                "message": "Sistema sobrecarregado. Tente novamente em alguns minutos.",
                "error": "Queue full"
            }), 503  # Service Unavailable

        # O processamento será feito pela fila em background
        # WebSocket notificará automaticamente sobre o progresso

        # Retornar imediatamente com o import_id
        return jsonify({
            "message": "Importação iniciada",
            "import_id": import_id,
            "total_files": len(xml_files),
            "invalid_files": invalid_files,
            "status": "processing"
        }), 202  # 202 Accepted - processamento iniciado

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"[BATCH] Erro ao processar lote de XMLs: {str(e)}")
        print(f"[BATCH] Stack trace completo:\n{error_trace}")

        # Enviar notificação de erro via WebSocket
        if 'websocket_service' in locals() and websocket_service and 'import_id' in locals():
            websocket_service.send_import_error(import_id, {
                "message": "Erro ao processar solicitação",
                "error": str(e)
            })

        return jsonify({
            "message": "Erro ao processar solicitação",
            "error": str(e),
            "stack_trace": error_trace
        }), 500

@importacao_bp.route('/api/importacoes', methods=['GET'])
@jwt_required()
def listar_importacoes():
    """
    Lista as importações de XML realizadas
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Construir a query base
        query = ImportacaoXML.query

        # Filtrar por empresa se especificado
        if empresa_id:
            query = query.filter_by(empresa_id=empresa_id)

        # Aplicar filtros com base no tipo de usuário
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todas as importações
            pass
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem importações do seu escritório
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas importações das empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            query = query.filter(ImportacaoXML.empresa_id.in_(empresas_permitidas))

        # Ordenar por data de importação (mais recentes primeiro)
        query = query.order_by(ImportacaoXML.data_importacao.desc())

        # Executar a query
        importacoes = query.all()

        return jsonify({
            "importacoes": [importacao.to_dict() for importacao in importacoes]
        }), 200

    except Exception as e:
        print(f"Erro ao listar importações: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@importacao_bp.route('/api/importacoes/<int:importacao_id>', methods=['GET'])
@jwt_required()
def obter_importacao(importacao_id):
    """
    Obtém os detalhes de uma importação específica
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar a importação
        importacao = db.session.get(ImportacaoXML, importacao_id)

        if not importacao:
            return jsonify({"message": "Importação não encontrada"}), 404

        # Verificar permissões para visualizar a importação
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem ver qualquer importação
            pass
        elif usuario.tipo_usuario == 'escritorio' and importacao.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem ver importações do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and importacao.empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem ver importações das empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para visualizar esta importação"}), 403

        return jsonify({
            "importacao": importacao.to_dict()
        }), 200

    except Exception as e:
        print(f"Erro ao obter importação: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@importacao_bp.route('/api/importacoes', methods=['POST'])
@jwt_required()
def importar_xml():
    """
    Importa um arquivo XML
    """
    try:
        # Verificar se há arquivo na requisição
        if 'arquivo' not in request.files:
            return jsonify({"message": "Nenhum arquivo enviado"}), 400

        arquivo = request.files['arquivo']

        # Verificar se o arquivo tem nome
        if arquivo.filename == '':
            return jsonify({"message": "Nenhum arquivo selecionado"}), 400

        # Verificar se o arquivo é um XML
        if not allowed_file(arquivo.filename):
            return jsonify({"message": "Formato de arquivo não permitido. Use XML"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Salvar o arquivo temporariamente
        filename = secure_filename(arquivo.filename)
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        arquivo.save(filepath)

        # Ler o conteúdo do arquivo
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                xml_content = f.read()
        except UnicodeDecodeError:
            # Tentar com outra codificação se UTF-8 falhar
            try:
                with open(filepath, 'r', encoding='latin-1') as f:
                    xml_content = f.read()
            except Exception as e:
                return jsonify({"message": f"Erro ao ler o arquivo XML: {str(e)}"}), 400

        # Extrair o CNPJ do emitente e destinatário do XML
        try:
            processor = XMLProcessor(xml_content)
            emitente = processor.get_emitente()
            destinatario = processor.get_destinatario()
            cnpj_emitente = emitente.get('cnpj')
            cnpj_destinatario = destinatario.get('cnpj')

            if not cnpj_emitente:
                return jsonify({"message": "Não foi possível extrair o CNPJ do emitente do XML"}), 400

            print(f"CNPJ do emitente extraído do XML: {cnpj_emitente}")
            print(f"CNPJ do destinatário extraído do XML: {cnpj_destinatario}")

            # Buscar empresas considerando o escritório do usuário
            empresa_emitente = None
            empresa_destinatario = None
            
            if cnpj_emitente:
                empresa_emitente = buscar_empresa_por_cnpj(cnpj_emitente, usuario)
            
            if cnpj_destinatario:
                empresa_destinatario = buscar_empresa_por_cnpj(cnpj_destinatario, usuario)

            empresas_encontradas = []
            if empresa_emitente:
                empresas_encontradas.append(empresa_emitente)
            if empresa_destinatario and (not empresa_emitente or empresa_destinatario.id != empresa_emitente.id):
                empresas_encontradas.append(empresa_destinatario)

            if not empresas_encontradas:
                return jsonify({
                    "message": f"Não foi encontrada nenhuma empresa cadastrada com o CNPJ {cnpj_emitente} (emitente) nem com o CNPJ {cnpj_destinatario} (destinatário). Por favor, cadastre a empresa antes de importar o XML."
                }), 400

            resultados_empresas = []

            for emp in empresas_encontradas:
                if usuario.is_admin or usuario.tipo_usuario == 'admin':
                    tem_permissao = True
                elif usuario.tipo_usuario == 'escritorio' and emp.escritorio_id == usuario.escritorio_id:
                    tem_permissao = True
                elif usuario.empresas_permitidas and emp.id in usuario.empresas_permitidas:
                    tem_permissao = True
                else:
                    tem_permissao = False

                if not tem_permissao:
                    resultados_empresas.append({
                        'empresa_id': emp.id,
                        'empresa_nome': emp.razao_social,
                        'success': False,
                        'message': f"Você não tem permissão para importar para a empresa {emp.razao_social} (CNPJ: {emp.cnpj})"
                    })
                    continue

                service = XMLImportService(
                    empresa_id=emp.id,
                    escritorio_id=emp.escritorio_id,
                    usuario_id=usuario_id
                )

                resultado = service.import_xml(xml_content, filename)
                resultados_empresas.append({
                    'empresa_id': emp.id,
                    'empresa_nome': emp.razao_social,
                    **resultado
                })

        except Exception as e:
            return jsonify({"message": f"Erro ao processar o XML: {str(e)}"}), 400

        # Remover o arquivo temporário
        try:
            os.remove(filepath)
        except:
            pass

        if len(resultados_empresas) == 1:
            resultado = resultados_empresas[0]
            if resultado.get('success'):
                return jsonify({
                    "message": "XML importado com sucesso",
                    "importacao": resultado
                }), 201
            else:
                return jsonify({
                    "message": resultado.get('message'),
                    "error": resultado.get('error'),
                    "stack_trace": resultado.get('stack_trace')
                }), 400
        else:
            sucesso = any(r.get('success') for r in resultados_empresas)
            return jsonify({
                "message": "XML processado para múltiplas empresas",
                "resultados": resultados_empresas
            }), 201 if sucesso else 400

    except Exception as e:
        print(f"Erro ao importar XML: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500


@importacao_bp.route('/api/importacoes/sped', methods=['POST'])
@jwt_required()
def importar_sped():
    """
    Importa um arquivo SPED
    """
    try:
        # Verificar se há arquivo na requisição
        if 'arquivo' not in request.files:
            return jsonify({"message": "Nenhum arquivo enviado"}), 400

        arquivo = request.files['arquivo']

        # Verificar se o arquivo tem nome
        if arquivo.filename == '':
            return jsonify({"message": "Nenhum arquivo selecionado"}), 400

        # Verificar se o arquivo é um TXT
        if not arquivo.filename.lower().endswith('.txt'):
            return jsonify({"message": "Formato de arquivo não permitido. Use TXT"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Salvar o arquivo temporariamente
        filename = secure_filename(arquivo.filename)
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        arquivo.save(filepath)

        # Ler o conteúdo do arquivo
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                sped_content = f.read()
        except UnicodeDecodeError:
            # Tentar com outra codificação se UTF-8 falhar
            try:
                with open(filepath, 'r', encoding='latin-1') as f:
                    sped_content = f.read()
            except Exception as e:
                return jsonify({"message": f"Erro ao ler o arquivo SPED: {str(e)}"}), 400

        # Processar o SPED para obter informações da empresa
        try:
            processor = SPEDProcessor(sped_content)
            empresa_data = processor.get_empresa_data()
            cnpj_empresa = empresa_data.get('cnpj')

            if not cnpj_empresa:
                return jsonify({"message": "CNPJ da empresa não encontrado no arquivo SPED"}), 400

        except Exception as e:
            return jsonify({"message": f"Erro ao processar o SPED: {str(e)}"}), 400

        # Verificar se empresa existe considerando o contexto do usuário
        empresa = None
        # Buscar empresa por CNPJ
        empresa = buscar_empresa_por_cnpj(cnpj_empresa, usuario)
        
        if not empresa:
            return jsonify({
                "message": f"Empresa não cadastrada (CNPJ: {cnpj_empresa})"
            }), 400

        # Verificar permissões do usuário para a empresa
        if usuario.tipo_usuario == 'empresa' and usuario.empresa_id != empresa.id:
            return jsonify({"message": "Sem permissão para importar para esta empresa"}), 403

        if usuario.tipo_usuario == 'escritorio' and usuario.escritorio_id != empresa.escritorio_id:
            return jsonify({"message": "Sem permissão para importar para esta empresa"}), 403

        # Usar import_id fornecido pelo cliente ou gerar um novo
        import_id = request.form.get('import_id')
        if not import_id:
            import uuid
            import_id = str(uuid.uuid4())

        # Obter serviço de WebSocket
        from services.websocket_service import get_websocket_service
        websocket_service = get_websocket_service()

        # Enviar notificação de início
        if websocket_service:
            websocket_service.send_sped_import_start(import_id, {
                'message': 'Iniciando importação do arquivo SPED',
                'filename': filename,
                'empresa': empresa.razao_social,
                'cnpj': empresa.cnpj,
                'status': 'iniciando'
            })

        # Criar serviço de importação com WebSocket
        import_service = SPEDImportService(
            empresa_id=empresa.id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id,
            websocket_service=websocket_service,
            import_id=import_id
        )

        resultado = import_service.import_sped(sped_content, filename)

        # Remover o arquivo temporário
        try:
            os.remove(filepath)
        except:
            pass

        if resultado['success']:
            return jsonify({
                "message": "SPED importado com sucesso",
                "import_id": import_id,
                "importacao": resultado,
                "totais": resultado.get('totais', {})
            }), 201
        else:
            return jsonify({
                "message": resultado['message'],
                "import_id": import_id,
                "error": resultado.get('error'),
                "stack_trace": resultado.get('stack_trace')
            }), 400

    except Exception as e:
        print(f"Erro ao importar SPED: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500


@importacao_bp.route('/api/importacoes/sped/historico', methods=['GET'])
@jwt_required()
def listar_importacoes_sped():
    """
    Lista o histórico de importações SPED
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Construir query baseada no tipo de usuário
        query = ImportacaoSped.query

        if usuario.tipo_usuario == 'empresa':
            query = query.filter_by(empresa_id=usuario.empresa_id)
        elif usuario.tipo_usuario == 'escritorio':
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        # Admin pode ver todas

        # Ordenar por data de importação (mais recentes primeiro)
        importacoes = query.order_by(ImportacaoSped.data_importacao.desc()).all()

        return jsonify({
            "importacoes": [importacao.to_dict() for importacao in importacoes]
        }), 200

    except Exception as e:
        print(f"Erro ao listar importações SPED: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@importacao_bp.route('/api/importacoes/status/<import_id>', methods=['GET'])
@jwt_required()
def verificar_status_importacao(import_id):
    """
    Verifica o status de uma importação assíncrona
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar importação assíncrona
        from models.importacao_async import ImportacaoAsync
        importacao = ImportacaoAsync.query.get(import_id)

        if not importacao:
            return jsonify({"message": "Importação não encontrada"}), 404

        # Verificar permissão
        if importacao.usuario_id != usuario_id and usuario.escritorio_id != importacao.escritorio_id:
            return jsonify({"message": "Acesso negado"}), 403

        return jsonify({
            "success": True,
            "importacao": importacao.to_dict()
        })

    except Exception as e:
        print(f"Erro ao verificar status da importação: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@importacao_bp.route('/api/importacoes/ativas', methods=['GET'])
@jwt_required()
def listar_importacoes_ativas():
    """
    Lista importações ativas do usuário
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar importações ativas
        from models.importacao_async import ImportacaoAsync
        importacoes = ImportacaoAsync.obter_ativas_usuario(usuario_id)

        return jsonify({
            "success": True,
            "importacoes": [imp.to_dict() for imp in importacoes],
            "total": len(importacoes)
        })

    except Exception as e:
        print(f"Erro ao listar importações ativas: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@importacao_bp.route('/api/importacoes/historico', methods=['GET'])
@jwt_required()
def listar_historico_importacoes():
    """
    Lista histórico de importações do usuário
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Parâmetros de filtro
        limite = request.args.get('limite', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        tipo = request.args.get('tipo')  # xml_batch, sped, etc.

        # Buscar importações
        from models.importacao_async import ImportacaoAsync
        query = ImportacaoAsync.query.filter_by(usuario_id=usuario_id)

        if tipo:
            query = query.filter_by(tipo=tipo)

        importacoes = query.order_by(ImportacaoAsync.data_inicio.desc())\
                          .offset(offset)\
                          .limit(limite)\
                          .all()

        total = query.count()

        return jsonify({
            "success": True,
            "importacoes": [imp.to_dict() for imp in importacoes],
            "total": total,
            "limite": limite,
            "offset": offset
        })

    except Exception as e:
        print(f"Erro ao listar histórico de importações: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@importacao_bp.route('/api/importacoes/optimized', methods=['POST'])
@jwt_required()
def importar_xml_optimized():
    """
    Importa XMLs com otimizações de performance e suporte a ZIP
    COM DETECÇÃO AUTOMÁTICA DE EMPRESA (igual à importação original)
    """
    try:
        # Verificar se há arquivo na requisição
        if 'arquivo' not in request.files:
            return jsonify({"message": "Nenhum arquivo enviado"}), 400

        arquivo = request.files['arquivo']

        # Verificar se o arquivo tem nome
        if arquivo.filename == '':
            return jsonify({"message": "Nenhum arquivo selecionado"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Verificar se é arquivo ZIP ou XML individual
        filename = arquivo.filename.lower()

        # Usar import_id fornecido ou gerar um novo
        import_id = request.form.get('import_id')
        if not import_id:
            import uuid
            import_id = str(uuid.uuid4())

        from services.websocket_service import get_websocket_service
        websocket_service = get_websocket_service()

        if filename.endswith('.zip'):
            # Para ZIP, usar detecção automática por XML
            zip_content = arquivo.read()
            resultado = _process_zip_optimized(
                zip_content,
                arquivo.filename,
                usuario,
                usuario_id,
                websocket_service=websocket_service,
                import_id=import_id,
            )

        elif filename.endswith('.xml'):
            # Para XML individual, usar mesma lógica da importação original
            xml_content = arquivo.read().decode('utf-8')
            resultado = _process_single_xml_optimized(xml_content, arquivo.filename, usuario, usuario_id)

        else:
            return jsonify({"message": "Formato não suportado. Use XML ou ZIP"}), 400

        if resultado['success']:
            return jsonify({
                "message": resultado.get("message", "Importação otimizada concluída com sucesso"),
                "import_id": import_id,
                "resultado": resultado,
                "performance": resultado.get('performance', {}),
                "cache_stats": resultado.get('stats', {})
            }), 201
        else:
            return jsonify({
                "message": resultado['message'],
                "errors": resultado.get('errors', []),
                "performance": resultado.get('performance', {}),
                "import_id": import_id
            }), 400

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"💥 Erro na importação otimizada: {str(e)}")
        print(f"Stack trace: {error_trace}")

        return jsonify({
            "message": "Erro ao processar importação otimizada",
            "error": str(e),
            "stack_trace": error_trace
        }), 500


def _process_single_xml_optimized(xml_content: str, filename: str, usuario, usuario_id: int):
    """
    Processa um XML individual com detecção automática de empresa (igual à importação original)
    """
    try:
        # Extrair o CNPJ do emitente e destinatário do XML (MESMA LÓGICA DA IMPORTAÇÃO ORIGINAL)
        processor = XMLProcessor(xml_content)
        emitente = processor.get_emitente()
        destinatario = processor.get_destinatario()
        cnpj_emitente = emitente.get('cnpj')
        cnpj_destinatario = destinatario.get('cnpj')

        if not cnpj_emitente:
            return {
                'success': False,
                'message': "Não foi possível extrair o CNPJ do emitente do XML"
            }

        print(f"🔍 CNPJ do emitente extraído do XML: {cnpj_emitente}")
        print(f"🔍 CNPJ do destinatário extraído do XML: {cnpj_destinatario}")

        # Buscar empresas considerando o contexto do usuário
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        empresa_emitente = None
        empresa_destinatario = None
        
        if cnpj_emitente:
            empresa_emitente = buscar_empresa_por_cnpj(cnpj_emitente, usuario)
        
        if cnpj_destinatario:
            empresa_destinatario = buscar_empresa_por_cnpj(cnpj_destinatario, usuario)

        empresas_encontradas = []
        if empresa_emitente:
            empresas_encontradas.append(empresa_emitente)
        if empresa_destinatario and (not empresa_emitente or empresa_destinatario.id != empresa_emitente.id):
            empresas_encontradas.append(empresa_destinatario)

        if not empresas_encontradas:
            return {
                'success': False,
                'message': f"Não foi encontrada nenhuma empresa cadastrada com o CNPJ {cnpj_emitente} (emitente) nem com o CNPJ {cnpj_destinatario} (destinatário). Por favor, cadastre a empresa antes de importar o XML."
            }

        resultados_empresas = []

        for emp in empresas_encontradas:
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                tem_permissao = True
            elif usuario.tipo_usuario == 'escritorio' and emp.escritorio_id == usuario.escritorio_id:
                tem_permissao = True
            elif usuario.empresas_permitidas and emp.id in usuario.empresas_permitidas:
                tem_permissao = True
            else:
                tem_permissao = False

            if not tem_permissao:
                resultados_empresas.append({
                    'empresa_id': emp.id,
                    'empresa_nome': emp.razao_social,
                    'success': False,
                    'message': f"Você não tem permissão para importar para a empresa {emp.razao_social} (CNPJ: {emp.cnpj})"
                })
                continue

            optimized_service = OptimizedXMLImportService(
                empresa_id=emp.id,
                escritorio_id=emp.escritorio_id,
                usuario_id=usuario_id,
                app=current_app._get_current_object()
            )

            xml_files = [{
                'filename': filename,
                'content': xml_content
            }]

            resultado = optimized_service.import_xml_batch(xml_files)
            optimized_service.clear_cache()

            resultado['empresa_id'] = emp.id
            resultado['empresa_nome'] = emp.razao_social

            resultados_empresas.append(resultado)

        if len(resultados_empresas) == 1:
            return resultados_empresas[0]
        else:
            success = any(r.get('success') for r in resultados_empresas)
            return {
                'success': success,
                'message': 'XML processado para múltiplas empresas',
                'resultados': resultados_empresas
            }
    except Exception as e:
        import traceback
        print(f"💥 Erro ao processar XML individual otimizado: {str(e)}")
        print(f"Stack trace: {traceback.format_exc()}")
        return {
            'success': False,
            'message': f"Erro ao processar XML: {str(e)}"
        }


def _extract_xmls_from_zip_batch(zip_content: bytes, zip_filename: str) -> List[dict]:
    """
    Extrai arquivos XML de um ZIP para importação em lote
    """
    import zipfile
    import io
    
    xml_files = []
    
    try:
        with zipfile.ZipFile(io.BytesIO(zip_content), 'r') as zip_file:
            for file_info in zip_file.filelist:
                if file_info.filename.lower().endswith('.xml') and not file_info.is_dir():
                    try:
                        xml_content = zip_file.read(file_info.filename).decode('utf-8')
                        xml_files.append({
                            'filename': file_info.filename,
                            'content': xml_content,
                            'source_zip': zip_filename
                        })
                    except Exception as e:
                        print(f"[BATCH] Erro ao extrair {file_info.filename} do ZIP {zip_filename}: {str(e)}")
                        
    except Exception as e:
        raise ValueError(f"Erro ao processar arquivo ZIP {zip_filename}: {str(e)}")
        
    return xml_files


def _process_zip_optimized(
    zip_content: bytes,
    filename: str,
    usuario,
    usuario_id: int,
    websocket_service=None,
    import_id: str | None = None,
):
    """
    Processa um arquivo ZIP com detecção automática de empresas por XML
    """
    import zipfile
    import io
    from collections import defaultdict

    resultado_final = {
        'success': True,
        'message': 'Importação ZIP otimizada concluída',
        'successful_imports': 0,
        'failed_imports': 0,
        'errors': [],
        'empresas_processadas': [],
        'performance': {}
    }

    try:
        print(f"🗜️ Processando arquivo ZIP: {filename}")

        from models.importacao_async import ImportacaoAsync
        from models import db

        # Verificar se o usuário tem um escritório_id válido
        if not hasattr(usuario, 'escritorio_id') or usuario.escritorio_id is None:
            resultado_final.update({
                'success': False,
                'message': 'Usuário não está associado a nenhum escritório. Por favor, verifique suas permissões.'
            })
            return resultado_final

        # Criar registro de importação antes de extrair para permitir reconexão
        importacao_async = None
        if import_id:
            importacao_async = ImportacaoAsync.query.get(import_id)
            if not importacao_async:
                importacao_async = ImportacaoAsync(
                    id=import_id,
                    usuario_id=usuario_id,
                    escritorio_id=usuario.escritorio_id,
                    tipo='xml_zip',
                    status='iniciando',
                    total_arquivos=0,
                    mensagem_atual='Preparando arquivo ZIP',
                )
                db.session.add(importacao_async)
            else:
                importacao_async.status = 'iniciando'
                importacao_async.escritorio_id = usuario.escritorio_id  # Garantir que o escritório está atualizado
                importacao_async.mensagem_atual = 'Preparando arquivo ZIP'
            db.session.commit()

        # Extrair XMLs do ZIP
        xml_files = []
        with zipfile.ZipFile(io.BytesIO(zip_content), 'r') as zip_file:
            for file_info in zip_file.filelist:
                if file_info.filename.lower().endswith('.xml') and not file_info.is_dir():
                    try:
                        xml_content = zip_file.read(file_info.filename).decode('utf-8')
                        xml_files.append({
                            'filename': file_info.filename,
                            'content': xml_content,
                            'source_zip': filename
                        })
                    except Exception as e:
                        print(f"⚠️ Erro ao extrair {file_info.filename}: {str(e)}")

        if not xml_files:
            resultado_final.update({
                'success': False,
                'message': 'Nenhum arquivo XML válido encontrado no ZIP'
            })
            if importacao_async:
                importacao_async.marcar_erro('Nenhum XML válido encontrado')
                db.session.commit()
            if websocket_service and import_id:
                websocket_service.send_import_complete(import_id, {'results': resultado_final})
            return resultado_final

        print(f"📦 Extraídos {len(xml_files)} XMLs do ZIP")

        total_xmls = len(xml_files)

        if importacao_async:
            importacao_async.status = 'processando'
            importacao_async.total_arquivos = total_xmls
            importacao_async.mensagem_atual = 'Iniciando processamento'
            db.session.commit()

        if websocket_service and import_id:
            websocket_service.send_import_start(import_id, {
                'total_files': total_xmls,
                'filename': filename,
                'status': 'iniciando',
            })

        # Agrupar XMLs por empresa (detectar empresa de cada XML)
        xmls_por_empresa = defaultdict(list)
        xmls_sem_empresa = []

        for xml_file in xml_files:
            try:
                # Detectar empresa do XML
                processor = XMLProcessor(xml_file['content'])
                emitente = processor.get_emitente()
                destinatario = processor.get_destinatario()
                cnpj_emitente = emitente.get('cnpj')
                cnpj_destinatario = destinatario.get('cnpj')

                empresas_xml = []
                # Buscar empresas considerando o contexto do usuário
                empresa_emitente = None
                empresa_destinatario = None
                
                if cnpj_emitente:
                    empresa_emitente = buscar_empresa_por_cnpj(cnpj_emitente, usuario)
                
                if cnpj_destinatario:
                    empresa_destinatario = buscar_empresa_por_cnpj(cnpj_destinatario, usuario)

                if empresa_emitente:
                    empresas_xml.append(empresa_emitente)
                if empresa_destinatario and (not empresa_emitente or empresa_destinatario.id != empresa_emitente.id):
                    empresas_xml.append(empresa_destinatario)

                if not empresas_xml:
                    xmls_sem_empresa.append({
                        'filename': xml_file['filename'],
                        'error': f'Empresa não encontrada (Emitente: {cnpj_emitente}, Destinatário: {cnpj_destinatario})'
                    })
                    continue

                for empresa in empresas_xml:
                    tem_permissao = False
                    if usuario.is_admin or usuario.tipo_usuario == 'admin':
                        tem_permissao = True
                    elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
                        tem_permissao = True
                    elif usuario.empresas_permitidas and empresa.id in usuario.empresas_permitidas:
                        tem_permissao = True

                    if tem_permissao:
                        xmls_por_empresa[empresa.id].append(xml_file)
                        print(f"✅ XML {xml_file['filename']} → Empresa {empresa.razao_social} (ID: {empresa.id})")
                    else:
                        xmls_sem_empresa.append({
                            'filename': xml_file['filename'],
                            'error': f'Sem permissão para empresa {empresa.razao_social} (CNPJ: {empresa.cnpj})'
                        })

            except Exception as e:
                xmls_sem_empresa.append({
                    'filename': xml_file['filename'],
                    'error': f'Erro ao processar XML: {str(e)}'
                })

         # Reportar progresso inicial para XMLs sem empresa
        processed_count = 0
        success_count = 0
        error_count = len(xmls_sem_empresa)
        if websocket_service and import_id and xmls_sem_empresa:
            for invalid in xmls_sem_empresa:
                processed_count += 1
                websocket_service.send_progress_update(
                    import_id,
                    {
                        'processed': processed_count,
                        'total': total_xmls,
                        'current_file': invalid.get('filename'),
                        'success_count': success_count,
                        'error_count': processed_count,
                    },
                )
                if importacao_async:
                    importacao_async.atualizar_progresso(
                        arquivos_processados=processed_count,
                        arquivos_sucesso=success_count,
                        arquivos_erro=processed_count,
                        mensagem=f"Arquivo invalido: {invalid.get('filename')}",
                    )
                    db.session.commit()

        # Processar XMLs agrupados por empresa
        resultado_final['failed_imports'] = len(xmls_sem_empresa)
        resultado_final['errors'].extend(xmls_sem_empresa)

        start_time = time.time()
        processed_count = len(xmls_sem_empresa)
        success_count = 0
        error_count = len(xmls_sem_empresa)

        for empresa_id, xmls_empresa in xmls_por_empresa.items():
            try:
                empresa = Empresa.query.get(empresa_id)
                print(f"🏢 Processando {len(xmls_empresa)} XMLs para empresa {empresa.razao_social}")

                # Criar serviço otimizado para esta empresa
                optimized_service = OptimizedXMLImportService(
                    empresa_id=empresa.id,
                    escritorio_id=empresa.escritorio_id,
                    usuario_id=usuario_id,
                    app=current_app._get_current_object()
                )

                def progress_cb(data):
                    nonlocal processed_count, success_count, error_count
                    processed_count = data['processed']
                    success_count = data['success_count']
                    error_count = data['error_count']
                    if websocket_service and import_id:
                        websocket_service.send_progress_update(import_id, data)
                    if importacao_async:
                        importacao_async.atualizar_progresso(
                            arquivos_processados=processed_count,
                            arquivos_sucesso=success_count,
                            arquivos_erro=error_count,
                            mensagem=f"Processando: {data.get('current_file')}",
                        )
                        db.session.commit()

                # Processar XMLs da empresa
                resultado_empresa = optimized_service.import_xml_batch(
                    xmls_empresa,
                    progress_callback=progress_cb,
                    total_files=total_xmls,
                    start_processed=processed_count,
                    start_success=success_count,
                    start_failed=error_count,
                )

                # Limpar cache
                optimized_service.clear_cache()

                # Consolidar resultados
                resultado_final['successful_imports'] += resultado_empresa.get('successful_imports', 0)
                resultado_final['failed_imports'] += resultado_empresa.get('failed_imports', 0)
                resultado_final['errors'].extend(resultado_empresa.get('errors', []))

                resultado_final['empresas_processadas'].append({
                    'empresa_id': empresa.id,
                    'empresa_nome': empresa.razao_social,
                    'xmls_processados': len(xmls_empresa),
                    'sucessos': resultado_empresa.get('successful_imports', 0),
                    'falhas': resultado_empresa.get('failed_imports', 0)
                })

                print(f"✅ Empresa {empresa.razao_social}: {resultado_empresa.get('successful_imports', 0)} sucessos, {resultado_empresa.get('failed_imports', 0)} falhas")

            except Exception as e:
                print(f"💥 Erro ao processar empresa {empresa_id}: {str(e)}")
                resultado_final['failed_imports'] += len(xmls_empresa)
                for xml_file in xmls_empresa:
                    resultado_final['errors'].append({
                        'filename': xml_file['filename'],
                        'error': f'Erro no processamento da empresa: {str(e)}'
                    })

        # Calcular métricas de performance
        end_time = time.time()
        total_time = end_time - start_time
        total_xmls = len(xml_files)

        resultado_final['performance'] = {
            'total_time_seconds': round(total_time, 2),
            'total_xmls': total_xmls,
            'xmls_per_second': round(total_xmls / total_time, 2) if total_time > 0 else 0,
            'success_rate': round(resultado_final['successful_imports'] / total_xmls * 100, 2) if total_xmls > 0 else 0,
            'empresas_detectadas': len(xmls_por_empresa)
        }

        resultado_final['success'] = resultado_final['successful_imports'] > 0
        print(f"🎉 ZIP processado: {resultado_final['successful_imports']} sucessos, {resultado_final['failed_imports']} falhas em {total_time:.2f}s")

        if websocket_service and import_id:
            websocket_service.send_import_complete(import_id, {
                'results': resultado_final,
            })

        if importacao_async:
            importacao_async.marcar_concluido(resultado_final)
            db.session.commit()

        return resultado_final

    except Exception as e:
        import traceback
        print(f"💥 Erro ao processar ZIP otimizado: {str(e)}")
        print(f"Stack trace: {traceback.format_exc()}")
        resultado_final['message'] = f"Erro ao processar arquivo ZIP: {str(e)}"
        resultado_final['errors'].append({'error': str(e)})
        resultado_final['success'] = resultado_final['successful_imports'] > 0

        if websocket_service and import_id:
            websocket_service.send_import_complete(import_id, {
                'results': resultado_final,
            })

        if importacao_async:
            if resultado_final['success']:
                importacao_async.marcar_concluido(resultado_final)
            else:
                importacao_async.marcar_erro(str(e))
            db.session.commit()

        return resultado_final

@importacao_bp.route('/api/service-upload', methods=['POST'])
def import_xml_service():
    """Endpoint para serviços externos enviarem XML automaticamente."""
    api_key = request.headers.get('X-API-KEY') or request.form.get('api_key')
    expected_key = os.getenv('SERVICE_UPLOAD_KEY')
    if expected_key and api_key != expected_key:
        return jsonify({"message": "Chave de API inválida"}), 401

    escritorio_id = request.form.get('escritorio_id', type=int)
    if not escritorio_id:
        return jsonify({"message": "escritorio_id é obrigatório"}), 400

    if 'arquivo' not in request.files:
        return jsonify({"message": "Nenhum arquivo enviado"}), 400
    arquivo = request.files['arquivo']
    if arquivo.filename == '' or not allowed_file(arquivo.filename):
        return jsonify({"message": "Arquivo inválido"}), 400

    try:
        xml_content = arquivo.read().decode('utf-8')
        processor = XMLProcessor(xml_content)
        emitente = processor.get_emitente()
        destinatario = processor.get_destinatario()
        cnpj_emitente = emitente.get('cnpj') or emitente.get('cpf')
        cnpj_destinatario = destinatario.get('cnpj') or destinatario.get('cpf')
        empresa = None
        if cnpj_emitente:
            empresa = buscar_empresa_por_cnpj(cnpj_emitente, usuario)
        if not empresa and cnpj_destinatario:
            empresa = buscar_empresa_por_cnpj(cnpj_destinatario, usuario)
        if not empresa:
            return jsonify({"message": "Empresa não cadastrada"}), 400
    except Exception as e:
        return jsonify({"message": f"Erro ao processar XML: {str(e)}"}), 400

    service = XMLImportService(empresa.id, escritorio_id, usuario_id=0)
    result = service.import_xml(xml_content, arquivo.filename)
    status = 201 if result.get('success') else 400
    return jsonify(result), status
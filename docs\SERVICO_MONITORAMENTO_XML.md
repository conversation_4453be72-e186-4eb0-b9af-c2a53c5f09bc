
# Documentação: Serviço de Monitoramento e Envio Automático de XML

Este documento detalha a arquitetura e o funcionamento do serviço de monitoramento automático de arquivos XML, projetado para simplificar e automatizar o processo de importação de notas fiscais.

## Visão Geral

O sistema é composto por dois componentes principais:

1.  **Script de Monitoramento (`xml_service_example.py`):** Um serviço leve, escrito em Python, que é executado na máquina do cliente. Sua única função é monitorar uma pasta específica e enviar qualquer novo arquivo XML para o backend.
2.  **Endpoint de API (`/api/service-upload`):** Uma rota segura no backend da aplicação principal, responsável por receber, autenticar e processar os arquivos XML enviados pelo script de monitoramento.

---

## Como o Sistema Sabe Quais Arquivos Já Foram Importados?

Esta é a questão central. A resposta curta é: **o script de monitoramento não sabe**.

A inteligência para evitar duplicatas reside inteiramente no **backend**, o que torna o script do cliente extremamente simples e confiável.

### O Papel do Script de Monitoramento (Cliente)

-   **Stateless (Sem Estado):** O script `xml_service_example.py` não mantém nenhum registro ou banco de dados dos arquivos que já enviou. Ele simplesmente reage ao evento de "criação de arquivo" na pasta que está monitorando.
-   **"Atire e Esqueça":** Ao detectar um novo arquivo `.xml`, ele o envia para a API e imprime o resultado (sucesso ou falha) no console. Ele não tenta reenviar em caso de falha nem verifica o conteúdo do arquivo.

### O Papel do Backend (Aplicação Principal)

-   **Stateful (Com Estado):** O backend é quem mantém o estado de todas as importações no banco de dados.
-   **Verificação de Duplicidade por Chave Única:** Quando o endpoint `/api/service-upload` recebe um arquivo, o `XMLImportService` executa os seguintes passos:
    1.  Extrai a **Chave de Acesso da NF-e** (`chave_nf`) do conteúdo do arquivo XML. Esta chave é um identificador único e universal para cada nota fiscal no Brasil.
    2.  Antes de inserir qualquer dado, ele consulta a tabela `importacao_xml` no banco de dados para verificar se já existe um registro com a mesma `chave_nf`.
    3.  **Se a chave já existe**, o backend simplesmente descarta a nova importação, possivelmente registrando um log de que uma duplicata foi recebida. Ele retorna uma mensagem de sucesso ou de "duplicata ignorada" para o script do cliente.
    4.  **Se a chave não existe**, o backend processa e salva a nova nota fiscal no banco de dados.

Essa abordagem garante que, mesmo que o serviço do cliente seja reiniciado ou envie o mesmo arquivo várias vezes por engano, **nenhuma nota fiscal será duplicada** no sistema principal.

---

## Fluxo de Execução Completo

1.  **Criação do Arquivo:** Um sistema ERP no servidor do cliente salva um novo arquivo (ex: `nota_123.xml`) na pasta monitorada (ex: `C:	emp_xmls`).
2.  **Detecção:** O script `xml_service_example.py`, que está rodando em segundo plano, detecta a criação do novo arquivo através da biblioteca `watchdog`.
3.  **Envio:** O script imediatamente abre o arquivo e envia seu conteúdo via uma requisição `POST` para o endpoint `/api/service-upload`, juntamente com a `API_KEY` e o `ESCRITORIO_ID` configurados.
4.  **Autenticação:** O backend valida a `API_KEY` para garantir que a requisição é legítima.
5.  **Processamento e Verificação:** O `XMLImportService` no backend extrai a `chave_nf` e verifica sua existência no banco de dados.
6.  **Persistência:** Se a nota for nova, ela é salva na tabela `importacao_xml`, associada ao usuário "Sistema" (ID 0).
7.  **Resposta:** O backend retorna uma resposta JSON (ex: `{"message": "XML importado com sucesso"}`), que é então exibida no console onde o script de monitoramento está sendo executado.


import api from './authService'

export interface ImportacaoXML {
  id: number
  arquivo: string
  nota_fiscal: string
  data_emissao: string
  data_importacao: string
  status: 'sucesso' | 'erro' | 'processando'
  empresa_id?: number
  usuario_id: number
}

export interface ImportacaoSPED {
  id: number
  arquivo: string
  empresa_id: number
  data_importacao: string
  status: 'sucesso' | 'erro' | 'processando'
  total_registros: number
  registros_processados: number
}

export interface ImportacaoAtiva {
  id: string
  tipo: 'xml_batch' | 'xml_zip' | 'sped'
  status: 'processando' | 'concluido' | 'erro'
  arquivos_processados: number
  total_arquivos: number
  data_inicio: string
  usuario_id: number
  total_files: number
}

export interface ImportacaoResponse {
  total_files: number
  success: boolean
  message: string
  import_id?: string
  importacoes?: ImportacaoXML[] | ImportacaoSPED[]
  total?: number
  totais?: {
    notas: number
    itens: number
    clientes: number
    produtos: number
  }
}

export const importacaoService = {
  /**
   * Lista importações XML
   */
  async getImportacoesXML(): Promise<ImportacaoXML[]> {
    const response = await api.get('/importacoes')
    return response.data.importacoes || []
  },

  /**
   * Lista importações SPED
   */
  async getImportacoesSPED(): Promise<ImportacaoSPED[]> {
    const response = await api.get('/importacoes/sped/historico')
    return response.data.importacoes || []
  },

  /**
   * Lista importações ativas
   */
  async getImportacoesAtivas(): Promise<ImportacaoAtiva[]> {
    const response = await api.get('/importacoes/ativas')
    return response.data.importacoes || []
  },

  /**
   * Importa arquivo XML individual
   */
  async importarXML(file: File): Promise<ImportacaoResponse> {
    const formData = new FormData()
    formData.append('arquivo', file) // API Flask espera 'arquivo'

    const response = await api.post('/importacoes', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  /**
   * Importa arquivos XML em lote
   */
  async importarXMLBatch(files: FileList, importId: string): Promise<ImportacaoResponse> {
    const formData = new FormData()

    Array.from(files).forEach((file) => {
      formData.append('arquivos', file) // API Flask espera 'arquivos'
    })
    formData.append('import_id', importId)

    const response = await api.post('/importacoes/batch', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  /**
   * Importa arquivos XML otimizado
   */
  async importarXMLOtimizado(files: FileList, importId: string): Promise<ImportacaoResponse> {
    const formData = new FormData()

    // A API otimizada espera apenas um arquivo por vez
    if (files.length > 0) {
      formData.append('arquivo', files[0]) // API Flask espera 'arquivo'
      formData.append('import_id', importId)
    }

    const response = await api.post('/importacoes/optimized', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  /**
   * Importa arquivo SPED
   */
  async importarSPED(file: File, importId?: string): Promise<ImportacaoResponse> {
    const formData = new FormData()
    formData.append('arquivo', file) // API Flask espera 'arquivo'
    if (importId) {
      formData.append('import_id', importId)
    }

    const response = await api.post('/importacoes/sped', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  /**
   * Verifica status de uma importação
   */
  async getStatusImportacao(importId: string) {
    const response = await api.get(`/importacoes/status/${importId}`)
    return response.data
  },

  /**
   * Obtém detalhes de uma importação específica
   */
  async getImportacao(importacaoId: number) {
    const response = await api.get(`/importacoes/${importacaoId}`)
    return response.data
  },

  /**
   * Lista histórico de importações com paginação
   */
  async getHistoricoImportacoes(params?: {
    tipo?: 'xml' | 'sped'
    pagina?: number
    limite?: number
  }) {
    const searchParams = new URLSearchParams()
    if (params?.tipo) searchParams.append('tipo', params.tipo)
    if (params?.pagina) searchParams.append('pagina', params.pagina.toString())
    if (params?.limite) searchParams.append('limite', params.limite.toString())

    const response = await api.get(`/importacoes/historico?${searchParams}`)
    return response.data
  },
}
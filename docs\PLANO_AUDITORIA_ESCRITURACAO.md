# Plano de Implementação - Auditoria de Escrituração

## 🎯 Objetivo
Implementar a funcionalidade de auditoria de escrituração das notas de entrada, comparando valores totais entre XML e SPED, com interface para aprovação e justificativas.

## 🔍 Problemas Identificados
1. **Exclusão de notas faltantes não funciona** - Mensagem "INFO: Funcionalidade em desenvolvimento"
2. **Auditoria de escrituração incompleta** - Falta interface e funcionalidades específicas
3. **Falta paginação** na tabela de auditoria
4. **Falta filtros nas colunas** da tabela
5. **Falta modal de detalhes** comparativo XML vs SPED

## 📋 Etapas de Implementação

### Etapa 1: Corrigir Exclusão de Notas Faltantes
- **Arquivo**: `back/routes/auditoria_entrada_routes.py`
- **Ação**: Implementar rota DELETE para notas faltantes
- **Funcionalidade**: Excluir XML/SPED do banco e diretório

### Etapa 2: Melhorar Serviço de Auditoria de Escrituração
- **Arquivo**: `back/services/auditoria_entrada_service.py`
- **Ações**:
  - Adicionar método específico para auditoria de escrituração
  - Comparar valores totais XML vs SPED
  - Calcular status de conformidade (conforme/divergente)
  - Implementar paginação

### Etapa 3: Criar Nova Rota para Auditoria de Escrituração
- **Arquivo**: `back/routes/auditoria_entrada_routes.py`
- **Rotas**:
  - `GET /api/auditoria-entrada/escrituracao` - Listar com paginação e filtros
  - `POST /api/auditoria-entrada/aprovar-escrituracao` - Aprovar com justificativa
  - `GET /api/auditoria-entrada/detalhes-comparativo/{id}` - Detalhes XML vs SPED

### Etapa 4: Atualizar Modelo de Banco de Dados
- **Arquivo**: `db/migration_auditoria_escrituracao.sql`
- **Alterações**:
  - Adicionar campo `status_escrituracao` na tabela `auditoria_entrada`
  - Adicionar campo `justificativa_escrituracao`
  - Adicionar campo `data_aprovacao_escrituracao`
  - Adicionar campo `usuario_aprovacao_escrituracao`

### Etapa 5: Implementar Frontend - Tabela de Escrituração
- **Arquivo**: `front/static/js/auditoria_entrada.js`
- **Funcionalidades**:
  - Tabela com paginação (DataTables)
  - Filtros nas colunas
  - Comparação visual de valores (verde/vermelho)
  - Seleção múltipla para aprovação em massa
  - Modal de detalhes comparativo

### Etapa 6: Implementar Modal de Detalhes Comparativo
- **Arquivo**: `front/static/js/auditoria_entrada.js`
- **Conteúdo**:
  - Dados do XML (emitente, produtos, totais)
  - Dados do SPED (produtos, totais)
  - Comparação lado a lado
  - Campo para justificativa
  - Botão de aprovação individual

### Etapa 7: Implementar Sistema de Aprovação
- **Arquivo**: `front/static/js/auditoria_entrada.js`
- **Funcionalidades**:
  - Aprovação individual com justificativa
  - Aprovação em massa
  - Modal de justificativa para divergências
  - Atualização de status em tempo real

## 🗂️ Arquivos que Serão Modificados

### Backend
1. `back/routes/auditoria_entrada_routes.py` - Novas rotas e correção de exclusão
2. `back/services/auditoria_entrada_service.py` - Métodos de escrituração
3. `back/models/auditoria_entrada.py` - Novos campos se necessário

### Frontend
1. `front/static/js/auditoria_entrada.js` - Interface de escrituração
2. `front/static/js/notas_faltantes.js` - Correção de exclusão

### Banco de Dados
1. `db/migration_auditoria_escrituracao.sql` - Novos campos

## 🎨 Interface Proposta

### Tabela de Escrituração
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ☑ | Nº NF | Emitente | Data Entrada | Valor XML | Valor SPED | Status | Ações │
├─────────────────────────────────────────────────────────────────────────────┤
│ ☑ | 12345 | Empresa A| 15/01/2024  | R$ 1.000  | R$ 1.000   | ✅     | 👁 ✅  │
│ ☑ | 12346 | Empresa B| 15/01/2024  | R$ 2.000  | R$ 1.950   | ❌     | 👁 ✅  │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Modal de Detalhes
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                    Detalhes Comparativos - NF 12346                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ XML                          │ SPED                                         │
│ Emitente: Empresa B          │ Emitente: Empresa B                         │
│ Data: 15/01/2024            │ Data: 15/01/2024                            │
│ Valor Total: R$ 2.000,00    │ Valor Total: R$ 1.950,00                   │
│                             │                                              │
│ Produtos:                   │ Produtos:                                    │
│ - Produto A: R$ 1.000       │ - Produto A: R$ 950                         │
│ - Produto B: R$ 1.000       │ - Produto B: R$ 1.000                       │
├─────────────────────────────────────────────────────────────────────────────┤
│ Justificativa: [___________________________________________]                │
│                                                    [Aprovar] [Cancelar]     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 🔄 Fluxo de Trabalho
1. Usuário acessa aba "Escrituração"
2. Sistema carrega dados com paginação
3. Usuário visualiza comparação XML vs SPED
4. Para divergências, usuário clica em "Detalhes"
5. Modal mostra comparação detalhada
6. Usuário adiciona justificativa (se necessário)
7. Usuário aprova individualmente ou em massa
8. Sistema atualiza status e registra aprovação

## ⚡ Melhorias de Performance
- Paginação server-side
- Filtros otimizados
- Cache de consultas frequentes
- Índices no banco de dados

## 🧪 Testes Necessários
- Teste de comparação de valores
- Teste de aprovação com justificativa
- Teste de paginação e filtros
- Teste de exclusão de notas faltantes
- Teste de modal de detalhes

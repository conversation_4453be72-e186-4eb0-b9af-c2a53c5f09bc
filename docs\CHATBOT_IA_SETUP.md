# 🤖 Chatbot IA - Sistema de Auditoria Fiscal

## 📋 Visão Geral

O Chatbot IA é uma funcionalidade avançada que permite aos usuários fazer perguntas em linguagem natural sobre dados de auditoria fiscal. Utilizando a API da OpenAI (GPT-4o-mini), o sistema pode responder perguntas como:

- "Qual o valor do ICMS da nota 1234 da empresa ABC?"
- "Quantas inconsistências temos este mês?"
- "Mostre os produtos com mais problemas de auditoria"
- "Qual a situação da auditoria da empresa X?"

## 🚀 Instalação e Configuração

### 1. Instalar Dependências

```bash
pip install openai==1.58.1
```

### 2. Configurar Variáveis de Ambiente

Adicione as seguintes variáveis ao seu arquivo `.env`:

```env
# Configurações OpenAI para Chatbot IA
OPENAI_API_KEY=sua_chave_openai_aqui
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=1500
OPENAI_TEMPERATURE=0.3
```

**Como obter a chave da OpenAI:**
1. Acesse https://platform.openai.com/
2. Faça login ou crie uma conta
3. Vá em "API Keys" no menu lateral
4. Clique em "Create new secret key"
5. Copie a chave e cole no arquivo `.env`

### 3. Executar Script SQL

Execute o arquivo `db/chatbot_ia_setup.sql` no seu PostgreSQL:

```bash
psql -U postgres -d auditoria_fiscal -f db/chatbot_ia_setup.sql
```

Ou pelo pgAdmin 4:
1. Abra o pgAdmin 4
2. Conecte ao seu banco `auditoria_fiscal`
3. Clique com botão direito no banco > Query Tool
4. Abra o arquivo `db/chatbot_ia_setup.sql`
5. Execute o script (F5)

### 4. Reiniciar o Servidor

```bash
python back/app.py
```

## 🎯 Funcionalidades

### Tipos de Perguntas Suportadas

#### 📄 Notas Fiscais
- "Mostre dados da nota 1234"
- "Qual o valor total da nota X da empresa Y?"
- "Quantas notas temos da empresa ABC?"

#### 🔍 Auditoria
- "Qual o status da auditoria da empresa X?"
- "Quantas inconsistências encontramos?"
- "Mostre problemas de ICMS"

#### 📊 Tributos
- "Qual o valor do IPI da nota 1234?"
- "Mostre alíquotas de PIS/COFINS"
- "Há problemas com ICMS-ST?"

#### 🏢 Empresas e Clientes
- "Dados da empresa ABC"
- "Clientes com mais problemas"
- "Atividade do cliente X"

#### 📈 Relatórios e Estatísticas
- "Resumo do mês atual"
- "Produtos mais problemáticos"
- "Cenários inconsistentes"

### Interface do Usuário

#### Botão Flutuante
- Localizado no canto inferior direito
- Ícone de robô azul
- Badge com número de conversas (se houver histórico)

#### Janela do Chat
- Design moderno e responsivo
- Suporte a modo escuro
- Histórico de conversas
- Sugestões de perguntas

#### Ações Disponíveis
- 👍/👎 Avaliar respostas
- 📋 Copiar respostas
- 🗑️ Limpar conversa
- ➖ Minimizar chat

## 🛠️ Arquitetura Técnica

### Backend

#### Serviços
- `ChatbotIAService`: Processamento principal da IA
- `OpenAI Integration`: Comunicação com GPT-4o-mini
- `SQL Query Generator`: Geração inteligente de consultas

#### Modelos de Dados
- `ChatbotConversas`: Histórico de conversas
- `ChatbotTemplates`: Templates de perguntas
- `vw_chatbot_dados_completos`: View otimizada para consultas

#### APIs
- `POST /api/chatbot/pergunta`: Processar pergunta
- `GET /api/chatbot/historico`: Obter histórico
- `POST /api/chatbot/avaliar`: Avaliar resposta
- `DELETE /api/chatbot/limpar-historico`: Limpar histórico

### Frontend

#### Componentes
- `ChatbotIA`: Classe principal do chat
- Interface responsiva e acessível
- Integração com sistema de autenticação
- Suporte a temas claro/escuro

#### Funcionalidades
- Envio de mensagens em tempo real
- Formatação automática de respostas
- Sistema de sugestões inteligentes
- Feedback visual para ações

## 📊 Monitoramento e Analytics

### Para Administradores

Acesse estatísticas em `/api/chatbot/estatisticas`:
- Total de conversas
- Conversas por dia
- Avaliação média das respostas
- Tempo médio de resposta
- Categorias mais consultadas

### Logs e Debugging

- Todas as conversas são salvas no banco
- Queries SQL são registradas (modo desenvolvimento)
- Tempo de resposta é monitorado
- Erros são logados automaticamente

## 🔧 Personalização

### Adicionando Novos Templates

```sql
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao) 
VALUES (
    'custom',
    'minha pergunta {parametro}',
    'SELECT * FROM tabela WHERE campo = ''{parametro}''',
    'Descrição do template'
);
```

### Configurando Respostas

Ajuste os parâmetros no `.env`:
- `OPENAI_MAX_TOKENS`: Tamanho máximo da resposta
- `OPENAI_TEMPERATURE`: Criatividade (0.0-1.0)
- `OPENAI_MODEL`: Modelo da OpenAI a usar

## 🚨 Troubleshooting

### Problemas Comuns

#### Erro: "OpenAI API Key não configurada"
- Verifique se `OPENAI_API_KEY` está no `.env`
- Confirme se a chave é válida
- Reinicie o servidor

#### Erro: "Tabelas do chatbot não encontradas"
- Execute o script `db/chatbot_ia_setup.sql`
- Verifique conexão com PostgreSQL
- Confirme permissões do usuário

#### Chatbot não aparece na interface
- Verifique se os arquivos CSS/JS foram carregados
- Confirme se o usuário está autenticado
- Verifique console do navegador para erros

#### Respostas lentas ou timeout
- Verifique conexão com internet
- Considere aumentar `OPENAI_MAX_TOKENS`
- Otimize consultas SQL complexas

### Logs Úteis

```bash
# Logs do Flask
tail -f logs/app.log

# Logs do PostgreSQL
tail -f /var/log/postgresql/postgresql.log

# Logs do navegador
F12 > Console
```

## 📈 Melhorias Futuras

### Funcionalidades Planejadas
- [ ] Integração com relatórios PDF
- [ ] Comandos de voz
- [ ] Sugestões automáticas baseadas em contexto
- [ ] Integração com WhatsApp/Telegram
- [ ] Dashboard de analytics avançado

### Otimizações
- [ ] Cache de consultas frequentes
- [ ] Compressão de respostas
- [ ] Pré-processamento de dados
- [ ] Índices adicionais no banco

## 🤝 Suporte

Para dúvidas ou problemas:
1. Verifique este documento
2. Consulte os logs de erro
3. Teste com perguntas simples primeiro
4. Verifique configurações da OpenAI

## 📝 Changelog

### v1.0.0 (2024-12-19)
- ✅ Implementação inicial do chatbot
- ✅ Integração com OpenAI GPT-4o-mini
- ✅ Interface responsiva e moderna
- ✅ Sistema de templates de perguntas
- ✅ Histórico de conversas
- ✅ Sistema de avaliação de respostas
- ✅ Suporte a modo escuro
- ✅ View materializada para performance
- ✅ APIs RESTful completas

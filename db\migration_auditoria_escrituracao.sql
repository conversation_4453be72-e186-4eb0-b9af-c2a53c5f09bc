-- Migration: Auditoria de Escrituração
-- Adiciona campos específicos para auditoria de escrituração das notas de entrada

BEGIN;

-- 1. Adicionar campos para auditoria de escrituração na tabela auditoria_entrada
ALTER TABLE auditoria_entrada ADD COLUMN IF NOT EXISTS status_escrituracao VARCHAR(20) DEFAULT 'pendente';
ALTER TABLE auditoria_entrada ADD COLUMN IF NOT EXISTS justificativa_escrituracao TEXT;
ALTER TABLE auditoria_entrada ADD COLUMN IF NOT EXISTS data_aprovacao_escrituracao TIMESTAMP;
ALTER TABLE auditoria_entrada ADD COLUMN IF NOT EXISTS usuario_aprovacao_escrituracao INTEGER REFERENCES usuario(id);

-- 2. Adicionar campos para comparação de valores totais
ALTER TABLE auditoria_entrada ADD COLUMN IF NOT EXISTS xml_valor_total_nota DECIMAL(15,2);
ALTER TABLE auditoria_entrada ADD COLUMN IF NOT EXISTS sped_valor_total_nota DECIMAL(15,2);
ALTER TABLE auditoria_entrada ADD COLUMN IF NOT EXISTS divergencia_valor_total DECIMAL(15,2);
ALTER TABLE auditoria_entrada ADD COLUMN IF NOT EXISTS percentual_divergencia DECIMAL(5,2);

-- 3. Adicionar índices para melhorar performance
CREATE INDEX IF NOT EXISTS idx_auditoria_entrada_status_escrituracao 
ON auditoria_entrada(empresa_id, status_escrituracao);

CREATE INDEX IF NOT EXISTS idx_auditoria_entrada_data_aprovacao 
ON auditoria_entrada(data_aprovacao_escrituracao);

CREATE INDEX IF NOT EXISTS idx_auditoria_entrada_usuario_aprovacao 
ON auditoria_entrada(usuario_aprovacao_escrituracao);

-- 4. Adicionar comentários
COMMENT ON COLUMN auditoria_entrada.status_escrituracao IS 'Status da auditoria de escrituração: pendente, aprovado, rejeitado';
COMMENT ON COLUMN auditoria_entrada.justificativa_escrituracao IS 'Justificativa para aprovação de divergências na escrituração';
COMMENT ON COLUMN auditoria_entrada.data_aprovacao_escrituracao IS 'Data e hora da aprovação da escrituração';
COMMENT ON COLUMN auditoria_entrada.usuario_aprovacao_escrituracao IS 'Usuário que aprovou a escrituração';
COMMENT ON COLUMN auditoria_entrada.xml_valor_total_nota IS 'Valor total da nota no XML';
COMMENT ON COLUMN auditoria_entrada.sped_valor_total_nota IS 'Valor total da nota no SPED';
COMMENT ON COLUMN auditoria_entrada.divergencia_valor_total IS 'Diferença entre valor XML e SPED';
COMMENT ON COLUMN auditoria_entrada.percentual_divergencia IS 'Percentual de divergência entre XML e SPED';

-- 5. Criar função para calcular divergência automaticamente
CREATE OR REPLACE FUNCTION calcular_divergencia_escrituracao()
RETURNS TRIGGER AS $$
BEGIN
    -- Calcular divergência se ambos os valores estão preenchidos
    IF NEW.xml_valor_total_nota IS NOT NULL AND NEW.sped_valor_total_nota IS NOT NULL THEN
        NEW.divergencia_valor_total = NEW.xml_valor_total_nota - NEW.sped_valor_total_nota;
        
        -- Calcular percentual de divergência
        IF NEW.xml_valor_total_nota > 0 THEN
            NEW.percentual_divergencia = (NEW.divergencia_valor_total / NEW.xml_valor_total_nota) * 100;
        ELSE
            NEW.percentual_divergencia = 0;
        END IF;
        
        -- Definir status baseado na divergência apenas quando o status
        -- ainda está pendente (evita sobrescrever aprovacoes manuais)
        IF NEW.status_escrituracao IS NULL OR NEW.status_escrituracao = 'pendente' THEN
            IF ABS(NEW.divergencia_valor_total) <= 0.01 THEN
                NEW.status_escrituracao = 'conforme';
            ELSE
                NEW.status_escrituracao = 'divergente';
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Criar trigger para calcular divergência automaticamente
-- Verificar se o trigger já existe antes de tentar removê-lo
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_calcular_divergencia_escrituracao') THEN
        DROP TRIGGER trigger_calcular_divergencia_escrituracao ON auditoria_entrada;
    END IF;
END $$;

CREATE TRIGGER trigger_calcular_divergencia_escrituracao
    BEFORE INSERT OR UPDATE ON auditoria_entrada
    FOR EACH ROW
    EXECUTE FUNCTION calcular_divergencia_escrituracao();

-- 7. Atualizar registros existentes (calcular divergências para dados já existentes)
UPDATE auditoria_entrada 
SET 
    xml_valor_total_nota = xml_valor_total,
    sped_valor_total_nota = sped_valor_item
WHERE xml_valor_total IS NOT NULL AND sped_valor_item IS NOT NULL;

-- 8. Criar view para relatórios de escrituração
CREATE OR REPLACE VIEW vw_auditoria_escrituracao AS
SELECT
    ae.id,
    ae.empresa_id,
    e.razao_social as empresa_nome,
    ae.numero_nf,
    ae.chave_nf,
    ae.xml_data_entrada,
    ae.sped_data_entrada_saida,
    ae.xml_valor_total_nota,
    ae.sped_valor_total_nota,
    ae.divergencia_valor_total,
    ae.percentual_divergencia,
    ae.status_escrituracao,
    ae.justificativa_escrituracao,
    ae.data_aprovacao_escrituracao,
    u.nome as usuario_aprovacao_nome,
    ae.mes_referencia,
    ae.ano_referencia,
    CASE
        WHEN ABS(COALESCE(ae.divergencia_valor_total, 0)) <= 0.01 THEN 'Conforme'
        WHEN ae.status_escrituracao = 'aprovado' THEN 'Aprovado com Justificativa'
        ELSE 'Divergente'
    END as status_display
FROM auditoria_entrada ae
JOIN empresa e ON ae.empresa_id = e.id
LEFT JOIN usuario u ON ae.usuario_aprovacao_escrituracao = u.id
WHERE ae.xml_valor_total_nota IS NOT NULL
  AND ae.sped_valor_total_nota IS NOT NULL;

-- 9. Comentário na view
COMMENT ON VIEW vw_auditoria_escrituracao IS 'View para consultas de auditoria de escrituração com dados consolidados';

COMMIT;

-- Instruções de uso:
-- 1. Execute este script no banco de dados
-- 2. Os campos serão adicionados à tabela auditoria_entrada
-- 3. O trigger calculará automaticamente as divergências
-- 4. Use a view vw_auditoria_escrituracao para consultas otimizadas
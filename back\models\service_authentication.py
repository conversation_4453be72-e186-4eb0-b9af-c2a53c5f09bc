from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from .escritorio import db

class ServiceAuthentication(db.Model):
    __tablename__ = 'service_authentication'
    
    id = db.Column(db.Integer, primary_key=True)
    portal_user_id = db.Column(db.String(50), nullable=False)
    portal_office_id = db.Column(db.String(50), nullable=False, unique=True)
    office_cnpj = db.Column(db.String(20), nullable=False)
    user_name = db.Column(db.String(255), nullable=False)
    user_email = db.Column(db.String(255), nullable=False)
    user_role = db.Column(db.String(50), nullable=False)
    jwt_token = db.Column(db.Text, nullable=False)
    decoded_payload = db.Column(db.JSON, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    def __repr__(self):
        return f"<ServiceAuthentication {self.user_email} - {self.office_cnpj}>"

    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'portal_user_id': self.portal_user_id,
            'portal_office_id': self.portal_office_id,
            'office_cnpj': self.office_cnpj,
            'user_name': self.user_name,
            'user_email': self.user_email,
            'user_role': self.user_role,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active,
        }

    def is_token_expired(self):
        """Check if the JWT token is expired"""
        return datetime.utcnow() >= self.expires_at
-- Migration para corrigir a constraint da tabela matching_manual_temporario
-- Permite múltiplos matches temporários para o mesmo SPED item, mas apenas um pendente por vez

-- Remover a constraint existente
ALTER TABLE matching_manual_temporario DROP CONSTRAINT IF EXISTS matching_manual_temporario_sped_item_id_usuario_id_key;

-- Criar um índice único parcial que permite apenas um registro pendente por usuário/sped_item
CREATE UNIQUE INDEX IF NOT EXISTS matching_manual_temporario_unique_pendente_idx
    ON matching_manual_temporario (sped_item_id, usuario_id)
    WHERE status = 'pendente';

-- Comentário explicativo
COMMENT ON INDEX matching_manual_temporario_unique_pendente_idx IS
'Permite apenas um match temporário pendente por usuário/item SPED, mas permite múltiplos registros processados';

-- Log da migração
INSERT INTO log_atividade (usuario_id, acao, descricao)
VALUES (1, 'Migration', 'Corrigida constraint da tabela matching_manual_temporario para permitir múltiplos matches processados');

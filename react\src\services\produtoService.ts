import api from './authService'
import type { Produto, ProdutosResponse } from '@/types/produto'

export const produtoService = {
  async getProdutos(
    empresaId: number,
    page = 1,
    perPage = 100
  ): Promise<ProdutosResponse> {
    const params = new URLSearchParams({
      empresa_id: empresaId.toString(),
      page: page.toString(),
      per_page: perPage.toString()
    })
    const response = await api.get(`/produtos?${params.toString()}`)
    return response.data
  },

  async getProduto(id: number): Promise<Produto> {
    const response = await api.get<{ produto: Produto }>(`/produtos/${id}`)
    return response.data.produto
  },

  async createProduto(data: Partial<Produto> & { empresa_id: number }): Promise<{ message: string }> {
    const response = await api.post('/produtos', data)
    return response.data
  },

  async updateProduto(id: number, data: Partial<Produto>): Promise<{ message: string }> {
    const response = await api.put(`/produtos/${id}`, data)
    return response.data
  },

  async deleteProduto(id: number): Promise<{ message: string }> {
    const response = await api.delete(`/produtos/${id}`)
    return response.data
  }
}
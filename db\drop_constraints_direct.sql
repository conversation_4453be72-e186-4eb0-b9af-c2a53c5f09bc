-- Script para remover diretamente as restrições de unicidade que estão causando problemas

-- Remover restrição da tabela cenario_icms
ALTER TABLE cenario_icms DROP CONSTRAINT IF EXISTS cenario_icms_empresa_id_cliente_id_produto_id_cfop_status_key;
ALTER TABLE cenario_icms DROP CONSTRAINT IF EXISTS cenario_icms_empresa_id_cliente_id_produto_id_status_key;
ALTER TABLE cenario_icms DROP CONSTRAINT IF EXISTS cenario_icms_unique_except_producao;

-- Remover restrição da tabela cenario_icms_st
ALTER TABLE cenario_icms_st DROP CONSTRAINT IF EXISTS cenario_icms_st_empresa_id_cliente_id_produto_id_cfop_status_key;
ALTER TABLE cenario_icms_st DROP CONSTRAINT IF EXISTS cenario_icms_st_empresa_id_cliente_id_produto_id_status_key;
ALTER TABLE cenario_icms_st DROP CONSTRAINT IF EXISTS cenario_icms_st_unique_except_producao;

-- Remover restrição da tabela cenario_pis
ALTER TABLE cenario_pis DROP CONSTRAINT IF EXISTS cenario_pis_empresa_id_cliente_id_produto_id_cfop_status_key;
ALTER TABLE cenario_pis DROP CONSTRAINT IF EXISTS cenario_pis_empresa_id_cliente_id_produto_id_status_key;
ALTER TABLE cenario_pis DROP CONSTRAINT IF EXISTS cenario_pis_unique_except_producao;

-- Remover restrição da tabela cenario_cofins
ALTER TABLE cenario_cofins DROP CONSTRAINT IF EXISTS cenario_cofins_empresa_id_cliente_id_produto_id_cfop_status_key;
ALTER TABLE cenario_cofins DROP CONSTRAINT IF EXISTS cenario_cofins_empresa_id_cliente_id_produto_id_status_key;
ALTER TABLE cenario_cofins DROP CONSTRAINT IF EXISTS cenario_cofins_unique_except_producao;

-- Remover restrição da tabela cenario_difal
ALTER TABLE cenario_difal DROP CONSTRAINT IF EXISTS cenario_difal_empresa_id_cliente_id_produto_id_cfop_status_key;
ALTER TABLE cenario_difal DROP CONSTRAINT IF EXISTS cenario_difal_empresa_id_cliente_id_produto_id_status_key;
ALTER TABLE cenario_difal DROP CONSTRAINT IF EXISTS cenario_difal_unique_except_producao;

-- Adicionar novas restrições que permitam múltiplos cenários em produção
-- Estas restrições só se aplicam a cenários com status 'novo' ou 'inconsistente'
ALTER TABLE cenario_icms ADD CONSTRAINT cenario_icms_novo_inconsistente_unique
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status IN ('novo', 'inconsistente'));

ALTER TABLE cenario_icms_st ADD CONSTRAINT cenario_icms_st_novo_inconsistente_unique
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status IN ('novo', 'inconsistente'));

ALTER TABLE cenario_pis ADD CONSTRAINT cenario_pis_novo_inconsistente_unique
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status IN ('novo', 'inconsistente'));

ALTER TABLE cenario_cofins ADD CONSTRAINT cenario_cofins_novo_inconsistente_unique
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status IN ('novo', 'inconsistente'));

ALTER TABLE cenario_difal ADD CONSTRAINT cenario_difal_novo_inconsistente_unique
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status IN ('novo', 'inconsistente'));

-- Migração para corrigir precisão de todos os campos DECIMAL(5,4) problemáticos
-- Data: 2025-06-16
-- Descrição: Corrige campos que podem causar estouro numérico

BEGIN;

-- 1. Corrigir campos na tabela auditoria_comparativa_impostos
ALTER TABLE auditoria_comparativa_impostos 
ALTER COLUMN sped_icms_credito TYPE DECIMAL(6,4);

-- 2. Corrigir campos na tabela item_nota_entrada
ALTER TABLE item_nota_entrada 
ALTER COLUMN p_red_icms TYPE DECIMAL(6,4),
ALTER COLUMN p_red_icms_st TYPE DECIMAL(6,4),
ALTER COLUMN p_mva_icms_st TYPE DECIMAL(6,4),
ALTER COLUMN p_red_ipi TYPE DECIMAL(6,4),
ALTER COLUMN p_red_cofins TYPE DECIMAL(6,4);

-- 3. Corrigir campos na tabela historico_auditoria_entrada (se existir)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'historico_auditoria_entrada') THEN
        ALTER TABLE historico_auditoria_entrada 
        ALTER COLUMN pis_aliquota_padrao TYPE DECIMAL(6,4),
        ALTER COLUMN cofins_aliquota_padrao TYPE DECIMAL(6,4);
    END IF;
END $$;

-- 4. Corrigir campos na tabela historico_matching_aprendizado
ALTER TABLE historico_matching_aprendizado 
ALTER COLUMN confidence_score_original TYPE DECIMAL(6,4);

-- 5. Adicionar comentários atualizados
COMMENT ON COLUMN auditoria_comparativa_impostos.sped_icms_credito IS 'Percentual de crédito ICMS Simples Nacional (0.0000 a 99.9999)';
COMMENT ON COLUMN item_nota_entrada.p_red_icms IS 'Percentual de redução ICMS (0.0000 a 99.9999)';
COMMENT ON COLUMN item_nota_entrada.p_red_icms_st IS 'Percentual de redução ICMS-ST (0.0000 a 99.9999)';
COMMENT ON COLUMN item_nota_entrada.p_mva_icms_st IS 'Percentual MVA ICMS-ST (0.0000 a 99.9999)';
COMMENT ON COLUMN item_nota_entrada.p_red_ipi IS 'Percentual de redução IPI (0.0000 a 99.9999)';
COMMENT ON COLUMN item_nota_entrada.p_red_cofins IS 'Percentual de redução COFINS (0.0000 a 99.9999)';
COMMENT ON COLUMN historico_matching_aprendizado.confidence_score_original IS 'Score de confiança original (0.0000 a 99.9999)';

COMMIT;

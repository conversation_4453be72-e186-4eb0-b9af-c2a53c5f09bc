import api from './authService'

export interface ICMSSTSuggestion {
  cenario_id: number
  cenario_atual?: any
  ncm?: string
  cest?: string
  cfop?: string
  cst?: string
  tipo_st?: string
  divergencias?: any[]
  dados_originais?: any
  sugestao?: any
  descricao?: string
  pode_aplicar_automaticamente?: boolean
}

export interface ICMSSTValidationResult {
  total_cenarios?: number
  cenarios_com_sugestoes?: number
  total_sugestoes?: number
  message: string
  sugestoes: ICMSSTSuggestion[]
}

export const icmsStValidationService = {
  async validateICMSST(
    empresaId: number,
    status: string
  ): Promise<ICMSSTValidationResult> {
    const response = await api.post('/cenarios/icms-st/validate', {
      empresa_id: empresaId,
      cfop_filtro: null,
      status_filtro: status,
    })
    return response.data
  },

  async applySuggestion(cenarioId: number, divergencias: any[]) {
    const response = await api.post('/cenarios/icms-st/apply-suggestion', {
      cenario_id: cenarioId,
      sugestoes: divergencias,
    })
    return response.data
  },

  async validateCFOPCST(
    empresaId: number,
    status: string
  ): Promise<ICMSSTValidationResult> {
    const response = await api.post('/cenarios/icms-st/validate-cfop-cst', {
      empresa_id: empresaId,
      filtros: { status },
    })
    return response.data
  },

  async applyCFOPCSTSuggestion(cenarioId: number, sugestao: any) {
    const response = await api.post(
      '/cenarios/icms-st/apply-cfop-cst-suggestion',
      {
        cenario_id: cenarioId,
        sugestao,
      }
    )
    return response.data
  },
}

export default icmsStValidationService
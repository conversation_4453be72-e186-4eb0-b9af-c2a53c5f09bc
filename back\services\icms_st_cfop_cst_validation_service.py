"""
Serviço de Validação CFOP x CST x Alíquota para cenários ICMS-ST
Implementa regras de negócio para validar combinações CFOP/CST/Alíquota
"""

from models import db, CenarioICMSST
from models.ipi_validation_result import IPIValidationResult
from sqlalchemy import and_
from decimal import Decimal
import logging
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class ICMSSTCFOPCSTValidationService:
    """Serviço para validação de combinações CFOP x CST x Alíquota para ICMS-ST"""
    
    # Regras configuráveis CFOP x CST para ICMS-ST
    # Estrutura: CFOP -> CST -> {'deve_ter_aliquota': bool, 'descricao': str}
    # Propriedades especiais por CFOP:
    # - '_csts_exclusivos': [lista] - Se definido, CFOP só pode ter esses CSTs
    # - '_cst_obrigatorio': 'CST' - Se definido, CFOP deve ter esse CST específico
    REGRAS_CFOP_CST = {
        '5401': {  # Venda de produção do estabelecimento em operação com produto sujeito ao regime de substituição tributária
            '_csts_exclusivos': ['10', '70'],  # Só permite CST 10 ou 70
            '10': {'deve_ter_aliquota': True, 'descricao': 'Tributada e com cobrança do ICMS por substituição tributária'},
            '70': {'deve_ter_aliquota': True, 'descricao': 'Com redução de base de cálculo e cobrança do ICMS por substituição tributária'}
        },
        '5405': {  # Venda de mercadoria sujeita ao regime de substituição tributária
            '_cst_obrigatorio': '60',  # CST obrigatório
            '60': {'deve_ter_aliquota': False, 'descricao': 'ICMS cobrado anteriormente por substituição tributária'}
        },
        '6401': {  # Venda de produção do estabelecimento em operação com produto sujeito ao regime de substituição tributária (interestadual)
            '_csts_exclusivos': ['10', '70'],  # Só permite CST 10 ou 70
            '10': {'deve_ter_aliquota': True, 'descricao': 'Tributada e com cobrança do ICMS por substituição tributária'},
            '70': {'deve_ter_aliquota': True, 'descricao': 'Com redução de base de cálculo e cobrança do ICMS por substituição tributária'}
        },
        '6403': {  # Venda de mercadoria adquirida ou recebida de terceiros em operação com produto sujeito ao regime de substituição tributária (interestadual)
            '_csts_exclusivos': ['10', '70'],  # Só permite CST 10 ou 70
            '10': {'deve_ter_aliquota': True, 'descricao': 'Tributada e com cobrança do ICMS por substituição tributária'},
            '70': {'deve_ter_aliquota': True, 'descricao': 'Com redução de base de cálculo e cobrança do ICMS por substituição tributária'}
        },
        '5910': {  # Remessa de mercadoria por conta e ordem de terceiros, em venda à ordem
            '_csts_exclusivos': ['10', '60', '70'],  # Permite CST 10, 60 ou 70
            '10': {'deve_ter_aliquota': True, 'descricao': 'Tributada e com cobrança do ICMS por substituição tributária'},
            '60': {'deve_ter_aliquota': False, 'descricao': 'ICMS cobrado anteriormente por substituição tributária'},
            '70': {'deve_ter_aliquota': True, 'descricao': 'Com redução de base de cálculo e cobrança do ICMS por substituição tributária'}
        },
        '5949': {  # Outra saída de mercadoria ou prestação de serviço não especificado
            '_csts_exclusivos': ['10', '60', '70'],  # Permite CST 10, 60 ou 70
            '10': {'deve_ter_aliquota': True, 'descricao': 'Tributada e com cobrança do ICMS por substituição tributária'},
            '60': {'deve_ter_aliquota': False, 'descricao': 'ICMS cobrado anteriormente por substituição tributária'},
            '70': {'deve_ter_aliquota': True, 'descricao': 'Com redução de base de cálculo e cobrança do ICMS por substituição tributária'}
        }
    }
    
    def __init__(self):
        self.logger = logger

    def validar_cenarios_empresa(self, empresa_id: int, filtros: Dict = None) -> Dict:
        """
        Valida combinações CFOP x CST x Alíquota de uma empresa para ICMS-ST
        
        Args:
            empresa_id (int): ID da empresa
            filtros (Dict, optional): Filtros adicionais para os cenários
            
        Returns:
            Dict: Resultado da validação com sugestões
        """
        try:
            # Buscar cenários ICMS-ST de saída da empresa
            cenarios = self._buscar_cenarios_para_validacao(empresa_id, filtros)
            
            if not cenarios:
                return {
                    'success': True,
                    'total_cenarios': 0,
                    'cenarios_com_sugestoes': 0,
                    'sugestoes': [],
                    'message': 'Nenhum cenário ICMS-ST encontrado para validação CFOP x CST'
                }
            
            # Validar cada cenário
            sugestoes = []
            for cenario in cenarios:
                sugestao = self._validar_cenario_individual(cenario)
                if sugestao:
                    sugestoes.append(sugestao)
            
            return {
                'success': True,
                'total_cenarios': len(cenarios),
                'cenarios_com_sugestoes': len(sugestoes),
                'sugestoes': sugestoes,
                'message': f'Validação CFOP x CST ICMS-ST concluída. {len(sugestoes)} cenários com problemas de {len(cenarios)} analisados.'
            }
            
        except Exception as e:
            self.logger.error(f"Erro na validação CFOP x CST ICMS-ST: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Erro interno na validação CFOP x CST ICMS-ST'
            }

    def _buscar_cenarios_para_validacao(self, empresa_id: int, filtros: Dict = None) -> List[CenarioICMSST]:
        """
        Busca cenários ICMS-ST que devem ser validados para CFOP x CST
        """
        # CFOPs que temos regras definidas
        cfops_validos = list(self.REGRAS_CFOP_CST.keys())
        
        query = CenarioICMSST.query.filter(
            and_(
                CenarioICMSST.empresa_id == empresa_id,
                CenarioICMSST.direcao == 'saida',
                CenarioICMSST.cfop.in_(cfops_validos)
            )
        )

        # Aplicar filtros adicionais se fornecidos
        if filtros:
            if filtros.get('status'):
                query = query.filter(CenarioICMSST.status == filtros['status'])
            if filtros.get('ncm'):
                query = query.filter(CenarioICMSST.ncm.like(f"%{filtros['ncm']}%"))
            if filtros.get('cliente_id'):
                query = query.filter(CenarioICMSST.cliente_id == filtros['cliente_id'])
            if filtros.get('produto_id'):
                query = query.filter(CenarioICMSST.produto_id == filtros['produto_id'])

        return query.all()

    def _validar_cenario_individual(self, cenario: CenarioICMSST) -> Optional[Dict]:
        """
        Valida um cenário individual para CFOP x CST x Alíquota
        
        Args:
            cenario (CenarioICMSST): Cenário a ser validado
            
        Returns:
            Optional[Dict]: Sugestão de correção ou None se não houver problemas
        """
        cfop = cenario.cfop
        cst = cenario.cst
        aliquota_atual = float(cenario.icms_st_aliquota) if cenario.icms_st_aliquota else 0.0
        
        # Verificar se temos regra para este CFOP
        if cfop not in self.REGRAS_CFOP_CST:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'CFOP_SEM_REGRA',
                'descricao': f'CFOP {cfop} não possui regras definidas para validação ICMS-ST',
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': None,
                'pode_aplicar_automaticamente': False
            }
        
        # Obter regras do CFOP
        regras_cfop = self.REGRAS_CFOP_CST[cfop]
        
        # Verificar se existe restrição de CSTs exclusivos
        csts_exclusivos = regras_cfop.get('_csts_exclusivos')
        cst_obrigatorio = regras_cfop.get('_cst_obrigatorio')
        
        # Filtrar apenas as regras de CST (excluir propriedades especiais que começam com _)
        csts_validos = [k for k in regras_cfop.keys() if not k.startswith('_')]
        
        # Validação 1: CST obrigatório
        if cst_obrigatorio and cst != cst_obrigatorio:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'CST_OBRIGATORIO_INCORRETO',
                'descricao': f'CFOP {cfop} deve ter obrigatoriamente CST {cst_obrigatorio}, mas está com CST {cst}',
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': {
                    'cst_sugerido': cst_obrigatorio,
                    'motivo': f'CST obrigatório para CFOP {cfop}'
                },
                'pode_aplicar_automaticamente': True
            }
        
        # Validação 2: CSTs exclusivos
        if csts_exclusivos and cst not in csts_exclusivos:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'CST_NAO_PERMITIDO_PARA_CFOP',
                'descricao': f'CFOP {cfop} só permite CSTs: {", ".join(csts_exclusivos)}, mas está com CST {cst}',
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': {
                    'csts_permitidos': csts_exclusivos,
                    'cst_sugerido': self._sugerir_melhor_cst(csts_exclusivos, aliquota_atual),
                    'motivo': f'CSTs exclusivos para CFOP {cfop}'
                },
                'pode_aplicar_automaticamente': len(csts_exclusivos) == 1  # Só aplica automaticamente se há apenas 1 opção
            }
        
        # Validação 3: CST não existe nas regras
        if cst not in csts_validos:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': 'CST_INVALIDO_PARA_CFOP',
                'descricao': f'CST {cst} não é válido para CFOP {cfop}. CSTs válidos: {", ".join(csts_validos)}',
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': {
                    'csts_validos': csts_validos,
                    'cst_sugerido': self._sugerir_melhor_cst(csts_validos, aliquota_atual)
                },
                'pode_aplicar_automaticamente': False  # Requer decisão manual
            }
        
        # Verificar regra de alíquota para a combinação CFOP x CST
        regra = regras_cfop[cst]
        deve_ter_aliquota = regra['deve_ter_aliquota']
        
        problema = None
        
        if deve_ter_aliquota and aliquota_atual == 0.0:
            problema = {
                'tipo_problema': 'ALIQUOTA_ZERADA_INCORRETA',
                'descricao': f'CFOP {cfop} + CST {cst} deve ter alíquota ICMS-ST, mas está zerada. {regra["descricao"]}',
                'sugestao_tipo': 'DEFINIR_ALIQUOTA'
            }
        elif not deve_ter_aliquota and aliquota_atual > 0.0:
            problema = {
                'tipo_problema': 'ALIQUOTA_PREENCHIDA_INCORRETA',
                'descricao': f'CFOP {cfop} + CST {cst} não deve ter alíquota ICMS-ST, mas está com {aliquota_atual}%. {regra["descricao"]}',
                'sugestao_tipo': 'ZERAR_ALIQUOTA'
            }
        
        if problema:
            return {
                'cenario_id': cenario.id,
                'tipo_problema': problema['tipo_problema'],
                'descricao': problema['descricao'],
                'cenario_atual': self._cenario_to_dict(cenario),
                'sugestao': {
                    'aliquota_sugerida': 0.0 if problema['sugestao_tipo'] == 'ZERAR_ALIQUOTA' else None,
                    'deve_ter_aliquota': deve_ter_aliquota,
                    'regra_aplicada': regra['descricao']
                },
                'pode_aplicar_automaticamente': problema['sugestao_tipo'] == 'ZERAR_ALIQUOTA'
            }
        
        return None

    def _sugerir_melhor_cst(self, csts_disponiveis: List[str], aliquota_atual: float) -> str:
        """
        Sugere o melhor CST baseado na alíquota atual e CSTs disponíveis
        
        Args:
            csts_disponiveis (List[str]): Lista de CSTs válidos
            aliquota_atual (float): Alíquota atual do cenário
            
        Returns:
            str: CST sugerido
        """
        # Se há apenas uma opção, retornar ela
        if len(csts_disponiveis) == 1:
            return csts_disponiveis[0]
        
        # Priorizar baseado na alíquota atual
        if aliquota_atual > 0.0:
            # Se tem alíquota, priorizar CSTs que devem ter alíquota (10, 70)
            for cst in ['10', '70']:
                if cst in csts_disponiveis:
                    return cst
        else:
            # Se não tem alíquota, priorizar CST 60 (cobrado anteriormente)
            if '60' in csts_disponiveis:
                return '60'
        
        # Fallback: retornar o primeiro disponível
        return csts_disponiveis[0]

    def _cenario_to_dict(self, cenario: CenarioICMSST) -> Dict:
        """
        Converte cenário ICMS-ST para dicionário com informações relevantes
        """
        return {
            'id': cenario.id,
            'ncm': cenario.ncm,
            'cfop': cenario.cfop,
            'cst': cenario.cst,
            'aliquota': float(cenario.icms_st_aliquota) if cenario.icms_st_aliquota else 0.0,
            'aliquota_st': float(cenario.icms_st_aliquota) if cenario.icms_st_aliquota else 0.0,
            'reducao': float(cenario.icms_st_p_red_bc) if cenario.icms_st_p_red_bc else 0.0,
            'mva': float(cenario.icms_st_p_mva) if cenario.icms_st_p_mva else 0.0,
            'cliente_id': cenario.cliente_id,
            'produto_id': cenario.produto_id,
            'status': cenario.status
        }

    def aplicar_sugestao(self, cenario_id: int, sugestao: Dict, usuario: str = None) -> Dict:
        """
        Aplica uma sugestão de correção CFOP x CST a um cenário ICMS-ST
        """
        try:
            cenario = CenarioICMSST.query.get(cenario_id)
            if not cenario:
                return {
                    'success': False,
                    'message': 'Cenário ICMS-ST não encontrado'
                }

            # Salvar dados originais para histórico
            dados_originais = self._cenario_to_dict(cenario)

            # Aplicar correções
            if 'cst_sugerido' in sugestao:
                cenario.cst = sugestao['cst_sugerido']

            if 'aliquota_sugerida' in sugestao:
                cenario.icms_st_aliquota = Decimal(str(sugestao['aliquota_sugerida']))

            # Salvar histórico da validação
            self._salvar_historico_validacao(
                cenario,
                dados_originais,
                sugestao,
                usuario,
                aplicada=True
            )

            db.session.commit()

            return {
                'success': True,
                'message': 'Sugestão CFOP x CST ICMS-ST aplicada com sucesso',
                'cenario_atualizado': self._cenario_to_dict(cenario)
            }

        except Exception as e:
            db.session.rollback()
            self.logger.error(f"Erro ao aplicar sugestão CFOP x CST ICMS-ST: {str(e)}")
            return {
                'success': False,
                'message': f'Erro ao aplicar sugestão: {str(e)}'
            }

    def _salvar_historico_validacao(self, cenario: CenarioICMSST, dados_originais: Dict,
                                   sugestao: Dict, usuario: str = None, aplicada: bool = False):
        """
        Salva o histórico da validação CFOP x CST ICMS-ST
        """
        try:
            # Criar registro de validação (reutilizando o modelo IPI por simplicidade)
            validacao = IPIValidationResult.criar_validacao(
                empresa_id=cenario.empresa_id,
                escritorio_id=cenario.escritorio_id,
                cenario_id=cenario.id,
                tipo_problema='CFOP_CST_ALIQUOTA_ICMS_ST_INCORRETA',
                descricao_problema=f"Correção CFOP x CST ICMS-ST aplicada: {dados_originais.get('cfop')}/{dados_originais.get('cst')} → Alíquota ST {dados_originais.get('aliquota_st')}% → {sugestao.get('aliquota_sugerida', 'N/A')}%",
                dados_originais=dados_originais,
                sugestao=sugestao,
                tipi_data={'tipo_validacao': 'CFOP_CST_ALIQUOTA_ICMS_ST'}
            )

            if aplicada:
                validacao.marcar_como_aplicada(usuario, automatica=True)

        except Exception as e:
            self.logger.error(f"Erro ao salvar histórico de validação CFOP x CST ICMS-ST: {str(e)}")

    def obter_regras_configuradas(self) -> Dict:
        """
        Retorna as regras CFOP x CST configuradas para edição
        """
        return self.REGRAS_CFOP_CST

    def atualizar_regras(self, novas_regras: Dict) -> Dict:
        """
        Atualiza as regras CFOP x CST (para futuras implementações de edição)
        """
        # Por enquanto, as regras são estáticas no código
        # Futuramente pode ser implementado salvamento em banco de dados
        return {
            'success': False,
            'message': 'Edição de regras não implementada. Edite o arquivo icms_st_cfop_cst_validation_service.py'
        }
import logging
import os
import time
import threading
import requests
import jwt as pyjwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .auth_config import AuthConfig
from ..models.service_authentication import ServiceAuthentication
from ..models.service_logs import ServiceLogs

class AuthenticationService:
    """
    Service responsible for managing portal authentication and JWT tokens.
    Handles login, token storage, and automatic refresh.
    """
    
    def __init__(self, config: AuthConfig):
        self.config = config
        self.logger = self._setup_logger()
        self.engine = None
        self.Session = None
        self._running = False
        self._refresh_thread = None
        
        # Initialize database connection
        self._init_database()
    
    def _setup_logger(self) -> logging.Logger:
        """Setup structured logging for the authentication service"""
        logger = logging.getLogger('authentication_service')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _init_database(self) -> None:
        """Initialize database connection and session factory"""
        try:
            self.engine = create_engine(
                self.config.database_url,
                pool_size=5,
                max_overflow=10,
                pool_timeout=30,
                pool_recycle=1800,
                pool_pre_ping=True
            )
            self.Session = sessionmaker(bind=self.engine)
            self.logger.info("Database connection initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize database connection: {str(e)}")
            raise
    
    def _log_to_database(self, operation_type: str, status: str, message: str = "", 
                        operation_details: Optional[Dict] = None, error_message: str = None,
                        execution_time_ms: int = None) -> None:
        """Log operation to database service_logs table"""
        try:
            with self.Session() as session:
                # Get current active authentication record for reference
                auth_record = session.query(ServiceAuthentication).filter_by(is_active=True).first()
                
                log_entry = ServiceLogs(
                    service_auth_id=auth_record.id if auth_record else None,
                    portal_office_id=auth_record.portal_office_id if auth_record else 'unknown',
                    operation_type=operation_type,
                    operation_details=operation_details,
                    status=status,
                    error_message=error_message,
                    execution_time_ms=execution_time_ms
                )
                session.add(log_entry)
                session.commit()
        except Exception as e:
            self.logger.error(f"Failed to log to database: {str(e)}")
    
    def start(self) -> None:
        """Start the authentication service"""
        if not self.config.validate():
            missing_fields = self.config.get_missing_fields()
            error_msg = f"Invalid configuration. Missing fields: {', '.join(missing_fields)}"
            self.logger.error(error_msg)
            self._log_to_database('service_startup', 'failed', error_message=error_msg)
            raise ValueError(error_msg)
        
        self.logger.info("Starting Authentication Service")
        self._log_to_database('service_startup', 'success', 'Authentication Service starting', {
            'portal_url': self.config.portal_url,
            'refresh_interval': self.config.refresh_interval
        })
        
        # Perform initial authentication
        if self.authenticate_with_portal():
            self.logger.info("Initial authentication successful")
            self._log_to_database('portal_authentication', 'success', 'Initial authentication successful')
        else:
            self.logger.error("Initial authentication failed")
            self._log_to_database('portal_authentication', 'failed', error_message='Initial authentication failed')
        
        # Start background refresh thread
        self._running = True
        self._refresh_thread = threading.Thread(target=self._refresh_loop, daemon=True)
        self._refresh_thread.start()
        
        self.logger.info("Authentication Service started successfully")
        self._log_to_database('service_startup', 'success', 'Authentication Service started successfully')
    
    def stop(self) -> None:
        """Stop the authentication service"""
        self.logger.info("Stopping Authentication Service")
        self._log_to_database('service_shutdown', 'success', 'Authentication Service stopping')
        
        self._running = False
        if self._refresh_thread and self._refresh_thread.is_alive():
            self._refresh_thread.join(timeout=5)
        
        self.logger.info("Authentication Service stopped")
        self._log_to_database('service_shutdown', 'success', 'Authentication Service stopped')
    
    def authenticate_with_portal(self) -> bool:
        """
        Authenticate with the portal and store the JWT token
        Returns True if authentication was successful, False otherwise
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"Attempting portal authentication for {self.config.escritorio_email}")
            
            # Prepare login payload
            payload = {
                'email': self.config.escritorio_email,
                'password': self.config.escritorio_password
            }
            
            # Make login request with enhanced error handling
            try:
                response = requests.post(
                    self.config.portal_url,
                    json=payload,
                    timeout=30,
                    headers={
                        'Content-Type': 'application/json',
                        'User-Agent': 'XML-Service-Authentication/1.0'
                    }
                )
            except requests.exceptions.Timeout:
                error_msg = "Portal login request timed out after 30 seconds"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            except requests.exceptions.ConnectionError as e:
                error_msg = f"Failed to connect to portal: {str(e)}"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            
            # Enhanced status code handling
            if response.status_code == 401:
                error_msg = "Portal authentication failed: Invalid credentials"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    operation_details={'status_code': response.status_code},
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            elif response.status_code == 403:
                error_msg = "Portal authentication failed: Access forbidden"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    operation_details={'status_code': response.status_code},
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            elif response.status_code == 429:
                error_msg = "Portal authentication failed: Rate limit exceeded"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    operation_details={'status_code': response.status_code},
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            elif response.status_code >= 500:
                error_msg = f"Portal server error: {response.status_code}"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    operation_details={
                                        'status_code': response.status_code,
                                        'response_text': response.text[:500]
                                    },
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            elif response.status_code != 200:
                error_msg = f"Portal login failed with status {response.status_code}: {response.text[:200]}"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    operation_details={
                                        'status_code': response.status_code,
                                        'response_text': response.text[:500]
                                    },
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            
            # Parse JSON response with error handling
            try:
                response_data = response.json()
            except ValueError as e:
                error_msg = f"Invalid JSON response from portal: {str(e)}"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    operation_details={'response_text': response.text[:500]},
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            
            # Validate response structure
            user_data = response_data.get('user', {})
            if not user_data:
                error_msg = "No user data in portal response"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    operation_details={'response_keys': list(response_data.keys())},
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            
            # Validate required user fields
            required_fields = ['id', 'email', 'officeId', 'officeCnpj']
            missing_fields = [field for field in required_fields if not user_data.get(field)]
            if missing_fields:
                error_msg = f"Missing required user fields: {', '.join(missing_fields)}"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    operation_details={'user_data_keys': list(user_data.keys())},
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            
            # Extract JWT token from response cookies (portal sends token in cookies)
            jwt_token = None
            if 'Set-Cookie' in response.headers:
                # Parse cookies to find the token
                cookies = response.cookies
                jwt_token = cookies.get('token')
                
                if jwt_token:
                    self.logger.info("JWT token extracted from response cookies")
                else:
                    self.logger.warning("No 'token' cookie found in response")
            
            # Fallback: try to find token in response body
            if not jwt_token:
                jwt_token = (response_data.get('token') or 
                            response_data.get('access_token') or 
                            response_data.get('jwt') or
                            user_data.get('token'))
            
            if not jwt_token:
                # If no token in response, create internal JWT for API access
                self.logger.info("No JWT token in portal response, creating internal token")
                jwt_token = self._create_internal_jwt_token(user_data)
                if not jwt_token:
                    error_msg = "Failed to create internal JWT token"
                    self.logger.error(error_msg)
                    self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                        execution_time_ms=int((time.time() - start_time) * 1000))
                    return False
            
            # Decode and validate JWT token
            decoded_payload = self.decode_jwt_token(jwt_token)
            if not decoded_payload:
                # If decoding fails but we have valid user data, create internal token
                self.logger.warning("JWT token decoding failed, creating internal token")
                jwt_token = self._create_internal_jwt_token(user_data)
                if jwt_token:
                    decoded_payload = self.decode_jwt_token(jwt_token)
                
                if not decoded_payload:
                    error_msg = "Failed to decode or create valid JWT token"
                    self.logger.error(error_msg)
                    self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                        execution_time_ms=int((time.time() - start_time) * 1000))
                    return False
            
            # Store authentication data securely
            if not self.store_token_securely(jwt_token, user_data, decoded_payload):
                error_msg = "Failed to store authentication data securely"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                    execution_time_ms=int((time.time() - start_time) * 1000))
                return False
            
            execution_time = int((time.time() - start_time) * 1000)
            self.logger.info(f"Portal authentication successful for {user_data.get('email')} ({execution_time}ms)")
            self._log_to_database('portal_authentication', 'success', 'Portal authentication successful', {
                'user_email': user_data.get('email'),
                'user_name': user_data.get('name'),
                'office_id': user_data.get('officeId'),
                'office_cnpj': user_data.get('officeCnpj'),
                'user_role': user_data.get('role')
            }, execution_time_ms=execution_time)
            
            return True
            
        except requests.RequestException as e:
            error_msg = f"Network error during portal authentication: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                execution_time_ms=int((time.time() - start_time) * 1000))
            return False
        except Exception as e:
            error_msg = f"Unexpected error during portal authentication: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            self._log_to_database('portal_authentication', 'failed', error_message=error_msg,
                                execution_time_ms=int((time.time() - start_time) * 1000))
            return False
    
    def decode_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Decode JWT token using the appropriate key and algorithm
        Returns decoded payload or None if decoding fails
        """
        try:
            # First, try to decode without verification to check the algorithm
            unverified_header = pyjwt.get_unverified_header(token)
            algorithm = unverified_header.get('alg', 'RS256')
            
            if algorithm == 'RS256':
                # Use public key for RS256
                public_key = self._get_public_key()
                if not public_key:
                    self.logger.warning("No public key available for RS256 token verification")
                    return None
                
                payload = pyjwt.decode(token, public_key, algorithms=["RS256"])
                self.logger.info("JWT token decoded successfully using RS256")
                
            elif algorithm == 'HS256':
                # Use secret key for HS256 (internal tokens)
                secret_key = os.getenv('JWT_SECRET_KEY', 'xml-service-default-secret-key')
                payload = pyjwt.decode(token, secret_key, algorithms=["HS256"])
                self.logger.info("JWT token decoded successfully using HS256")
                
            else:
                error_msg = f"Unsupported JWT algorithm: {algorithm}"
                self.logger.error(error_msg)
                self._log_to_database('jwt_decode', 'failed', error_message=error_msg)
                return None
            
            # Validate token claims
            current_time = int(time.time())
            
            # Check expiration
            if 'exp' in payload and payload['exp'] < current_time:
                error_msg = "JWT token has expired"
                self.logger.error(error_msg)
                self._log_to_database('jwt_decode', 'failed', error_message=error_msg)
                return None
            
            # Check not before
            if 'nbf' in payload and payload['nbf'] > current_time:
                error_msg = "JWT token not yet valid"
                self.logger.error(error_msg)
                self._log_to_database('jwt_decode', 'failed', error_message=error_msg)
                return None
            
            # Check issued at (allow some clock skew)
            if 'iat' in payload and payload['iat'] > current_time + 300:  # 5 minutes skew
                error_msg = "JWT token issued in the future"
                self.logger.error(error_msg)
                self._log_to_database('jwt_decode', 'failed', error_message=error_msg)
                return None
            
            return payload
            
        except pyjwt.ExpiredSignatureError:
            error_msg = "JWT token has expired"
            self.logger.error(error_msg)
            self._log_to_database('jwt_decode', 'failed', error_message=error_msg)
            return None
        except pyjwt.InvalidTokenError as e:
            error_msg = f"Invalid JWT token: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('jwt_decode', 'failed', error_message=error_msg)
            return None
        except pyjwt.InvalidSignatureError:
            error_msg = "JWT token signature verification failed"
            self.logger.error(error_msg)
            self._log_to_database('jwt_decode', 'failed', error_message=error_msg)
            return None
        except Exception as e:
            error_msg = f"Unexpected error decoding JWT token: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            self._log_to_database('jwt_decode', 'failed', error_message=error_msg)
            return None
    
    def _get_public_key(self) -> Optional[str]:
        """Get the public key for JWT verification"""
        try:
            if self.config.public_key_path and os.path.exists(self.config.public_key_path):
                with open(self.config.public_key_path, 'r') as f:
                    return f.read().strip()
            else:
                # Fallback to environment variable
                import os
                return os.getenv('PORTAL_JWT_PUBLIC_KEY')
        except Exception as e:
            error_msg = f"Failed to read public key: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('public_key_read', 'failed', error_message=error_msg)
            return None
    
    def _get_private_key(self) -> Optional[str]:
        """Get the private key for JWT signing (for internal tokens)"""
        try:
            # Try to get private key from environment or file
            private_key_path = os.getenv('PORTAL_JWT_PRIVATE_KEY_FILE')
            if private_key_path and os.path.exists(private_key_path):
                with open(private_key_path, 'r') as f:
                    return f.read().strip()
            else:
                # Fallback to environment variable
                return os.getenv('PORTAL_JWT_PRIVATE_KEY')
        except Exception as e:
            error_msg = f"Failed to read private key: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def _create_internal_jwt_token(self, user_data: Dict) -> Optional[str]:
        """
        Create an internal JWT token for API access when portal doesn't provide one
        """
        try:
            # Get private key for signing
            private_key = self._get_private_key()
            if not private_key:
                # If no private key available, create a simple token with user data
                # This will be used for internal API authentication
                payload = {
                    'user_id': user_data.get('id'),
                    'email': user_data.get('email'),
                    'office_id': user_data.get('officeId'),
                    'office_cnpj': user_data.get('officeCnpj'),
                    'role': user_data.get('role', 'user'),
                    'permissions': user_data.get('permissions', {}),
                    'iat': int(time.time()),
                    'exp': int(time.time()) + 3600,  # 1 hour expiration
                    'iss': 'xml-service-auth',
                    'aud': 'fiscal-api'
                }
                
                # Use HS256 with a secret key for internal tokens
                secret_key = os.getenv('JWT_SECRET_KEY', 'xml-service-default-secret-key')
                token = pyjwt.encode(payload, secret_key, algorithm='HS256')
                
                self.logger.info("Created internal JWT token using HS256")
                return token
            else:
                # Use RS256 with private key if available
                payload = {
                    'user_id': user_data.get('id'),
                    'email': user_data.get('email'),
                    'office_id': user_data.get('officeId'),
                    'office_cnpj': user_data.get('officeCnpj'),
                    'role': user_data.get('role', 'user'),
                    'permissions': user_data.get('permissions', {}),
                    'iat': int(time.time()),
                    'exp': int(time.time()) + 3600,  # 1 hour expiration
                    'iss': 'xml-service-auth',
                    'aud': 'fiscal-api'
                }
                
                token = pyjwt.encode(payload, private_key, algorithm='RS256')
                self.logger.info("Created internal JWT token using RS256")
                return token
                
        except Exception as e:
            error_msg = f"Failed to create internal JWT token: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('internal_jwt_creation', 'failed', error_message=error_msg)
            return None
    
    def store_authentication_data(self, jwt_token: str, user_data: Dict, decoded_payload: Dict) -> None:
        """Store authentication data in the database"""
        try:
            with self.Session() as session:
                # Calculate token expiration
                exp_timestamp = decoded_payload.get('exp')
                expires_at = datetime.utcfromtimestamp(exp_timestamp) if exp_timestamp else datetime.utcnow() + timedelta(hours=1)
                
                # Check if authentication record already exists
                office_id = user_data.get('officeId')
                existing_auth = session.query(ServiceAuthentication).filter_by(
                    portal_office_id=office_id
                ).first()
                
                if existing_auth:
                    # Update existing record
                    existing_auth.portal_user_id = user_data.get('id', '')
                    existing_auth.office_cnpj = user_data.get('officeCnpj', '')
                    existing_auth.user_name = user_data.get('name', '')
                    existing_auth.user_email = user_data.get('email', '')
                    existing_auth.user_role = user_data.get('role', '')
                    existing_auth.jwt_token = jwt_token
                    existing_auth.decoded_payload = decoded_payload
                    existing_auth.expires_at = expires_at
                    existing_auth.updated_at = datetime.utcnow()
                    existing_auth.is_active = True
                else:
                    # Create new record
                    auth_record = ServiceAuthentication(
                        portal_user_id=user_data.get('id', ''),
                        portal_office_id=office_id,
                        office_cnpj=user_data.get('officeCnpj', ''),
                        user_name=user_data.get('name', ''),
                        user_email=user_data.get('email', ''),
                        user_role=user_data.get('role', ''),
                        jwt_token=jwt_token,
                        decoded_payload=decoded_payload,
                        expires_at=expires_at,
                        is_active=True
                    )
                    session.add(auth_record)
                
                session.commit()
                self.logger.info("Authentication data stored successfully")
                
        except SQLAlchemyError as e:
            error_msg = f"Database error storing authentication data: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('auth_data_storage', 'failed', error_message=error_msg)
            raise
        except Exception as e:
            error_msg = f"Unexpected error storing authentication data: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('auth_data_storage', 'failed', error_message=error_msg)
            raise
    
    def refresh_token_if_needed(self) -> bool:
        """
        Check if token needs refresh and refresh if necessary
        Returns True if token is valid (either current or refreshed), False otherwise
        """
        try:
            with self.Session() as session:
                # Get active authentication record
                auth_record = session.query(ServiceAuthentication).filter_by(
                    is_active=True
                ).first()
                
                if not auth_record:
                    self.logger.warning("No active authentication record found")
                    return self.authenticate_with_portal()
                
                # Check if token is expired or will expire soon (within 5 minutes)
                time_until_expiry = auth_record.expires_at - datetime.utcnow()
                if time_until_expiry.total_seconds() <= 300:  # 5 minutes
                    self.logger.info("Token expired or expiring soon, refreshing...")
                    return self.authenticate_with_portal()
                
                self.logger.debug(f"Token valid for {time_until_expiry}")
                return True
                
        except Exception as e:
            error_msg = f"Error checking token expiration: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('token_refresh_check', 'failed', error_message=error_msg)
            return False
    
    def _refresh_loop(self) -> None:
        """
        Background thread loop for token refresh and maintenance
        Handles token refresh, cleanup, and health monitoring
        """
        cleanup_counter = 0
        cleanup_interval = 24  # Run cleanup every 24 refresh cycles (24 hours if refresh_interval is 1 hour)
        
        while self._running:
            try:
                # Check and refresh tokens if needed
                refresh_success = self.refresh_token_if_needed()
                
                if not refresh_success:
                    self.logger.warning("Token refresh failed, will retry in next cycle")
                
                # Periodic cleanup of expired tokens
                cleanup_counter += 1
                if cleanup_counter >= cleanup_interval:
                    self.logger.info("Running periodic token cleanup...")
                    cleanup_result = self.cleanup_expired_tokens()
                    if cleanup_result['success']:
                        self.logger.info(f"Cleanup completed: {cleanup_result['cleaned_up_count']} tokens removed")
                    cleanup_counter = 0
                
                # Log token statistics periodically
                if cleanup_counter % 6 == 0:  # Every 6 cycles
                    stats = self.get_token_statistics()
                    if 'error' not in stats:
                        self.logger.info(f"Token stats - Active: {stats['active_tokens']}, "
                                       f"Expired: {stats['expired_tokens']}, "
                                       f"Expiring soon: {stats['expiring_soon']}")
                
                # Sleep for the configured interval
                time.sleep(self.config.refresh_interval)
                
            except Exception as e:
                error_msg = f"Error in refresh loop: {str(e)}"
                self.logger.error(error_msg, exc_info=True)
                self._log_to_database('token_refresh_loop', 'failed', error_message=error_msg)
                time.sleep(60)  # Wait 1 minute before retrying
    
    def get_valid_token(self) -> Optional[str]:
        """
        Get a valid JWT token from the database
        Returns the token string or None if no valid token is available
        """
        try:
            with self.Session() as session:
                auth_record = session.query(ServiceAuthentication).filter_by(
                    is_active=True
                ).first()
                
                if not auth_record:
                    self.logger.warning("No active authentication record found")
                    return None
                
                if auth_record.is_token_expired():
                    self.logger.warning("Stored token is expired")
                    return None
                
                return auth_record.jwt_token
                
        except Exception as e:
            error_msg = f"Error retrieving valid token: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('token_retrieval', 'failed', error_message=error_msg)
            return None
    
    def authenticate_with_retry(self, max_retries: int = 3, base_delay: float = 1.0) -> bool:
        """
        Authenticate with portal using exponential backoff retry logic
        Returns True if authentication was successful, False otherwise
        """
        for attempt in range(max_retries):
            try:
                if self.authenticate_with_portal():
                    return True
                
                if attempt < max_retries - 1:  # Don't sleep on the last attempt
                    delay = base_delay * (2 ** attempt)  # Exponential backoff
                    self.logger.info(f"Authentication attempt {attempt + 1} failed, retrying in {delay} seconds...")
                    self._log_to_database('portal_authentication_retry', 'info', 
                                        f'Retrying authentication in {delay} seconds', {
                                            'attempt': attempt + 1,
                                            'max_retries': max_retries,
                                            'delay_seconds': delay
                                        })
                    time.sleep(delay)
                
            except Exception as e:
                error_msg = f"Authentication attempt {attempt + 1} failed with error: {str(e)}"
                self.logger.error(error_msg)
                self._log_to_database('portal_authentication_retry', 'failed', error_message=error_msg, 
                                    operation_details={
                                        'attempt': attempt + 1,
                                        'max_retries': max_retries
                                    })
                
                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt)
                    time.sleep(delay)
        
        self.logger.error(f"All {max_retries} authentication attempts failed")
        self._log_to_database('portal_authentication_retry', 'failed', 
                            f'All {max_retries} authentication attempts failed')
        return False
    
    def test_portal_connection(self) -> Dict[str, Any]:
        """
        Test the portal connection without performing authentication
        Returns connection status and response details
        """
        start_time = time.time()
        result = {
            'success': False,
            'status_code': None,
            'response_time_ms': 0,
            'error_message': None,
            'portal_reachable': False
        }
        
        try:
            self.logger.info(f"Testing portal connection to {self.config.portal_url}")
            
            # Make a simple HEAD or GET request to test connectivity
            response = requests.get(
                self.config.portal_url.replace('/auth/login', '/health'),  # Try health endpoint first
                timeout=10,
                headers={'User-Agent': 'XML-Service-Authentication/1.0'}
            )
            
            result['portal_reachable'] = True
            result['status_code'] = response.status_code
            result['response_time_ms'] = int((time.time() - start_time) * 1000)
            
            if response.status_code < 500:  # Any response below 500 means portal is reachable
                result['success'] = True
                self.logger.info(f"Portal connection test successful ({result['response_time_ms']}ms)")
            else:
                result['error_message'] = f"Portal returned server error: {response.status_code}"
                self.logger.warning(result['error_message'])
                
        except requests.exceptions.Timeout:
            result['error_message'] = "Portal connection timed out"
            result['response_time_ms'] = int((time.time() - start_time) * 1000)
            self.logger.error(result['error_message'])
        except requests.exceptions.ConnectionError as e:
            result['error_message'] = f"Failed to connect to portal: {str(e)}"
            result['response_time_ms'] = int((time.time() - start_time) * 1000)
            self.logger.error(result['error_message'])
        except Exception as e:
            result['error_message'] = f"Unexpected error testing portal connection: {str(e)}"
            result['response_time_ms'] = int((time.time() - start_time) * 1000)
            self.logger.error(result['error_message'])
        
        # Log the test result
        self._log_to_database('portal_connection_test', 
                            'success' if result['success'] else 'failed',
                            'Portal connection test completed',
                            {
                                'portal_reachable': result['portal_reachable'],
                                'status_code': result['status_code']
                            },
                            error_message=result['error_message'],
                            execution_time_ms=result['response_time_ms'])
        
        return result
    
    def get_authentication_status(self) -> Dict[str, Any]:
        """
        Get current authentication status and token information
        Returns detailed status information
        """
        try:
            with self.Session() as session:
                auth_record = session.query(ServiceAuthentication).filter_by(
                    is_active=True
                ).first()
                
                if not auth_record:
                    return {
                        'authenticated': False,
                        'error': 'No active authentication record found'
                    }
                
                current_time = datetime.utcnow()
                time_until_expiry = auth_record.expires_at - current_time
                
                return {
                    'authenticated': True,
                    'user_email': auth_record.user_email,
                    'user_name': auth_record.user_name,
                    'office_id': auth_record.portal_office_id,
                    'office_cnpj': auth_record.office_cnpj,
                    'user_role': auth_record.user_role,
                    'token_expires_at': auth_record.expires_at.isoformat(),
                    'token_expires_in_seconds': int(time_until_expiry.total_seconds()),
                    'token_expired': auth_record.is_token_expired(),
                    'created_at': auth_record.created_at.isoformat(),
                    'updated_at': auth_record.updated_at.isoformat() if auth_record.updated_at else None
                }
                
        except Exception as e:
            error_msg = f"Error getting authentication status: {str(e)}"
            self.logger.error(error_msg)
            return {
                'authenticated': False,
                'error': error_msg
            }
    
    def store_token_securely(self, jwt_token: str, user_data: Dict, decoded_payload: Dict, 
                           encrypt_token: bool = True) -> bool:
        """
        Store authentication token securely in the database with optional encryption
        Returns True if storage was successful, False otherwise
        """
        start_time = time.time()
        
        try:
            with self.Session() as session:
                # Calculate token expiration
                exp_timestamp = decoded_payload.get('exp')
                expires_at = datetime.utcfromtimestamp(exp_timestamp) if exp_timestamp else datetime.utcnow() + timedelta(hours=1)
                
                # Encrypt token if requested
                stored_token = self._encrypt_token(jwt_token) if encrypt_token else jwt_token
                
                # Check if authentication record already exists for this office
                office_id = user_data.get('officeId')
                existing_auth = session.query(ServiceAuthentication).filter_by(
                    portal_office_id=office_id
                ).first()
                
                if existing_auth:
                    # Deactivate old record and create new one for audit trail
                    existing_auth.is_active = False
                    existing_auth.updated_at = datetime.utcnow()
                    
                    # Create new active record
                    auth_record = ServiceAuthentication(
                        portal_user_id=user_data.get('id', ''),
                        portal_office_id=office_id,
                        office_cnpj=user_data.get('officeCnpj', ''),
                        user_name=user_data.get('name', ''),
                        user_email=user_data.get('email', ''),
                        user_role=user_data.get('role', ''),
                        jwt_token=stored_token,
                        decoded_payload=decoded_payload,
                        expires_at=expires_at,
                        is_active=True
                    )
                    session.add(auth_record)
                    
                    self.logger.info(f"Updated authentication record for office {office_id}")
                else:
                    # Create new record
                    auth_record = ServiceAuthentication(
                        portal_user_id=user_data.get('id', ''),
                        portal_office_id=office_id,
                        office_cnpj=user_data.get('officeCnpj', ''),
                        user_name=user_data.get('name', ''),
                        user_email=user_data.get('email', ''),
                        user_role=user_data.get('role', ''),
                        jwt_token=stored_token,
                        decoded_payload=decoded_payload,
                        expires_at=expires_at,
                        is_active=True
                    )
                    session.add(auth_record)
                    
                    self.logger.info(f"Created new authentication record for office {office_id}")
                
                session.commit()
                
                execution_time = int((time.time() - start_time) * 1000)
                self.logger.info(f"Token stored securely ({execution_time}ms)")
                self._log_to_database('token_storage', 'success', 'Token stored securely', {
                    'office_id': office_id,
                    'user_email': user_data.get('email'),
                    'encrypted': encrypt_token,
                    'expires_at': expires_at.isoformat()
                }, execution_time_ms=execution_time)
                
                return True
                
        except SQLAlchemyError as e:
            error_msg = f"Database error storing token: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('token_storage', 'failed', error_message=error_msg,
                                execution_time_ms=int((time.time() - start_time) * 1000))
            return False
        except Exception as e:
            error_msg = f"Unexpected error storing token: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            self._log_to_database('token_storage', 'failed', error_message=error_msg,
                                execution_time_ms=int((time.time() - start_time) * 1000))
            return False
    
    def validate_stored_token(self, office_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Validate stored token and return validation results
        Returns detailed validation information
        """
        try:
            with self.Session() as session:
                query = session.query(ServiceAuthentication).filter_by(is_active=True)
                if office_id:
                    query = query.filter_by(portal_office_id=office_id)
                
                auth_record = query.first()
                
                if not auth_record:
                    return {
                        'valid': False,
                        'error': 'No active authentication record found',
                        'needs_refresh': True
                    }
                
                current_time = datetime.utcnow()
                time_until_expiry = auth_record.expires_at - current_time
                expires_in_seconds = int(time_until_expiry.total_seconds())
                
                # Check if token is expired
                if auth_record.is_token_expired():
                    self.logger.warning(f"Token expired for office {auth_record.portal_office_id}")
                    return {
                        'valid': False,
                        'error': 'Token has expired',
                        'needs_refresh': True,
                        'expired_at': auth_record.expires_at.isoformat(),
                        'office_id': auth_record.portal_office_id
                    }
                
                # Check if token will expire soon (within 5 minutes)
                needs_refresh = expires_in_seconds <= 300
                
                # Try to decode the stored token
                stored_token = self._decrypt_token(auth_record.jwt_token)
                decoded_payload = self.decode_jwt_token(stored_token)
                
                if not decoded_payload:
                    return {
                        'valid': False,
                        'error': 'Token decoding failed',
                        'needs_refresh': True,
                        'office_id': auth_record.portal_office_id
                    }
                
                return {
                    'valid': True,
                    'needs_refresh': needs_refresh,
                    'expires_in_seconds': expires_in_seconds,
                    'expires_at': auth_record.expires_at.isoformat(),
                    'office_id': auth_record.portal_office_id,
                    'user_email': auth_record.user_email,
                    'user_role': auth_record.user_role,
                    'token_length': len(stored_token)
                }
                
        except Exception as e:
            error_msg = f"Error validating stored token: {str(e)}"
            self.logger.error(error_msg)
            return {
                'valid': False,
                'error': error_msg,
                'needs_refresh': True
            }
    
    def cleanup_expired_tokens(self) -> Dict[str, Any]:
        """
        Clean up expired tokens from the database
        Returns cleanup statistics
        """
        start_time = time.time()
        
        try:
            with self.Session() as session:
                current_time = datetime.utcnow()
                
                # Find expired tokens
                expired_tokens = session.query(ServiceAuthentication).filter(
                    ServiceAuthentication.expires_at < current_time,
                    ServiceAuthentication.is_active == True
                ).all()
                
                cleanup_count = len(expired_tokens)
                
                if cleanup_count > 0:
                    # Deactivate expired tokens
                    for token in expired_tokens:
                        token.is_active = False
                        token.updated_at = current_time
                        self.logger.info(f"Deactivated expired token for office {token.portal_office_id}")
                    
                    session.commit()
                
                execution_time = int((time.time() - start_time) * 1000)
                
                result = {
                    'success': True,
                    'cleaned_up_count': cleanup_count,
                    'execution_time_ms': execution_time
                }
                
                self.logger.info(f"Token cleanup completed: {cleanup_count} tokens deactivated ({execution_time}ms)")
                self._log_to_database('token_cleanup', 'success', f'Cleaned up {cleanup_count} expired tokens', 
                                    result, execution_time_ms=execution_time)
                
                return result
                
        except Exception as e:
            error_msg = f"Error during token cleanup: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('token_cleanup', 'failed', error_message=error_msg,
                                execution_time_ms=int((time.time() - start_time) * 1000))
            return {
                'success': False,
                'error': error_msg,
                'cleaned_up_count': 0
            }
    
    def get_token_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about stored tokens
        Returns token statistics
        """
        try:
            with self.Session() as session:
                current_time = datetime.utcnow()
                
                # Count active tokens
                active_tokens = session.query(ServiceAuthentication).filter_by(is_active=True).count()
                
                # Count expired tokens
                expired_tokens = session.query(ServiceAuthentication).filter(
                    ServiceAuthentication.expires_at < current_time,
                    ServiceAuthentication.is_active == True
                ).count()
                
                # Count tokens expiring soon (within 1 hour)
                expiring_soon = session.query(ServiceAuthentication).filter(
                    ServiceAuthentication.expires_at > current_time,
                    ServiceAuthentication.expires_at < current_time + timedelta(hours=1),
                    ServiceAuthentication.is_active == True
                ).count()
                
                # Get oldest and newest tokens
                oldest_token = session.query(ServiceAuthentication).filter_by(is_active=True).order_by(
                    ServiceAuthentication.created_at.asc()
                ).first()
                
                newest_token = session.query(ServiceAuthentication).filter_by(is_active=True).order_by(
                    ServiceAuthentication.created_at.desc()
                ).first()
                
                return {
                    'active_tokens': active_tokens,
                    'expired_tokens': expired_tokens,
                    'expiring_soon': expiring_soon,
                    'oldest_token_created': oldest_token.created_at.isoformat() if oldest_token else None,
                    'newest_token_created': newest_token.created_at.isoformat() if newest_token else None,
                    'total_offices': active_tokens  # Each office should have one active token
                }
                
        except Exception as e:
            error_msg = f"Error getting token statistics: {str(e)}"
            self.logger.error(error_msg)
            return {
                'error': error_msg
            }
    
    def _encrypt_token(self, token: str) -> str:
        """
        Encrypt JWT token for secure storage
        For now, returns the token as-is. In production, implement proper encryption.
        """
        # TODO: Implement proper encryption using cryptography library
        # For now, we'll store tokens in plain text but this should be encrypted in production
        return token
    
    def _decrypt_token(self, encrypted_token: str) -> str:
        """
        Decrypt JWT token from storage
        For now, returns the token as-is. In production, implement proper decryption.
        """
        # TODO: Implement proper decryption using cryptography library
        # For now, we'll assume tokens are stored in plain text
        return encrypted_token
    
    def manage_tokens(self) -> Dict[str, Any]:
        """
        Comprehensive token management method that handles validation, refresh, and cleanup
        Returns management results and recommendations
        """
        start_time = time.time()
        management_result = {
            'success': True,
            'actions_taken': [],
            'recommendations': [],
            'statistics': {},
            'errors': []
        }
        
        try:
            self.logger.info("Starting comprehensive token management...")
            
            # 1. Get current token statistics
            stats = self.get_token_statistics()
            management_result['statistics'] = stats
            
            if 'error' not in stats:
                self.logger.info(f"Current token stats - Active: {stats['active_tokens']}, "
                               f"Expired: {stats['expired_tokens']}, "
                               f"Expiring soon: {stats['expiring_soon']}")
            
            # 2. Validate stored tokens
            validation_result = self.validate_stored_token()
            
            if not validation_result['valid']:
                if validation_result['needs_refresh']:
                    self.logger.info("Token validation failed, attempting refresh...")
                    refresh_success = self.authenticate_with_retry(max_retries=2)
                    
                    if refresh_success:
                        management_result['actions_taken'].append('token_refreshed')
                        self.logger.info("Token successfully refreshed")
                    else:
                        management_result['errors'].append('token_refresh_failed')
                        management_result['success'] = False
                        self.logger.error("Token refresh failed")
            else:
                if validation_result['needs_refresh']:
                    management_result['recommendations'].append('schedule_token_refresh')
                    self.logger.info(f"Token valid but expires in {validation_result['expires_in_seconds']} seconds")
            
            # 3. Clean up expired tokens
            if stats.get('expired_tokens', 0) > 0:
                cleanup_result = self.cleanup_expired_tokens()
                if cleanup_result['success']:
                    management_result['actions_taken'].append(f"cleaned_up_{cleanup_result['cleaned_up_count']}_tokens")
                    self.logger.info(f"Cleaned up {cleanup_result['cleaned_up_count']} expired tokens")
                else:
                    management_result['errors'].append('cleanup_failed')
                    self.logger.error("Token cleanup failed")
            
            # 4. Generate recommendations
            if stats.get('expiring_soon', 0) > 0:
                management_result['recommendations'].append('tokens_expiring_soon')
            
            if stats.get('active_tokens', 0) == 0:
                management_result['recommendations'].append('no_active_tokens_authenticate_required')
            
            if stats.get('active_tokens', 0) > 5:
                management_result['recommendations'].append('too_many_active_tokens_review_needed')
            
            execution_time = int((time.time() - start_time) * 1000)
            management_result['execution_time_ms'] = execution_time
            
            self.logger.info(f"Token management completed ({execution_time}ms)")
            self._log_to_database('token_management', 'success', 'Comprehensive token management completed',
                                management_result, execution_time_ms=execution_time)
            
            return management_result
            
        except Exception as e:
            error_msg = f"Error during token management: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            management_result['success'] = False
            management_result['errors'].append(error_msg)
            
            self._log_to_database('token_management', 'failed', error_message=error_msg,
                                execution_time_ms=int((time.time() - start_time) * 1000))
            
            return management_result
    
    def get_token_for_api_use(self, office_id: Optional[str] = None) -> Optional[str]:
        """
        Get a valid token for API use, automatically refreshing if needed
        Returns the token string or None if no valid token is available
        """
        try:
            # First, validate the stored token
            validation_result = self.validate_stored_token(office_id)
            
            if validation_result['valid'] and not validation_result['needs_refresh']:
                # Token is valid and doesn't need refresh
                with self.Session() as session:
                    query = session.query(ServiceAuthentication).filter_by(is_active=True)
                    if office_id:
                        query = query.filter_by(portal_office_id=office_id)
                    
                    auth_record = query.first()
                    if auth_record:
                        token = self._decrypt_token(auth_record.jwt_token)
                        self.logger.debug(f"Retrieved valid token for office {auth_record.portal_office_id}")
                        return token
            
            # Token needs refresh or is invalid
            self.logger.info("Token needs refresh, attempting authentication...")
            if self.authenticate_with_retry(max_retries=2):
                # Try to get the token again after refresh
                with self.Session() as session:
                    query = session.query(ServiceAuthentication).filter_by(is_active=True)
                    if office_id:
                        query = query.filter_by(portal_office_id=office_id)
                    
                    auth_record = query.first()
                    if auth_record:
                        token = self._decrypt_token(auth_record.jwt_token)
                        self.logger.info(f"Retrieved refreshed token for office {auth_record.portal_office_id}")
                        return token
            
            self.logger.error("Failed to get valid token for API use")
            return None
            
        except Exception as e:
            error_msg = f"Error getting token for API use: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_database('token_retrieval_api', 'failed', error_message=error_msg)
            return None
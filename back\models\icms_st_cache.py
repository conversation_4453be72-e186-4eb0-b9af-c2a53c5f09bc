"""
Modelo para cache de consultas ICMS-ST da API externa
"""
from sqlalchemy import Column, Integer, String, Numeric, DateTime, Text, Index, Boolean
from sqlalchemy.sql import func
from .escritorio import db

class ICMSSTCacheModel(db.Model):
    __tablename__ = 'icms_st_cache'
    
    id = Column(Integer, primary_key=True)
    
    # Identificação da consulta
    tipo_st = Column(String(20), nullable=False)  # 'interna' ou 'interestadual'
    ncm = Column(String(20), nullable=False)
    estado_origem = Column(String(2), nullable=True)  # Para ST interestadual
    estado_destino = Column(String(2), nullable=True)  # Para ST interestadual
    estado = Column(String(2), nullable=True)  # Para ST interna
    destinacao_mercadoria = Column(String(50), nullable=True)  # Para ST interestadual
    
    # Dados do produto
    codigo = Column(String(20), nullable=True)
    cest = Column(String(20), nullable=False)
    descricao = Column(Text, nullable=True)
    observacao = Column(Text, nullable=True)
    segmento = Column(String(100), nullable=True)
    codigo_segmento = Column(String(10), nullable=True)
    
    # Alíquotas
    aliquota_interna = Column(Numeric(10, 4), nullable=True)
    aliquota_interestadual = Column(Numeric(10, 4), nullable=True)
    fundo_pobreza = Column(String(10), nullable=True)
    
    # MVAs
    mva = Column(Numeric(10, 4), nullable=True)
    mva_ajustada = Column(Numeric(10, 4), nullable=True)
    mva_ajustada_4 = Column(Numeric(10, 4), nullable=True)
    mva_ajustada_7 = Column(Numeric(10, 4), nullable=True)
    mva_ajustada_12 = Column(Numeric(10, 4), nullable=True)
    mva_positiva = Column(Numeric(10, 4), nullable=True)
    mva_negativa = Column(Numeric(10, 4), nullable=True)
    mva_neutra = Column(Numeric(10, 4), nullable=True)
    
    # Vigência
    vigencia_inicial = Column(String(20), nullable=True)
    vigencia_final = Column(String(20), nullable=True)
    
    # Base legal
    base_legal_st = Column(Text, nullable=True)
    data_efeito_st = Column(String(20), nullable=True)
    base_calculo = Column(Text, nullable=True)
    norma_base_calculo = Column(Text, nullable=True)
    prazo_recolhimento = Column(Text, nullable=True)
    
    # Aplicabilidade
    aplicabilidade = Column(Text, nullable=True)
    nao_aplicabilidade = Column(Text, nullable=True)
    
    # Campos específicos para ST interestadual
    regime_origem = Column(String(50), nullable=True)
    regime_destino = Column(String(50), nullable=True)
    destino_produto = Column(String(100), nullable=True)
    base_legal_int = Column(Text, nullable=True)
    observacao_int = Column(Text, nullable=True)
    base_calculo_int = Column(Text, nullable=True)
    prazo_recolhimento_int = Column(Text, nullable=True)
    norma_observacao_st = Column(Text, nullable=True)
    norma_prazo_recolhimento = Column(Text, nullable=True)
    
    # Variações de MVA (JSON para flexibilidade)
    variacao_mva = Column(Text, nullable=True)  # JSON string
    reducao_mva = Column(Text, nullable=True)
    
    # Controle de cache
    data_consulta = Column(DateTime, default=func.now())
    hash_consulta = Column(String(64), nullable=False)
    
    # Índices para performance
    __table_args__ = (
        Index('idx_icms_st_lookup_interna', 'tipo_st', 'ncm', 'estado'),
        Index('idx_icms_st_lookup_inter', 'tipo_st', 'ncm', 'estado_origem', 'estado_destino', 'destinacao_mercadoria'),
        Index('idx_icms_st_hash', 'hash_consulta'),
        Index('idx_icms_st_data', 'data_consulta'),
        Index('idx_icms_st_cest', 'cest'),
    )
    
    @classmethod
    def buscar_st_interna(cls, ncm, estado):
        """
        Busca dados de ST interna no cache
        """
        return cls.query.filter(
            cls.tipo_st == 'interna',
            cls.ncm == ncm,
            cls.estado == estado
        ).all()
    
    @classmethod
    def buscar_st_interestadual(cls, ncm, estado_origem, estado_destino, destinacao_mercadoria):
        """
        Busca dados de ST interestadual no cache
        """
        return cls.query.filter(
            cls.tipo_st == 'interestadual',
            cls.ncm == ncm,
            cls.estado_origem == estado_origem,
            cls.estado_destino == estado_destino,
            cls.destinacao_mercadoria == destinacao_mercadoria
        ).all()
    
    @classmethod
    def buscar_por_hash(cls, hash_consulta):
        """
        Busca dados no cache pelo hash da consulta
        """
        return cls.query.filter(cls.hash_consulta == hash_consulta).all()
    
    def to_dict(self):
        """
        Converte o modelo para dicionário
        """
        return {
            'id': self.id,
            'tipo_st': self.tipo_st,
            'ncm': self.ncm,
            'estado_origem': self.estado_origem,
            'estado_destino': self.estado_destino,
            'estado': self.estado,
            'destinacao_mercadoria': self.destinacao_mercadoria,
            'codigo': self.codigo,
            'cest': self.cest,
            'descricao': self.descricao,
            'observacao': self.observacao,
            'segmento': self.segmento,
            'codigo_segmento': self.codigo_segmento,
            'aliquota_interna': float(self.aliquota_interna) if self.aliquota_interna else None,
            'aliquota_interestadual': float(self.aliquota_interestadual) if self.aliquota_interestadual else None,
            'fundo_pobreza': self.fundo_pobreza,
            'mva': float(self.mva) if self.mva else None,
            'mva_ajustada': float(self.mva_ajustada) if self.mva_ajustada else None,
            'mva_ajustada_4': float(self.mva_ajustada_4) if self.mva_ajustada_4 else None,
            'mva_ajustada_7': float(self.mva_ajustada_7) if self.mva_ajustada_7 else None,
            'mva_ajustada_12': float(self.mva_ajustada_12) if self.mva_ajustada_12 else None,
            'mva_positiva': float(self.mva_positiva) if self.mva_positiva else None,
            'mva_negativa': float(self.mva_negativa) if self.mva_negativa else None,
            'mva_neutra': float(self.mva_neutra) if self.mva_neutra else None,
            'vigencia_inicial': self.vigencia_inicial,
            'vigencia_final': self.vigencia_final,
            'base_legal_st': self.base_legal_st,
            'data_efeito_st': self.data_efeito_st,
            'base_calculo': self.base_calculo,
            'norma_base_calculo': self.norma_base_calculo,
            'prazo_recolhimento': self.prazo_recolhimento,
            'aplicabilidade': self.aplicabilidade,
            'nao_aplicabilidade': self.nao_aplicabilidade,
            'regime_origem': self.regime_origem,
            'regime_destino': self.regime_destino,
            'destino_produto': self.destino_produto,
            'base_legal_int': self.base_legal_int,
            'observacao_int': self.observacao_int,
            'base_calculo_int': self.base_calculo_int,
            'prazo_recolhimento_int': self.prazo_recolhimento_int,
            'norma_observacao_st': self.norma_observacao_st,
            'norma_prazo_recolhimento': self.norma_prazo_recolhimento,
            'variacao_mva': self.variacao_mva,
            'reducao_mva': self.reducao_mva,
            'data_consulta': self.data_consulta.isoformat() if self.data_consulta else None
        }
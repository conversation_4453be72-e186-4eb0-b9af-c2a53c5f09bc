-- Migration: Criação das tabelas para Auditoria Comparativa de Impostos
-- Data: 2024-01-XX
-- Descrição: Cria tabelas para matching inteligente e auditoria comparativa entre XML e SPED

-- Tabela principal para auditoria comparativa de impostos
CREATE TABLE IF NOT EXISTS auditoria_comparativa_impostos (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    
    -- Identificação da nota
    chave_nf VARCHAR(44) NOT NULL,
    numero_nf VARCHAR(20) NOT NULL,
    
    -- IDs dos itens relacionados
    xml_item_id INTEGER REFERENCES nota_fiscal_item(id),
    sped_item_id INTEGER REFERENCES item_nota_entrada(id),
    
    -- Informações do matching
    match_type VARCHAR(20), -- 'direct', 'embedding', 'manual', 'unmatched_xml', 'unmatched_sped'
    confidence_score DECIMAL(6,4), -- Score de confiança do match (0-99.9999)
    match_details JSONB, -- Detalhes do algoritmo de matching
    
    -- Status geral da auditoria
    status_auditoria VARCHAR(20) DEFAULT 'pendente', -- pendente, em_analise, aprovado, rejeitado
    data_auditoria TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usuario_auditoria INTEGER REFERENCES usuario(id),
    
    -- === DADOS XML ===
    xml_descricao VARCHAR(255),
    xml_quantidade DECIMAL(15,4),
    xml_valor_unitario DECIMAL(15,4),
    xml_valor_total DECIMAL(15,2),
    xml_unidade VARCHAR(10),
    xml_ncm VARCHAR(20),
    xml_cfop VARCHAR(10),
    
    -- === DADOS SPED ===
    sped_descricao VARCHAR(255),
    sped_quantidade DECIMAL(15,4),
    sped_valor_item DECIMAL(15,2),
    sped_unidade VARCHAR(10),
    sped_cfop VARCHAR(10),
    sped_cod_item VARCHAR(50),
    
    -- === ICMS ===
    -- XML (limitado)
    xml_icms_valor DECIMAL(15,2),
    
    -- SPED (completo)
    sped_cst_icms VARCHAR(3),
    sped_icms_bc DECIMAL(15,2),
    sped_icms_aliquota DECIMAL(5,2),
    sped_icms_valor DECIMAL(15,2),
    sped_icms_reducao DECIMAL(5,4),
    
    -- Status e observações ICMS
    status_icms VARCHAR(20) DEFAULT 'pendente', -- conforme, divergente, pendente, aprovado
    obs_icms TEXT,
    
    -- === ICMS-ST ===
    -- SPED
    sped_icms_st_bc DECIMAL(15,2),
    sped_icms_st_aliquota DECIMAL(5,2),
    sped_icms_st_valor DECIMAL(15,2),
    sped_icms_st_mva DECIMAL(5,4),
    sped_icms_st_reducao DECIMAL(5,4),
    
    -- Status e observações ICMS-ST
    status_icms_st VARCHAR(20) DEFAULT 'pendente',
    obs_icms_st TEXT,
    
    -- === IPI ===
    -- SPED
    sped_cst_ipi VARCHAR(2),
    sped_ipi_bc DECIMAL(15,2),
    sped_ipi_aliquota DECIMAL(5,2),
    sped_ipi_valor DECIMAL(15,2),
    sped_ipi_reducao DECIMAL(5,4),
    
    -- Status e observações IPI
    status_ipi VARCHAR(20) DEFAULT 'pendente',
    obs_ipi TEXT,
    
    -- === PIS ===
    -- SPED
    sped_cst_pis VARCHAR(2),
    sped_pis_bc DECIMAL(15,2),
    sped_pis_aliquota DECIMAL(5,4),
    sped_pis_valor DECIMAL(15,2),
    
    -- Status e observações PIS
    status_pis VARCHAR(20) DEFAULT 'pendente',
    obs_pis TEXT,
    
    -- === COFINS ===
    -- SPED
    sped_cst_cofins VARCHAR(2),
    sped_cofins_bc DECIMAL(15,2),
    sped_cofins_aliquota DECIMAL(5,4),
    sped_cofins_valor DECIMAL(15,2),
    
    -- Status e observações COFINS
    status_cofins VARCHAR(20) DEFAULT 'pendente',
    obs_cofins TEXT,
    
    -- === CAMPOS DE CONTROLE ===
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usuario_criacao INTEGER REFERENCES usuario(id),
    usuario_atualizacao INTEGER REFERENCES usuario(id),
    
    -- Campos para rastreamento de alterações nos dados SPED
    sped_alterado BOOLEAN DEFAULT FALSE,
    historico_alteracoes JSONB -- Log de alterações feitas
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_empresa ON auditoria_comparativa_impostos(empresa_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_chave_nf ON auditoria_comparativa_impostos(chave_nf);
CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_xml_item ON auditoria_comparativa_impostos(xml_item_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_sped_item ON auditoria_comparativa_impostos(sped_item_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_status ON auditoria_comparativa_impostos(status_auditoria);
CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_match_type ON auditoria_comparativa_impostos(match_type);

-- Tabela para histórico de aprendizado do matching
CREATE TABLE IF NOT EXISTS historico_matching_aprendizado (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,

    -- IDs dos itens
    xml_item_id INTEGER REFERENCES nota_fiscal_item(id) NOT NULL,
    sped_item_id INTEGER REFERENCES item_nota_entrada(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id), -- ID do cliente para aprendizado

    -- Códigos dos produtos para aprendizado futuro
    xml_codigo_produto VARCHAR(50), -- Código do produto no XML
    sped_codigo_produto VARCHAR(50), -- Código do produto no SPED

    -- Dados do matching original
    match_type_original VARCHAR(20),
    confidence_score_original DECIMAL(5,4),

    -- Ação do usuário
    acao_usuario VARCHAR(30) NOT NULL, -- 'aprovado', 'rejeitado', 'corrigido'
    usuario_id INTEGER REFERENCES usuario(id) NOT NULL,
    data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Características dos itens para aprendizado
    caracteristicas_match JSONB, -- Características que levaram ao match
    feedback_usuario TEXT -- Comentário opcional do usuário
);

-- Índices para a tabela de aprendizado
CREATE INDEX IF NOT EXISTS idx_historico_matching_empresa ON historico_matching_aprendizado(empresa_id);
CREATE INDEX IF NOT EXISTS idx_historico_matching_xml_item ON historico_matching_aprendizado(xml_item_id);
CREATE INDEX IF NOT EXISTS idx_historico_matching_sped_item ON historico_matching_aprendizado(sped_item_id);
CREATE INDEX IF NOT EXISTS idx_historico_matching_cliente ON historico_matching_aprendizado(cliente_id);
CREATE INDEX IF NOT EXISTS idx_historico_matching_acao ON historico_matching_aprendizado(acao_usuario);
CREATE INDEX IF NOT EXISTS idx_historico_matching_data ON historico_matching_aprendizado(data_acao);
CREATE INDEX IF NOT EXISTS idx_historico_matching_produtos ON historico_matching_aprendizado(empresa_id, cliente_id, xml_codigo_produto, sped_codigo_produto);
CREATE INDEX IF NOT EXISTS idx_historico_matching_xml_codigo ON historico_matching_aprendizado(xml_codigo_produto);
CREATE INDEX IF NOT EXISTS idx_historico_matching_sped_codigo ON historico_matching_aprendizado(sped_codigo_produto);

-- Trigger para atualizar data_atualizacao automaticamente
CREATE OR REPLACE FUNCTION update_auditoria_comparativa_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.data_atualizacao = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_auditoria_comparativa_timestamp
    BEFORE UPDATE ON auditoria_comparativa_impostos
    FOR EACH ROW
    EXECUTE FUNCTION update_auditoria_comparativa_timestamp();

-- Comentários nas tabelas
COMMENT ON TABLE auditoria_comparativa_impostos IS 'Tabela principal para auditoria comparativa de impostos entre XML e SPED';
COMMENT ON TABLE historico_matching_aprendizado IS 'Histórico de matches aprovados/rejeitados para machine learning';

COMMENT ON COLUMN auditoria_comparativa_impostos.match_type IS 'Tipo de matching: direct, embedding, manual, unmatched_xml, unmatched_sped';
COMMENT ON COLUMN auditoria_comparativa_impostos.confidence_score IS 'Score de confiança do matching (0.0 a 1.0)';
COMMENT ON COLUMN auditoria_comparativa_impostos.match_details IS 'Detalhes JSON do algoritmo de matching utilizado';
COMMENT ON COLUMN auditoria_comparativa_impostos.sped_alterado IS 'Indica se os dados SPED foram alterados pelo usuário';
COMMENT ON COLUMN auditoria_comparativa_impostos.historico_alteracoes IS 'Log JSON das alterações realizadas nos dados SPED';

-- Tabela para armazenar matches manuais temporários (1 SPED para N XMLs)
CREATE TABLE IF NOT EXISTS matching_manual_temporario (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    usuario_id INTEGER REFERENCES usuario(id) NOT NULL,

    -- Item SPED principal (1)
    sped_item_id INTEGER REFERENCES item_nota_entrada(id) NOT NULL,
    sped_descricao VARCHAR(255),
    sped_codigo VARCHAR(50),

    -- Itens XML relacionados (N) - armazenados como array JSON
    xml_items_ids INTEGER[] NOT NULL, -- Array de IDs dos itens XML
    xml_items_data JSONB NOT NULL, -- Dados detalhados dos itens XML

    -- Informações do matching
    justificativa TEXT, -- Justificativa do usuário para o match manual
    confianca_usuario INTEGER DEFAULT 5, -- Escala 1-5 da confiança do usuário

    -- Controle
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_processamento TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pendente', -- pendente, processado, cancelado

    -- Índices para performance
    UNIQUE(sped_item_id, usuario_id) -- Um SPED só pode ter um match manual pendente por usuário
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_matching_manual_temporario_empresa ON matching_manual_temporario(empresa_id);
CREATE INDEX IF NOT EXISTS idx_matching_manual_temporario_usuario ON matching_manual_temporario(usuario_id);
CREATE INDEX IF NOT EXISTS idx_matching_manual_temporario_status ON matching_manual_temporario(status);
CREATE INDEX IF NOT EXISTS idx_matching_manual_temporario_sped ON matching_manual_temporario(sped_item_id);

-- Comentários
COMMENT ON TABLE matching_manual_temporario IS 'Armazena matches manuais temporários antes de serem processados e salvos na auditoria_comparativa_impostos';
COMMENT ON COLUMN matching_manual_temporario.xml_items_ids IS 'Array de IDs dos itens XML que serão pareados com o item SPED';
COMMENT ON COLUMN matching_manual_temporario.xml_items_data IS 'Dados JSON detalhados dos itens XML para facilitar processamento';
COMMENT ON COLUMN matching_manual_temporario.confianca_usuario IS 'Nível de confiança do usuário no match (1=baixa, 5=alta)';

-- Inserir dados de exemplo ou configurações iniciais se necessário
-- (Pode ser adicionado posteriormente conforme necessidade)

COMMIT;

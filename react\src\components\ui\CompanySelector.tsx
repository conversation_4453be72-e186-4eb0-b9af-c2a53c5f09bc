import { useQuery } from '@tanstack/react-query'
import { useFilterStore } from '@/store/filterStore'
import { dashboardService } from '@/services/dashboardService'
import { Dropdown } from '@/components/ui/Dropdown'

export function CompanySelector() {
  const { selectedCompany, setCompany } = useFilterStore()

  const { data: empresas, isLoading } = useQuery({
    queryKey: ['empresas-list'],
    queryFn: () => dashboardService.getEmpresas(),
    staleTime: 5 * 60 * 1000, // 5 minutos
  })

  const options =
    empresas?.map((empresa) => ({
      value: empresa.id.toString(),
      label: empresa.razao_social,
    })) || []

  const handleChange = (value: string | string[]) => {
    if (Array.isArray(value)) return
    setCompany(value ? parseInt(value) : null)
  }

  return (
    <Dropdown
      options={options}
      value={selectedCompany ? selectedCompany.toString() : undefined}
      onChange={handleChange}
      placeholder="Todas as empresas"
      className="w-48"
      loading={isLoading}
    />
  )
}
import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Table } from '@/components/ui/Table'
import { Badge } from '@/components/ui/Badge'
import { Tooltip } from '@/components/ui/Tooltip'
import { Modal } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import { Textarea } from '@/components/ui/Textarea'
import {
  AuditoriaComparativaItem,
  auditoriaComparativaService,
} from '@/services/auditoriaComparativaService'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { mapAuditoriaComparativaItem } from '@/utils/auditoriaComparativa'
import type {
  AuditoriaComparativaFilterState,
  AuditoriaComparativaFilterOptions,
} from '@/hooks/useAuditoriaComparativaFilters'
import { AuditoriaComparativaFilterRow } from './AuditoriaComparativaFilterRow'
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll'
import { GlobalSelectionModal } from './GlobalSelectionModal'
import { useFilterStore } from '@/store/filterStore'

interface TabelaAuditoriaComparativaProps {
  data: AuditoriaComparativaItem[]
  tributo: string
  loading?: boolean
  onGerarAuditoria?: () => void
  onFiltrarPendentes?: () => void
  filtrosPendentes?: boolean
  filters: AuditoriaComparativaFilterState
  options: AuditoriaComparativaFilterOptions
  filtersLoading: boolean
  hasActiveFilters: boolean
  onUpdateFilter: (
    filter: keyof AuditoriaComparativaFilterState,
    values: string[]
  ) => void
  onUpdateTextFilter: (
    filter: keyof AuditoriaComparativaFilterState,
    value: string
  ) => void
  onClearAllFilters: () => void
  hasMore: boolean
  onLoadMore: () => void
  isLoadingMore: boolean
}

interface DetalhesModalProps {
  isOpen: boolean
  onClose: () => void
  item: AuditoriaComparativaItem | null
}

interface AprovarModalProps {
  isOpen: boolean
  onClose: () => void
  count: number
  onConfirm: (observacoes: string) => void
}

interface EditarSpedModalProps {
  isOpen: boolean
  onClose: () => void
  count: number
  tributo: string
  onConfirm: (dados: any) => void
}

function DetalhesModal({ isOpen, onClose, item }: DetalhesModalProps) {
  const empresaId = useSelectedCompany()
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [detalhes, setDetalhes] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  React.useEffect(() => {
    if (isOpen && item && empresaId) {
      setLoading(true)
      auditoriaComparativaService
        .obterDetalhes(item.id, empresaId)
        .then(setDetalhes)
        .catch(console.error)
        .finally(() => setLoading(false))
    }
  }, [isOpen, item, empresaId])

  if (!item) return null

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Detalhes da Auditoria Comparativa"
      size="lg"
    >
      <div className="space-y-6">
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Nota Fiscal */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">
                Nota Fiscal
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Número:
                  </span>
                  <p className="font-medium">{item.numero_nf}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Data Emissão:
                  </span>
                  <p className="font-medium">
                    {new Date(item.data_emissao).toLocaleDateString('pt-BR')}
                  </p>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-600 dark:text-gray-400">
                    Chave:
                  </span>
                  <p className="font-mono text-xs break-all">{item.chave_nf}</p>
                </div>
              </div>
            </div>

            {/* Participante */}
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-3">
                Participante
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="col-span-2">
                  <span className="text-gray-600 dark:text-gray-400">
                    Razão Social:
                  </span>
                  <p className="font-medium">
                    {item.participante_razao_social}
                  </p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    CNPJ:
                  </span>
                  <p className="font-medium">{item.participante_cnpj}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">UF:</span>
                  <p className="font-medium">{item.participante_uf}</p>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-600 dark:text-gray-400">
                    Regime:
                  </span>
                  <p className="font-medium">{item.regime_parceiro}</p>
                </div>
              </div>
            </div>

            {/* Produto */}
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-3">
                Produto
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Código SPED:
                  </span>
                  <p className="font-medium">{item.produto_codigo_sped}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Código Nota:
                  </span>
                  <p className="font-medium">{item.produto_codigo_nota}</p>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-600 dark:text-gray-400">
                    Descrição:
                  </span>
                  <p className="font-medium">{item.produto_descricao}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    NCM NFe:
                  </span>
                  <p className="font-medium">{item.ncm_nfe}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    NCM SPED:
                  </span>
                  <p className="font-medium">{item.ncm_sped}</p>
                </div>
              </div>
            </div>

            {/* Comparação Fiscal */}
            <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
              <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-3">
                Comparação Fiscal
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    CFOP SPED:
                  </span>
                  <p className="font-medium">{item.cfop_sped}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    CFOP Nota:
                  </span>
                  <p className="font-medium">{item.cfop_nota}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    CST SPED:
                  </span>
                  <p className="font-medium">{item.cst_sped}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    CST Nota:
                  </span>
                  <p className="font-medium">{item.cst_nota}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Base Cálculo SPED:
                  </span>
                  <p className="font-medium">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL',
                    }).format(item.base_calculo_sped)}
                  </p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Base Cálculo Nota:
                  </span>
                  <p className="font-medium">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL',
                    }).format(item.base_calculo_nota)}
                  </p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Alíquota SPED:
                  </span>
                  <p className="font-medium">{item.aliquota_sped}%</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Alíquota Nota:
                  </span>
                  <p className="font-medium">{item.aliquota_nota}%</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Valor SPED:
                  </span>
                  <p className="font-medium">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL',
                    }).format(item.valor_sped)}
                  </p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Valor Nota:
                  </span>
                  <p className="font-medium">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL',
                    }).format(item.valor_nota)}
                  </p>
                </div>
              </div>
            </div>

            {/* Status */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                Status
              </h4>
              <div className="flex items-center gap-4">
                <Badge
                  variant={
                    item.match_status === 'matched' ? 'success' : 'warning'
                  }
                >
                  {item.match_status === 'matched' ? 'Matched' : 'Unmatched'}
                </Badge>
                <Badge variant={item.aprovado ? 'success' : 'secondary'}>
                  {item.aprovado ? 'Aprovado' : 'Pendente'}
                </Badge>
              </div>
              {item.observacoes && (
                <div className="mt-3">
                  <span className="text-gray-600 dark:text-gray-400">
                    Observações:
                  </span>
                  <p className="font-medium mt-1">{item.observacoes}</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </Modal>
  )
}

function AprovarModal({
  isOpen,
  onClose,
  count,
  onConfirm,
}: AprovarModalProps) {
  const [observacoes, setObservacoes] = useState('')

  const handleConfirm = () => {
    onConfirm(observacoes)
    setObservacoes('')
    onClose()
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Aprovar Selecionados">
      <div className="space-y-4">
        <p className="text-gray-600 dark:text-gray-400">
          Você está prestes a aprovar {count} registro(s). Esta ação não pode
          ser desfeita.
        </p>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Observações (opcional)
          </label>
          <Textarea
            value={observacoes}
            onChange={(e) => setObservacoes(e.target.value)}
            placeholder="Digite observações sobre a aprovação..."
            rows={3}
          />
        </div>

        <div className="flex justify-end gap-3">
          <Button variant="ghost" onClick={onClose}>
            Cancelar
          </Button>
          <Button variant="primary" onClick={handleConfirm}>
            Aprovar {count} registro(s)
          </Button>
        </div>
      </div>
    </Modal>
  )
}

function EditarSpedModal({
  isOpen,
  onClose,
  count,
  tributo,
  onConfirm,
}: EditarSpedModalProps) {
  const [dados, setDados] = useState({
    cfop: '',
    cst: '',
    base_calculo: '',
    aliquota: '',
    valor: '',
  })

  const handleConfirm = () => {
    onConfirm(dados)
    setDados({ cfop: '', cst: '', base_calculo: '', aliquota: '', valor: '' })
    onClose()
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Editar SPED em Massa - ${tributo.toUpperCase()}`}
    >
      <div className="space-y-4">
        <p className="text-gray-600 dark:text-gray-400">
          Editando dados SPED para {count} registro(s). Deixe em branco os
          campos que não deseja alterar.
        </p>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              CFOP
            </label>
            <Input
              value={dados.cfop}
              onChange={(e) =>
                setDados((prev) => ({ ...prev, cfop: e.target.value }))
              }
              placeholder="Ex: 1102"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              CST
            </label>
            <Input
              value={dados.cst}
              onChange={(e) =>
                setDados((prev) => ({ ...prev, cst: e.target.value }))
              }
              placeholder="Ex: 00"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Base de Cálculo (R$)
            </label>
            <Input
              type="number"
              step="0.01"
              value={dados.base_calculo}
              onChange={(e) =>
                setDados((prev) => ({ ...prev, base_calculo: e.target.value }))
              }
              placeholder="0.00"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Alíquota (%)
            </label>
            <Input
              type="number"
              step="0.01"
              value={dados.aliquota}
              onChange={(e) =>
                setDados((prev) => ({ ...prev, aliquota: e.target.value }))
              }
              placeholder="0.00"
            />
          </div>

          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Valor (R$)
            </label>
            <Input
              type="number"
              step="0.01"
              value={dados.valor}
              onChange={(e) =>
                setDados((prev) => ({ ...prev, valor: e.target.value }))
              }
              placeholder="0.00"
            />
          </div>
        </div>

        <div className="flex justify-end gap-3">
          <Button variant="ghost" onClick={onClose}>
            Cancelar
          </Button>
          <Button variant="primary" onClick={handleConfirm}>
            Aplicar Alterações
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export function TabelaAuditoriaComparativa({
  data,
  tributo,
  loading,
  onGerarAuditoria,
  onFiltrarPendentes,
  filtrosPendentes,
  filters,
  options,
  filtersLoading,
  hasActiveFilters,
  onUpdateFilter,
  onUpdateTextFilter,
  onClearAllFilters,
  hasMore,
  onLoadMore,
  isLoadingMore,
}: TabelaAuditoriaComparativaProps) {
  const [selectedIds, setSelectedIds] = useState<number[]>([])
  const [detalhesModal, setDetalhesModal] = useState<{
    isOpen: boolean
    item: AuditoriaComparativaItem | null
  }>({
    isOpen: false,
    item: null,
  })
  const [aprovarModal, setAprovarModal] = useState(false)
  const [editarSpedModal, setEditarSpedModal] = useState(false)
  const [globalSelectionModal, setGlobalSelectionModal] = useState<{
    isOpen: boolean
    actionType: 'approve' | 'edit'
  }>({ isOpen: false, actionType: 'approve' })
  const [actionScope, setActionScope] = useState<'selected' | 'all'>('selected')
  const [totalFilteredCount, setTotalFilteredCount] = useState(data.length)

  const queryClient = useQueryClient()
  const empresaId = useSelectedCompany()
  const { selectedYear, selectedMonth } = useFilterStore()

  // Hook de scroll infinito
  const { loadingRef } = useInfiniteScroll({
    hasMore,
    isLoading: isLoadingMore,
    onLoadMore,
  })

  const aprovarMutation = useMutation({
    mutationFn: async ({
      ids,
      observacoes,
      applyToAll,
    }: {
      ids: number[]
      observacoes?: string
      applyToAll?: boolean
    }) => {
      if (applyToAll) {
        const response = await auditoriaComparativaService.buscarPorPeriodo({
          empresaId,
          mes: selectedMonth,
          ano: selectedYear,
          tributo,
          filtros: filters,
          page: 1,
          pageSize: totalFilteredCount,
        })
        const matches = response.auditorias
          .filter(
            (item) => !item.aprovado && item.xml_item_id && item.sped_item_id
          )
          .map((item) => ({
            xml_item_id: item.xml_item_id!,
            sped_item_id: item.sped_item_id!,
          }))
        if (matches.length === 0) {
          throw new Error('Nenhum match válido encontrado para aprovação')
        }
        return auditoriaComparativaService.aprovarSelecionados({
          matches,
          empresaId,
          tributo,
          feedback: observacoes,
        })
      }
      if (ids.length === 1) {
        const item = data.find((i) => i.id === ids[0])
        if (!item) {
          throw new Error('Item não encontrado')
        }
        if (!empresaId) {
          throw new Error('Empresa não selecionada')
        }
        if (!item.xml_item_id || !item.sped_item_id) {
          throw new Error('Dados de match incompletos')
        }
        return auditoriaComparativaService.aprovarItem(
          item.xml_item_id,
          item.sped_item_id,
          empresaId,
          tributo,
          observacoes
        )
      }
      if (!empresaId) {
        throw new Error('Empresa não selecionada')
      }
      const itensSelecionados = data.filter((item) => ids.includes(item.id))
      const matches = itensSelecionados
        .filter((item) => item.xml_item_id && item.sped_item_id)
        .map((item) => ({
          xml_item_id: item.xml_item_id!,
          sped_item_id: item.sped_item_id!,
        }))
      if (matches.length === 0) {
        throw new Error('Nenhum match válido encontrado para aprovação')
      }
      return auditoriaComparativaService.aprovarSelecionados({
        matches,
        empresaId,
        tributo,
        feedback: observacoes,
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auditoria-comparativa'] })
      setSelectedIds([])
      setActionScope('selected')
    },
  })

  const editarSpedMutation = useMutation({
    mutationFn: async ({
      ids,
      dados,
      applyToAll,
    }: {
      ids: number[]
      dados: any
      applyToAll?: boolean
    }) => {
      if (!empresaId) {
        throw new Error('Empresa não selecionada')
      }
      if (applyToAll) {
        if (!empresaId) {
          throw new Error('Empresa não selecionada')
        }
        const response = await auditoriaComparativaService.buscarPorPeriodo({
          empresaId,
          mes: selectedMonth,
          ano: selectedYear,
          tributo,
          filtros: filters,
          page: 1,
          pageSize: totalFilteredCount,
        })
        const allIds = response.auditorias.map((item) => item.id)
        return auditoriaComparativaService.editarSpedMassa({
          ids: allIds,
          empresaId,
          tributo,
          dados,
        })
      }
      return auditoriaComparativaService.editarSpedMassa({
        ids,
        empresaId,
        tributo,
        dados,
      })    
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auditoria-comparativa'] })
      setSelectedIds([])
      setActionScope('selected')
    },
  })

  const formatCurrency = (value: number | null | undefined) => {
    if (value === null || value === undefined || isNaN(Number(value))) {
      return 'R$ 0,00'
    }
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(Number(value))
  }

  const toggleSelectAll = () => {
    const selectableItems = data.filter((item) => !item.aprovado)
    const selectableIds = selectableItems.map((item) => item.id)

    if (
      selectedIds.length === selectableIds.length &&
      selectableIds.length > 0
    ) {
      setSelectedIds([])
    } else {
      setSelectedIds(selectableIds)
    }
  }

  const toggleSelectRow = (id: number) => {
    const item = data.find((item) => item.id === id)

    if (item?.aprovado) {
      return
    }

    setSelectedIds((prev) =>
      prev.includes(id)
        ? prev.filter((selectedId) => selectedId !== id)
        : [...prev, id]
    )
  }

  const fetchTotalFiltered = async () => {
    if (!empresaId) return data.length
    try {
      const response = await auditoriaComparativaService.buscarPorPeriodo({
        empresaId,
        mes: selectedMonth,
        ano: selectedYear,
        tributo,
        filtros: filters,
        page: 1,
        pageSize: 1,
      })
      return response.total_registros || data.length
    } catch (err) {
      console.error('Erro ao obter contagem filtrada', err)
      return data.length
    }
  }

  const handleAprovarClick = async () => {
    const total = await fetchTotalFiltered()
    setTotalFilteredCount(total)
    if (total > data.length) {
      setGlobalSelectionModal({ isOpen: true, actionType: 'approve' })
    } else {
      setActionScope('selected')
      setAprovarModal(true)
    }
  }

  const handleEditarSpedClick = async () => {
    const total = await fetchTotalFiltered()
    setTotalFilteredCount(total)
    if (total > data.length) {
      setGlobalSelectionModal({ isOpen: true, actionType: 'edit' })
    } else {
      setActionScope('selected')
      setEditarSpedModal(true)
    }
  }

  const handleAprovar = (observacoes: string) => {
    aprovarMutation.mutate({
      ids: selectedIds,
      observacoes,
      applyToAll: actionScope === 'all',
    })
  }

  const handleEditarSped = (dados: any) => {
    editarSpedMutation.mutate({
      ids: selectedIds,
      dados,
      applyToAll: actionScope === 'all',
    })
  }

  // Função para mapear dados do backend para o formato esperado
  const mapearDados = (record: AuditoriaComparativaItem) => {
    return mapAuditoriaComparativaItem(record, tributo)
  }

  // Função para gerar colunas baseado no tributo
  const getColumnsForTributo = (tributo: string) => {
    // Colunas em ordem idêntica ao frontend legado
    const columns = [
      {
        key: 'select',
        title: (
          <input
            type="checkbox"
            checked={
              selectedIds.length ===
                data.filter((item) => !item.aprovado).length &&
              data.filter((item) => !item.aprovado).length > 0
            }
            onChange={toggleSelectAll}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        ),
        width: '40px',
        render: (_, record: AuditoriaComparativaItem) => {
          const isApproved = record.aprovado

          return (
            <input
              type="checkbox"
              checked={selectedIds.includes(record.id)}
              onChange={() => toggleSelectRow(record.id)}
              disabled={isApproved}
              className={`rounded border-gray-300 text-primary-600 focus:ring-primary-500 ${
                isApproved ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              title={isApproved ? 'Este registro já foi aprovado' : ''}
            />
          )
        },
      },
      {
        key: 'numero_nf',
        title: 'Nota Fiscal',
        width: '80px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="font-medium text-sm">{record.numero_nf}</span>
        ),
      },
      {
        key: 'data_emissao',
        title: 'Emissão',
        width: '90px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm">
            {new Date(record.data_emissao).toLocaleDateString('pt-BR')}
          </span>
        ),
      },
      {
        key: 'participante',
        title: 'Nome/Razão Social',
        width: '150px',
        render: (_, record: AuditoriaComparativaItem) => (
          <Tooltip
            content={`${record.participante_razao_social} - ${record.participante_cnpj}`}
          >
            <div>
              <div className="font-medium truncate max-w-xs text-sm">
                {record.participante_razao_social}
              </div>
            </div>
          </Tooltip>
        ),
      },
      {
        key: 'uf',
        title: 'UF',
        width: '60px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm">{record.participante_uf}</span>
        ),
      },
      {
        key: 'regime',
        title: 'Regime Parceiro',
        width: '100px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm">{record.regime_parceiro}</span>
        ),
      },
      {
        key: 'produto_codigo_sped',
        title: 'PRODUTO CÓDIGO SPED',
        width: '120px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm font-mono">
            {record.produto_codigo_sped || '-'}
          </span>
        ),
      },
      {
        key: 'produto_codigo_nota',
        title: 'PRODUTO CÓDIGO NOTA',
        width: '120px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm font-mono">
            {record.produto_codigo_nota || '-'}
          </span>
        ),
      },
      {
        key: 'descricao',
        title: 'Descrição',
        width: '200px',
        render: (_, record: AuditoriaComparativaItem) => (
          <Tooltip content={record.produto_descricao || '-'}>
            <div className="text-sm truncate max-w-xs">
              {record.produto_descricao || '-'}
            </div>
          </Tooltip>
        ),
      },
      {
        key: 'ncm_nfe',
        title: 'NCM NFe',
        width: '80px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm font-mono">{record.ncm_nfe || '-'}</span>
        ),
      },
      {
        key: 'ncm_sped',
        title: 'NCM SPED',
        width: '80px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm font-mono">{record.ncm_sped || '-'}</span>
        ),
      },
      {
        key: 'tipo',
        title: 'Tipo',
        width: '80px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm">{record.tipo || '-'}</span>
        ),
      },
      {
        key: 'descricao_tipo',
        title: 'Descrição Tipo',
        width: '150px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm">{record.descricao_tipo || '-'}</span>
        ),
      },
      {
        key: 'produto_nota',
        title: 'Produto Nota',
        width: '100px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span className="text-sm">{record.produto_nota || '-'}</span>
        ),
      },
    ]

    // Adiciona colunas específicas para ICMS e ICMS-ST antes dos CFOPs
    if (tributo === 'icms' || tributo === 'icms_st') {
      columns.push(
        {
          key: 'origem_sped',
          title: 'Origem SPED',
          width: '80px',
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">{record.origem_sped || '-'}</span>
          ),
        },
        {
          key: 'origem_nota',
          title: 'Origem Nota',
          width: '80px',
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">{record.origem_nota || '-'}</span>
          ),
        }
      )
    }

    // Adicionar colunas de CFOP logo após origens
    columns.push(
      {
        key: 'cfop_sped',
        title: 'CFOP SPED',
        width: '80px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span
            className={`text-sm font-mono ${record.cfop_sped !== record.cfop_nota ? 'text-red-600 font-medium' : ''}`}
          >
            {record.cfop_sped || '-'}
          </span>
        ),
      },
      {
        key: 'cfop_nota',
        title: 'CFOP Nota',
        width: '80px',
        render: (_, record: AuditoriaComparativaItem) => (
          <span
            className={`text-sm font-mono ${record.cfop_sped !== record.cfop_nota ? 'text-blue-600 font-medium' : ''}`}
          >
            {record.cfop_nota || '-'}
          </span>
        ),
      }
    )

    // Colunas específicas para PIS/COFINS
    if (tributo === 'pis' || tributo === 'cofins' || tributo === 'pis_cofins') {
      columns.push(
        {
          key: 'cst_pis_sped',
          title: 'CST PIS SPED',
          width: '80px',
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">{record.tributos?.pis?.cst || '-'}</span>
          ),
        },
        {
          key: 'cst_cofins_sped',
          title: 'CST COFINS SPED',
          width: '80px',
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {record.tributos?.cofins?.cst || '-'}
            </span>
          ),
        },
        {
          key: 'cst_pis_nota',
          title: 'CST PIS Nota',
          width: '80px',
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">{record.xml_data?.pis_cst || '-'}</span>
          ),
        },
        {
          key: 'cst_cofins_nota',
          title: 'CST COFINS Nota',
          width: '80px',
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {record.xml_data?.cofins_cst || '-'}
            </span>
          ),
        },
        // Bases de cálculo PIS/COFINS
        {
          key: 'base_pis_sped',
          title: 'Base PIS SPED R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {formatCurrency(record.tributos?.pis?.bc || 0)}
            </span>
          ),
        },
        {
          key: 'base_cofins_sped',
          title: 'Base COFINS SPED R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {formatCurrency(record.tributos?.cofins?.bc || 0)}
            </span>
          ),
        },
        {
          key: 'base_pis_nota',
          title: 'Base PIS Nota R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {formatCurrency(record.xml_data?.pis_bc || 0)}
            </span>
          ),
        },
        {
          key: 'base_cofins_nota',
          title: 'Base COFINS Nota R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {formatCurrency(record.xml_data?.cofins_bc || 0)}
            </span>
          ),
        },
        // Alíquotas PIS/COFINS
        {
          key: 'aliq_pis_sped',
          title: 'Alíq PIS SPED %',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const aliquota = record.tributos?.pis?.aliquota ?? 0
            return (
              <span className="text-sm">{Number(aliquota).toFixed(2)}%</span>
            )
          },
        },
        {
          key: 'aliq_cofins_sped',
          title: 'Alíq COFINS SPED %',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const aliquota = record.tributos?.cofins?.aliquota ?? 0
            return (
              <span className="text-sm">{Number(aliquota).toFixed(2)}%</span>
            )
          },
        },
        {
          key: 'aliq_pis_nota',
          title: 'Alíq PIS Nota %',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const aliquota = record.xml_data?.pis_aliquota ?? 0
            return (
              <span className="text-sm">{Number(aliquota).toFixed(2)}%</span>
            )
          },
        },
        {
          key: 'aliq_cofins_nota',
          title: 'Alíq COFINS Nota %',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const aliquota = record.xml_data?.cofins_aliquota ?? 0
            return (
              <span className="text-sm">{Number(aliquota).toFixed(2)}%</span>
            )
          },
        },
        // Valores PIS/COFINS
        {
          key: 'valor_pis_sped',
          title: 'Valor PIS SPED R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {formatCurrency(record.tributos?.pis?.valor || 0)}
            </span>
          ),
        },
        {
          key: 'valor_cofins_sped',
          title: 'Valor COFINS SPED R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {formatCurrency(record.tributos?.cofins?.valor || 0)}
            </span>
          ),
        },
        {
          key: 'valor_pis_nota',
          title: 'Valor PIS Nota R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {formatCurrency(record.xml_data?.pis_valor || 0)}
            </span>
          ),
        },
        {
          key: 'valor_cofins_nota',
          title: 'Valor COFINS Nota R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">
              {formatCurrency(record.xml_data?.cofins_valor || 0)}
            </span>
          ),
        }
      )
    } else {
      // Colunas padrão para outros tributos
      columns.push(
        {
          key: 'cst_sped',
          title: 'CST SPED',
          width: '80px',
          render: (_, record: AuditoriaComparativaItem) => (
            <span
              className={`text-sm ${record.cst_sped !== record.cst_nota ? 'text-red-600 font-medium' : ''}`}
            >
              {record.cst_sped}
            </span>
          ),
        },
        {
          key: 'cst_nota',
          title: 'CST Nota',
          width: '80px',
          render: (_, record: AuditoriaComparativaItem) => (
            <span
              className={`text-sm ${record.cst_sped !== record.cst_nota ? 'text-blue-600 font-medium' : ''}`}
            >
              {record.cst_nota}
            </span>
          ),
        }
      )

      // CSOSN para ICMS e ICMS-ST
      if (tributo === 'icms' || tributo === 'icms_st') {
        columns.push({
          key: 'csosn_nota',
          title: 'CSOSN Nota',
          width: '80px',
          render: (_, record: AuditoriaComparativaItem) => (
            <span className="text-sm">{record.csosn_nota || '-'}</span>
          ),
        })

        // Redução para ICMS e ICMS-ST
        columns.push(
          {
            key: 'reducao_sped',
            title: 'Redução SPED %',
            width: '100px',
            align: 'right' as const,
            render: (_, record: AuditoriaComparativaItem) => {
              const reducao = record.reducao_sped ?? 0
              return (
                <span className="text-sm">{Number(reducao).toFixed(2)}%</span>
              )
            },
          },
          {
            key: 'reducao_nota',
            title: 'Redução Nota %',
            width: '100px',
            align: 'right' as const,
            render: (_, record: AuditoriaComparativaItem) => {
              const reducao = record.reducao_nota ?? 0
              return (
                <span className="text-sm">{Number(reducao).toFixed(2)}%</span>
              )
            },
          }
        )

        // MVA para ICMS-ST
        if (tributo === 'icms_st') {
          columns.push({
            key: 'mva_nota',
            title: 'MVA% Nota',
            width: '100px',
            align: 'right' as const,
            render: (_, record: AuditoriaComparativaItem) => {
              const mva = record.mva_nota ?? 0
              return <span className="text-sm">{Number(mva).toFixed(2)}%</span>
            },
          })
        }
      }

      // Base de cálculo, alíquota e valor padrão
      columns.push(
        {
          key: 'base_sped',
          title: 'Base SPED R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const isDifferent =
              Math.abs(record.base_calculo_sped - record.base_calculo_nota) >
              0.01
            return (
              <span
                className={`text-sm ${isDifferent ? 'text-red-600 font-medium' : ''}`}
              >
                {formatCurrency(record.base_calculo_sped)}
              </span>
            )
          },
        },
        {
          key: 'base_nota',
          title: 'Base Nota R$',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const isDifferent =
              Math.abs(record.base_calculo_sped - record.base_calculo_nota) >
              0.01
            return (
              <span
                className={`text-sm ${isDifferent ? 'text-blue-600 font-medium' : ''}`}
              >
                {formatCurrency(record.base_calculo_nota)}
              </span>
            )
          },
        },
        {
          key: 'aliquota_sped',
          title: 'Alíquota SPED %',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const aliquotaSped = record.aliquota_sped ?? 0
            const aliquotaNota = record.aliquota_nota ?? 0
            const isDifferent = Math.abs(aliquotaSped - aliquotaNota) > 0.01
            return (
              <span
                className={`text-sm ${isDifferent ? 'text-red-600 font-medium' : ''}`}
              >
                {Number(aliquotaSped).toFixed(2)}%
              </span>
            )
          },
        },
        {
          key: 'aliquota_nota',
          title: 'Alíquota Nota %',
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const aliquotaSped = record.aliquota_sped ?? 0
            const aliquotaNota = record.aliquota_nota ?? 0
            const isDifferent = Math.abs(aliquotaSped - aliquotaNota) > 0.01
            return (
              <span
                className={`text-sm ${isDifferent ? 'text-blue-600 font-medium' : ''}`}
              >
                {Number(aliquotaNota).toFixed(2)}%
              </span>
            )
          },
        },
        {
          key: 'valor_sped',
          title: `${getTributoDisplayName(tributo)} SPED R$`,
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const isDifferent =
              Math.abs(record.valor_sped - record.valor_nota) > 0.01
            return (
              <span
                className={`text-sm ${isDifferent ? 'text-red-600 font-medium' : ''}`}
              >
                {formatCurrency(record.valor_sped)}
              </span>
            )
          },
        },
        {
          key: 'valor_nota',
          title: `${getTributoDisplayName(tributo)} Nota R$`,
          width: '100px',
          align: 'right' as const,
          render: (_, record: AuditoriaComparativaItem) => {
            const isDifferent =
              Math.abs(record.valor_sped - record.valor_nota) > 0.01
            return (
              <span
                className={`text-sm ${isDifferent ? 'text-blue-600 font-medium' : ''}`}
              >
                {formatCurrency(record.valor_nota)}
              </span>
            )
          },
        }
      )
    }

    // Colunas finais
    columns.push(
      {
        key: 'match',
        title: 'Match',
        width: '100px',
        render: (_, record: AuditoriaComparativaItem) => {
          const badges: Record<string, { label: string; variant: any }> = {
            direct: { label: 'Direto', variant: 'success' },
            embedding: { label: 'IA', variant: 'primary' },
            manual: { label: 'Manual', variant: 'info' },
            unmatched_xml: { label: 'XML sem par', variant: 'warning' },
            unmatched_sped: { label: 'SPED sem par', variant: 'warning' },
          }
          const info = badges[record.match_type || ''] || {
            label: record.match_status === 'matched' ? 'Match' : 'Unmatched',
            variant: record.match_status === 'matched' ? 'success' : 'warning',
          }
          return (
            <div className="space-y-1 text-center">
              <Badge variant={info.variant} size="sm">
                {info.label}
              </Badge>
              {record.confidence_score != null &&
                record.confidence_score > 0 && (
                  <div className="text-xs text-gray-500">
                    {Math.round(record.confidence_score * 100)}%
                  </div>
                )}
            </div>
          )
        },
      },
      {
        key: 'status',
        title: 'Status',
        width: '80px',
        render: (_, record: AuditoriaComparativaItem) => (
          <Badge variant={record.aprovado ? 'success' : 'secondary'} size="sm">
            {record.aprovado ? 'Aprovado' : 'Pendente'}
          </Badge>
        ),
      },
      {
        key: 'acoes',
        title: 'Ações',
        width: '100px',
        render: (_, record: AuditoriaComparativaItem) => (
          <div className="flex gap-1">
            <Tooltip content="Detalhes da Análise">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDetalhesModal({ isOpen: true, item: record })}
                icon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                }
              />
            </Tooltip>

            <Tooltip content="Regras: Análise">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Abrir modal de regras específico para este item
                  console.log('Abrir regras para item:', record.id)
                }}
                icon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 10h16M4 14h16M4 18h16"
                    />
                  </svg>
                }
              />
            </Tooltip>

            {!record.aprovado && (
              <Tooltip content="Aprovar">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedIds([record.id])
                    setAprovarModal(true)
                  }}
                  icon={
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  }
                />
              </Tooltip>
            )}
          </div>
        ),
      }
    )

    return columns
  }

  const getTributoDisplayName = (tipo: string) => {
    const names: Record<string, string> = {
      icms: 'ICMS',
      icms_st: 'ICMS-ST',
      ipi: 'IPI',
      pis: 'PIS',
      cofins: 'COFINS',
    }
    return names[tipo] || tipo.toUpperCase()
  }

  const columns = getColumnsForTributo(tributo)

  return (
    <div className="space-y-4">
      {/* Ações em massa */}
      <Card className="p-4">
        <div className="flex flex-wrap gap-2 items-center justify-between">
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filtrosPendentes ? 'warning' : 'outline'}
              size="sm"
              onClick={onFiltrarPendentes}
              icon={
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
                  />
                </svg>
              }
            >
              {filtrosPendentes ? 'Mostrando Pendentes' : 'Filtrar Pendentes'}
            </Button>

            <Button
              variant="success"
              size="sm"
              onClick={handleAprovarClick}
              disabled={selectedIds.length === 0}
              loading={aprovarMutation.isPending}
              icon={
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              }
            >
              Aprovar Selecionados ({selectedIds.length})
            </Button>

            <Button
              variant="warning"
              size="sm"
              onClick={handleEditarSpedClick}
              disabled={selectedIds.length === 0}
              loading={editarSpedMutation.isPending}
              icon={
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
              }
            >
              Editar SPED em Massa
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const selectableItems = data.filter((item) => !item.aprovado)
                setSelectedIds(selectableItems.map((item) => item.id))
              }}
              icon={
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              }
            >
              Selecionar Todos
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedIds([])}
              disabled={selectedIds.length === 0}
              icon={
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              }
            >
              Limpar Seleção
            </Button>
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400">
            {data.length} registros • {selectedIds.length} selecionados
          </div>
        </div>
      </Card>

      {/* Tabela */}
      <Card>
        <Table
          columns={columns}
          data={data.map(mapearDados)}
          loading={loading}
          emptyMessage="Nenhum registro encontrado"
          showTopScrollbar={true} // Habilitar scrollbar superior
          filterRow={
            <AuditoriaComparativaFilterRow
              columns={columns}
              filters={filters}
              options={options}
              isLoading={filtersLoading}
              hasActiveFilters={hasActiveFilters}
              onUpdateFilter={onUpdateFilter}
              onUpdateTextFilter={onUpdateTextFilter}
              onClearAllFilters={onClearAllFilters}
            />
          }
        />
        {hasMore && (
          <div ref={loadingRef} className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
            <span className="ml-2 text-sm text-gray-600">
              Carregando mais...
            </span>
          </div>
        )}
      </Card>

      {/* Modais */}
      <DetalhesModal
        isOpen={detalhesModal.isOpen}
        onClose={() => setDetalhesModal({ isOpen: false, item: null })}
        item={detalhesModal.item}
      />

      <AprovarModal
        isOpen={aprovarModal}
        onClose={() => setAprovarModal(false)}
        count={actionScope === 'all' ? totalFilteredCount : selectedIds.length}
        onConfirm={handleAprovar}
      />

      <EditarSpedModal
        isOpen={editarSpedModal}
        onClose={() => setEditarSpedModal(false)}
        count={actionScope === 'all' ? totalFilteredCount : selectedIds.length}
        tributo={tributo}
        onConfirm={handleEditarSped}
      />

      <GlobalSelectionModal
        isOpen={globalSelectionModal.isOpen}
        onClose={() =>
          setGlobalSelectionModal({ ...globalSelectionModal, isOpen: false })
        }
        totalFilteredCount={totalFilteredCount}
        visibleCount={data.length}
        selectedCount={selectedIds.length}
        actionType={globalSelectionModal.actionType}
        onConfirm={(applyToAll) => {
          setActionScope(applyToAll ? 'all' : 'selected')
          setGlobalSelectionModal({ ...globalSelectionModal, isOpen: false })
          if (globalSelectionModal.actionType === 'approve') {
            setAprovarModal(true)
          } else {
            setEditarSpedModal(true)
          }
        }}
      />
    </div>
  )
}
-- <PERSON><PERSON><PERSON> colunas para NCM e CFOP na tabela de resultados da auditoria
-- Permite uma auditoria mais de<PERSON>hada, comparando não apenas valores, mas também NCM e CFOP.

ALTER TABLE auditoria_resultado
ADD COLUMN ncm_nota VARCHAR(20),
ADD COLUMN ncm_cenario VARCHAR(20),
ADD COLUMN cfop_nota VARCHAR(10),
ADD COLUMN cfop_cenario VARCHAR(10),
ADD COLUMN inconsistencia_ncm BOOLEAN DEFAULT FALSE,
ADD COLUMN inconsistencia_cfop BOOLEAN DEFAULT FALSE;

COMMENT ON COLUMN auditoria_resultado.ncm_nota IS 'NCM do item na nota fiscal.';
COMMENT ON COLUMN auditoria_resultado.ncm_cenario IS 'NCM do cenário aplicado na auditoria.';
COMMENT ON COLUMN auditoria_resultado.cfop_nota IS 'CFOP do item na nota fiscal.';
COMMENT ON COLUMN auditoria_resultado.cfop_cenario IS 'CFOP do cenário aplicado na auditoria.';
COMMENT ON COLUMN auditoria_resultado.inconsistencia_ncm IS 'Indica se houve divergência entre o NCM da nota e do cenário.';
COMMENT ON COLUMN auditoria_resultado.inconsistencia_cfop IS 'Indica se houve divergência entre o CFOP da nota e do cenário.';

# BLOCO HTTPS (PRINCIPAL)
server {
    listen 443 ssl;
    server_name www.audittei.com.br audittei.com.br;

    ssl_certificate /etc/letsencrypt/live/www.audittei.com.br/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.audittei.com.br/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # Novo frontend React Fiscal
    location ^~ /fiscal {
        root /home/<USER>/audittei/front/fiscal;
        index index.html;
        try_files $uri $uri/ /fiscal/index.html;
        add_header Cache-Control "public";
        
        # Configurações de cache para arquivos estáticos
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }

    location = /portal {
       return 301 /portal/;
    }

    location ^~ /portal/ {
       root /home/<USER>/audittei/front/;
       index index.html;
       try_files $uri $uri/ /portal/index.html;
    }

    location ^~ /api/autenticacao/ {
       proxy_pass http://127.0.0.1:3334/;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
       proxy_redirect off;
    }

    location = /nfse {
       return 301 /nfse/;
    }

    location ^~ /nfse/ {
       root /home/<USER>/audittei/front/;
       index index.html;
       try_files $uri $uri/ /nfse/index.html;
    }

    location ^~ /api/nfse/ {
       proxy_pass http://127.0.0.1:8000/;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
       proxy_redirect off;
    }

    location ^~ /api/nfse/ws/ {
       proxy_pass http://127.0.0.1:8000/;
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
       proxy_redirect off;
    }

    # Flask + API + JS router
    location /fiscal/ {
        proxy_pass http://localhost:5000/fiscal/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Script-Name /fiscal;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_buffering off;
        client_max_body_size 100M;
    }

    # WebSocket para o fiscal
    location /fiscal/socket.io {
        proxy_pass http://localhost:5000/fiscal/socket.io;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_buffering off;
    }

    # Aplicação Contábil
    location /contabil/static/ {
        alias /home/<USER>/audittei/front/conciliador/front/static/;
        expires 30d;
        add_header Cache-Control "public";
        access_log off;
    }

    location /contabil/ {
        proxy_pass http://localhost:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Script-Name /contabil;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_buffering off;
        client_max_body_size 100M;
    }

    # Landing page em React/Next.js
    location / {
        proxy_pass http://localhost:3000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    access_log /var/log/nginx/audittei.access.log;
    error_log /var/log/nginx/audittei.error.log;
}

# BLOCO HTTP (REDIRECIONA PARA HTTPS)
server {
    listen 80;
    server_name www.audittei.com.br audittei.com.br;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 301 https://$host$request_uri;
    }

    access_log /var/log/nginx/audittei.access.log;
    error_log /var/log/nginx/audittei.error.log;
}

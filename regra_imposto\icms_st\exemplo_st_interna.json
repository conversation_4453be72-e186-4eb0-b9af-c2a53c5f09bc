// ALém do óbvio precisamos salvar na base de dados, as informações de descrição, segmento, vigencia_inicla, vigencia_final,  base_legal_st, aplicabilidade e não aplicabilidade.

{
    "registros": 3,
    "resposta": [
        {
            "codigo": "274143",
            "sigla_estado": "SP",
            // pode vir assim, pode vir assim 09.01, ou completa
            "ncm": "0901",
            // cest do produto (precisamos verificar)
            "cest": "17.096.00",
            "descricao": "Café torrado e moído, em embalagens de conteúdo inferior ou igual a 2 kg, exceto os classificados nos CEST 17.096.04 e 17.096.05",
            "observacao": "",
            "segmento": "Produtos da indústria alimentícia",
            // aliquota
            "aliquota_interna": "18.00",
            "fundo_pobreza": "",
            // mva se a aliquota for 18
            "mva": "30.55",
            "mva_ajustada": "",
            // mva se a aliquota for 4
            "mva_ajustada_4": "52.84",
            "mva_ajustada_7": "",
            "mva_ajustada_12": "40.10",
            "mva_positiva": "",
            "mva_negativa": "",
            "mva_neutra": "",
            "vigencia_inicial": "01/08/2023",
            "vigencia_final": "30/04/2026",
            "base_legal_st": "Art. 313-W e 313-X do RICMS/SP",
            "data_efeito_st": "01/11/2013",
            "base_calculo": "MVA: Portaria SRE Nº 43 DE 29/06/2023 - Vigência de 01/08/2023 a 30/04/2026.",
            "prazo_recolhimento": "",
            "aplicabilidade": "Artigo 313-W do RICMS/SP",
            "nao_aplicabilidade": "Artigo 264 do RICMS/SP",
            "variacao_mva": "",
            "reducao_mva": ""
        },
        {
            "codigo": "274144",
            "sigla_estado": "SP",
            "ncm": "0901",
            // outro cest (outro registro no banco)
            "cest": "17.096.04",
            "descricao": "Café torrado e moído, em cápsulas, exceto os descritos no CEST 17.096.05",
            "observacao": "",
            "segmento": "Produtos da indústria alimentícia",
            "aliquota_interna": "18.00",
            "fundo_pobreza": "",
            "mva": "39.76",
            "mva_ajustada": "",
            "mva_ajustada_4": "63.62",
            "mva_ajustada_7": "",
            "mva_ajustada_12": "49.99",
            "mva_positiva": "",
            "mva_negativa": "",
            "mva_neutra": "",
            "vigencia_inicial": "01/08/2023",
            "vigencia_final": "30/04/2026",
            "base_legal_st": "Art. 313-W e 313-X do RICMS/SP",
            "data_efeito_st": "01/11/2013",
            "base_calculo": "MVA: Portaria SRE Nº 43 DE 29/06/2023 - Vigência de 01/08/2023 a 30/04/2026.",
            "prazo_recolhimento": "",
            "aplicabilidade": "Artigo 313-W do RICMS/SP",
            "nao_aplicabilidade": "Artigo 264 do RICMS/SP",
            "variacao_mva": "",
            "reducao_mva": ""
        },
        {
            "codigo": "274145",
            "sigla_estado": "SP",
            "ncm": "0901",
            "cest": "17.096.05",
            "descricao": "Café descafeinado torrado e moído, em cápsulas",
            "observacao": "",
            "segmento": "Produtos da indústria alimentícia",
            "aliquota_interna": "18.00",
            "fundo_pobreza": "",
            "mva": "39.36",
            "mva_ajustada": "",
            "mva_ajustada_4": "63.15",
            "mva_ajustada_7": "",
            "mva_ajustada_12": "49.56",
            "mva_positiva": "",
            "mva_negativa": "",
            "mva_neutra": "",
            "vigencia_inicial": "01/08/2023",
            "vigencia_final": "30/04/2026",
            "base_legal_st": "Art. 313-W e 313-X do RICMS/SP",
            "data_efeito_st": "01/11/2013",
            "base_calculo": "MVA: Portaria SRE Nº 43 DE 29/06/2023 - Vigência de 01/08/2023 a 30/04/2026.",
            "prazo_recolhimento": "",
            "aplicabilidade": "Artigo 313-W do RICMS/SP",
            "nao_aplicabilidade": "Artigo 264 do RICMS/SP",
            "variacao_mva": "",
            "reducao_mva": ""
        }
    ]
}
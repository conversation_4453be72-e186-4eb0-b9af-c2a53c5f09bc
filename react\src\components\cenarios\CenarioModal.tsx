import React, { useState, useEffect } from 'react'
import type { CenarioTributo, TipoTributo } from '@/types/cenarios'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { Input, Select, Textarea } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'

interface CenarioModalProps {
  cenario: CenarioTributo | null
  tipoTributo: TipoTributo
  isOpen: boolean
  onClose: () => void
  onSave: (cenario: CenarioTributo) => void
}

export function CenarioModal({ cenario, tipoTributo, isOpen, onClose, onSave }: CenarioModalProps) {
  const [activeTab, setActiveTab] = useState<'cliente' | 'produto' | 'tributo'>('cliente')
  const [formData, setFormData] = useState<any>({})

  useEffect(() => {
    if (cenario) {
      setFormData({
        ...cenario,
        // Garantir que os dados do cliente e produto estejam disponíveis
        cliente: cenario.cliente || {},
        produto: cenario.produto || {}
      })
    }
  }, [cenario])

  if (!isOpen || !cenario) return null

  const handleSave = () => {
    onSave(formData)
    onClose()
  }

  const updateFormData = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateClienteData = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      cliente: {
        ...prev.cliente,
        [field]: value
      }
    }))
  }

  const updateProdutoData = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      produto: {
        ...prev.produto,
        [field]: value
      }
    }))
  }

  const getTributoName = (tipo: TipoTributo): string => {
    const names = {
      icms: 'ICMS',
      icms_st: 'ICMS-ST',
      ipi: 'IPI',
      pis: 'PIS',
      cofins: 'COFINS',
      difal: 'DIFAL'
    }
    return names[tipo]
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Detalhes do Cenário ${getTributoName(tipoTributo)}`}
      size="2xl"
      footer={
        <>
          <Button
            variant="ghost"
            onClick={onClose}
          >
            Cancelar
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            }
            glow
          >
            Salvar Alterações
          </Button>
        </>
      }
    >
      <div className="space-y-6">
        {/* Modern Tabs */}
        <Card className="p-2 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex gap-2">
            <Button
              variant={activeTab === 'cliente' ? 'primary' : 'ghost'}
              size="md"
              onClick={() => setActiveTab('cliente')}
              className="flex-1 justify-center"
              icon={
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                </svg>
              }
              glow={activeTab === 'cliente'}
            >
              Cliente
            </Button>
            
            <Button
              variant={activeTab === 'produto' ? 'primary' : 'ghost'}
              size="md"
              onClick={() => setActiveTab('produto')}
              className="flex-1 justify-center"
              icon={
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM8.5 13a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0z" clipRule="evenodd" />
                </svg>
              }
              glow={activeTab === 'produto'}
            >
              Produto
            </Button>
            
            <Button
              variant={activeTab === 'tributo' ? 'primary' : 'ghost'}
              size="md"
              onClick={() => setActiveTab('tributo')}
              className="flex-1 justify-center"
              icon={
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" clipRule="evenodd" />
                </svg>
              }
              glow={activeTab === 'tributo'}
            >
              Tributo
            </Button>
          </div>
        </Card>

        {/* Tab Content */}
        <div className="min-h-[400px]">
          {activeTab === 'cliente' && (
            <ClienteTab 
              cliente={formData.cliente || {}} 
              onUpdate={updateClienteData}
            />
          )}
          {activeTab === 'produto' && (
            <ProdutoTab 
              produto={formData.produto || {}} 
              cenario={formData}
              onUpdateProduto={updateProdutoData}
              onUpdateCenario={updateFormData}
            />
          )}
          {activeTab === 'tributo' && (
            <TributoTab 
              cenario={formData}
              tipoTributo={tipoTributo}
              onUpdate={updateFormData}
            />
          )}
        </div>
      </div>
    </Modal>
  )
}

// Componente da aba Cliente
function ClienteTab({ cliente, onUpdate }: { cliente: any, onUpdate: (field: string, value: any) => void }) {
  return (
    <Card className="space-y-6" gradient>
      <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 p-4 rounded-xl border border-primary-200 dark:border-primary-700 mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-xl flex items-center justify-center">
            <svg className="w-5 h-5 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
            </svg>
          </div>
          <div>
            <h4 className="text-lg font-bold text-gray-900 dark:text-white gradient-text">
              Dados do Cliente
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Informações do participante da operação
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Razão Social"
          value={cliente.razao_social || cliente.nome || ''}
          onChange={(e) => onUpdate('razao_social', e.target.value)}
          leftIcon={
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm6 0a2 2 0 104 0 2 2 0 00-4 0z" clipRule="evenodd" />
            </svg>
          }
          fullWidth
        />
        
        <Input
          label="CNPJ/CPF"
          value={cliente.cnpj || cliente.cpf || ''}
          onChange={(e) => onUpdate('cnpj', e.target.value)}
          leftIcon={
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm6 0a2 2 0 104 0 2 2 0 00-4 0z" clipRule="evenodd" />
            </svg>
          }
          disabled
          fullWidth
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Inscrição Estadual"
          value={cliente.inscricao_estadual || ''}
          onChange={(e) => onUpdate('inscricao_estadual', e.target.value)}
          leftIcon={
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
            </svg>
          }
          fullWidth
        />
        
        <Input
          label="CNAE"
          value={cliente.cnae || ''}
          onChange={(e) => onUpdate('cnae', e.target.value)}
          leftIcon={
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6z" clipRule="evenodd" />
            </svg>
          }
          fullWidth
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Cidade"
          value={cliente.municipio || ''}
          onChange={(e) => onUpdate('municipio', e.target.value)}
          leftIcon={
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
          }
          fullWidth
        />
        
        <Input
          label="UF"
          value={cliente.uf || ''}
          onChange={(e) => onUpdate('uf', e.target.value)}
          leftIcon={
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
            </svg>
          }
          fullWidth
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Select
          label="Atividade"
          value={cliente.atividade || ''}
          onChange={(e) => onUpdate('atividade', e.target.value)}
          options={[
            { value: '', label: 'Selecione...' },
            { value: 'Indústria ou Equiparado', label: 'Indústria ou Equiparado' },
            { value: 'Comércio Varejista', label: 'Comércio Varejista' },
            { value: 'Comércio Atacadista', label: 'Comércio Atacadista' },
            { value: 'Distribuidor', label: 'Distribuidor' },
            { value: 'Produtor Rural', label: 'Produtor Rural' },
            { value: 'Consumidor Final', label: 'Consumidor Final' },
            { value: 'Órgão Público', label: 'Órgão Público' },
            { value: 'Serviço', label: 'Serviço' },
            { value: 'Não Contribuinte', label: 'Não Contribuinte' }
          ]}
          fullWidth
        />
        
        <Select
          label="Destinação"
          value={cliente.destinacao || ''}
          onChange={(e) => onUpdate('destinacao', e.target.value)}
          options={[
            { value: '', label: 'Selecione...' },
            { value: 'Industrialização', label: 'Industrialização' },
            { value: 'Revenda', label: 'Revenda' },
            { value: 'Ativo Imobilizado', label: 'Ativo Imobilizado' },
            { value: 'Uso e Consumo', label: 'Uso e Consumo' },
            { value: 'Varejista', label: 'Varejista' }
          ]}
          fullWidth
        />
      </div>

      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-700">
        <div className="flex items-center gap-3">
          <input
            type="checkbox"
            checked={cliente.simples_nacional || false}
            onChange={(e) => onUpdate('simples_nacional', e.target.checked)}
            className="h-5 w-5 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-all duration-200"
          />
          <div>
            <label className="text-sm font-medium text-gray-900 dark:text-white">
              🏢 Simples Nacional
            </label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Empresa optante pelo regime tributário do Simples Nacional
            </p>
          </div>
        </div>
      </Card>
    </Card>
  )
}// Componente da aba Produto
function ProdutoTab({ 
  produto, 
  cenario, 
  onUpdateProduto, 
  onUpdateCenario 
}: { 
  produto: any
  cenario: any
  onUpdateProduto: (field: string, value: any) => void
  onUpdateCenario: (field: string, value: any) => void
}) {
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Dados do Produto</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Código
            </label>
            <input
              type="text"
              value={produto.codigo || ''}
              onChange={(e) => onUpdateProduto('codigo', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              readOnly
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Descrição
            </label>
            <input
              type="text"
              value={produto.descricao || ''}
              onChange={(e) => onUpdateProduto('descricao', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              NCM
            </label>
            <input
              type="text"
              value={cenario.ncm || produto.ncm || ''}
              onChange={(e) => onUpdateCenario('ncm', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              CEST
            </label>
            <input
              type="text"
              value={produto.cest || cenario.cest || ''}
              onChange={(e) => onUpdateProduto('cest', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              EX
            </label>
            <input
              type="text"
              value={produto.ex || cenario.ex || ''}
              onChange={(e) => onUpdateProduto('ex', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Unidade
            </label>
            <input
              type="text"
              value={produto.unidade_comercial || produto.unidade || ''}
              onChange={(e) => onUpdateProduto('unidade_comercial', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              CFOP
            </label>
            <input
              type="text"
              value={produto.cfop || cenario.cfop || ''}
              onChange={(e) => onUpdateCenario('cfop', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

// Componente da aba Tributo
function TributoTab({ 
  cenario, 
  tipoTributo, 
  onUpdate 
}: { 
  cenario: any
  tipoTributo: TipoTributo
  onUpdate: (field: string, value: any) => void
}) {
  const renderTributoFields = () => {
    switch (tipoTributo) {
      case 'icms':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  CST
                </label>
                <input
                  type="text"
                  value={cenario.cst || ''}
                  onChange={(e) => onUpdate('cst', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Origem
                </label>
                <input
                  type="text"
                  value={cenario.origem || ''}
                  onChange={(e) => onUpdate('origem', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Alíquota (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.aliquota || 0}
                  onChange={(e) => onUpdate('aliquota', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Redução BC (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.p_red_bc || 0}
                  onChange={(e) => onUpdate('p_red_bc', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Modalidade BC
                </label>
                <input
                  type="text"
                  value={cenario.mod_bc || ''}
                  onChange={(e) => onUpdate('mod_bc', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Percentual Diferimento (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.p_dif || 0}
                  onChange={(e) => onUpdate('p_dif', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
          </div>
        )

      case 'icms_st':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  CST
                </label>
                <input
                  type="text"
                  value={cenario.cst || ''}
                  onChange={(e) => onUpdate('cst', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Origem
                </label>
                <input
                  type="text"
                  value={cenario.origem || ''}
                  onChange={(e) => onUpdate('origem', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Alíquota ICMS (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.aliquota || 0}
                  onChange={(e) => onUpdate('aliquota', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Redução BC ICMS (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.p_red_bc || 0}
                  onChange={(e) => onUpdate('p_red_bc', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  MVA (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.icms_st_p_mva || 0}
                  onChange={(e) => onUpdate('icms_st_p_mva', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Alíquota ICMS-ST (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.icms_st_aliquota || 0}
                  onChange={(e) => onUpdate('icms_st_aliquota', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Redução BC ICMS-ST (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.icms_st_p_red_bc || 0}
                  onChange={(e) => onUpdate('icms_st_p_red_bc', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Modalidade BC ICMS-ST
                </label>
                <input
                  type="text"
                  value={cenario.icms_st_mod_bc || ''}
                  onChange={(e) => onUpdate('icms_st_mod_bc', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
          </div>
        )

      case 'ipi':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  CST
                </label>
                <input
                  type="text"
                  value={cenario.cst || ''}
                  onChange={(e) => onUpdate('cst', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Alíquota (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.aliquota || 0}
                  onChange={(e) => onUpdate('aliquota', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  EX
                </label>
                <input
                  type="text"
                  value={cenario.ex || ''}
                  onChange={(e) => onUpdate('ex', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
          </div>
        )

      case 'pis':
      case 'cofins':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  CST
                </label>
                <input
                  type="text"
                  value={cenario.cst || ''}
                  onChange={(e) => onUpdate('cst', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Alíquota (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.aliquota || 0}
                  onChange={(e) => onUpdate('aliquota', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Redução BC (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.p_red_bc || 0}
                  onChange={(e) => onUpdate('p_red_bc', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
          </div>
        )

      case 'difal':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  CST
                </label>
                <input
                  type="text"
                  value={cenario.cst || ''}
                  onChange={(e) => onUpdate('cst', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Origem
                </label>
                <input
                  type="text"
                  value={cenario.origem || ''}
                  onChange={(e) => onUpdate('origem', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  % ICMS UF Dest
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.p_icms_uf_dest || 0}
                  onChange={(e) => onUpdate('p_icms_uf_dest', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  % ICMS Inter
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.p_icms_inter || 0}
                  onChange={(e) => onUpdate('p_icms_inter', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  % FCP UF Dest
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cenario.p_fcp_uf_dest || 0}
                  onChange={(e) => onUpdate('p_fcp_uf_dest', parseFloat(e.target.value) || 0)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
          </div>
        )

      default:
        return <div>Tipo de tributo não suportado</div>
    }
  }

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Detalhes do {tipoTributo.toUpperCase()}
        </h4>
        {renderTributoFields()}
      </div>
    </div>
  )
}
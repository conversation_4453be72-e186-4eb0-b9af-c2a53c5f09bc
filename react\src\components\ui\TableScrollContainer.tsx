import React, { useRef, useState, useEffect, ReactNode } from 'react'
import { cn } from '@/utils/cn'

interface TableScrollContainerProps {
  children: ReactNode
  className?: string
  containerClassName?: string
}

export function TableScrollContainer({
  children,
  className,
  containerClassName
}: TableScrollContainerProps) {
  const topRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [showTop, setShowTop] = useState(false)
  const contentWidthRef = useRef(0)

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const update = () => {
      const table = container.querySelector('table') as HTMLElement | null
      if (!table) {
        setShowTop(false)
        return
      }
      const contentWidth = table.scrollWidth
      const need = contentWidth > container.clientWidth
      contentWidthRef.current = contentWidth
      setShowTop(need)
    }

    update()

    const resizeObserver = new ResizeObserver(update)
    resizeObserver.observe(container)
    const table = container.querySelector('table')
    if (table) resizeObserver.observe(table)
    return () => resizeObserver.disconnect()
  }, [children])

  useEffect(() => {
    const top = topRef.current
    const bottom = containerRef.current
    if (!top || !bottom || !showTop) return

    const syncTop = () => {
      bottom.scrollLeft = top.scrollLeft
    }
    const syncBottom = () => {
      top.scrollLeft = bottom.scrollLeft
    }
    top.addEventListener('scroll', syncTop)
    bottom.addEventListener('scroll', syncBottom)
    return () => {
      top.removeEventListener('scroll', syncTop)
      bottom.removeEventListener('scroll', syncBottom)
    }
  }, [showTop])

  return (
    <div className={cn('w-full overflow-hidden', className)}>
      {showTop && (
        <div ref={topRef} className="table-top-scrollbar overflow-x-auto">
          <div style={{ width: contentWidthRef.current, height: 1 }} />
        </div>
      )}
      <div ref={containerRef} className={cn('overflow-x-auto', containerClassName)}>
        {children}
      </div>
    </div>
  )
}
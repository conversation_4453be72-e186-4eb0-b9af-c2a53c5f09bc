import api from './authService'
import type { CenarioTributo } from '@/types/cenarios'

interface UpdateStatusRequest {
  empresa_id: number
  escritorio_id: number
  status: string
  data_inicio_vigencia?: string
}

interface BatchUpdateRequest {
  empresa_id: number
  escritorio_id: number
  status: string
  cenario_ids: number[]
}

interface BulkEditRequest {
  empresa_id: number
  escritorio_id: number
  cenario_ids: number[]
  ncm?: string
  cst?: string
  aliquota?: number
}

class CenarioActionsService {
  private mapFilters(filters: any): Record<string, string> {
    const filterMapping: Record<string, string> = {
      cfops: 'cfop',
      ncms: 'ncm',
      csts: 'cst',
      estados: 'uf',
      aliquotas: 'aliquota',
      aliquotas_st: 'aliquota_st',
      reducoes: 'reducao',
      reducoes_st: 'reducao_st',
      mvas: 'mva',
      atividades: 'atividade',
      destinacoes: 'destinacao',
      produto_codigo: 'produto_codigo',
      produto_descricao: 'produto_descricao',
      cliente_razao: 'cliente_razao',
      cliente_cnpj: 'cliente_cnpj',
      origem: 'origem',
      cest: 'cest',
      ex: 'ex',
      status: 'status'
    }

    const params: Record<string, string> = {}
    Object.entries(filters || {}).forEach(([key, value]) => {
      if (Array.isArray(value) && value.length > 0) {
        const backendKey = filterMapping[key] || key
        params[backendKey] = value.join(',')
      } else if (typeof value === 'string' && value.trim()) {
        const backendKey = filterMapping[key] || key
        params[backendKey] = value.trim()
      }
    })
    return params
  }
  /**
   * Atualiza o status de um cenário individual
   */
  async updateCenarioStatus(
    tipoTributo: string,
    cenarioId: number, 
    newStatus: string,
    empresaId: number
  ): Promise<{ success: boolean; message?: string }> {
    // Obter dados do usuário atual
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
    const escritorioId = parseInt(currentUser.escritorio_id || '0')

    if (isNaN(empresaId) || empresaId <= 0) {
      throw new Error('ID da empresa inválido. Por favor, selecione uma empresa válida.')
    }

    if (isNaN(escritorioId) || escritorioId <= 0) {
      throw new Error('ID do escritório não encontrado. Por favor, faça login novamente.')
    }

    const requestData: UpdateStatusRequest = {
      empresa_id: empresaId,
      escritorio_id: escritorioId,
      status: newStatus,
    }

    // Adicionar data de início de vigência automaticamente para status 'producao'
    if (newStatus === 'producao') {
      const today = new Date()
      const dataInicioVigencia = today.toISOString().split('T')[0]
      requestData.data_inicio_vigencia = dataInicioVigencia
    }

    const response = await api.put(
      `/cenarios/${tipoTributo}/${cenarioId}/status`,
      requestData
    )

    return response.data
  }

  /**
   * Atualiza o status de múltiplos cenários
   */
  async batchUpdateStatus(
    tipoTributo: string,
    cenarioIds: number[],
    newStatus: string,
    empresaId: number
  ): Promise<{ success: boolean; message?: string; updated_count?: number; task_id?: string }> {
    // Obter dados do usuário atual
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
    const escritorioId = parseInt(currentUser.escritorio_id || '0')

    if (isNaN(empresaId) || empresaId <= 0) {
      throw new Error('ID da empresa inválido. Por favor, selecione uma empresa válida.')
    }

    if (isNaN(escritorioId) || escritorioId <= 0) {
      throw new Error('ID do escritório não encontrado. Por favor, faça login novamente.')
    }

    const requestData: BatchUpdateRequest = {
      empresa_id: empresaId,
      escritorio_id: escritorioId,
      status: newStatus,
      cenario_ids: cenarioIds,
    }

    const response = await api.put(
      `/cenarios/${tipoTributo}/bulk-status`,
      requestData
    )

    return response.data
  }

  async updateStatusByFilters(
    tipoTributo: string,
    filters: any,
    newStatus: string,
    empresaId: number
  ): Promise<{ success: boolean; message?: string; updated_count?: number; task_id?: string }> {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
    const escritorioId = parseInt(currentUser.escritorio_id || '0')

    const mappedFilters = this.mapFilters(filters)

    const requestData = {
      empresa_id: empresaId,
      escritorio_id: escritorioId,
      status: newStatus,
      apply_to_all: true,
      filters: mappedFilters
    }

    const response = await api.put(
      `/cenarios/${tipoTributo}/bulk-status`,
      requestData
    )

    return response.data
  }

  /**
   * Edita múltiplos cenários
   */
  async bulkEditCenarios(
    tipoTributo: string,
    cenarioIds: number[],
    changes: { ncm?: string; cst?: string; aliquota?: number },
    empresaId: number
  ): Promise<{ success: boolean; message?: string; updated_count?: number }> {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
    const escritorioId = parseInt(currentUser.escritorio_id || '0')

    if (isNaN(empresaId) || empresaId <= 0) {
      throw new Error('ID da empresa inválido. Por favor, selecione uma empresa válida.')
    }

    if (isNaN(escritorioId) || escritorioId <= 0) {
      throw new Error('ID do escritório não encontrado. Por favor, faça login novamente.')
    }

    const requestData: BulkEditRequest = {
      empresa_id: empresaId,
      escritorio_id: escritorioId,
      cenario_ids: cenarioIds,
      ...changes
    }

    const response = await api.put(
      `/cenarios/${tipoTributo}/bulk-edit`,
      requestData
    )

    return response.data
  }

  async bulkEditByFilters(
    tipoTributo: string,
    filters: any,
    changes: { ncm?: string; cst?: string; aliquota?: number },
    empresaId: number
  ): Promise<{ success: boolean; message?: string; updated_count?: number }> {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
    const escritorioId = parseInt(currentUser.escritorio_id || '0')

    const mappedFilters = this.mapFilters(filters)

    const requestData = {
      empresa_id: empresaId,
      escritorio_id: escritorioId,
      cenario_ids: [],
      apply_to_all: true,
      filters: mappedFilters,
      ...changes
    }

    const response = await api.put(
      `/cenarios/${tipoTributo}/bulk-edit`,
      requestData
    )

    return response.data
  }

  /**
   * Atualiza um cenário individual
   */
  async updateCenario(
    tipoTributo: string,
    cenario: CenarioTributo,
    empresaId: number
  ): Promise<{ success: boolean; message?: string; recalculo_realizado?: boolean; tipo_tributo_recalculado?: string }> {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
    const escritorioId = parseInt(currentUser.escritorio_id || '0')

    if (isNaN(empresaId) || empresaId <= 0) {
      throw new Error('ID da empresa inválido. Por favor, selecione uma empresa válida.')
    }

    if (isNaN(escritorioId) || escritorioId <= 0) {
      throw new Error('ID do escritório não encontrado. Por favor, faça login novamente.')
    }

    const requestData = {
      ...cenario,
      empresa_id: empresaId,
      escritorio_id: escritorioId,
    }

    const response = await api.put(
      `/cenarios/${tipoTributo}/${cenario.id}`,
      requestData
    )

    return response.data
  }

  /**
   * Exclui um cenário
   */
  async deleteCenario(
    tipoTributo: string,
    cenarioId: number,
    empresaId: number
  ): Promise<{ success: boolean; message?: string }> {
    // Obter dados do usuário atual
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
    const escritorioId = parseInt(currentUser.escritorio_id || '0')

    const response = await api.delete(
      `/cenarios/${tipoTributo}/${cenarioId}`,
      {
        data: {
          empresa_id: empresaId,
          escritorio_id: escritorioId
        }
      }
    )

    return response.data
  }

  async deleteByFilters(
    tipoTributo: string,
    filters: any,
    empresaId: number
  ): Promise<{ success: boolean; deleted_count?: number }> {
    const mappedFilters = this.mapFilters(filters)
    const params = new URLSearchParams({
      empresa_id: empresaId.toString(),
      direcao: 'saida',
      ...mappedFilters,
      page: '1',
      per_page: '1000'
    })
    const response = await api.get(`/cenarios/${tipoTributo}?${params.toString()}`)
    const ids = (response.data.cenarios || []).map((c: any) => c.id)
    for (const id of ids) {
      await this.deleteCenario(tipoTributo, id, empresaId)
    }
    return { success: true, deleted_count: ids.length }
  }

  /**
   * Envia cenários filtrados para produção (bulk operation)
   */
  async bulkSendToProduction(
    tipoTributo: string,
    empresaId: number
  ): Promise<{ success: boolean; message?: string; updated_count?: number }> {
    // Obter dados do usuário atual
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
    const escritorioId = parseInt(currentUser.escritorio_id || '0')

    const response = await api.post(
      `/cenarios/${tipoTributo}/bulk-to-production`,
      {
        empresa_id: empresaId,
        escritorio_id: escritorioId
      }
    )

    return response.data
  }
}

export const cenarioActionsService = new CenarioActionsService()
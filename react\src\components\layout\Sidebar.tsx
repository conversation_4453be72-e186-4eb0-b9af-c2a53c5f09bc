import React, { useState, useRef, useEffect } from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import { cn } from '@/utils/cn'
import { useSidebar } from '@/hooks/useSidebar'
import { useAuth } from '@/hooks/useAuth'
import { Tooltip } from '@/components/ui/Tooltip'
import { 
  Home, 
  FileText, 
  ArrowRight, 
  ArrowLeft, 
  Workflow, 
  UsersRound, 
  Package, 
  Upload, 
  FileCode, 
  Calculator,
  ChevronDown
} from 'lucide-react'

interface NavigationItem {
  name: string
  href?: string
  icon: React.ReactNode
  children?: Array<{
    name: string
    href: string
    icon: React.ReactNode
  }>
}

const navigation: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/fiscal/dashboard',
    icon: <Home className="w-5 h-5" />,
  },
  {
    name: 'Auditoria',
    icon: <FileText className="w-5 h-5" />,
    children: [
      {
        name: 'Entrada',
        href: '/fiscal/auditoria/entrada',
        icon: <ArrowRight className="w-4 h-4" />,
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        href: '/fiscal/auditoria/saida',
        icon: <ArrowLeft className="w-4 h-4" />,
      },
    ],
  },
  {
    name: 'Cenários',
    href: '/fiscal/cenarios',
    icon: <Workflow className="w-5 h-5" />,
  },
  {
    name: 'Participantes',
    href: '/fiscal/clientes',
    icon: <UsersRound className="w-5 h-5" />,
  },
  {
    name: 'Produtos',
    href: '/fiscal/produtos',
    icon: <Package className="w-5 h-5" />,
  },
  {
    name: 'Importação',
    href: '/fiscal/importacao',
    icon: <Upload className="w-5 h-5" />,
  },
  {
    name: 'Gestão de XMLs',
    href: '/fiscal/gestao-xmls',
    icon: <FileCode className="w-5 h-5" />,
  },
  {
    name: 'Apuração',
    href: '/fiscal/apuracao',
    icon: <Calculator className="w-5 h-5" />,
  },
]

interface DropdownMenuProps {
  children: Array<{
    name: string
    href: string
    icon: React.ReactNode
  }>
  isCollapsed: boolean
  triggerRef: React.RefObject<HTMLButtonElement>
}

function DropdownMenu({ children, isCollapsed, triggerRef }: DropdownMenuProps) {
  const [position, setPosition] = useState({ top: 0, left: 0 })
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect()
      if (isCollapsed) {
        // Posiciona à direita quando colapsada
        setPosition({
          top: rect.top,
          left: rect.right + 8,
        })
      } else {
        // Posiciona embaixo quando expandida
        setPosition({
          top: rect.bottom + 4,
          left: rect.left,
        })
      }
    }
  }, [isCollapsed])

  return (
    <div
      ref={dropdownRef}
      className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-2 min-w-[160px] animate-in fade-in-0 zoom-in-95 duration-200"
      style={{
        top: position.top,
        left: position.left,
      }}
    >
      {children.map((child) => (
        <NavLink
          key={child.name}
          to={child.href}
          className={({ isActive }) =>
            cn(
              'flex items-center space-x-3 px-4 py-2 text-sm font-medium transition-colors hover:bg-gray-100 dark:hover:bg-gray-700',
              isActive
                ? 'text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/20'
                : 'text-gray-700 dark:text-gray-300'
            )
          }
        >
          {child.icon}
          <span>{child.name}</span>
        </NavLink>
      ))}
    </div>
  )
}

import { User, Building2, Users, LogOut } from 'lucide-react'

export function Sidebar() {
  const { isCollapsed } = useSidebar()
  const { user, logout } = useAuth()
  const location = useLocation()
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const hoverTimeoutRef = useRef<number | null>(null)

  const auditoriaRef = useRef<HTMLButtonElement>(null)
  const userMenuRef = useRef<HTMLButtonElement>(null)

  const handleLogout = () => {
    logout()
    window.location.href = 'https://www.audittei.com.br/portal'
  }

  const handleDropdownToggle = (itemName: string) => {
    if (isCollapsed) return
    setOpenDropdown(openDropdown === itemName ? null : itemName)
  }

  const handleMouseEnter = (itemName: string) => {
    if (isCollapsed) {
      if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current)
      hoverTimeoutRef.current = setTimeout(() => setOpenDropdown(itemName), 150)
    }
  }

  const handleMouseLeave = () => {
    if (isCollapsed) {
      if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current)
      hoverTimeoutRef.current = setTimeout(() => setOpenDropdown(null), 150)
    }
  }

  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current)
    }
  }, [])

  const isItemActive = (item: NavigationItem) => {
    if (item.href) return location.pathname === item.href
    if (item.children) {
      return item.children.some(child => location.pathname === child.href)
    }
    return false
  }

  return (
    <aside 
      className={cn(
        'bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out flex flex-col',
        isCollapsed ? 'w-16' : 'w-64'
      )}
    >
      <div className={cn('p-4 flex-grow', isCollapsed && 'px-2')}>
        <nav className="space-y-1">
          {navigation.map((item) => {
            const isActive = isItemActive(item)
            
            if (item.children) {
              return (
                <div 
                  key={item.name} 
                  className="relative"
                  onMouseEnter={() => handleMouseEnter(item.name)}
                  onMouseLeave={handleMouseLeave}
                >
                  <Tooltip content={item.name} disabled={!isCollapsed}>
                    <button
                      ref={item.name === 'Auditoria' ? auditoriaRef : null}
                      onClick={() => handleDropdownToggle(item.name)}
                      className={cn(
                        'w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 group',
                        isActive
                          ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-md'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700 hover:shadow-sm'
                      )}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={cn('transition-transform duration-200', isActive && 'scale-110')}>
                          {item.icon}
                        </div>
                        {!isCollapsed && <span className="truncate">{item.name}</span>}
                      </div>
                      {!isCollapsed && (
                        <ChevronDown
                          className={cn('w-4 h-4 transition-transform duration-200', openDropdown === item.name && !isCollapsed ? 'rotate-180' : '')}
                        />
                      )}
                    </button>
                  </Tooltip>

                  {!isCollapsed && openDropdown === item.name && (
                    <div className="mt-1 ml-6 space-y-1">
                      {item.children.map((child) => (
                        <NavLink
                          key={child.name}
                          to={child.href}
                          className={({ isActive }) =>
                            cn(
                              'flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                              isActive
                                ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300'
                                : 'text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-700/50'
                            )
                          }
                        >
                          {child.icon}
                          <span>{child.name}</span>
                        </NavLink>
                      ))}
                    </div>
                  )}

                  {isCollapsed && openDropdown === item.name && item.name === 'Auditoria' && (
                    <DropdownMenu
                      children={item.children}
                      isCollapsed={isCollapsed}
                      triggerRef={auditoriaRef}
                    />
                  )}
                </div>
              )
            }

            return (
              <Tooltip key={item.name} content={item.name} disabled={!isCollapsed}>
                <NavLink
                  to={item.href!}
                  className={({ isActive }) =>
                    cn(
                      'flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 group',
                      isActive
                        ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-md'
                        : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700 hover:shadow-sm'
                    )
                  }
                >
                  <div className={cn('transition-transform duration-200 group-hover:scale-110', isActive && 'scale-110')}>
                    {item.icon}
                  </div>
                  {!isCollapsed && <span className="truncate">{item.name}</span>}
                </NavLink>
              </Tooltip>
            )
          })}
        </nav>
      </div>

      {/* User Menu */}
      <div className="mt-auto p-2 border-t border-gray-200 dark:border-gray-700">
        <div className="relative">
          <button
            ref={userMenuRef}
            onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
            className={cn(
              'w-full flex items-center p-2 rounded-lg transition-colors hover:bg-gray-100 dark:hover:bg-gray-700',
              isCollapsed ? 'justify-center' : 'justify-start'
            )}
          >
            <div className="w-9 h-9 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {user?.nome?.charAt(0).toUpperCase()}
              </span>
            </div>
            {!isCollapsed && (
              <div className="ml-3 text-left">
                <p className="text-sm font-semibold text-gray-800 dark:text-white truncate">{user?.nome}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Admin</p>
              </div>
            )}
          </button>

          {isUserMenuOpen && (
            <div 
              className="absolute bottom-full left-0 mb-2 w-56 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-2 z-50"
              onMouseLeave={() => setIsUserMenuOpen(false)}
            >
              <NavLink to="/fiscal/dashboard?section=perfil" className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                <User className="w-4 h-4 mr-2" />
                Meu Perfil
              </NavLink>
              <NavLink to="/fiscal/empresas" className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                <Building2 className="w-4 h-4 mr-2" />
                Empresas
              </NavLink>
              <NavLink to="/fiscal/usuarios" className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                <Users className="w-4 h-4 mr-2" />
                Usuários
              </NavLink>
              <div className="border-t border-gray-200 dark:border-gray-600 my-1"></div>
              <a 
                href="#" 
                onClick={(e) => { 
                  e.preventDefault(); 
                  handleLogout(); 
                }} 
                className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sair
              </a>
            </div>
          )}
        </div>
      </div>
    </aside>
  )
}
# Implementação da Validação CFOP x CST x Alíquota

## Visão Geral

A validação CFOP x CST x Alíquota é um sistema que verifica se as combinações de CFOP (Código Fiscal de Operações e Prestações), CST (Código de Situação Tributária) e alíquotas estão corretas nos cenários IPI de saída.

## Funcionalidades

### 1. Validação Automática
- Verifica se a combinação CFOP + CST é válida
- Valida se cenários que devem ter alíquota não estão zerados
- Valida se cenários que não devem ter alíquota não estão preenchidos
- Identifica CSTs inválidos para determinados CFOPs

### 2. Sugestões de Correção
- **Zerar Alíquota**: Para combinações que não devem ter alíquota
- **Definir Alíquota**: Para combinações que devem ter alíquota (requer intervenção manual)
- **Corrigir CST**: Para CSTs inválidos (requer intervenção manual)

### 3. Aplicação Automática
- Sugestões de "zerar alíquota" podem ser aplicadas automaticamente
- Outras correções requerem revisão manual

## Arquivos Implementados

### Backend
1. **`back/services/cfop_cst_validation_service.py`** - Serviço principal
   - Regras configuráveis CFOP x CST x Alíquota
   - Lógica de validação
   - Aplicação de sugestões

2. **`back/routes/cenario_routes.py`** - Endpoints da API
   - `/api/cenarios/ipi/validate-cfop-cst` - Executar validação
   - `/api/cenarios/ipi/apply-cfop-cst-suggestion` - Aplicar sugestão
   - `/api/cenarios/ipi/cfop-cst-rules` - Obter regras configuradas

### Frontend
3. **`front/static/js/cfop_cst_validation.js`** - Interface de usuário
   - Botão "Validar CFOP x CST" na página de cenários
   - Modal com problemas encontrados
   - Aplicação de correções em lote

4. **`front/templates/dashboard.html`** - Inclusão do script

## Regras Configuradas

### Estrutura das Regras
```python
REGRAS_CFOP_CST = {
    'CFOP': {
        'CST': {
            'deve_ter_aliquota': bool,
            'descricao': 'Descrição da regra'
        }
    }
}
```

### CFOPs Suportados
- **5101** - Venda de produção do estabelecimento
- **5102** - Venda de mercadoria adquirida ou recebida de terceiros
- **5116** - Venda de produção do estabelecimento originada de encomenda para entrega futura
- **5118** - Venda de produção do estabelecimento entregue ao destinatário por conta e ordem do adquirente originário
- **5201** - Devolução de compra para industrialização
- **5908** - Remessa de bem por conta de contrato de comodato
- **5911** - Remessa de produção do estabelecimento, com fim específico de exportação
- **5915** - Remessa de mercadoria ou bem para fim de demonstração
- **5923** - Remessa de mercadoria por conta e ordem de terceiros, em venda à ordem
- **6101** - Venda de produção do estabelecimento (interestadual)
- **6102** - Venda de mercadoria adquirida ou recebida de terceiros (interestadual)
- **6118** - Venda de produção do estabelecimento entregue ao destinatário por conta e ordem do adquirente originário (interestadual)

### CSTs Suportados
- **49** - Outras entradas (pode ter alíquota)
- **50** - Saída/Entrada tributada (deve ter alíquota)
- **51** - Saída/Entrada tributável com alíquota zero (não deve ter alíquota)
- **52** - Saída/Entrada isenta (não deve ter alíquota)
- **53** - Saída/Entrada não tributada (não deve ter alíquota)
- **54** - Saída/Entrada imune (não deve ter alíquota)
- **55** - Saída/Entrada com suspensão (não deve ter alíquota)
- **99** - Outras saídas/entradas (geralmente sem alíquota)

## Como Usar

### 1. Acessar a Validação
1. Navegue para **Cenários > Saída > IPI**
2. Selecione uma empresa
3. Clique no botão **"Validar CFOP x CST"** (azul, com ícone de branch)

### 2. Analisar Resultados
- Modal mostra problemas encontrados em tabela
- Cada linha indica:
  - CFOP e CST atual
  - Alíquota atual
  - Descrição do problema
  - Sugestão de correção

### 3. Aplicar Correções
- **Aplicar Todas**: Aplica todas as correções automáticas
- **Aplicar Selecionadas**: Aplica apenas as marcadas
- **Aplicar Individual**: Botão na linha específica
- **Editar Manual**: Para problemas que requerem revisão

## Tipos de Problemas

### 1. ALIQUOTA_ZERADA_INCORRETA
- **Problema**: Combinação CFOP + CST deve ter alíquota, mas está zerada
- **Exemplo**: CFOP 5101 + CST 50 com alíquota 0%
- **Ação**: Requer definição manual da alíquota

### 2. ALIQUOTA_PREENCHIDA_INCORRETA
- **Problema**: Combinação CFOP + CST não deve ter alíquota, mas está preenchida
- **Exemplo**: CFOP 5101 + CST 53 com alíquota 12%
- **Ação**: Pode ser zerada automaticamente

### 3. CST_INVALIDO_PARA_CFOP
- **Problema**: CST não é válido para o CFOP informado
- **Exemplo**: CST inexistente nas regras do CFOP
- **Ação**: Requer correção manual do CST

### 4. CFOP_SEM_REGRA
- **Problema**: CFOP não possui regras configuradas
- **Ação**: Adicionar regras no código ou ignorar

## Personalização

### Adicionar/Editar Regras
Para modificar as regras, edite o arquivo `back/services/cfop_cst_validation_service.py`:

```python
# Adicionar novo CFOP
'NOVO_CFOP': {
    '50': {'deve_ter_aliquota': True, 'descricao': 'Saída tributada - deve ter alíquota'},
    '51': {'deve_ter_aliquota': False, 'descricao': 'Saída tributável com alíquota zero'},
    # ... outros CSTs
}

# Modificar regra existente
'5101': {
    '50': {'deve_ter_aliquota': True, 'descricao': 'Nova descrição'},
    # ... outros CSTs
}
```

### Personalizar Interface
- Modificar `front/static/js/cfop_cst_validation.js`
- Ajustar estilos e comportamentos
- Customizar mensagens e textos

## Integração com Sistema Existente

### Histórico de Validações
- Utiliza a tabela `ipi_validation_results` existente
- Registra todas as aplicações de sugestões
- Mantém auditoria completa das alterações

### Permissões
- Utiliza o sistema de autenticação JWT existente
- Respeita permissões por empresa e escritório
- Integra com o controle de acesso atual

## Fluxo de Funcionamento

```
Frontend (cfop_cst_validation.js)
    ↓ AJAX
API Endpoints (cenario_routes.py)
    ↓ Service Call
CFOPCSTValidationService
    ↓ Database Query
CenarioIPI + Regras Configuradas
    ↓ Validation Logic
Sugestões de Correção
    ↓ Response
Modal com Resultados
    ↓ User Action
Aplicação de Correções
    ↓ Database Update
Cenários Atualizados + Histórico
```

## Monitoramento

### Logs
- Todas as operações são logadas
- Erros são capturados e reportados
- Histórico de aplicações é mantido

### Métricas
- Total de cenários validados
- Número de problemas encontrados
- Taxa de aplicação automática vs manual
- Tipos de problemas mais comuns

## Exemplo de Uso

### Cenário: CFOP 5101 + CST 53 + Alíquota 12%
1. **Problema Identificado**: CST 53 (não tributada) não deve ter alíquota
2. **Sugestão**: Zerar alíquota para 0%
3. **Ação**: Pode ser aplicada automaticamente
4. **Resultado**: Cenário corrigido para alíquota 0%

### Cenário: CFOP 5101 + CST 50 + Alíquota 0%
1. **Problema Identificado**: CST 50 (tributada) deve ter alíquota
2. **Sugestão**: Definir alíquota apropriada
3. **Ação**: Requer intervenção manual
4. **Resultado**: Usuário deve editar o cenário manualmente

## Benefícios

1. **Consistência**: Garante que combinações CFOP x CST estejam corretas
2. **Eficiência**: Correções automáticas para casos simples
3. **Auditoria**: Histórico completo de todas as alterações
4. **Flexibilidade**: Regras facilmente configuráveis
5. **Integração**: Funciona junto com a validação TIPI existente
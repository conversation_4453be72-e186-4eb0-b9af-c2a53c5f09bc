import React from 'react'
import type { CenarioTributo } from '@/types/cenarios'
import { FilterRow } from '../filters/FilterRow'
import { useAdvancedFilters } from '@/hooks/useAdvancedFilters'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'
import { Edit, Check, AlertTriangle, Trash2 } from 'lucide-react'

interface COFINSTableProps {
  cenarios: CenarioTributo[]
  selectedCenarios: number[]
  onEdit: (cenario: CenarioTributo) => void
  onSendToProduction: (cenario: CenarioTributo) => void
  onMarkAsInconsistent: (cenario: CenarioTributo) => void
  onDelete: (cenario: CenarioTributo) => void
  onSelectCenario: (cenarioId: number, checked: boolean) => void
  onSelectAll: (checked: boolean) => void
  status?: 'novo' | 'producao' | 'inconsistente'
  onFilteredDataChange?: (filters: any, filteredCenarios: CenarioTributo[], total: number, hasMore: boolean) => void
  totalCount?: number
}

// Função para formatar percentual
function formatPercentage(value: number | null | undefined): string {
  return `${value ?? 0}%`
}

// Função para renderizar os 4 botões de ação
function renderActionButtons(
  cenario: CenarioTributo, 
  onEdit: (cenario: CenarioTributo) => void,
  onSendToProduction: (cenario: CenarioTributo) => void,
  onMarkAsInconsistent: (cenario: CenarioTributo) => void,
  onDelete: (cenario: CenarioTributo) => void
) {
  return (
    <div className="flex justify-end space-x-1">
      <button 
        onClick={() => onEdit(cenario)}
        className="p-1 text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 hover:bg-primary-50 rounded"
        title="Editar"
      >
        <Edit className="w-4 h-4" />
      </button>
      <button 
        onClick={() => onSendToProduction(cenario)}
        className="p-1 text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 rounded"
        title="Enviar para Produção"
      >
        <Check className="w-4 h-4" />
      </button>
      <button 
        onClick={() => onMarkAsInconsistent(cenario)}
        className="p-1 text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300 hover:bg-yellow-50 rounded"
        title="Marcar como Inconsistente"
      >
        <AlertTriangle className="w-4 h-4" />
      </button>
      <button 
        onClick={() => onDelete(cenario)}
        className="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 rounded"
        title="Excluir"
      >
        <Trash2 className="w-4 h-4" />
      </button>
    </div>
  )
}

export function COFINSTable({
  cenarios,
  selectedCenarios,
  onEdit,
  onSendToProduction,
  onMarkAsInconsistent,
  onDelete,
  onSelectCenario,
  onSelectAll,
  status = 'novo',
  onFilteredDataChange,
  totalCount
}: COFINSTableProps) {
  const empresaId = useSelectedCompany()
  const allSelected = cenarios.length > 0 && selectedCenarios.length === cenarios.length
  const someSelected = selectedCenarios.length > 0 && selectedCenarios.length < cenarios.length



  // Hook para filtros avançados
  const {
    filters,
    options,
    isLoading: filtersLoading,
    isLoadingData,
    hasActiveFilters,
    hasMore,
    loadMore,
    updateFilter,
    updateTextFilter,
    clearAllFilters,
    getFilteredOptions
  } = useAdvancedFilters({
    tipoTributo: 'cofins',
    empresaId: empresaId || undefined,
    status,
    onFiltersChange: (newFilters, filteredData, total, hasMoreData) => {
      onFilteredDataChange?.(newFilters, filteredData, total, hasMoreData)
    },
    initialData: cenarios,
    initialTotal: totalCount
  })

  // Hook para scroll infinito
  const { loadingRef } = useInfiniteScroll({
    hasMore,
    isLoading: isLoadingData,
    onLoadMore: loadMore
  })

  const displayedCenarios = cenarios

  return (
    <TableScrollContainer>
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-900">
          <tr>
            <th className="px-3 py-3 text-left">
              <input 
                type="checkbox" 
                className="rounded border-gray-300"
                checked={allSelected}
                ref={input => {
                  if (input) input.indeterminate = someSelected
                }}
                onChange={(e) => onSelectAll(e.target.checked)}
              />
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Produto
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Descrição
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              CFOP
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              NCM
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              CST
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Estado
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              % COFINS
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              % Red. BC
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Cliente
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Simples Nacional
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Atividade
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Destinação
            </th>
            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Ações
            </th>
          </tr>

          <FilterRow
            tipoTributo="cofins"
            filters={filters}
            options={options}
            isLoading={filtersLoading}
            hasActiveFilters={hasActiveFilters}
            onUpdateFilter={updateFilter}
            onUpdateTextFilter={updateTextFilter}
            onClearAllFilters={clearAllFilters}
            getFilteredOptions={getFilteredOptions}
          />
        </thead>
        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {displayedCenarios.length === 0 ? (
            <tr>
              <td colSpan={13} className="px-6 py-12 text-center">
                <div className="flex flex-col items-center">
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    {hasActiveFilters ? 'Nenhum cenário encontrado com os filtros aplicados' : 'Nenhum cenário COFINS encontrado'}
                  </h3>
                </div>
              </td>
            </tr>
          ) : (
            displayedCenarios.map((cenario) => {
            const cofinsCenario = cenario as any
            const isSelected = selectedCenarios.includes(cenario.id)
            
            return (
              <tr 
                key={cenario.id} 
                className={`hover:bg-gray-50 dark:hover:bg-gray-700 ${
                  isSelected ? 'bg-primary-50 dark:bg-primary-900/20' : ''
                }`}
              >
                <td className="px-3 py-4">
                  <input 
                    type="checkbox" 
                    className="rounded border-gray-300"
                    checked={isSelected}
                    onChange={(e) => onSelectCenario(cenario.id, e.target.checked)}
                  />
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cenario.produto?.codigo || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white max-w-xs truncate">
                  {cenario.produto?.descricao || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cenario.cfop || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cenario.ncm || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cofinsCenario.cst || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cenario.cliente?.uf || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {formatPercentage(cofinsCenario.aliquota)}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {formatPercentage(cofinsCenario.p_red_bc)}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  <div>
                    <div className="font-medium">{cenario.cliente?.razao_social || '-'}</div>
                    <div className="text-gray-500 text-xs">{cenario.cliente?.cnpj || '-'}</div>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    cofinsCenario.simples_nacional || cenario.cliente?.simples_nacional 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {cofinsCenario.simples_nacional || cenario.cliente?.simples_nacional ? 'Sim' : 'Não'}
                  </span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cofinsCenario.atividade || cenario.cliente?.atividade || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cofinsCenario.destinacao || cenario.cliente?.destinacao || (cenario.direcao === 'entrada' ? 'Entrada' : 'Saída')}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                  {renderActionButtons(cenario, onEdit, onSendToProduction, onMarkAsInconsistent, onDelete)}
                </td>
              </tr>
            )
          })
        )}
        </tbody>
      </table>

      {/* Elemento para scroll infinito */}
      {hasMore && (
        <div ref={loadingRef} className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
          <span className="ml-2 text-sm text-gray-600">Carregando mais...</span>
        </div>
      )}
    </TableScrollContainer>
  )
}
"""
Modelo para controle manual de status de auditoria
"""

from .escritorio import db
from sqlalchemy.sql import func
from datetime import datetime

class AuditoriaStatusManual(db.Model):
    __tablename__ = 'auditoria_status_manual'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.<PERSON>umn(db.<PERSON>ger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    tipo_tributo = db.Column(db.<PERSON>um('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'), nullable=False)
    status = db.Column(db.Enum('nao_aplicavel', 'aplicavel'), nullable=False, default='aplicavel')
    motivo = db.Column(db.Text)
    data_marcacao = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    usuario_id = db.<PERSON>umn(db.<PERSON>, db.<PERSON><PERSON>('usuario.id'), nullable=False)
    ano = db.Column(db.Integer, nullable=False, default=lambda: datetime.utcnow().year)
    mes = db.Column(db.Integer, nullable=False, default=lambda: datetime.utcnow().month)
    
    # Relacionamentos
    empresa = db.relationship('Empresa', backref='status_manuais')
    usuario = db.relationship('Usuario', backref='marcacoes_status')
    
    # Constraint única
    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'tipo_tributo', 'ano', 'mes', name='unique_empresa_tributo_mes'),
    )
    
    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'tipo_tributo': self.tipo_tributo,
            'status': self.status,
            'motivo': self.motivo,
            'data_marcacao': self.data_marcacao.isoformat() if self.data_marcacao else None,
            'usuario_id': self.usuario_id,
            'usuario_nome': self.usuario.nome if self.usuario else None,
            'ano': self.ano,
            'mes': self.mes
        }
    
    @staticmethod
    def obter_status_empresa(empresa_id, ano=None, mes=None):
        """Obtém todos os status manuais de uma empresa para um período."""
        query = AuditoriaStatusManual.query.filter_by(empresa_id=empresa_id)
        if ano is not None and mes is not None:
            query = query.filter_by(ano=ano, mes=mes)
        return query.all()
    
    @staticmethod
    def obter_status_tributo(empresa_id, tipo_tributo, ano, mes):
        """Obtém o status manual de um tributo específico no período"""
        return AuditoriaStatusManual.query.filter_by(
            empresa_id=empresa_id,
            tipo_tributo=tipo_tributo,
            ano=ano,
            mes=mes
        ).first()
    
    @staticmethod
    def marcar_status(empresa_id, tipo_tributo, status, motivo, usuario_id, ano, mes):
        """Marca ou atualiza o status manual de um tributo"""
        status_existente = AuditoriaStatusManual.obter_status_tributo(
            empresa_id, tipo_tributo, ano, mes
        )
        
        if status_existente:
            # Atualizar existente
            status_existente.status = status
            status_existente.motivo = motivo
            status_existente.data_marcacao = datetime.utcnow()
            status_existente.usuario_id = usuario_id
        else:
            # Criar novo
            status_existente = AuditoriaStatusManual(
                empresa_id=empresa_id,
                tipo_tributo=tipo_tributo,
                status=status,
                motivo=motivo,
                usuario_id=usuario_id,
                ano=ano,
                mes=mes
            )
            db.session.add(status_existente)
        
        db.session.commit()
        return status_existente
    
    @staticmethod
    def remover_status(empresa_id, tipo_tributo, ano, mes):
        """Remove o status manual (volta ao padrão)"""
        status_existente = AuditoriaStatusManual.obter_status_tributo(
            empresa_id, tipo_tributo, ano, mes
        )
        
        if status_existente:
            db.session.delete(status_existente)
            db.session.commit()
            return True
        return False

import { useState, useCallback, useRef } from 'react'

interface SimpleTextFilterProps {
  placeholder?: string
  onFilter: (value: string) => void
  className?: string
  debounce?: boolean
}

export function SimpleTextFilter({ 
  placeholder = "Filtrar...", 
  onFilter, 
  className = "",
  debounce = true
}: SimpleTextFilterProps) {
  const [value, setValue] = useState('')
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null)

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setValue(newValue)
    
    // Se debounce está desabilitado, aplicar filtro imediatamente
    if (!debounce) {
      onFilter(newValue)
    }
  }, [onFilter, debounce])

  const handleKeyPress = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Cancelar qualquer debounce pendente
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current)
      }
      // Aplicar filtro imediatamente quando Enter for pressionado
      onFilter(value)
    }
  }, [value, onFilter])

  const handleBlur = useCallback(() => {
    // Aplicar filtro quando o campo perde o foco (para debounce mode)
    if (debounce) {
      // Cancelar qualquer debounce pendente
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current)
      }
      onFilter(value)
    }
  }, [value, onFilter, debounce])

  const handleClear = useCallback(() => {
    setValue('')
    // Aplicar filtro imediatamente ao limpar
    onFilter('')
  }, [onFilter])

  return (
    <div className={`relative ${className}`}>
      <input
        type="text"
        value={value}
        onChange={handleChange}
        onKeyPress={handleKeyPress}
        onBlur={handleBlur}
        placeholder={placeholder}
        className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded-md 
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                   placeholder-gray-500 dark:placeholder-gray-400
                   focus:ring-1 focus:ring-primary-500 focus:border-primary-500
                   transition-colors duration-200"
        title={debounce ? "Digite e pressione Enter ou clique fora para filtrar" : "Filtro aplicado automaticamente"}
      />
      {value && (
        <button
          onClick={handleClear}
          className="absolute right-1 top-1/2 transform -translate-y-1/2 
                     text-gray-400 hover:text-gray-600 dark:hover:text-gray-300
                     w-4 h-4 flex items-center justify-center text-xs"
          title="Limpar filtro"
        >
          ×
        </button>
      )}
    </div>
  )
}

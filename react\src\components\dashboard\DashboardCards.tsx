import { DashboardStats } from '@/services/dashboardService'
import { StatsCard } from '@/components/ui/Card'

interface DashboardCardsProps {
  stats?: DashboardStats
}

// Função para formatar valores grandes de moeda
function formatCurrency(value: number): string {
  if (value >= 1000000) {
    // Para valores acima de 1 milhão
    return `R$ ${(value / 1000000).toLocaleString('pt-BR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} mi`
  } else if (value >= 1000) {
    // Para valores acima de 1 mil
    return `R$ ${(value / 1000).toLocaleString('pt-BR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} mil`
  } else {
    // Para valores menores
    return `R$ ${value.toLocaleString('pt-BR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })}`
  }
}

export function DashboardCards({ stats }: DashboardCardsProps) {
  // Verificação de segurança para stats
  if (!stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <StatsCard
          title="Total de Empresas"
          value="0"
          change={{
            value: 0,
            type: 'increase',
            period: 'vs mês anterior'
          }}
          icon={
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
            </svg>
          }
          color="primary"
        />

        <StatsCard
          title="Total de Impostos"
          value="R$ 0,00"
          change={{
            value: 0,
            type: 'increase',
            period: 'vs mês anterior'
          }}
          icon={
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 2a1 1 0 00-1 1v1.07A7.002 7.002 0 003 11a7 7 0 0014 0 7.002 7.002 0 00-6-6.93V3a1 1 0 00-1-1zm1 12a1 1 0 11-2 0h2zm-2-2V9h2v3h-2z" />
            </svg>
          }
          color="blue"
        />

        <StatsCard
          title="Empresas Auditadas"
          value="0"
          change={{
            value: 0,
            type: 'increase',
            period: '0% do total'
          }}
          icon={
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          }
          color="success"
        />

        <StatsCard
          title="Empresas Pendentes"
          value="0"
          change={{
            value: 0,
            type: 'decrease',
            period: '0% do total'
          }}
          icon={
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
          }
          color="warning"
        />

        <StatsCard
          title="Taxa de Conclusão"
          value="0%"
          change={{
            value: 0,
            type: 'increase',
            period: 'vs período anterior'
          }}
          icon={
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          }
          color="error"
        />
      </div>
    )
  }

  // Calcular percentuais para mostrar mudanças
  const totalEmpresas = stats.total_empresas || 0
  const auditadas = stats.empresas_auditadas || 0
  const pendentes = stats.empresas_pendentes || 0
  
  const percentualAuditadas = totalEmpresas > 0 ? Math.round((auditadas / totalEmpresas) * 100) : 0
  const percentualPendentes = totalEmpresas > 0 ? Math.round((pendentes / totalEmpresas) * 100) : 0

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      <StatsCard
        title="Total de Empresas"
        value={totalEmpresas.toLocaleString()}
        change={{
          value: 8,
          type: 'increase',
          period: 'vs mês anterior'
        }}
        icon={
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
          </svg>
        }
        color="primary"
      />

      <StatsCard
        title="Total de Impostos"
        value={formatCurrency(stats.total_impostos_auditados || 0)}
        fullValue={(stats.total_impostos_auditados || 0).toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}
        change={{
          value: 2,
          type: 'increase',
          period: 'vs mês anterior'
        }}
        icon={
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 2a1 1 0 00-1 1v1.07A7.002 7.002 0 003 11a7 7 0 0014 0 7.002 7.002 0 00-6-6.93V3a1 1 0 00-1-1zm1 12a1 1 0 11-2 0h2zm-2-2V9h2v3h-2z" />
          </svg>
        }
        color="blue"
      />

      <StatsCard
        title="Empresas Auditadas"
        value={auditadas.toLocaleString()}
        change={{
          value: percentualAuditadas,
          type: 'increase',
          period: `${percentualAuditadas}% do total`
        }}
        icon={
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        }
        color="success"
      />

      <StatsCard
        title="Empresas Pendentes"
        value={pendentes.toLocaleString()}
        change={{
          value: percentualPendentes,
          type: pendentes > auditadas ? 'increase' : 'decrease',
          period: `${percentualPendentes}% do total`
        }}
        icon={
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        }
        color="warning"
      />

      <StatsCard
        title="Taxa de Conclusão"
        value={`${percentualAuditadas}%`}
        change={{
          value: 15,
          type: 'increase',
          period: 'vs período anterior'
        }}
        icon={
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        }
        color={percentualAuditadas >= 80 ? 'success' : percentualAuditadas >= 50 ? 'warning' : 'error'}
      />
    </div>
  )
}
-- Migração para adicionar campos JSONB de status por tributo na tabela auditoria_comparativa_impostos
-- Data: 2025-06-16

-- Adicionar campos JSONB se não existirem
ALTER TABLE auditoria_comparativa_impostos
ADD COLUMN IF NOT EXISTS status_tributos JSONB DEFAULT '{"icms": "pendente", "icms_st": "pendente", "ipi": "pendente", "pis": "pendente", "cofins": "pendente"}';

ALTER TABLE auditoria_comparativa_impostos
ADD COLUMN IF NOT EXISTS aprovacoes_tributos JSONB DEFAULT '{}';

-- Atualizar registros existentes que não têm os campos
UPDATE auditoria_comparativa_impostos
SET status_tributos = '{"icms": "pendente", "icms_st": "pendente", "ipi": "pendente", "pis": "pendente", "cofins": "pendente"}'
WHERE status_tributos IS NULL;

UPDATE auditoria_comparativa_impostos
SET aprovacoes_tributos = '{}'
WHERE aprovacoes_tributos IS NULL;

-- Criar índices para os novos campos JSONB
CREATE INDEX IF NOT EXISTS idx_auditoria_status_tributos ON auditoria_comparativa_impostos USING GIN (status_tributos);
CREATE INDEX IF NOT EXISTS idx_auditoria_aprovacoes_tributos ON auditoria_comparativa_impostos USING GIN (aprovacoes_tributos);

-- Comentários para documentação
COMMENT ON COLUMN auditoria_comparativa_impostos.status_tributos IS 'Status específico por tributo em formato JSONB';
COMMENT ON COLUMN auditoria_comparativa_impostos.aprovacoes_tributos IS 'Histórico de aprovações por tributo em formato JSONB';

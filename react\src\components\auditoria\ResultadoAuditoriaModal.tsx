import { Modal } from '@/components/ui/Modal'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import type { MatchingStats } from '@/services/auditoriaComparativaService'

interface ResultadoAuditoriaModalProps {
  isOpen: boolean
  onClose: () => void
  stats: MatchingStats
}

export function ResultadoAuditoriaModal({ isOpen, onClose, stats }: ResultadoAuditoriaModalProps) {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Resumo da Auditoria"
      footer={<Button onClick={onClose}>Fechar</Button>}
      size="lg"
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
        <Card className="p-4 text-center">
          <div className="w-12 h-12 mx-auto mb-2 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
            {stats.total_matches}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">Total de Matches</p>
        </Card>
        <Card className="p-4 text-center">
          <div className="w-12 h-12 mx-auto mb-2 bg-indigo-100 dark:bg-indigo-900/50 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5a1 1 0 012 0v14a1 1 0 01-2 0V5z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 10a1 1 0 012 0v9a1 1 0 01-2 0v-9zM15 7a1 1 0 012 0v12a1 1 0 01-2 0V7z" />
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-1">
            {stats.match_rate.toFixed(1)}%
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">Taxa de Match</p>
        </Card>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <Card className="p-4 text-center">
          <div className="w-12 h-12 mx-auto mb-2 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
            {stats.direct_matches}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">Matches Diretos</p>
        </Card>
        <Card className="p-4 text-center">
          <div className="w-12 h-12 mx-auto mb-2 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
            {stats.embedding_matches}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">Matches por IA</p>
        </Card>
        <Card className="p-4 text-center">
          <div className="w-12 h-12 mx-auto mb-2 bg-orange-100 dark:bg-orange-900/50 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636" />
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-1">
            {stats.unmatched_xml_items}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">XML sem par</p>
        </Card>
        <Card className="p-4 text-center">
          <div className="w-12 h-12 mx-auto mb-2 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h3 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-1">
            {stats.unmatched_sped_items}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">SPED sem par</p>
        </Card>
      </div>
    </Modal>
  )
}

export default ResultadoAuditoriaModal
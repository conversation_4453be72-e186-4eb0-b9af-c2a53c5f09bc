import api from './authService'

export interface IPISuggestion {
  cenario_id: number
  cenario_atual: any
  sugestao?: any
  descricao: string
  pode_aplicar_automaticamente: boolean
}

export interface IPIValidationResult {
  total_cenarios: number
  cenarios_com_sugestoes: number
  message: string
  sugestoes: IPISuggestion[]
}

export const ipiValidationService = {
  async validateIPI(
    empresaId: number,
    status: string
  ): Promise<IPIValidationResult> {
    const response = await api.post('/cenarios/ipi/validate', {
      empresa_id: empresaId,
      filtros: { status },
    })
    return response.data
  },

  async applySuggestion(cenarioId: number, sugestao: any) {
    const response = await api.post('/cenarios/ipi/apply-suggestion', {
      cenario_id: cenarioId,
      sugestao,
    })
    return response.data
  },

  async validateCFOPCST(
    empresaId: number,
    status: string
  ): Promise<IPIValidationResult> {
    const response = await api.post('/cenarios/ipi/validate-cfop-cst', {
      empresa_id: empresaId,
      filtros: { status },
    })
    return response.data
  },

  async applyCFOPCSTSuggestion(cenarioId: number, sugestao: any) {
    const response = await api.post('/cenarios/ipi/apply-cfop-cst-suggestion', {
      cenario_id: cenarioId,
      sugestao,
    })
    return response.data
  },
}

export default ipiValidationService
import { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './store/authStore'
import { LoginPage } from './pages/LoginPage'
import { DashboardLayout } from './components/layout/DashboardLayout'
import { DashboardPage } from './pages/DashboardPage'
import { EmpresaDashboardPage } from './pages/EmpresaDashboardPage'
import { ImportacaoPage } from './pages/ImportacaoPage'
import { CenariosPage } from './pages/CenariosPage'
import { CenarioTributoPage } from './pages/CenarioTributoPage'
import { ClientesPage } from './pages/ClientesPage'
import { EmpresasPage } from './pages/EmpresasPage'
import { ProdutosPage } from './pages/ProdutosPage'
import { GestaoXMLsPage } from './pages/GestaoXMLsPage'
import { UsuariosPage } from './pages/UsuariosPage'
import { ApuracaoPage } from './pages/ApuracaoPage'
import { LoadingSpinner } from './components/ui/LoadingSpinner'
import { TestAuthPage } from './components/dev/TestAuthPage'
import { AuditoriaSaidaPage } from './pages/AuditoriaSaidaPage'
import { AuditoriaEntradaPage } from './pages/AuditoriaEntradaPage'
import { AuditoriaComparativaPage } from './pages/AuditoriaComparativaPage'
import { AuditoriaDashboardPage } from './pages/AuditoriaDashboardPage'

function App() {
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore()

  useEffect(() => {
    // Verifica autenticação na inicialização
    checkAuth()
  }, [checkAuth])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">
            Verificando autenticação...
          </p>
        </div>
      </div>
    )
  }

  return (
    <Routes>
      {/* Rota de teste para desenvolvimento */}
      <Route path="/fiscal/test-auth" element={<TestAuthPage />} />

      <Route
        path="/fiscal/login"
        element={
          isAuthenticated ? (
            <Navigate to="/fiscal/dashboard" replace />
          ) : (
            <LoginPage />
          )
        }
      />

      <Route
        path="/fiscal/*"
        element={
          isAuthenticated ? (
            <DashboardLayout>
              <Routes>
                <Route path="/dashboard" element={<DashboardPage />} />
                <Route
                  path="/dashboard/empresa/:empresaId"
                  element={<EmpresaDashboardPage />}
                />
                <Route
                  path="/auditoria/entrada"
                  element={<AuditoriaEntradaPage />}
                />
                <Route
                  path="/auditoria/entrada/auditoria"
                  element={<AuditoriaComparativaPage />}
                />
                <Route
                  path="/auditoria/entrada/auditoria/:tributo"
                  element={<AuditoriaComparativaPage />}
                />
                <Route
                  path="/auditoria/saida"
                  element={<AuditoriaSaidaPage />}
                />
                <Route
                  path="/auditoria/saida/:tipoTributo"
                  element={<AuditoriaDashboardPage />}
                />
                <Route path="/importacao" element={<ImportacaoPage />} />
                <Route path="/gestao-xmls" element={<GestaoXMLsPage />} />
                <Route path="/produtos" element={<ProdutosPage />} />
                <Route path="/empresas" element={<EmpresasPage />} />
                <Route path="/usuarios" element={<UsuariosPage />} />
                <Route path="/clientes" element={<ClientesPage />} />
                <Route path="/apuracao" element={<ApuracaoPage />} />
                <Route path="/cenarios" element={<CenariosPage />} />
                <Route path="/cenarios/saida" element={<CenariosPage />} />
                <Route
                  path="/cenarios/saida/:tipoTributo"
                  element={<CenarioTributoPage />}
                />
                <Route
                  path="/"
                  element={<Navigate to="/fiscal/dashboard" replace />}
                />
                {/* Outras rotas serão adicionadas aqui */}
              </Routes>
            </DashboardLayout>
          ) : (
            <Navigate to="/fiscal/login" replace />
          )
        }
      />

      {/* Redirecionar rotas raiz para /fiscal */}
      <Route path="/" element={<Navigate to="/fiscal/dashboard" replace />} />
    </Routes>
  )
}

export default App

# Instruções para Deploy do Frontend React na AWS Lightsail

## 1. Build do Frontend React Localmente

Primeiro, vamos buildar o frontend React localmente:

```bash
# Navegue até a pasta do frontend React
cd react

# Instale as dependências (se ainda não tiver feito)
npm ci

# Faça o build da aplicação
npm run build
```

Isso criará uma pasta `dist` com os arquivos otimizados para produção.

## 2. Preparação para Deploy na AWS Lightsail

### Estrutura de diretórios no servidor:
```
/home/<USER>/audittei/
├── back/              # Backend (já existente)
├── front/             # Frontend React (nova pasta)
│   ├── assets/
│   ├── index.html
│   └── ... (outros arquivos buildados)
└── nginx-audittei-react.conf  # Configuração Nginx atualizada
```

### Passos para copiar os arquivos:

1. **Copiar os arquivos buildados para o servidor:**
```bash
# Na sua máquina local, após o build
cd react

# Copiar os arquivos buildados para o servidor
scp -r dist/* ubuntu@<IP_DO_SERVIDOR>:/home/<USER>/audittei/front/
```

2. **Copiar a configuração Nginx atualizada:**
```bash
# Copiar o arquivo de configuração Nginx
scp nginx-audittei-react.conf ubuntu@<IP_DO_SERVIDOR>:/home/<USER>/audittei/
```

## 3. Configuração no Servidor AWS Lightsail

Conecte-se ao servidor via SSH:

```bash
ssh ubuntu@<IP_DO_SERVIDOR>
```

### Passos no servidor:

1. **Substituir a configuração Nginx:**
```bash
# Fazer backup da configuração atual
sudo cp /etc/nginx/sites-available/audittei.conf /etc/nginx/sites-available/audittei.conf.backup

# Copiar a nova configuração
sudo cp /home/<USER>/audittei/nginx-audittei-react.conf /etc/nginx/sites-available/audittei.conf
```

2. **Verificar a configuração do Nginx:**
```bash
sudo nginx -t
```

3. **Reiniciar o Nginx:**
```bash
sudo systemctl reload nginx
```

## 4. Verificação

Após seguir todos os passos, o novo frontend React deve estar disponível em https://www.audittei.com.br

## 5. Comandos úteis para troubleshooting

```bash
# Verificar status do Nginx
sudo systemctl status nginx

# Verificar logs do Nginx
sudo tail -f /var/log/nginx/error.log

# Verificar logs de acesso
sudo tail -f /var/log/nginx/audittei.access.log

# Listar arquivos no diretório do frontend
ls -la /home/<USER>/audittei/front/
```
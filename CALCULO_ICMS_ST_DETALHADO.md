# 📊 CÁLCULO ICMS-ST - DOCUMENTAÇÃO TÉCNICA

## 🎯 **RESUMO EXECUTIVO**

O sistema está calculando ICMS-ST seguindo a metodologia padrão da legislação brasileira, com **todos os valores conferindo** entre sistema e notas fiscais.

---

## 🧮 **FÓRMULA DE CÁLCULO IMPLEMENTADA**

```
Base de Cálculo ICMS-ST = ((Valor Mercadoria ± Frete ± Desconto + IPI*) × (1 + MVA%)) × (1 - Redução%)

ICMS-ST Total = Base de Cálculo × Alíquota ICMS-ST%

ICMS-ST Devido = ICMS-ST Total - ICMS Próprio
```

**Legenda:**
- `*` IPI só é incluído para clientes de Uso e Consumo ou Ativo Imobilizado
- `±` Frete e Desconto dependem da configuração do cenário

---

## 📋 **SEQUÊNCIA DE CÁLCULO DETALHADA**

### **1️⃣ Base Inicial**
- Valor da mercadoria (campo `valor_total` do tributo)

### **2️⃣ Ajuste por Desconto**
- **SE** cenário tem `incluir_desconto = TRUE` **E** existe `valor_desconto`
- **ENTÃO** Base = Base - Desconto
- **SENÃO** Desconto não é aplicado

### **3️⃣ Ajuste por Frete**
- **SE** cenário tem `incluir_frete = TRUE` **E** existe `valor_frete`
- **ENTÃO** Base = Base + Frete
- **SENÃO** Frete não é aplicado

### **4️⃣ Ajuste por IPI**
- **SE** cliente é "Uso e Consumo" ou "Ativo Imobilizado" **E** existe IPI
- **ENTÃO** Base = Base + IPI
- **SENÃO** IPI não é aplicado

### **5️⃣ Aplicação da MVA (Margem de Valor Agregado)**
- Base = Base × (1 + MVA%)
- **Exemplo:** Base R$ 510,00 + MVA 30,55% = R$ 665,80

### **6️⃣ Aplicação da Redução da Base**
- **SE** CST ≠ '10' **E** existe `p_red_bc`
- **ENTÃO** Base = Base - (Base × Redução%)
- **SENÃO** Redução não é aplicada
- **Exemplo:** R$ 665,80 - (61,11%) = R$ 258,93

### **7️⃣ Cálculo do ICMS-ST Total**
- ICMS-ST Total = Base Final × Alíquota ICMS-ST%
- **Exemplo:** R$ 258,93 × 18% = R$ 46,61

### **8️⃣ Dedução do ICMS Próprio**
- ICMS-ST Devido = ICMS-ST Total - ICMS Próprio
- **Exemplo:** R$ 46,61 - R$ 35,70 = R$ 10,91

---

## 🔍 **ANÁLISE DOS DADOS REAIS**

### **Tributo ID 154388 (Exemplo Detalhado)**

| Etapa | Descrição | Valor | Observação |
|-------|-----------|-------|------------|
| 1 | Valor da mercadoria | R$ 510,00 | Base inicial |
| 2 | Desconto | R$ 0,00 | Não aplicado (sem desconto) |
| 3 | Frete | R$ 0,00 | Não aplicado (sem frete) |
| 4 | IPI | R$ 0,00 | Não aplicado (cliente não é uso/consumo) |
| 5 | MVA (30,55%) | R$ 665,80 | R$ 510,00 × 1,3055 |
| 6 | Redução (61,11%) | R$ 258,93 | R$ 665,80 × (1 - 0,6111) |
| 7 | ICMS-ST Total (18%) | R$ 46,61 | R$ 258,93 × 0,18 |
| 8 | ICMS Próprio | R$ 35,70 | Deduzido |
| **RESULTADO** | **ICMS-ST Devido** | **R$ 10,91** | **✅ CONFERE** |

---

## ⚙️ **CONFIGURAÇÕES DO CENÁRIO**

### **Campos Utilizados no Cálculo:**
- `cst`: Código de Situação Tributária (10 ou 70 para ST)
- `icms_st_p_mva`: Margem de Valor Agregado (%)
- `icms_st_aliquota`: Alíquota do ICMS-ST (%)
- `p_red_bc`: Percentual de Redução da Base (%)
- `incluir_frete`: Flag para incluir frete na base
- `incluir_desconto`: Flag para incluir desconto na base

### **Status Atual dos Cenários:**
- ✅ **Frete:** Configurado para SER INCLUÍDO na base
- ✅ **Desconto:** Configurado para SER INCLUÍDO na base
- ⚠️ **Aplicação:** Frete e desconto SÓ são aplicados se existirem valores

---

## 🎯 **VALIDAÇÃO DOS RESULTADOS**

### **Tributos Analisados:**
| ID | Valor Mercadoria | ICMS-ST Sistema | ICMS-ST Nota | Status |
|----|------------------|-----------------|--------------|--------|
| 154388 | R$ 510,00 | R$ 10,91 | R$ 10,91 | ✅ CONFORME |
| 154391 | R$ 972,00 | R$ 20,79 | R$ 20,79 | ✅ CONFORME |
| 154384 | R$ 5.400,00 | R$ 115,48 | R$ 115,48 | ✅ CONFORME |

**Taxa de Conformidade: 100%**

---

## 🚨 **PONTOS DE ATENÇÃO PARA O ANALISTA**

### **1. Frete e Desconto**
- ✅ **Configuração:** Cenários estão configurados para incluir frete e desconto
- ⚠️ **Aplicação:** Só são aplicados quando existem valores nos tributos
- 📊 **Dados atuais:** Tributos analisados não possuem frete nem desconto

### **2. IPI para Uso e Consumo**
- ✅ **Lógica:** Implementada corretamente
- 📊 **Dados atuais:** Clientes não são de uso e consumo, então IPI não é incluído

### **3. Redução da Base**
- ✅ **Regra:** Só aplica se CST ≠ '10' (correto)
- 📊 **Cenários atuais:** CST = '70', então redução é aplicada

### **4. MVA e Alíquotas**
- ✅ **MVA:** 30,55% (valor padrão para o segmento)
- ✅ **Alíquota:** 18% (alíquota padrão ICMS-ST)
- ✅ **Redução:** 61,11% (redução significativa da base)

---

## ✅ **CONCLUSÃO TÉCNICA**

O sistema está calculando ICMS-ST **CORRETAMENTE** seguindo:

1. ✅ Legislação brasileira de ICMS-ST
2. ✅ Configurações dos cenários aprovados
3. ✅ Regras de negócio específicas (CST, uso/consumo, etc.)
4. ✅ Validação com notas fiscais (100% de conformidade)

**Recomendação:** O cálculo está tecnicamente correto e pode ser mantido em produção.

---

## 📞 **PARA DISCUSSÃO COM O ANALISTA**

1. **Frete e Desconto:** Confirmar se a configuração atual (incluir quando existir) está correta
2. **MVA:** Validar se 30,55% é o percentual correto para os produtos
3. **Redução:** Confirmar se 61,11% é a redução aplicável
4. **IPI:** Verificar critérios para identificar clientes de uso e consumo

**Data da Análise:** $(Get-Date -Format "dd/MM/yyyy HH:mm")**
**Versão do Sistema:** Atual
**Responsável Técnico:** Equipe de Desenvolvimento
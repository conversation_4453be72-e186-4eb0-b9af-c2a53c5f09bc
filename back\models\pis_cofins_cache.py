"""Modelo de cache para consultas PIS/COFINS"""

from sqlalchemy import Column, Integer, String, Numeric, DateTime, Text, JSON, Index
from sqlalchemy.sql import func

from .escritorio import db


class PisCofinsCacheModel(db.Model):
    """Tabela utilizada para armazenar as respostas da API LegisWeb"""

    __tablename__ = "pis_cofins_cache"

    id = Column(Integer, primary_key=True)

    # NCM original informado na consulta e sua versão normalizada
    ncm_original = Column("ncm", String(20), nullable=False)
    ncm_norm = Column(String(20), nullable=False)

    # Informações de origem/destino da consulta
    regime_tributario_origem_code = Column(String(5), nullable=False)
    atividade_origem_code = Column(String(5), nullable=False)
    atividade_destino_code = Column(String(5), nullable=True)

    # Dados do tributo específico
    tributo = Column(String(10), nullable=False)  # PIS ou COFINS
    cst_code = Column(String(5), nullable=True)
    cst_desc = Column(Text, nullable=True)
    valor = Column(Numeric(10, 4), nullable=True)
    tipo_valor = Column(String(20), nullable=True)

    # Informações adicionais para fallback de alíquota padrão
    exibir_aliquota_padrao = Column(String(5), nullable=True)
    aliquota_padrao_json = Column(JSON, nullable=True)

    # Metadados / informações legais
    regra = Column(String(100), nullable=True)
    aplicabilidade = Column(Text, nullable=True)
    descricao_produto = Column(Text, nullable=True)
    id_base_legal = Column(String(20), nullable=True)
    desc_base_legal = Column(Text, nullable=True)

    # Controle de cache
    hash_consulta = Column(String(64), nullable=False, index=True)
    data_consulta = Column(DateTime, default=func.now(), index=True)

    # Índices para recuperação rápida
    __table_args__ = (
        Index(
            "idx_pis_cofins_lookup",
            "ncm_norm",
            "regime_tributario_origem_code",
            "atividade_origem_code",
        ),
    )

    @classmethod
    def buscar_por_parametros(
        cls,
        ncm_norm,
        regime_tributario_origem_code,
        atividade_origem_code,
    ):
        """Busca registros utilizando os parâmetros base de consulta"""

        return (
            cls.query.filter(
                cls.ncm_norm == ncm_norm,
                cls.regime_tributario_origem_code == regime_tributario_origem_code,
                cls.atividade_origem_code == atividade_origem_code,
            )
            .all()
        )

    @classmethod
    def buscar_por_hash(cls, hash_consulta):
        """Retorna todos os registros salvos para um determinado hash"""

        return cls.query.filter(cls.hash_consulta == hash_consulta).all()

    def to_dict(self):
        """Converte o registro em dicionário simples"""

        return {
            "id": self.id,
            "ncm_original": self.ncm_original,
            "ncm_norm": self.ncm_norm,
            "regime_tributario_origem_code": self.regime_tributario_origem_code,
            "atividade_origem_code": self.atividade_origem_code,
            "atividade_destino_code": self.atividade_destino_code,
            "tributo": self.tributo,
            "cst_code": self.cst_code,
            "cst_desc": self.cst_desc,
            "valor": float(self.valor) if self.valor is not None else None,
            "tipo_valor": self.tipo_valor,
            "exibir_aliquota_padrao": self.exibir_aliquota_padrao,
            "aliquota_padrao_json": self.aliquota_padrao_json,
            "regra": self.regra,
            "aplicabilidade": self.aplicabilidade,
            "descricao_produto": self.descricao_produto,
            "id_base_legal": self.id_base_legal,
            "desc_base_legal": self.desc_base_legal,
            "data_consulta": self.data_consulta.isoformat()
            if self.data_consulta
            else None,
            "hash_consulta": self.hash_consulta,
            # Campos de compatibilidade para consumidores antigos
            "cst_pis": f"{self.cst_code} - {self.cst_desc}" if self.tributo == "PIS" and self.cst_code else None,
            "aliquota_pis": float(self.valor) if self.tributo == "PIS" and self.valor is not None else None,
            "cst_cofins": f"{self.cst_code} - {self.cst_desc}" if self.tributo == "COFINS" and self.cst_code else None,
            "aliquota_cofins": float(self.valor) if self.tributo == "COFINS" and self.valor is not None else None,
        }
-- <PERSON><PERSON>t para corrigir e atualizar templates do chatbot
-- Este script corrige os templates existentes e adiciona novos templates específicos

BEGIN;

-- Limpar templates existentes que podem estar incorretos
DELETE FROM chatbot_templates WHERE categoria IN ('nota_fiscal', 'auditoria_inconsistencias', 'produtos_vendas', 'clientes_compras', 'estatisticas_gerais');

-- Templates corrigidos para consultas sobre notas fiscais específicas
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('nota_fiscal', 'nota fiscal numero dados', 
'SELECT 
    nfi.numero_nf,
    nfi.chave_nf,
    nfi.data_emissao,
    nfi.cfop,
    nfi.ncm,
    nfi.valor_total,
    e.razao_social as empresa_nome,
    c.razao_social as cliente_nome,
    c.cnpj as cliente_cnpj,
    c.uf as cliente_uf,
    p.descricao as produto_nome,
    p.codigo as produto_codigo,
    t.icms_valor, t.icms_aliquota, t.icms_cst,
    t.ipi_valor, t.ipi_aliquota, t.ipi_cst,
    t.pis_valor, t.pis_aliquota, t.pis_cst,
    t.cofins_valor, t.cofins_aliquota, t.cofins_cst
FROM nota_fiscal_item nfi
LEFT JOIN empresa e ON nfi.empresa_id = e.id
LEFT JOIN cliente c ON nfi.cliente_id = c.id
LEFT JOIN produto p ON nfi.produto_id = p.id
LEFT JOIN tributo t ON t.nota_fiscal_item_id = nfi.id',
'Template para consultas sobre notas fiscais específicas', true);

-- Templates para análise de inconsistências (corrigido)
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('auditoria_inconsistencias', 'inconsistencias problemas auditoria erros',
'SELECT 
    ar.tipo_tributo,
    ar.status,
    COUNT(*) as total_registros,
    SUM(ar.valor_nota) as valor_total_nota,
    SUM(ar.valor_calculado) as valor_total_calculado,
    SUM(ar.valor_nota - ar.valor_calculado) as diferenca_total,
    AVG(ABS(ar.valor_nota - ar.valor_calculado)) as diferenca_media,
    SUM(CASE WHEN ar.inconsistencia_valor THEN 1 ELSE 0 END) as inconsistencias_valor,
    SUM(CASE WHEN ar.inconsistencia_cst THEN 1 ELSE 0 END) as inconsistencias_cst,
    SUM(CASE WHEN ar.inconsistencia_aliquota THEN 1 ELSE 0 END) as inconsistencias_aliquota
FROM auditoria_resultado ar
WHERE ar.status = ''inconsistente''
GROUP BY ar.tipo_tributo, ar.status
ORDER BY total_registros DESC',
'Template para análise de inconsistências de auditoria', true);

-- Templates para análise de produtos (corrigido)
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('produtos_vendas', 'produtos vendidos mercadorias',
'SELECT 
    p.codigo,
    p.descricao,
    p.cest,
    e.razao_social as empresa_nome,
    COUNT(DISTINCT nfi.numero_nf) as total_notas,
    COUNT(DISTINCT nfi.cliente_id) as total_clientes,
    SUM(nfi.valor_total) as valor_total_vendas,
    AVG(nfi.valor_total) as valor_medio_venda,
    MIN(nfi.data_emissao) as primeira_venda,
    MAX(nfi.data_emissao) as ultima_venda
FROM produto p
LEFT JOIN nota_fiscal_item nfi ON p.id = nfi.produto_id
LEFT JOIN empresa e ON p.empresa_id = e.id
WHERE nfi.id IS NOT NULL
GROUP BY p.id, p.codigo, p.descricao, p.cest, e.razao_social
ORDER BY valor_total_vendas DESC',
'Template para análise de produtos e vendas', true);

-- Templates para análise de clientes (corrigido)
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('clientes_compras', 'clientes compradores destinatarios',
'SELECT 
    c.razao_social,
    c.cnpj,
    c.uf,
    c.municipio,
    c.atividade,
    c.destinacao,
    e.razao_social as empresa_nome,
    COUNT(DISTINCT nfi.numero_nf) as total_notas,
    COUNT(DISTINCT nfi.produto_id) as total_produtos,
    SUM(nfi.valor_total) as valor_total_compras,
    AVG(nfi.valor_total) as valor_medio_compra,
    MIN(nfi.data_emissao) as primeira_compra,
    MAX(nfi.data_emissao) as ultima_compra
FROM cliente c
LEFT JOIN nota_fiscal_item nfi ON c.id = nfi.cliente_id
LEFT JOIN empresa e ON c.empresa_id = e.id
WHERE nfi.id IS NOT NULL
GROUP BY c.id, c.razao_social, c.cnpj, c.uf, c.municipio, c.atividade, c.destinacao, e.razao_social
ORDER BY valor_total_compras DESC',
'Template para análise de clientes e compras', true);

-- Templates para estatísticas gerais (corrigido)
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('estatisticas_gerais', 'total quantidade resumo estatisticas',
'SELECT
    COUNT(DISTINCT nfi.numero_nf) as total_notas,
    COUNT(DISTINCT nfi.empresa_id) as total_empresas,
    COUNT(DISTINCT nfi.cliente_id) as total_clientes,
    COUNT(DISTINCT nfi.produto_id) as total_produtos,
    SUM(nfi.valor_total) as valor_total_operacoes,
    AVG(nfi.valor_total) as valor_medio_operacao,
    MIN(nfi.data_emissao) as data_mais_antiga,
    MAX(nfi.data_emissao) as data_mais_recente
FROM nota_fiscal_item nfi',
'Template para estatísticas gerais do sistema', true);

-- Novos templates específicos

-- Template para empresas
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('empresas', 'empresa empresas',
'SELECT 
    e.razao_social,
    e.cnpj,
    e.inscricao_estadual,
    e.atividade,
    COUNT(DISTINCT nfi.numero_nf) as total_notas,
    COUNT(DISTINCT nfi.cliente_id) as total_clientes,
    COUNT(DISTINCT nfi.produto_id) as total_produtos,
    SUM(nfi.valor_total) as valor_total_operacoes,
    AVG(nfi.valor_total) as valor_medio_operacao
FROM empresa e
LEFT JOIN nota_fiscal_item nfi ON e.id = nfi.empresa_id
GROUP BY e.id, e.razao_social, e.cnpj, e.inscricao_estadual, e.atividade
ORDER BY total_notas DESC',
'Template para análise de empresas', true);

-- Template para NCMs
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('ncm_analise', 'ncm ncms classificacao',
'SELECT 
    nfi.ncm,
    COUNT(DISTINCT nfi.numero_nf) as total_notas,
    COUNT(DISTINCT nfi.produto_id) as total_produtos,
    COUNT(DISTINCT nfi.cliente_id) as total_clientes,
    SUM(nfi.valor_total) as valor_total,
    AVG(nfi.valor_total) as valor_medio
FROM nota_fiscal_item nfi
WHERE nfi.ncm IS NOT NULL AND nfi.ncm != ''''
GROUP BY nfi.ncm
ORDER BY total_notas DESC',
'Template para análise de NCMs', true);

-- Template para CFOPs
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('cfop_analise', 'cfop cfops operacao',
'SELECT 
    nfi.cfop,
    COUNT(DISTINCT nfi.numero_nf) as total_notas,
    COUNT(DISTINCT nfi.produto_id) as total_produtos,
    COUNT(DISTINCT nfi.cliente_id) as total_clientes,
    SUM(nfi.valor_total) as valor_total,
    AVG(nfi.valor_total) as valor_medio
FROM nota_fiscal_item nfi
WHERE nfi.cfop IS NOT NULL AND nfi.cfop != ''''
GROUP BY nfi.cfop
ORDER BY total_notas DESC',
'Template para análise de CFOPs', true);

-- Template para análise por tributo específico (ICMS)
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('tributo_icms', 'icms imposto circulacao mercadorias',
'SELECT 
    ar.status,
    COUNT(*) as total_registros,
    SUM(ar.valor_nota) as valor_total_nota,
    SUM(ar.valor_calculado) as valor_total_calculado,
    SUM(ar.valor_nota - ar.valor_calculado) as diferenca_total,
    AVG(ar.valor_nota) as valor_medio_nota,
    AVG(ar.valor_calculado) as valor_medio_calculado
FROM auditoria_resultado ar
WHERE ar.tipo_tributo = ''icms''
GROUP BY ar.status
ORDER BY total_registros DESC',
'Template para análise específica do ICMS', true);

-- Template para análise por tributo específico (IPI)
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('tributo_ipi', 'ipi imposto produtos industrializados',
'SELECT 
    ar.status,
    COUNT(*) as total_registros,
    SUM(ar.valor_nota) as valor_total_nota,
    SUM(ar.valor_calculado) as valor_total_calculado,
    SUM(ar.valor_nota - ar.valor_calculado) as diferenca_total,
    AVG(ar.valor_nota) as valor_medio_nota,
    AVG(ar.valor_calculado) as valor_medio_calculado
FROM auditoria_resultado ar
WHERE ar.tipo_tributo = ''ipi''
GROUP BY ar.status
ORDER BY total_registros DESC',
'Template para análise específica do IPI', true);

-- Template para conformidade geral
INSERT INTO chatbot_templates (categoria, pergunta_template, sql_template, descricao, ativo) VALUES
('conformidade_geral', 'conformidade percentual auditoria',
'SELECT 
    ar.tipo_tributo,
    COUNT(*) as total_auditados,
    SUM(CASE WHEN ar.status = ''conforme'' THEN 1 ELSE 0 END) as total_conforme,
    SUM(CASE WHEN ar.status = ''inconsistente'' THEN 1 ELSE 0 END) as total_inconsistente,
    ROUND((SUM(CASE WHEN ar.status = ''conforme'' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2) as percentual_conformidade,
    SUM(ABS(ar.valor_nota - ar.valor_calculado)) as diferenca_total_absoluta,
    AVG(ABS(ar.valor_nota - ar.valor_calculado)) as diferenca_media_absoluta
FROM auditoria_resultado ar
GROUP BY ar.tipo_tributo
ORDER BY percentual_conformidade DESC',
'Template para análise de conformidade geral', true);

COMMIT;

-- Verificar se os templates foram inseridos corretamente
SELECT categoria, pergunta_template, ativo FROM chatbot_templates ORDER BY categoria;

import { useState, useEffect, useRef } from 'react'
import type { TipoTributo } from '@/types/cenarios'

interface FilterOption {
  value: string
  label: string
  related?: Record<string, string[]>
}

interface CenarioFilterProps {
  tipo: string
  placeholder: string
  options: FilterOption[]
  selectedValues: string[]
  onChange: (values: string[]) => void
  onApply: () => void
  onClear: () => void
  relatedFilters: Record<string, string[]>
}

export function CenarioFilter({
  tipo,
  placeholder,
  options,
  selectedValues,
  onChange,
  onApply,
  onClear,
  relatedFilters
}: CenarioFilterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredOptions, setFilteredOptions] = useState<FilterOption[]>(options)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Filter options based on search term
  useEffect(() => {
    const filtered = options.filter(option => 
      option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      option.value.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredOptions(filtered)
  }, [searchTerm, options])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleCheckboxChange = (value: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedValues, value])
    } else {
      onChange(selectedValues.filter(v => v !== value))
    }
  }

  const handleSelectAll = () => {
    const allValues = filteredOptions.map(option => option.value)
    onChange(allValues)
  }

  const handleClearSelection = () => {
    onChange([])
  }

  const handleApply = () => {
    onApply()
    setIsOpen(false)
  }

  const handleClear = () => {
    onClear()
    onChange([])
    setIsOpen(false)
  }

  const getInputValue = () => {
    if (selectedValues.length === 0) return ''
    if (selectedValues.length === 1) return selectedValues[0]
    return `${selectedValues.length} selecionados`
  }

  return (
    <div className="relative w-full" ref={dropdownRef}>
      <input
        type="text"
        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white dark:bg-gray-800 dark:border-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent cursor-pointer"
        placeholder={placeholder}
        value={getInputValue()}
        readOnly
        onClick={() => setIsOpen(!isOpen)}
      />
      
      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 max-h-80 overflow-hidden flex flex-col">
          {/* Search input */}
          <div className="p-2 border-b border-gray-200 dark:border-gray-700">
            <input
              type="text"
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white dark:bg-gray-800 dark:border-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder={`Buscar ${tipo}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              autoFocus
            />
          </div>
          
          {/* Select/Clear buttons */}
          <div className="p-2 border-b border-gray-200 dark:border-gray-700 flex justify-between">
            <button
              type="button"
              className="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-100 dark:bg-primary-900/30 dark:text-primary-300 rounded hover:bg-primary-200 dark:hover:bg-primary-800/50 transition-colors"
              onClick={handleSelectAll}
            >
              Todos
            </button>
            <button
              type="button"
              className="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 dark:bg-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              onClick={handleClearSelection}
            >
              Limpar
            </button>
          </div>
          
          {/* Options list */}
          <div className="flex-1 overflow-y-auto max-h-48">
            {filteredOptions.length === 0 ? (
              <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 text-center">
                Nenhuma opção disponível
              </div>
            ) : (
              <div className="py-1">
                {filteredOptions.map((option) => (
                  <label
                    key={option.value}
                    className="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      checked={selectedValues.includes(option.value)}
                      onChange={(e) => handleCheckboxChange(option.value, e.target.checked)}
                    />
                    <span className="ml-3 text-sm text-gray-900 dark:text-gray-100">
                      {option.label}
                    </span>
                  </label>
                ))}
              </div>
            )}
          </div>
          
          {/* Action buttons */}
          <div className="p-2 border-t border-gray-200 dark:border-gray-700 flex gap-2">
            <button
              type="button"
              className="flex-1 px-3 py-2 text-sm font-medium text-white bg-success-600 hover:bg-success-700 rounded-lg transition-colors"
              onClick={handleApply}
            >
              Aplicar Filtro
            </button>
            <button
              type="button"
              className="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
              onClick={handleClear}
            >
              Cancelar
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
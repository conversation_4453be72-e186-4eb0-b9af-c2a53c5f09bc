# Serviço Cliente XML - Auditoria Fiscal

## Descrição

Serviço executável para clientes que monitora automaticamente uma pasta de XMLs de notas fiscais, compacta os arquivos e os envia para o servidor de auditoria fiscal.

## Funcionalidades

- **Monitoramento Automático**: Monitora pasta configurada para novos arquivos XML
- **Interface Gráfica**: Interface simples para configuração
- **Envio <PERSON>**: Compacta arquivos em ZIP e envia via API autenticada
- **Controle de Tentativas**: Sistema inteligente de retry com cooldown
- **Persistência**: Salva configurações automaticamente
- **Auto-início**: Inicia automaticamente se configurado
- **Auto-destruição**: Remove-se automaticamente após falhas prolongadas

## Estrutura de Pastas

O serviço cria automaticamente as seguintes pastas no diretório monitorado:

```
[Diretório Monitorado]/
├── _pendentes/     # Arquivos aguardando envio
├── _importados/    # Arquivos enviados com sucesso
└── [arquivos.xml]  # Novos arquivos XML são detectados aqui
```

## Como Usar

### 1. Executar o Serviço

Execute o arquivo `xml_service_client.exe`

### 2. Configurar

Na interface que abrir, configure:

- **Chave API**: Fornecida pelo provedor do serviço
- **Diretório de Monitoramento**: Pasta onde os XMLs são salvos
- **Endpoint API**: URL do servidor (pré-configurado)

### 3. Salvar e Iniciar

- Clique em "Salvar Configurações"
- Clique em "Iniciar Serviço"
- O serviço começará a monitorar a pasta

### 4. Auto-início

Se as configurações estiverem válidas, o serviço inicia automaticamente em 30 segundos. Para cancelar, edite qualquer configuração.

## Comportamento do Sistema

### Fluxo Normal

1. Novo XML é detectado na pasta monitorada
2. Arquivo é movido para `_pendentes`
3. Arquivos pendentes são compactados em ZIP
4. ZIP é enviado para o servidor via API
5. Em caso de sucesso, arquivos são movidos para `_importados`

### Tratamento de Falhas

- **Falha no Envio**: Tenta novamente até 5 vezes
- **Múltiplas Falhas**: Entra em cooldown de 2 horas
- **Falhas Prolongadas**: Após 5 dias de falhas, auto-destrói a aplicação

### Códigos de Resposta da API

- **200/201**: Sucesso - arquivos movidos para `_importados`
- **403**: API Key não autorizada - para tentativas
- **Outros**: Erro temporário - tenta novamente

## Arquivos Gerados

- `xml_service_config.json`: Configurações salvas
- `xml_service.log`: Log detalhado das operações
- `cleanup.bat`: Script de auto-destruição (criado quando necessário)

## Segurança

- API Key é armazenada de forma ofuscada na interface
- Comunicação via HTTPS
- Arquivos temporários são limpos automaticamente
- Auto-destruição em caso de falhas prolongadas

## Requisitos do Sistema

- Windows 7 ou superior
- Conexão com internet
- Permissões de escrita na pasta monitorada

## Solução de Problemas

### Serviço não inicia

1. Verifique se a pasta de monitoramento existe
2. Confirme se a API Key está correta
3. Teste a conectividade com o servidor

### Arquivos não são enviados

1. Verifique o log para detalhes do erro
2. Confirme se os arquivos são XMLs válidos
3. Verifique se há espaço em disco suficiente

### Interface não abre

1. Execute como administrador
2. Verifique se não há outro processo em execução
3. Consulte o arquivo de log

## Suporte

Para suporte técnico, consulte os logs em `xml_service.log` e entre em contato com o suporte técnico fornecendo:

- Versão do sistema operacional
- Conteúdo do arquivo de log
- Descrição detalhada do problema

## Desenvolvimento

### Para Desenvolvedores

#### Instalar Dependências

```bash
pip install -r requirements_client.txt
```

#### Executar em Modo Desenvolvimento

```bash
python xml_service_client.py
```

#### Gerar Executável

```bash
python build_client.py
```

#### Limpar Arquivos de Build

```bash
python build_client.py clean
```

### Estrutura do Código

- `XMLServiceGUI`: Interface gráfica principal
- `XMLService`: Serviço de monitoramento e controle
- `FileUploadService`: Gerenciamento de upload de arquivos
- `ConfigManager`: Persistência de configurações
- `XMLMonitorHandler`: Handler para eventos de arquivo

### Configurações Avançadas

O arquivo `xml_service_config.json` permite configurações avançadas:

```json
{
  "api_key": "sua_chave_api",
  "monitor_directory": "C:\\caminho\\para\\xmls",
  "api_endpoint": "https://api.audittei.com/fiscal/api/service-upload",
  "escritorio_id": 1,
  "retry_attempts": 5,
  "retry_interval": 30,
  "failure_cooldown_hours": 2,
  "max_failure_days": 5
}
```
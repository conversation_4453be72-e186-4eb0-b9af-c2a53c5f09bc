import { useState } from 'react'
import {
  icmsStValidationService,
  ICMSSTValidationResult,
} from '@/services/icmsStValidationService'
import { ICMSSTValidationModal } from './ICMSSTValidationModal'
import { ICMSSTCFOPCSTValidationModal } from './ICMSSTCFOPCSTValidationModal'

interface Props {
  empresaId?: number
  status: string
  onRefresh?: () => void
  onFilter?: (ids: number[]) => void
  onClearFilters?: () => void
}

export function ICMSSTValidationActions({
  empresaId,
  status,
  onRefresh,
  onFilter,
  onClearFilters,
}: Props) {
  const [loadingICMS, setLoadingICMS] = useState(false)
  const [loadingCFOP, setLoadingCFOP] = useState(false)
  const [icmsResult, setIcmsResult] = useState<ICMSSTValidationResult | null>(null)
  const [cfopResult, setCfopResult] = useState<ICMSSTValidationResult | null>(null)
  const [showICMSModal, setShowICMSModal] = useState(false)
  const [showCFOPModal, setShowCFOPModal] = useState(false)

  if (!empresaId) return null

  const validateICMS = async () => {
    setLoadingICMS(true)
    try {
      const result = await icmsStValidationService.validateICMSST(
        empresaId,
        status
      )
      setIcmsResult(result)
      setShowICMSModal(true)
    } catch (e) {
      console.error(e)
      alert('Erro na validação ICMS-ST')
    } finally {
      setLoadingICMS(false)
    }
  }

  const validateCFOPCST = async () => {
    setLoadingCFOP(true)
    try {
      const result = await icmsStValidationService.validateCFOPCST(
        empresaId,
        status
      )
      setCfopResult(result)
      setShowCFOPModal(true)
    } catch (e) {
      console.error(e)
      alert('Erro na validação CFOP x CST')
    } finally {
      setLoadingCFOP(false)
    }
  }

  return (
    <div className="flex gap-3">
      <button
        onClick={validateICMS}
        disabled={loadingICMS}
        className="validation-button"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
        {loadingICMS ? 'Validando...' : 'Validar ICMS-ST'}
      </button>
      <button
        onClick={validateCFOPCST}
        disabled={loadingCFOP}
        className="analysis-button"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V12a1 1 0 102 0V9a1 1 0 01.293-.707L13.586 6H12a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293A1 1 0 0112 9v3a3 3 0 11-6 0V9a1 1 0 01.293-.707L8.586 6H7v1a1 1 0 01-2 0V4z" clipRule="evenodd" />
        </svg>
        {loadingCFOP ? 'Analisando...' : 'Analisar CFOP x CST'}
      </button>

      <ICMSSTValidationModal
        isOpen={showICMSModal}
        result={icmsResult}
        onClose={() => setShowICMSModal(false)}
        onApplied={onRefresh}
        onFilter={onFilter}
        onClearFilters={onClearFilters}
      />
      <ICMSSTCFOPCSTValidationModal
        isOpen={showCFOPModal}
        result={cfopResult}
        onClose={() => setShowCFOPModal(false)}
        onApplied={onRefresh}
        onFilter={onFilter}
        onClearFilters={onClearFilters}
      />
    </div>
  )
}
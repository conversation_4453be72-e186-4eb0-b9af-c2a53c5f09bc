import { useState, useCallback, useEffect, useRef } from 'react'
import type { TipoTributo } from '@/types/cenarios'

export interface FilterOption {
  value: string
  label: string
  count?: number
  related?: Record<string, string[]>
}

export interface FilterState {
  cfops: string[]
  ncms: string[]
  csts: string[]
  estados: string[]
  aliquotas: string[]
  aliquotas_st: string[]
  reducoes: string[]
  reducoes_st: string[]
  atividades: string[]
  destinacoes: string[]
  mvas: string[]
  // Text filters
  produto_codigo?: string
  produto_descricao?: string
  cliente_razao?: string
  cliente_cnpj?: string
  origem?: string
  cest?: string
  ex?: string
}

export interface FilterOptions {
  cfops: FilterOption[]
  ncms: FilterOption[]
  csts: FilterOption[]
  estados: FilterOption[]
  aliquotas: FilterOption[]
  aliquotas_st: FilterOption[]
  reducoes: FilterOption[]
  reducoes_st: FilterOption[]
  atividades: FilterOption[]
  destinacoes: FilterOption[]
  mvas: FilterOption[]
}

interface UseAdvancedFiltersProps {
  tipoTributo: TipoTributo
  empresaId?: number
  status: 'novo' | 'producao' | 'inconsistente'
  onFiltersChange?: (filters: FilterState, filteredData: any[], totalCount: number, hasMore: boolean) => void
  pageSize?: number
  initialData?: any[]
  initialTotal?: number
}

const initialFilterState: FilterState = {
  cfops: [],
  ncms: [],
  csts: [],
  estados: [],
  aliquotas: [],
  aliquotas_st: [],
  reducoes: [],
  reducoes_st: [],
  atividades: [],
  destinacoes: [],
  mvas: [],
}

const initialFilterOptions: FilterOptions = {
  cfops: [],
  ncms: [],
  csts: [],
  estados: [],
  aliquotas: [],
  aliquotas_st: [],
  reducoes: [],
  reducoes_st: [],
  atividades: [],
  destinacoes: [],
  mvas: [],
}

export function useAdvancedFilters({
  tipoTributo,
  empresaId,
  status,
  onFiltersChange,
  pageSize = 50,
  initialData = [],
  initialTotal,
}: UseAdvancedFiltersProps) {
  const [filters, setFilters] = useState<FilterState>(initialFilterState)
  const [options, setOptions] = useState<FilterOptions>(initialFilterOptions)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(initialTotal ?? initialData.length)
  const [hasMore, setHasMore] = useState((initialTotal ?? initialData.length) > initialData.length)
  const [allLoadedData, setAllLoadedData] = useState<any[]>(initialData)

  // Resetar estados internos quando dados externos mudarem
  useEffect(() => {
    setAllLoadedData(initialData)
    const total = initialTotal ?? initialData.length
    setTotalCount(total)
    setHasMore(total > initialData.length)
    setCurrentPage(1)
  }, [initialData, initialTotal])

  // Notificar componente pai sobre dados iniciais
  useEffect(() => {
    if (initialData.length > 0) {
      onFiltersChange?.(filters, initialData, totalCount, hasMore)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  
  // Ref para debounce de filtros de texto
  const textFilterTimeout = useRef<NodeJS.Timeout | null>(null)

  // Carregar opções de filtros do backend
  const loadFilterOptions = useCallback(
    async (currentFilters?: FilterState) => {
      if (!empresaId) return

      setIsLoading(true)
      setError(null)

      try {
        const params = new URLSearchParams({
          empresa_id: empresaId.toString(),
          direcao: 'saida', // Sistema atual só tem saída
          status: status
        })

        const appliedFilters = currentFilters || filters

        const filterMapping: Record<string, string> = {
          cfops: 'cfop',
          ncms: 'ncm',
          csts: 'cst',
          estados: 'uf',
          aliquotas: 'aliquota',
          aliquotas_st: 'aliquota_st',
          reducoes: 'reducao',
          reducoes_st: 'reducao_st',
          mvas: 'mva',
          atividades: 'atividade',
          destinacoes: 'destinacao'
        }

        Object.entries(appliedFilters).forEach(([key, values]) => {
          if (Array.isArray(values) && values.length > 0) {
            const backendKey = filterMapping[key] || key
            params.append(backendKey, values.join(','))
          } else if (typeof values === 'string' && values.trim()) {
            const backendKey = filterMapping[key] || key
            params.append(backendKey, values.trim())
          }
        })

        const response = await fetch(
          `/fiscal/api/cenarios/${tipoTributo}/filter-options?${params.toString()}`,
          {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
          }
        )

        if (!response.ok) {
          throw new Error(`Erro na requisição: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()

        if (data.success) {
          const opcoes = data.opcoes || {}
          setOptions({
            cfops: opcoes.cfops || [],
            ncms: opcoes.ncms || [],
            csts: opcoes.csts || [],
            estados: opcoes.estados || [],
            aliquotas: opcoes.aliquotas || [],
            aliquotas_st: opcoes.aliquotas_st || [],
            reducoes: opcoes.reducoes || [],
            reducoes_st: opcoes.reducoes_st || [],
            atividades: opcoes.atividades || [],
            destinacoes: opcoes.destinacoes || [],
            mvas: opcoes.mvas || [],
          })
        } else {
          throw new Error(data.message || 'Erro ao carregar opções de filtros')
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
        setError(errorMessage)
        console.error('Erro ao carregar opções de filtros:', err)

        // Em caso de erro, usar opções vazias
        setOptions(initialFilterOptions)
      } finally {
        setIsLoading(false)
      }
    },
    [tipoTributo, empresaId, status]
  )

  // Buscar dados filtrados do servidor
  const loadFilteredData = useCallback(async (filterState: FilterState, page: number = 1, append: boolean = false) => {
    if (!empresaId) return { data: [], total: 0, hasMore: false }

    setIsLoadingData(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        empresa_id: empresaId.toString(),
        direcao: 'saida',
        status: status,
        page: page.toString(),
        page_size: pageSize.toString()
      })

      // Mapeamento correto dos filtros para o backend
      const filterMapping: Record<string, string> = {
        'cfops': 'cfop',
        'ncms': 'ncm',
        'csts': 'cst',
        'estados': 'uf',
        'aliquotas': 'aliquota',
        'aliquotas_st': 'aliquota_st',
        'reducoes': 'reducao',
        'reducoes_st': 'reducao_st',
        'mvas': 'mva',
        'atividades': 'atividade',
        'destinacoes': 'destinacao'
      }

      // Adicionar filtros aos parâmetros com mapeamento correto
      Object.entries(filterState).forEach(([key, values]) => {
        if (Array.isArray(values) && values.length > 0) {
          const backendKey = filterMapping[key] || key
          params.append(backendKey, values.join(','))
          console.log(`🔗 Mapeando filtro: ${key} -> ${backendKey} = ${values.join(',')}`)
        } else if (typeof values === 'string' && values.trim()) {
          // Handle text filters
          const backendKey = filterMapping[key] || key
          params.append(backendKey, values.trim())
          console.log(`🔗 Mapeando filtro de texto: ${key} -> ${backendKey} = ${values.trim()}`)
        }
      })

      const response = await fetch(
        `/fiscal/api/cenarios/${tipoTributo}?${params.toString()}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (!response.ok) {
        throw new Error(`Erro na requisição: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        const cenarios = data.cenarios || []
        const total = data.pagination?.total || data.total || cenarios.length
        // Usar has_next da paginação se disponível, senão usar has_more, senão calcular
        const hasMoreData = data.pagination?.has_next ?? data.has_more ?? (cenarios.length === pageSize)

        // Atualizar estados
        setTotalCount(total)
        setHasMore(hasMoreData)
        setCurrentPage(page)

        console.log(`✅ Dados carregados: ${cenarios.length} cenários (página ${page})`, data)
        return {
          data: cenarios,
          total,
          hasMore: hasMoreData
        }
      } else {
        throw new Error(data.message || 'Erro ao carregar dados filtrados')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      setError(errorMessage)
      console.error('Erro ao carregar dados filtrados:', err)
      return { data: [], total: 0, hasMore: false }
    } finally {
      setIsLoadingData(false)
    }
  }, [tipoTributo, empresaId, status, pageSize])

  // Filtrar opções baseado nos relacionamentos com outros filtros
  const getFilteredOptions = useCallback((filterType: keyof FilterOptions): FilterOption[] => {
    const allOptions = options[filterType] || []

    // Se não há filtros ativos, mostrar todas as opções
    const hasActiveFilters = Object.keys(filters).some(
      key => key !== filterType && filters[key as keyof FilterState].length > 0
    )

    if (!hasActiveFilters) {
      return allOptions
    }

    // Filtrar opções baseado nos relacionamentos
    return allOptions.filter((option: FilterOption) => {
      if (!option.related) return true

      // Verificar se esta opção é compatível com os filtros selecionados
      for (const otherFilterType of Object.keys(filters) as (keyof FilterState)[]) {
        if (otherFilterType === filterType) continue

        const selectedValues = filters[otherFilterType]
        if (!Array.isArray(selectedValues) || selectedValues.length === 0) continue

        const relatedValues = option.related?.[otherFilterType]
        if (!relatedValues || relatedValues.length === 0) {
          continue
        }

        // Verificar se há interseção entre os valores relacionados e os selecionados
        const hasIntersection = selectedValues.some((value: string) =>
          relatedValues.includes(value)
        )

        if (!hasIntersection) {
          return false
        }
      }

      return true
    })
  }, [options, filters])

  // Atualizar um filtro específico
  const updateFilter = useCallback(async (filterType: keyof FilterState, values: string[]) => {
    console.log('🔍 Aplicando filtro:', { filterType, values })
    const newFilters = { ...filters, [filterType]: values }
    setFilters(newFilters)

    // Resetar dados acumulados quando filtros mudam
    setAllLoadedData([])
    setCurrentPage(1)

    // Buscar dados filtrados do servidor (sempre página 1 quando filtros mudam)
    const result = await loadFilteredData(newFilters, 1)
    console.log('📊 Resultado do filtro:', result)

    // Inicializar dados acumulados
    setAllLoadedData(result.data)
    onFiltersChange?.(newFilters, result.data, result.total, result.hasMore)
    await loadFilterOptions(newFilters)
  }, [filters, loadFilteredData, onFiltersChange, loadFilterOptions])

  // Limpar todos os filtros
  const clearAllFilters = useCallback(async () => {
    setFilters(initialFilterState)

    // Resetar dados acumulados
    setAllLoadedData([])
    setCurrentPage(1)

    // Buscar dados sem filtros
    const result = await loadFilteredData(initialFilterState, 1)
    setAllLoadedData(result.data)
    onFiltersChange?.(initialFilterState, result.data, result.total, result.hasMore)
    await loadFilterOptions(initialFilterState)
  }, [loadFilteredData, onFiltersChange, loadFilterOptions])

  // Limpar um filtro específico
  const clearFilter = useCallback(
    async (filterType: keyof FilterState) => {
      const newFilters: FilterState = { ...filters }
      const current = newFilters[filterType]
      if (Array.isArray(current)) {
        newFilters[filterType] = [] as any
      } else {
        delete (newFilters as any)[filterType]
      }
      setFilters(newFilters)

      // Buscar dados filtrados do servidor
      const result = await loadFilteredData(newFilters, 1)
      onFiltersChange?.(newFilters, result.data, result.total, result.hasMore)
      await loadFilterOptions(newFilters)
    },
    [filters, loadFilteredData, onFiltersChange, loadFilterOptions]
  )

  // Carregar mais dados (próxima página)
  const loadMore = useCallback(async () => {
    // Usar funções de atualização para garantir que temos os valores mais recentes
    const currentHasMore = hasMore;
    const currentIsLoadingData = isLoadingData;
    const currentPageValue = currentPage;
    
    console.log('🔄 Verificando se deve carregar mais dados:', { currentHasMore, currentIsLoadingData, currentPageValue });
    
    if (!currentHasMore || currentIsLoadingData) {
      console.log('⚠️ Não carregando mais dados: hasMore=', currentHasMore, 'isLoadingData=', currentIsLoadingData);
      return [];
    }

    console.log('🔄 Carregando mais dados...');
    const nextPage = currentPageValue + 1;
    const result = await loadFilteredData(filters, nextPage, true);

    if (result.data.length > 0) {
      // Concatenar novos dados aos existentes
      const newAllData = [...allLoadedData, ...result.data];
      setAllLoadedData(newAllData);

      // Retornar os novos dados concatenados para o componente
      onFiltersChange?.(filters, newAllData, result.total, result.hasMore);
      console.log('✅ Mais dados carregados:', result.data.length);
      return result.data;
    }

    console.log('⚠️ Nenhum dado adicional para carregar');
    return [];
  }, [filters, currentPage, hasMore, isLoadingData, loadFilteredData, allLoadedData, onFiltersChange]);

  // Atualizar filtros de texto com debounce
  const updateTextFilter = useCallback(async (filterType: keyof FilterState, value: string) => {
    console.log('🔍 Aplicando filtro de texto:', { filterType, value })
    
    // Atualizar estado imediatamente para UX responsivo
    const newFilters = { ...filters, [filterType]: value }
    setFilters(newFilters)

    // Limpar timeout anterior
    if (textFilterTimeout.current) {
      clearTimeout(textFilterTimeout.current)
    }

    // Aplicar debounce de 500ms
    textFilterTimeout.current = setTimeout(async () => {
      // Resetar dados acumulados quando filtros mudam
      setAllLoadedData([])
      setCurrentPage(1)

      // Buscar dados filtrados do servidor
      const result = await loadFilteredData(newFilters, 1)
      console.log('📊 Resultado do filtro de texto:', result)

      // Inicializar dados acumulados
      setAllLoadedData(result.data)
      onFiltersChange?.(newFilters, result.data, result.total, result.hasMore)
      await loadFilterOptions(newFilters)
    }, 500)
  }, [filters, loadFilteredData, onFiltersChange, loadFilterOptions])

  // Verificar se há filtros ativos (incluindo filtros de texto)
  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key as keyof FilterState]
    if (Array.isArray(value)) {
      return value.length > 0
    }
    return typeof value === 'string' && value.trim().length > 0
  })

  // Carregar opções quando os parâmetros mudarem
  useEffect(() => {
    loadFilterOptions()
  }, [loadFilterOptions])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (textFilterTimeout.current) {
        clearTimeout(textFilterTimeout.current)
      }
    }
  }, [])

  return {
    filters,
    options,
    isLoading,
    isLoadingData,
    error,
    hasActiveFilters,
    totalCount,
    hasMore,
    currentPage,
    updateFilter,
    updateTextFilter,
    clearAllFilters,
    clearFilter,
    loadMore,
    getFilteredOptions,
    reloadOptions: loadFilterOptions
  }
}

from models import db
from sqlalchemy import func
from datetime import datetime

class ImportacaoAsync(db.Model):
    """
    Modelo para controlar importações assíncronas
    """
    __tablename__ = 'importacao_async'
    
    id = db.Column(db.String(36), primary_key=True)  # UUID
    usuario_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON><PERSON>('usuario.id'), nullable=False)
    escritorio_id = db.<PERSON>umn(db.In<PERSON>ger, db.Foreign<PERSON>ey('escritorio.id'), nullable=False)
    tipo = db.Column(db.String(20), nullable=False)  # 'xml_batch', 'sped', etc.
    status = db.Column(db.String(20), default='iniciando')  # iniciando, processando, concluido, erro
    
    # Progresso
    total_arquivos = db.Column(db.Integer, default=0)
    arquivos_processados = db.Column(db.Integer, default=0)
    arquivos_sucesso = db.Column(db.Integer, default=0)
    arquivos_erro = db.Column(db.Integer, default=0)
    porcentagem = db.Column(db.Integer, default=0)
    
    # Dados da importação
    dados_entrada = db.Column(db.Text)  # JSON com dados de entrada
    resultado = db.Column(db.Text)  # JSON com resultado final
    mensagem_atual = db.Column(db.String(500))
    mensagem_erro = db.Column(db.Text)
    
    # Timestamps
    data_inicio = db.Column(db.DateTime, server_default=func.now())
    data_fim = db.Column(db.DateTime)
    data_ultima_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    usuario = db.relationship('Usuario', backref='importacoes_async')
    escritorio = db.relationship('Escritorio', backref='importacoes_async')
    
    def __repr__(self):
        return f"<ImportacaoAsync {self.id} - {self.tipo} - {self.status}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'usuario_id': self.usuario_id,
            'escritorio_id': self.escritorio_id,
            'tipo': self.tipo,
            'status': self.status,
            'total_arquivos': self.total_arquivos,
            'arquivos_processados': self.arquivos_processados,
            'arquivos_sucesso': self.arquivos_sucesso,
            'arquivos_erro': self.arquivos_erro,
            'porcentagem': self.porcentagem,
            'mensagem_atual': self.mensagem_atual,
            'mensagem_erro': self.mensagem_erro,
            'data_inicio': self.data_inicio.isoformat() if self.data_inicio else None,
            'data_fim': self.data_fim.isoformat() if self.data_fim else None,
            'data_ultima_atualizacao': self.data_ultima_atualizacao.isoformat() if self.data_ultima_atualizacao else None,
            'tempo_decorrido': self._calcular_tempo_decorrido(),
            'tempo_estimado': self._calcular_tempo_estimado()
        }
    
    def _calcular_tempo_decorrido(self):
        """Calcula o tempo decorrido desde o início"""
        if not self.data_inicio:
            return 0
        
        fim = self.data_fim if self.data_fim else datetime.utcnow()
        delta = fim - self.data_inicio
        return int(delta.total_seconds())
    
    def _calcular_tempo_estimado(self):
        """Calcula o tempo estimado para conclusão"""
        if self.arquivos_processados == 0 or self.total_arquivos == 0:
            return None
        
        tempo_decorrido = self._calcular_tempo_decorrido()
        if tempo_decorrido == 0:
            return None
        
        tempo_por_arquivo = tempo_decorrido / self.arquivos_processados
        arquivos_restantes = self.total_arquivos - self.arquivos_processados
        
        return int(tempo_por_arquivo * arquivos_restantes)
    
    def atualizar_progresso(self, arquivos_processados=None, arquivos_sucesso=None, 
                           arquivos_erro=None, mensagem=None):
        """Atualiza o progresso da importação"""
        if arquivos_processados is not None:
            self.arquivos_processados = arquivos_processados
        if arquivos_sucesso is not None:
            self.arquivos_sucesso = arquivos_sucesso
        if arquivos_erro is not None:
            self.arquivos_erro = arquivos_erro
        if mensagem is not None:
            self.mensagem_atual = mensagem
        
        # Calcular porcentagem
        if self.total_arquivos > 0:
            self.porcentagem = int((self.arquivos_processados / self.total_arquivos) * 100)
        
        self.data_ultima_atualizacao = datetime.utcnow()
    
    def marcar_concluido(self, resultado=None):
        """Marca a importação como concluída"""
        self.status = 'concluido'
        self.data_fim = datetime.utcnow()
        self.porcentagem = 100
        
        if resultado:
            import json
            self.resultado = json.dumps(resultado)
    
    def marcar_erro(self, mensagem_erro):
        """Marca a importação como erro"""
        self.status = 'erro'
        self.data_fim = datetime.utcnow()
        self.mensagem_erro = mensagem_erro
    
    @classmethod
    def obter_ativas_usuario(cls, usuario_id):
        """Obtém importações ativas de um usuário"""
        return cls.query.filter(
            cls.usuario_id == usuario_id,
            cls.status.in_(['iniciando', 'processando'])
        ).all()
    
    @classmethod
    def limpar_antigas(cls, dias=7):
        """Remove importações antigas (concluídas há mais de X dias)"""
        from datetime import timedelta
        
        data_limite = datetime.utcnow() - timedelta(days=dias)
        
        antigas = cls.query.filter(
            cls.status.in_(['concluido', 'erro']),
            cls.data_fim < data_limite
        ).all()
        
        for importacao in antigas:
            db.session.delete(importacao)
        
        db.session.commit()
        return len(antigas)

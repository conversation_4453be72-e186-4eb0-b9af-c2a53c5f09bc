from models import db, Empresa, Cliente, Produto, Tributo, ImportacaoXML, NotaFiscalItem
from utils import XMLProcessor
from utils.api_client import fetch_cnpj_data
from services.cenario_service import CenarioService
from datetime import datetime
import traceback   
from services.transactional import transactional_session

class XMLImportService:
    """
    Serviço para importação de arquivos XML de notas fiscais
    """

    def __init__(self, empresa_id, escritorio_id, usuario_id):
        """
        Inicializa o serviço de importação

        Args:
            empresa_id (int): ID da empresa
            escritorio_id (int): ID do escritório
            usuario_id (int): ID do usuário que está realizando a importação
        """
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id

    def import_xml(self, xml_content, filename, handle_counterpart=True):
        """
        Importa um arquivo XML

        Args:
            xml_content (str): Conteúdo do arquivo XML
            filename (str): Nome do arquivo
            handle_counterpart (bool): Se deve importar para a empresa contraparte

        Returns:
            dict: Resultado da importação
        """
        importacao = None
        try:
            with transactional_session():
                result, importacao = self._import_xml_internal(
                    xml_content,
                    filename,
                    handle_counterpart,
                )
                # Convert to dict while still in session
                if importacao and hasattr(importacao, 'to_dict'):
                    result['importacao'] = importacao.to_dict()
                return result

        except Exception as e:
            error_msg = str(e)
            stack_trace = traceback.format_exc()

            # Criar registro de importação com erro apenas se não foi criado antes
            with transactional_session():
                if not importacao:
                    importacao = self._create_error_import_record(filename, error_msg, stack_trace)

            return {
                'success': False,
                'message': f'Erro ao importar XML: {error_msg}',
                'error': error_msg,
                'stack_trace': stack_trace,
                'importacao': importacao
            }

    def _import_xml_internal(self, xml_content, filename, handle_counterpart):
        """Executa a lógica principal de importação dentro de uma sessão."""
        importacao = None

        # Processar o XML
        processor = XMLProcessor(xml_content)

        # Obter informações do XML
        info_nfe = processor.get_info_nfe()
        emitente = processor.get_emitente()
        destinatario = processor.get_destinatario()
        produtos = processor.get_produtos()
        totais = processor.get_totais()
        protocolo = processor.get_protocolo()
        cstat = protocolo.get('cStat')
        chave_nf = info_nfe.get('chave')

        if cstat == '101' and chave_nf:
            existente = ImportacaoXML.query.filter_by(
                empresa_id=self.empresa_id,
                chave_nf=chave_nf
            ).first()

            if existente:
                self._cancel_existing_importacao(existente)
                return {
                    'success': True,
                    'message': 'Nota cancelada atualizada',
                    'importacao_id': existente.id
                }, existente.to_dict()
            else:
                importacao = ImportacaoXML(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    usuario_id=self.usuario_id,
                    arquivo_nome=filename,
                    chave_nf=chave_nf,
                    numero_nf=info_nfe.get('numero'),
                    fin_nfe=info_nfe.get('fin_nfe'),
                    data_emissao=info_nfe.get('data_emissao'),
                    cnpj_emitente=emitente.get('cnpj'),
                    razao_social_emitente=emitente.get('nome'),
                    tipo_nota='1',
                    data_entrada=info_nfe.get('data_emissao'),
                    data_emissao_original=info_nfe.get('data_emissao'),
                    valor_total_nf=totais.get('valor_total_nf'),
                    crt_emitente=emitente.get('crt'),
                    csosn=produtos[0].get('icms_csosn') if produtos and produtos[0].get('icms_csosn') else None,
                    status='concluido',
                    status_validacao='cancelado'
                )
                db.session.add(importacao)
                db.session.flush()
                importacao_id = importacao.id
                importacao_data = importacao.to_dict()
                return {
                    'success': True,
                    'message': 'Nota cancelada registrada',
                    'importacao_id': importacao_id
                }, importacao_data

        emitente_raw = emitente.copy()
        destinatario_raw = destinatario.copy()

        empresa = Empresa.query.get(self.empresa_id)
        if not empresa:
            importacao = self._create_error_import_record(filename, 'Empresa não encontrada')
            return {
                'success': False,
                'message': 'Empresa não encontrada',
                'importacao': importacao
            }, importacao

        cnpj_emitente = self._safe_clean_document(emitente.get('cnpj'))
        cpf_emitente = self._safe_clean_document(emitente.get('cpf'))
        doc_emitente = cnpj_emitente or cpf_emitente

        cnpj_destinatario = self._safe_clean_document(destinatario.get('cnpj'))
        cpf_destinatario = self._safe_clean_document(destinatario.get('cpf'))
        doc_destinatario = cnpj_destinatario or cpf_destinatario

        cnpj_empresa = self._safe_clean_document(empresa.cnpj)

        print(f"DEBUG - Doc Emitente: {doc_emitente} (CNPJ: {cnpj_emitente}, CPF: {cpf_emitente})")
        print(f"DEBUG - Doc Destinatário: {doc_destinatario} (CNPJ: {cnpj_destinatario}, CPF: {cpf_destinatario})")
        print(f"DEBUG - CNPJ Empresa: {cnpj_empresa}")

        if doc_emitente == cnpj_empresa:
            tipo_operacao_xml = info_nfe.get('tipo_operacao', '1')
            if tipo_operacao_xml == '0':
                tipo_nota = '0'
                print("✅ Empresa como emitente com tpNF=0. Processando como nota de entrada.")
            else:
                tipo_nota = '1'
                print("✅ Empresa como emitente com tpNF=1. Processando como nota de saída.")
        elif doc_destinatario == cnpj_empresa:
            tipo_nota = '0'
            print(f"✅ FALLBACK ATIVADO: Empresa {empresa.razao_social} encontrada como destinatário. Processando como nota de entrada.")
            emitente_original = emitente.copy()
            crt_original = emitente_original.get('crt')
            emitente = destinatario.copy()
            emitente['crt'] = crt_original
            destinatario = emitente_original
            print(f"✅ Emitente e destinatário trocados. Novo emitente: {emitente.get('nome')}, CRT preservado: {crt_original}")
        else:
            error_msg = f'O documento do emitente ({doc_emitente}) e do destinatário ({doc_destinatario}) não correspondem ao CNPJ da empresa ({cnpj_empresa})'
            print(f"❌ ERRO: {error_msg}")
            importacao = self._create_error_import_record(filename, error_msg)
            return {
                'success': False,
                'message': error_msg,
                'importacao': importacao
            }, importacao

        crt_emitente = None
        if tipo_nota == '0':
            crt_emitente = emitente.get('crt')
            print(f"DEBUG - Nota de entrada detectada. CRT do emitente: {crt_emitente}")

        cliente = self._process_cliente(destinatario, crt_emitente)

        produtos_processados = []
        tributos_processados = []

        for produto_data in produtos:
            produto = self._process_produto(produto_data)
            tributo = self._process_tributo(produto_data, cliente.id, produto.id, info_nfe, tipo_nota)

            produtos_processados.append(produto)
            tributos_processados.append(tributo)

        importacao = ImportacaoXML(
            empresa_id=self.empresa_id,
            escritorio_id=self.escritorio_id,
            usuario_id=self.usuario_id,
            arquivo_nome=filename,
            chave_nf=info_nfe.get('chave'),
            numero_nf=info_nfe.get('numero'),
            fin_nfe=info_nfe.get('fin_nfe'),
            data_emissao=info_nfe.get('data_emissao'),
            cnpj_emitente=emitente.get('cnpj'),
            razao_social_emitente=emitente.get('nome'),
            tipo_nota=tipo_nota,
            data_entrada=info_nfe.get('data_emissao'),
            data_emissao_original=info_nfe.get('data_emissao'),
            valor_total_nf=totais.get('valor_total_nf'),
            crt_emitente=emitente.get('crt'),
            csosn=produtos[0].get('icms_csosn') if produtos and produtos[0].get('icms_csosn') else None,
            status='concluido',
            status_validacao='validado'
        )

        db.session.add(importacao)
        db.session.flush()

        # Convert all model instances to dictionaries while still in session
        cliente_dict = cliente.to_dict() if cliente else None
        produtos_dict = [p.to_dict() for p in produtos_processados if p]
        tributos_dict = [t.to_dict() for t in tributos_processados if t]
        importacao_id = importacao.id
        importacao_dict = importacao.to_dict()

        if handle_counterpart:
            self._import_for_other_company(
                xml_content,
                filename,
                tipo_nota,
                info_nfe,
                emitente_raw,
                destinatario_raw,
            )

        return {
            'success': True,
            'message': 'Importação realizada com sucesso',
            'importacao_id': importacao_id,
            'cliente': cliente_dict,
            'produtos': produtos_dict,
            'tributos': tributos_dict
        }, importacao_dict

    def _create_error_import_record(self, filename, error_msg, stack_trace=None):
        """
        Cria um registro de importação com erro

        Args:
            filename (str): Nome do arquivo
            error_msg (str): Mensagem de erro
            stack_trace (str): Stack trace do erro (opcional)

        Returns:
            dict | None: Dados da importação criada ou None se falhar
        """
        try:
            full_message = error_msg
            if stack_trace:
                full_message = f"{error_msg}\n\n{stack_trace}"

            importacao = ImportacaoXML(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                usuario_id=self.usuario_id,
                arquivo_nome=filename,
                status='erro',
                mensagem=full_message
            )

            db.session.add(importacao)
            db.session.flush()
            importacao_dict = importacao.to_dict()

            return importacao_dict

        except Exception as inner_e:
            return None

    def _cancel_existing_importacao(self, xml: ImportacaoXML):
        """Cancela uma importação existente removendo tributos e cenários."""
        tributos = db.session.query(Tributo).filter(
            Tributo.empresa_id == xml.empresa_id,
            Tributo.chave_nf == xml.chave_nf
        ).all()

        cenarios = {
            'icms': set(),
            'icms_st': set(),
            'ipi': set(),
            'pis': set(),
            'cofins': set(),
            'difal': set(),
        }

        for tributo in tributos:
            if tributo.cenario_icms_id:
                cenarios['icms'].add(tributo.cenario_icms_id)
            if tributo.cenario_icms_st_id:
                cenarios['icms_st'].add(tributo.cenario_icms_st_id)
            if tributo.cenario_ipi_id:
                cenarios['ipi'].add(tributo.cenario_ipi_id)
            if tributo.cenario_pis_id:
                cenarios['pis'].add(tributo.cenario_pis_id)
            if tributo.cenario_cofins_id:
                cenarios['cofins'].add(tributo.cenario_cofins_id)
            if tributo.cenario_difal_id:
                cenarios['difal'].add(tributo.cenario_difal_id)
            db.session.delete(tributo)

        field_map = {
            'icms': Tributo.cenario_icms_id,
            'icms_st': Tributo.cenario_icms_st_id,
            'ipi': Tributo.cenario_ipi_id,
            'pis': Tributo.cenario_pis_id,
            'cofins': Tributo.cenario_cofins_id,
            'difal': Tributo.cenario_difal_id,
        }

        cenario_service = CenarioService(xml.empresa_id, xml.escritorio_id)
        for tipo, ids in cenarios.items():
            for cid in ids:
                existe = db.session.query(Tributo).filter(field_map[tipo] == cid).first()
                if not existe:
                    cenario_service.excluir_cenario(cid, tipo)

        xml.status_validacao = 'cancelado'
        xml.observacoes_validacao = 'Cancelado automaticamente'
        db.session.commit()

    def _process_cliente(self, destinatario, crt_emitente=None):
        """
        Processa o destinatário e cria/atualiza o cliente

        Args:
            destinatario (dict): Dados do destinatário
            crt_emitente (str): CRT do emitente (para notas de entrada)

        Returns:
            Cliente: Objeto do cliente
        """
        # Obter CNPJ ou CPF do destinatário com tratamento seguro
        cnpj = self._safe_clean_document(destinatario.get('cnpj'))
        cpf = self._safe_clean_document(destinatario.get('cpf'))

        # Normalizar campos de endereço quando fornecidos em subdicionário
        endereco = destinatario.get('endereco')
        if isinstance(endereco, dict):
            for campo in ['logradouro', 'numero', 'bairro', 'municipio', 'uf',
                           'cep', 'pais', 'codigo_pais']:
                if not destinatario.get(campo):
                    destinatario[campo] = endereco.get(campo)

        # Se for pessoa física (CPF), usar o CPF como CNPJ para manter compatibilidade
        # com o modelo de dados atual
        documento = cnpj if cnpj else cpf if cpf else None

        # Registrar o tipo de documento para diagnóstico
        tipo_documento = "CNPJ" if cnpj else "CPF" if cpf else "Nenhum"
        print(f"DEBUG - Processando cliente: {tipo_documento} = {documento}")

        if not documento:
            raise ValueError("Destinatário sem CNPJ ou CPF. Não é possível processar o cliente.")

        # Verificar se o cliente já existe
        cliente = Cliente.query.filter_by(
            empresa_id=self.empresa_id,
            cnpj=documento
        ).first()

        # Garantir que a inscrição estadual seja uma string
        inscricao_estadual = destinatario.get('ie')
        if inscricao_estadual is not None and not isinstance(inscricao_estadual, str):
            # Se não for uma string, converter para string ou definir como None
            try:
                inscricao_estadual = str(inscricao_estadual)
            except:
                inscricao_estadual = None

        # Determinar atividade com base no indIEDest
        ind_ie_dest = destinatario.get('ind_ie_dest')
        atividade = None
        if ind_ie_dest == '9':
            atividade = 'Não Contribuinte'

        # Determinar destinação com base no indFinal
        ind_final = destinatario.get('ind_final')
        destinacao = None
        if ind_final == '1':
            destinacao = 'Uso e Consumo'

        # Variáveis para armazenar dados da API
        cnae = None
        descricao = None
        simples_nacional = False

        # Se for um novo cliente com CNPJ (não CPF), buscar dados adicionais na API
        if not cliente and cnpj and not cpf:
            try:
                # Buscar dados do CNPJ na API
                api_data = fetch_cnpj_data(cnpj)

                if api_data:
                    # Extrair CNAE, atividade, destinação e status do Simples Nacional
                    cnae = api_data.get('cnae')
                    descricao = api_data.get('descricao')
                    simples_nacional = api_data.get('simples_nacional', False)

                    # Verificar se a natureza jurídica é 'Produtor Rural' e tem prioridade
                    natureza_juridica_data = api_data.get('natureza_juridica', {})
                    descricao_natureza_juridica = natureza_juridica_data.get('descricao', '').lower() if natureza_juridica_data else ''
                    natureza_juridica = natureza_juridica_data.get('descricao', '') if natureza_juridica_data else None
                    
                    if 'produtor rural' in descricao_natureza_juridica:
                        # Se a natureza jurídica for 'Produtor Rural', definir a atividade como 'Produtor Rural'
                        # independentemente do CNAE
                        atividade = 'Produtor Rural'
                    elif descricao_natureza_juridica.startswith('órgão público'):
                        atividade = 'Orgão Público'
                    # Se não for 'Produtor Rural', verificar se já tem atividade definida
                    elif not atividade and api_data.get('atividade'):
                        # Se não tiver atividade definida pelo indIEDest, usar a da API
                        atividade = api_data.get('atividade')

                    # Se não tiver destinação definida pelo indFinal, usar a da API
                    if not destinacao and api_data.get('destinacao'):
                        destinacao = api_data.get('destinacao')

                    
                else:
                    print(f"Não foi possível obter dados do CNPJ {cnpj} na API.")
            except Exception as e:
                print(f"Erro ao buscar dados do CNPJ {cnpj} na API: {str(e)}")

        if cliente:
            # Cliente já existe, atualizar informações
            cliente.razao_social = destinatario.get('nome', cliente.razao_social)
            cliente.inscricao_estadual = inscricao_estadual if inscricao_estadual is not None else cliente.inscricao_estadual
            cliente.logradouro = destinatario.get('logradouro', cliente.logradouro)
            cliente.numero = destinatario.get('numero', cliente.numero)
            cliente.bairro = destinatario.get('bairro', cliente.bairro)
            cliente.municipio = destinatario.get('municipio', cliente.municipio)
            cliente.uf = destinatario.get('uf', cliente.uf)
            cliente.cep = destinatario.get('cep', cliente.cep)
            cliente.pais = destinatario.get('pais', cliente.pais)
            cliente.codigo_pais = destinatario.get('codigo_pais', cliente.codigo_pais)

            # Atualizar novos campos apenas se não estiverem definidos ou se tiverem valores específicos
            if ind_ie_dest:
                cliente.ind_ie_dest = ind_ie_dest
            if ind_final:
                cliente.ind_final = ind_final
            if atividade and not cliente.atividade:
                cliente.atividade = atividade
            if destinacao and not cliente.destinacao:
                cliente.destinacao = destinacao

            # Atualizar status do Simples Nacional baseado no CRT do emitente (para notas de entrada)
            if crt_emitente:
                cliente.simples_nacional = (crt_emitente == '1')
                print(f"DEBUG - Cliente {cliente.razao_social} atualizado: Simples Nacional = {cliente.simples_nacional} (CRT: {crt_emitente})")
        else:
            # Determinar status do Simples Nacional
            # Prioridade: CRT do emitente (para notas de entrada) > API (para novos clientes)
            if crt_emitente:
                simples_nacional = (crt_emitente == '1')
                print(f"DEBUG - Novo cliente: Simples Nacional definido pelo CRT = {simples_nacional} (CRT: {crt_emitente})")

            # Criar novo cliente
            cliente = Cliente(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cnpj=documento,  # Usar o documento (CNPJ ou CPF)
                razao_social=destinatario.get('nome', ''),
                inscricao_estadual=inscricao_estadual,
                logradouro=destinatario.get('logradouro'),
                numero=destinatario.get('numero'),
                bairro=destinatario.get('bairro'),
                municipio=destinatario.get('municipio'),
                uf=destinatario.get('uf'),
                cep=destinatario.get('cep'),
                pais=destinatario.get('pais'),
                codigo_pais=destinatario.get('codigo_pais'),
                ind_ie_dest=ind_ie_dest,
                ind_final=ind_final,
                atividade=atividade,
                destinacao=destinacao,
                cnae=cnae,  # Adicionar CNAE obtido da API
                descricao=descricao,
                natureza_juridica=natureza_juridica,  # Adicionar natureza jurídica
                simples_nacional=simples_nacional,  # Adicionar status do Simples Nacional
                status='novo',
                data_cadastro=datetime.now()
            )
            db.session.add(cliente)
            db.session.flush()  # Obter o ID sem commit

        return cliente

    def _process_produto(self, produto_data):
        """
        Processa o produto e verifica se já existe um produto com o mesmo código
        Se existir, usa o produto existente, caso contrário cria um novo

        Args:
            produto_data (dict): Dados do produto

        Returns:
            Produto: Objeto do produto
        """
        # Verificar se o CFOP está na lista para definir o tipo SPED
        cfop = str(produto_data.get('cfop', '')).strip()
        cfops_produto_acabado = ['5101', '6101', '5401', '6401', '6107-8', '61078']

        # Verificar se já existe um produto com o mesmo código
        produto = Produto.query.filter_by(
            empresa_id=self.empresa_id,
            codigo=produto_data.get('codigo', ''),
        ).first()

        if produto:
            return produto

        # Se não existir, criar um novo produto
        try:
            produto = Produto(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                codigo=produto_data.get('codigo', ''),
                descricao=produto_data.get('descricao', ''),
                # Não armazenar NCM e CFOP na tabela de produtos, apenas na tabela nota_fiscal_item
                unidade_comercial=produto_data.get('unidade_comercial'),
                unidade_tributavel=produto_data.get('unidade_tributavel'),
                codigo_ean=produto_data.get('codigo_ean'),
                codigo_ean_tributavel=produto_data.get('codigo_ean_tributavel'),
                unidade_tributaria=produto_data.get('unidade_tributaria'),
                tipo_sped='Produto Acabado' if cfop in cfops_produto_acabado else None,
                cest=produto_data.get('cest'),  # Adicionar o campo CEST
                status='novo'
            )
            db.session.add(produto)
            db.session.flush()  # Obter o ID sem commit

        except Exception as e:
            # Se ocorrer um erro, tentar encontrar o produto existente apenas pelo código
            db.session.rollback()

            # Buscar produto existente com o mesmo código e empresa
            produto = Produto.query.filter_by(
                empresa_id=self.empresa_id,
                codigo=produto_data.get('codigo', '')
            ).first()

            if not produto:
                # Se não encontrar, gerar um código único adicionando um timestamp
                import time
                unique_code = f"{produto_data.get('codigo', '')}-{int(time.time())}"

                produto = Produto(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    codigo=unique_code,
                    descricao=produto_data.get('descricao', ''),
                    # Não armazenar NCM e CFOP na tabela de produtos, apenas na tabela nota_fiscal_item
                    unidade_comercial=produto_data.get('unidade_comercial'),
                    unidade_tributavel=produto_data.get('unidade_tributavel'),
                    codigo_ean=produto_data.get('codigo_ean'),
                    codigo_ean_tributavel=produto_data.get('codigo_ean_tributavel'),
                    unidade_tributaria=produto_data.get('unidade_tributaria'),
                    tipo_sped='Produto Acabado' if cfop in cfops_produto_acabado else None,
                    cest=produto_data.get('cest'),  # Adicionar o campo CEST
                    status='novo'
                )
                db.session.add(produto)
                db.session.flush()  # Obter o ID sem commit
            else:
                print(f"Produto com código {produto_data.get('codigo')} já existe. Usando o existente.")
        return produto

    def _process_tributo(self, produto_data, cliente_id, produto_id, info_nfe, tipo_nota):
        """
        Processa os tributos do produto e os vincula a cenários existentes ou novos.

        Args:
            produto_data (dict): Dados do produto com tributos
            cliente_id (int): ID do cliente
            produto_id (int): ID do produto
            info_nfe (dict): Informações da NFe
            tipo_nota (str): Tipo da nota ('0' = entrada, '1' = saída)

        Returns:
            Tributo: Objeto do tributo
        """
        # Inicializar o serviço de cenários
        cenario_service = CenarioService(self.empresa_id, self.escritorio_id)

        # Verificar se já existe um item de nota fiscal para este produto/cliente/data/NF
        nota_fiscal_item = NotaFiscalItem.query.filter_by(
            empresa_id=self.empresa_id,
            cliente_id=cliente_id,
            produto_id=produto_id,
            data_emissao=info_nfe.get('data_emissao'),
            numero_nf=info_nfe.get('numero'),
            num_item=produto_data.get('num_item')
        ).first()

        # Se não existir, criar um novo item de nota fiscal
        if not nota_fiscal_item:
            nota_fiscal_item = NotaFiscalItem(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                data_emissao=info_nfe.get('data_emissao'),
                numero_nf=info_nfe.get('numero'),
                num_item=produto_data.get('num_item'),
                chave_nf=info_nfe.get('chave'),
                cfop=produto_data.get('cfop'),
                ncm=produto_data.get('ncm'),
                unidade_comercial=produto_data.get('unidade_comercial'),
                quantidade=produto_data.get('quantidade'),
                valor_unitario=produto_data.get('valor_unitario'),
                valor_total=produto_data.get('valor_total'),
                tipo_nota=tipo_nota,
                data_entrada=info_nfe.get('data_emissao') if tipo_nota == '0' else None
            )
            db.session.add(nota_fiscal_item)
            db.session.flush()

        # Verificar se já existe um tributo para este item de nota fiscal
        tributo = Tributo.query.filter_by(nota_fiscal_item_id=nota_fiscal_item.id).first()

        if not tributo:
            tributo = Tributo(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                data_emissao=info_nfe.get('data_emissao'),
                data_saida=info_nfe.get('data_saida'),
                numero_nf=info_nfe.get('numero'),
                chave_nf=info_nfe.get('chave'),
                tipo_operacao=tipo_nota, # Usar o tipo de nota determinado
                status='novo',
                auditoria_status='pendente',
                nota_fiscal_item_id=nota_fiscal_item.id
            )
            db.session.add(tributo)

        # Preencher dados do tributo
        self._fill_tributo_data(tributo, produto_data)

        # Vincular ou criar cenários para o tributo
        self._link_or_create_cenarios(tributo, nota_fiscal_item, cenario_service)

        return tributo

    def _fill_tributo_data(self, tributo, produto_data):
        """Preenche os dados de um objeto Tributo com as informações do XML."""
        # ICMS
        tributo.icms_origem = produto_data.get('icms_origem')
        tributo.icms_cst = produto_data.get('icms_cst')
        tributo.icms_csosn = produto_data.get('icms_csosn')
        tributo.icms_mod_bc = produto_data.get('icms_mod_bc')
        tributo.icms_p_red_bc = produto_data.get('icms_p_red_bc')
        tributo.icms_vbc = produto_data.get('icms_vbc')
        tributo.icms_aliquota = produto_data.get('icms_aliquota')
        tributo.icms_valor = produto_data.get('icms_valor')
        tributo.icms_v_op = produto_data.get('icms_v_op')
        tributo.icms_p_dif = produto_data.get('icms_p_dif')
        tributo.icms_v_dif = produto_data.get('icms_v_dif')

        # ICMS-ST
        tributo.icms_st_mod_bc = produto_data.get('icms_st_mod_bc')
        tributo.icms_st_p_mva = produto_data.get('icms_st_p_mva')
        tributo.icms_st_p_red_bc = produto_data.get('icms_st_p_red_bc')
        tributo.icms_st_vbc = produto_data.get('icms_st_vbc')
        tributo.icms_st_aliquota = produto_data.get('icms_st_aliquota')
        tributo.icms_st_valor = produto_data.get('icms_st_valor')

        # IPI
        tributo.ipi_cst = produto_data.get('ipi_cst')
        tributo.ipi_vbc = produto_data.get('ipi_vbc')
        tributo.ipi_aliquota = produto_data.get('ipi_aliquota')
        tributo.ipi_valor = produto_data.get('ipi_valor')
        tributo.ipi_codigo_enquadramento = produto_data.get('ipi_codigo_enquadramento')
        tributo.ipi_ex = produto_data.get('extipi')

        # PIS
        tributo.pis_cst = produto_data.get('pis_cst')
        tributo.pis_vbc = produto_data.get('pis_vbc')
        tributo.pis_aliquota = produto_data.get('pis_aliquota')
        tributo.pis_valor = produto_data.get('pis_valor')
        tributo.pis_p_red_bc = produto_data.get('pis_p_red_bc')

        # COFINS
        tributo.cofins_cst = produto_data.get('cofins_cst')
        tributo.cofins_vbc = produto_data.get('cofins_vbc')
        tributo.cofins_aliquota = produto_data.get('cofins_aliquota')
        tributo.cofins_valor = produto_data.get('cofins_valor')
        tributo.cofins_p_red_bc = produto_data.get('cofins_p_red_bc')

        # DIFAL
        tributo.difal_vbc = produto_data.get('difal_vbc')
        tributo.difal_p_fcp_uf_dest = produto_data.get('difal_p_fcp_uf_dest')
        tributo.difal_p_icms_uf_dest = produto_data.get('difal_p_icms_uf_dest')
        tributo.difal_p_icms_inter = produto_data.get('difal_p_icms_inter')
        tributo.difal_p_icms_inter_part = produto_data.get('difal_p_icms_inter_part')
        tributo.difal_v_fcp_uf_dest = produto_data.get('difal_v_fcp_uf_dest')
        tributo.difal_v_icms_uf_dest = produto_data.get('difal_v_icms_uf_dest')
        tributo.difal_v_icms_uf_remet = produto_data.get('difal_v_icms_uf_remet')

        # Valores do produto
        tributo.quantidade = produto_data.get('quantidade')
        tributo.valor_unitario = produto_data.get('valor_unitario')
        tributo.valor_total = produto_data.get('valor_total')
        tributo.valor_frete = produto_data.get('valor_frete')
        tributo.valor_desconto = produto_data.get('valor_desconto')

    def _link_or_create_cenarios(self, tributo, nota_fiscal_item, cenario_service):
        """Busca por cenários existentes ou cria novos e os vincula ao tributo."""
        direcao = 'entrada' if tributo.tipo_operacao == '0' else 'saida'
        if direcao == 'entrada':
            return
        ncm = nota_fiscal_item.ncm
        cfop = nota_fiscal_item.cfop

        # Mapeamento de tributos para seus dados e chaves de vínculo
        tributo_map = {
            'icms': (tributo.icms_cst, tributo.icms_csosn, 'cenario_icms_id'),
            'ipi': (tributo.ipi_cst, None, 'cenario_ipi_id'),
            'pis': (tributo.pis_cst, None, 'cenario_pis_id'),
            'cofins': (tributo.cofins_cst, None, 'cenario_cofins_id'),
            'icms_st': (tributo.icms_cst, None, 'cenario_icms_st_id'),
            'difal': (tributo.difal_v_icms_uf_dest, None, 'cenario_difal_id')
        }

        csts_icms_st = ['10', '30', '50', '70']

        for tipo, (cst, csosn, link_id_attr) in tributo_map.items():
            # Regra especial: só criar cenários ICMS-ST para alguns CSTs
            if tipo == 'icms_st' and cst not in csts_icms_st:
                continue
            # Processa apenas se o tributo existir na nota (verificado por CST/CSOSN ou valor)
            if cst is not None or csosn is not None or (tipo == 'difal' and tributo.difal_v_icms_uf_dest is not None):
               # Monta os dados do cenário a partir do tributo
                cenario_data = self._get_cenario_data(tipo, tributo, direcao, cfop, ncm)
                # A lógica de criação ou atualização é delegada ao CenarioService
                cenario = cenario_service.criar_cenario_importacao(
                    tributo.cliente_id, tributo.produto_id, tipo, cenario_data
                )
                if cenario:
                    setattr(tributo, link_id_attr, cenario.id)

    def _get_cenario_data(self, tipo_tributo, tributo, direcao, cfop, ncm):
        """Monta o dicionário de dados para a criação de um novo cenário."""
        data = {
            'direcao': direcao,
            'tipo_operacao': tributo.tipo_operacao,
            'cfop': cfop,
            'ncm': ncm
        }
        if tipo_tributo == 'icms':
            data.update({
                'origem': tributo.icms_origem,
                'cst': tributo.icms_cst,
                'mod_bc': tributo.icms_mod_bc,
                'p_red_bc': tributo.icms_p_red_bc,
                'aliquota': tributo.icms_aliquota,
                'p_dif': tributo.icms_p_dif
            })
        elif tipo_tributo == 'icms_st':
            data.update({
                'origem': tributo.icms_origem,
                'cst': tributo.icms_cst,
                'mod_bc': tributo.icms_mod_bc,
                'p_red_bc': tributo.icms_p_red_bc,
                'aliquota': tributo.icms_aliquota,
                'icms_st_mod_bc': tributo.icms_st_mod_bc,
                'icms_st_aliquota': tributo.icms_st_aliquota,
                'icms_st_p_mva': tributo.icms_st_p_mva,
                'icms_st_p_red_bc': tributo.icms_st_p_red_bc,
            })
        elif tipo_tributo == 'ipi':
            data.update({
                'cst': tributo.ipi_cst,
                'aliquota': tributo.ipi_aliquota,
                'ex': tributo.ipi_ex
            })
        elif tipo_tributo == 'pis':
            data.update({
                'cst': tributo.pis_cst,
                'aliquota': tributo.pis_aliquota,
                'p_red_bc': tributo.pis_p_red_bc,
            })
        elif tipo_tributo == 'cofins':
            data.update({
                'cst': tributo.cofins_cst,
                'aliquota': tributo.cofins_aliquota,
                'p_red_bc': tributo.cofins_p_red_bc,
            })
        elif tipo_tributo == 'difal':
            data.update({
                'origem': tributo.icms_origem,
                'cst': tributo.icms_cst,
                'mod_bc': tributo.icms_mod_bc,
                'p_red_bc': tributo.icms_p_red_bc,
                'aliquota': tributo.icms_aliquota,
                'p_fcp_uf_dest': tributo.difal_p_fcp_uf_dest,
                'p_icms_uf_dest': tributo.difal_p_icms_uf_dest,
                'p_icms_inter': tributo.difal_p_icms_inter,
                'p_icms_inter_part': tributo.difal_p_icms_inter_part,
            })
        # Para outros tributos, nenhum campo extra é necessário
        return data

    def _safe_clean_document(self, document):
        """
        Limpa um documento (CNPJ/CPF) de forma segura

        Args:
            document (str): Documento a ser limpo

        Returns:
            str: Documento limpo ou None se inválido
        """
        if not document or not isinstance(document, str):
            return None

        try:
            return document.replace('.', '').replace('/', '').replace('-', '').strip()
        except (AttributeError, TypeError):
            return None

    def _detectar_tipo_nota(self, emitente, destinatario):
        """
        Detecta se a nota é de entrada (0) ou saída (1) baseado no emitente e destinatário
        Implementa fallback: se empresa não for encontrada como emitente, verifica se é destinatário
        """
        try:
            # Buscar empresa atual
            from models import Empresa
            empresa = db.session.get(Empresa, self.empresa_id)

            if not empresa:
                return '1'  # Default para saída

            # Limpar documentos para comparação com tratamento seguro
            cnpj_empresa = self._safe_clean_document(empresa.cnpj)
            doc_emitente = self._safe_clean_document(emitente.get('cnpj')) or self._safe_clean_document(emitente.get('cpf'))
            doc_destinatario = self._safe_clean_document(destinatario.get('cnpj')) or self._safe_clean_document(destinatario.get('cpf'))

            # 1. Se o emitente é a própria empresa, é nota de saída
            if cnpj_empresa == doc_emitente:
                return '1'  # Saída

            # 2. Se o destinatário é a própria empresa, é nota de entrada
            elif cnpj_empresa == doc_destinatario:
                return '0'  # Entrada

            # 3. Fallback: verificar se alguma empresa cadastrada é o destinatário
            else:
                # Buscar se existe alguma empresa cadastrada com o documento do destinatário
                if doc_destinatario:
                    empresa_destinatario = db.session.query(Empresa).filter(
                        db.func.replace(
                            db.func.replace(
                                db.func.replace(Empresa.cnpj, '.', ''),
                                '/', ''
                            ),
                            '-', ''
                        ) == doc_destinatario
                    ).first()

                    if empresa_destinatario:
                        print(f"Fallback ativado: Empresa {empresa_destinatario.razao_social} encontrada como destinatário. Nota convertida para entrada.")
                        return '0'  # Entrada

                return '1'  # Default para saída

        except Exception as e:
            print(f"Erro ao detectar tipo de nota: {str(e)}")
            return '1'  # Default para saída em caso de erro

    def _get_company_by_document(self, document):
        """Retorna a empresa cujo CNPJ limpo corresponde ao documento informado."""
        if not document:
            return None

        cleaned = self._safe_clean_document(document)
        if not cleaned:
            return None

        return Empresa.query.filter(
            db.func.replace(db.func.replace(db.func.replace(Empresa.cnpj, '.', ''), '/', ''), '-', '') == cleaned
        ).first()

    def _import_for_other_company(self, xml_content, filename, tipo_nota, info_nfe, emitente_raw, destinatario_raw):
        """Realiza a importação do XML para a empresa contraparte, se cadastrada."""
        try:
            other_doc = None
            if tipo_nota == '1':
                other_doc = destinatario_raw.get('cnpj') or destinatario_raw.get('cpf')
            else:
                other_doc = emitente_raw.get('cnpj') or emitente_raw.get('cpf')

            other_company = self._get_company_by_document(other_doc)
            if not other_company or other_company.id == self.empresa_id:
                return

            chave = info_nfe.get('chave')
            existing = ImportacaoXML.query.filter_by(empresa_id=other_company.id, chave_nf=chave).first()
            if existing:
                return

            print(
                f"↪️  Importando XML complementar para {other_company.razao_social} ({other_company.cnpj})"
            )
            other_service = XMLImportService(
                other_company.id, other_company.escritorio_id, self.usuario_id
            )
            other_service.import_xml(xml_content, filename, handle_counterpart=False)
        except Exception as e:
            print(f"Erro ao importar XML para a outra empresa: {str(e)}")

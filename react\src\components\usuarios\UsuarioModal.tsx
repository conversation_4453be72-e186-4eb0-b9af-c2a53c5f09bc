import { useEffect, useState } from 'react'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { usuariosService } from '@/services/usuariosService'
import type { Usuario, Empresa } from '@/types/usuarios'
import { useAuthStore } from '@/store/authStore'

interface UsuarioModalProps {
  usuario?: Usuario | null
  isOpen: boolean
  onClose: () => void
  onSaved: () => void
}

export function UsuarioModal({
  usuario,
  isOpen,
  onClose,
  onSaved,
}: UsuarioModalProps) {
  const { user: currentUser } = useAuthStore()
  const [form, setForm] = useState<Partial<Usuario>>({})
  const [empresas, setEmpresas] = useState<Empresa[]>([])
  const [selectedEmpresas, setSelectedEmpresas] = useState<number[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen) {
      usuariosService.getEmpresas().then(setEmpresas)
      if (usuario) {
        setSelectedEmpresas(usuario.empresas_permitidas || [])
        setForm(usuario)
      } else {
        setSelectedEmpresas([])
        setForm({})
      }
    }
  }, [isOpen, usuario])

  const handleCheckbox = (id: number) => {
    setSelectedEmpresas((prev) =>
      prev.includes(id) ? prev.filter((e) => e !== id) : [...prev, id]
    )
  }

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    setForm((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      const payload: any = {
        ...form,
        empresas_permitidas: selectedEmpresas,
      }
      if (usuario) {
        await usuariosService.updateUsuario(usuario.id, payload)
      } else {
        if (currentUser?.is_admin || currentUser?.tipo_usuario === 'admin') {
          payload.escritorio_id = currentUser?.escritorio_id
        }
        await usuariosService.createUsuario(payload)
      }
      onSaved()
      onClose()
    } catch (err: any) {
      alert(err.message || 'Erro ao salvar usuário')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={usuario ? 'Editar Usuário' : 'Novo Usuário'}
      size="lg"
      footer={
        <div className="flex gap-3">
          <Button variant="ghost" onClick={onClose} disabled={loading}>
            Cancelar
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            loading={loading}
            glow
          >
            Salvar
          </Button>
        </div>
      }
    >
      {usuario ? (
        <div className="space-y-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Selecione as empresas permitidas para este usuário:
          </p>
          <div className="max-h-64 overflow-y-auto space-y-2">
            {empresas.map((emp) => (
              <label key={emp.id} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  className="modern-checkbox"
                  checked={selectedEmpresas.includes(emp.id)}
                  onChange={() => handleCheckbox(emp.id)}
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {emp.razao_social} ({emp.cnpj})
                </span>
              </label>
            ))}
            {empresas.length === 0 && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Nenhuma empresa disponível.
              </p>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Nome"
              name="nome"
              value={form.nome || ''}
              onChange={handleChange}
              required
              fullWidth
            />
            <Input
              label="Email"
              name="email"
              type="email"
              value={form.email || ''}
              onChange={handleChange}
              required
              fullWidth
            />
            <Input
              label="Senha"
              name="senha"
              type="password"
              onChange={handleChange}
              required
              fullWidth
            />
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Tipo de Usuário
              </label>
              <select
                name="tipo_usuario"
                value={form.tipo_usuario || 'usuario'}
                onChange={handleChange}
                className="modern-select w-full"
              >
                <option value="usuario">Usuário</option>
                {(currentUser?.is_admin ||
                  currentUser?.tipo_usuario === 'admin') && (
                  <>
                    <option value="escritorio">Escritório</option>
                    <option value="admin">Administrador</option>
                  </>
                )}
              </select>
            </div>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Empresas Permitidas
            </p>
            <div className="max-h-64 overflow-y-auto space-y-2 border rounded-xl p-3">
              {empresas.map((emp) => (
                <label key={emp.id} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    className="modern-checkbox"
                    checked={selectedEmpresas.includes(emp.id)}
                    onChange={() => handleCheckbox(emp.id)}
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {emp.razao_social} ({emp.cnpj})
                  </span>
                </label>
              ))}
              {empresas.length === 0 && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Nenhuma empresa disponível.
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </Modal>
  )
}
import { useState, useEffect } from 'react'
import { empresasService } from '@/services/empresasService'
import type { Empresa } from '@/types/empresas'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface EmpresaModalProps {
  empresa?: Empresa | null
  isOpen: boolean
  onClose: () => void
  onSaved: () => void
}

export function EmpresaModal({
    empresa,
    isOpen,
    onClose,
    onSaved,
  }: EmpresaModalProps) {
    const [form, setForm] = useState<Partial<Empresa>>({})
    const [loading, setLoading] = useState(false)
    const [cnpjLoading, setCnpjLoading] = useState(false)
    const [tab, setTab] = useState<
     'dados' | 'endereco' | 'fiscal' | 'observacoes'
   >('dados')

  useEffect(() => {
    if (empresa) {
      setForm(empresa)
    } else {
      setForm({})
    }
  }, [empresa])

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target
    setForm((prev) => ({ ...prev, [name]: value }))
  }

  const handleCnpjBlur = async () => {
    const cnpj = (form.cnpj || '').replace(/\D/g, '')
    if (cnpj.length !== 14) return
    try {
      setCnpjLoading(true)
      const data = await empresasService.consultarCNPJ(cnpj)
      if (data?.success && data.dados) {
        const ieAtiva =
          data.dados.inscricoes_estaduais?.find((ie: any) => ie.ativo) ||
          data.dados.inscricoes_estaduais?.[0]
        setForm((prev) => ({
          ...prev,
          razao_social: data.dados.razao_social || prev.razao_social,
          nome_fantasia: data.dados.nome_fantasia || prev.nome_fantasia,
          email: data.dados.email || prev.email,
          cnae: data.dados.cnae || prev.cnae,
          atividade: data.dados.atividade || prev.atividade,
          tributacao: data.dados.tributacao || prev.tributacao,
          inscricao_estadual:
            ieAtiva?.inscricao_estadual || prev.inscricao_estadual,
          cep: data.dados.cep || prev.cep,
          logradouro: data.dados.logradouro || prev.logradouro,
          numero: data.dados.numero || prev.numero,
          complemento: data.dados.complemento || prev.complemento,
          bairro: data.dados.bairro || prev.bairro,
          cidade: data.dados.cidade || prev.cidade,
          estado: data.dados.estado || prev.estado,
        }))
      } else if (data?.empresa_existente) {
        alert(`Empresa já cadastrada: ${data.empresa?.razao_social || ''}`)
      } else if (data?.message) {
        alert(data.message)
      }
    } catch (err) {
      console.error('Erro ao consultar CNPJ', err)
      } finally {
      setCnpjLoading(false)
    }
  }

  const handleBuscarCep = async () => {
    const cep = (form.cep || '').replace(/\D/g, '')
    if (cep.length !== 8) return
    try {
      const data = await empresasService.consultarCEP(cep)
      if (data?.success && data.dados) {
        setForm((prev) => ({
          ...prev,
          logradouro: data.dados.logradouro || prev.logradouro,
          bairro: data.dados.bairro || prev.bairro,
          cidade: data.dados.localidade || prev.cidade,
          estado: data.dados.uf || prev.estado,
        }))
      }
    } catch (err) {
      console.error('Erro ao consultar CEP', err)
    }
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      if (empresa) {
        await empresasService.updateEmpresa(empresa.id, form)
      } else {
        await empresasService.createEmpresa(form)
      }
      onSaved()
      onClose()
    } catch (err: any) {
      alert(
        err.response?.data?.message || err.message || 'Erro ao salvar empresa'
      )
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={empresa ? 'Editar Empresa' : 'Nova Empresa'}
      size="xl"
      footer={
        <div className="flex gap-3">
          <Button variant="ghost" onClick={onClose} disabled={loading}>
            Cancelar
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            loading={loading}
            glow
          >            Salvar
          </Button>
        </div>
      }
    >
      <div className="mb-6">
        <div className="flex gap-2 p-1 bg-gray-100 dark:bg-gray-700 rounded-xl">
          <Button
            variant={tab === 'dados' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setTab('dados')}
            className="flex-1 justify-center"
            glow={tab === 'dados'}
          >
            Dados Principais
          </Button>
          <Button
            variant={tab === 'endereco' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setTab('endereco')}
            className="flex-1 justify-center"
            glow={tab === 'endereco'}
          >
            Endereço
          </Button>
          <Button
            variant={tab === 'fiscal' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setTab('fiscal')}
            className="flex-1 justify-center"
            glow={tab === 'fiscal'}
          >
            Dados Fiscais
          </Button>
          <Button
            variant={tab === 'observacoes' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setTab('observacoes')}
            className="flex-1 justify-center"
            glow={tab === 'observacoes'}
          >
            Observações
          </Button>
        </div>
      </div>

      {tab === 'dados' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              CNPJ *
            </label>
            <div className="relative">
              <input
                type="text"
                name="cnpj"
                value={form.cnpj || ''}
                onChange={handleChange}
                onBlur={handleCnpjBlur}
                className="modern-input pr-10"
                placeholder="Digite o CNPJ"
              />
              {cnpjLoading && (
                <LoadingSpinner
                  size="sm"
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                />
              )}
            </div>
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Inscrição Estadual
            </label>
            <input
              type="text"
              name="inscricao_estadual"
              value={form.inscricao_estadual || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Razão Social *
            </label>
            <input
              type="text"
              name="razao_social"
              value={form.razao_social || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Nome Fantasia
            </label>
            <input
              type="text"
              name="nome_fantasia"
              value={form.nome_fantasia || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Email
            </label>
            <input
              type="email"
              name="email"
              value={form.email || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Responsável
            </label>
            <input
              type="text"
              name="responsavel"
              value={form.responsavel || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
        </div>
      )}

      {tab === 'endereco' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2 md:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              CEP
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                name="cep"
                value={form.cep || ''}
                onChange={handleChange}
                className="modern-input"
                placeholder="Digite o CEP"
              />
              <Button
                variant="secondary"
                onClick={handleBuscarCep}
                disabled={loading}
              >
                Buscar
              </Button>
            </div>
          </div>
          <div className="space-y-2 md:col-span-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Logradouro
            </label>
            <input
              type="text"
              name="logradouro"
              value={form.logradouro || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Número
            </label>
            <input
              type="text"
              name="numero"
              value={form.numero || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Complemento
            </label>
            <input
              type="text"
              name="complemento"
              value={form.complemento || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Bairro
            </label>
            <input
              type="text"
              name="bairro"
              value={form.bairro || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Cidade
            </label>
            <input
              type="text"
              name="cidade"
              value={form.cidade || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              UF
            </label>
            <input
              type="text"
              name="estado"
              value={form.estado || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
        </div>
      )}

      {tab === 'fiscal' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              CNAE
            </label>
            <input
              type="text"
              name="cnae"
              value={form.cnae || ''}
              onChange={handleChange}
              className="modern-input"
            />
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Atividade
            </label>
            <select
              name="atividade"
              value={form.atividade || ''}
              onChange={handleChange}
              className="modern-select"
            >
              <option value="">Selecione...</option>
              <option value="Não Aplicado">Não Aplicado</option>
              <option value="Indústria">Indústria</option>
              <option value="Comércio Varejista">Comércio Varejista</option>
              <option value="Comércio Atacadista">Comércio Atacadista</option>
              <option value="Distribuidor">Distribuidor</option>
              <option value="Produtor Rural">Produtor Rural</option>
              <option value="Não Contribuinte">Não Contribuinte</option>
            </select>
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Regime Tributário
            </label>
            <select
              name="tributacao"
              value={form.tributacao || ''}
              onChange={handleChange}
              className="modern-select"
            >
              <option value="">Selecione...</option>
              <option value="Lucro Presumido">Lucro Presumido</option>
              <option value="Lucro Real">Lucro Real</option>
              <option value="Simples Nacional">Simples Nacional</option>
              <option value="Simples Nacional - Sub-Limite">
                Simples Nacional - Sub-Limite
              </option>
            </select>
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              PIS/COFINS
            </label>            
            <select
              name="pis_cofins"
              value={form.pis_cofins || ''}
              onChange={handleChange}
              className="modern-select"
            >
              <option value="">Selecione...</option>
              <option value="Cumulativo">Cumulativo</option>
              <option value="Não cumulativo">Não cumulativo</option>
              <option value="Ambos">Ambos</option>
            </select>
          </div>
        </div>
      )}

      {tab === 'observacoes' && (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Observações
          </label>
          <textarea
            name="observacoes"
            value={form.observacoes || ''}
            onChange={handleChange}
            className="modern-input h-32"
          />
        </div>
      )}
    </Modal>
  )
}
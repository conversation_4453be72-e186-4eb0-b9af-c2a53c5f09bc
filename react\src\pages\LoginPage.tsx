import { useEffect, useState } from 'react'
import { useAuthStore } from '@/store/authStore'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { authService } from '@/services/authService'

export function LoginPage() {
  const [message, setMessage] = useState('Validando token...')
  const [showPortalButton, setShowPortalButton] = useState(false)
  const { login } = useAuthStore()

  useEffect(() => {
    const validatePortalAuth = async () => {
      try {
        console.log('Iniciando validação do token do portal...')
        setMessage('Validando token...')
        
        // Usa o authService para validar o token
        const data = await authService.validatePortalToken()
        console.log('Resposta da validação:', data)

        // Sucesso na autenticação
        const user = {
          id: data.usuario_id,
          nome: data.nome,
          email: '', // Não vem na resposta atual
          tipo_usuario: data.tipo_usuario,
          escritorio_id: data.escritorio_id,
          is_admin: data.is_admin,
        }

        login(data.access_token, user)
        setMessage('Autenticação realizada com sucesso! Redirecionando...')
        
      } catch (error: any) {
        console.error('Erro na validação:', error)
        
        // Trata diferentes tipos de erro
        if (error.response?.status === 401) {
          setMessage('Token inválido ou expirado.')
        } else if (error.response?.status === 403) {
          setMessage('Acesso não permitido para o sistema Fiscal.')
        } else if (error.response?.data?.message) {
          setMessage(error.response.data.message)
        } else {
          setMessage('Erro ao validar sessão. Por favor, tente novamente.')
        }
        
        setShowPortalButton(true)
      }
    }

    validatePortalAuth()
  }, [login])

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      {/* Background shapes */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200 rounded-full opacity-20 animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-300 rounded-full opacity-20 animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-primary-100 rounded-full opacity-30 animate-pulse delay-500" />
      </div>

      <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 w-full max-w-md">
        <div className="text-center">
          {/* Logo */}
          <div className="mb-8">
            <div className="w-16 h-16 rounded-xl mx-auto mb-4 flex items-center justify-center">
              <img 
               src="/fiscal/logo-branca.svg" 
               alt="Toggle sidebar" 
               className="w-16 h-16"
             />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Bem-vindo ao INTTAX Fiscal
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Autenticação via Portal
            </p>
          </div>

          {/* Loading and message */}
          <div className="space-y-4">
            <LoadingSpinner size="lg" className="mx-auto" />
            <p className="text-gray-600 dark:text-gray-400">{message}</p>
            
            {showPortalButton && (
              <a
                href="https://www.audittei.com.br/portal/"
                className="btn btn-primary btn-lg w-full mt-6"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Ir para o Portal
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
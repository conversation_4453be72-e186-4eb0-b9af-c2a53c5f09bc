import React, { useState, forwardRef } from 'react'
import { cn } from '@/utils/cn'

interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string
  error?: string
  helperText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'filled' | 'outlined'
  fullWidth?: boolean
  loading?: boolean
}

const inputSizes = {
  sm: 'px-3 py-2 text-sm',
  md: 'px-4 py-3 text-sm',
  lg: 'px-5 py-4 text-base'
}

const inputVariants = {
  default: 'border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 focus:border-primary-500 focus:ring-primary-500',
  filled: 'border-0 bg-gray-100 dark:bg-gray-700 focus:bg-white dark:focus:bg-gray-800 focus:ring-2 focus:ring-primary-500',
  outlined: 'border-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary-500 focus:ring-0'
}

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  size = 'md',
  variant = 'default',
  fullWidth = false,
  loading = false,
  className,
  disabled,
  ...props
}, ref) => {
  const [focused, setFocused] = useState(false)
  const hasValue = props.value !== undefined ? String(props.value).length > 0 : false

  return (
    <div className={cn('relative', fullWidth && 'w-full')}>
      {/* Floating Label */}
      {label && (
        <label
          className={cn(
            'absolute left-3 transition-all duration-200 pointer-events-none',
            focused || hasValue
              ? 'top-2 text-xs text-primary-600 dark:text-primary-400 font-medium'
              : size === 'sm'
                ? 'top-2.5 text-sm text-gray-500 dark:text-gray-400'
                : size === 'lg'
                  ? 'top-4 text-base text-gray-500 dark:text-gray-400'
                  : 'top-3 text-sm text-gray-500 dark:text-gray-400',
            leftIcon && 'left-10'
          )}
        >
          {label}
        </label>
      )}

      <div className="relative">
        {/* Left Icon */}
        {leftIcon && (
          <div className={cn(
            'absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500',
            size === 'sm' ? 'w-4 h-4' : 'w-5 h-5'
          )}>
            {leftIcon}
          </div>
        )}

        {/* Input */}
        <input
          ref={ref}
          className={cn(
            'w-full rounded-xl transition-all duration-200 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500',
            'focus:outline-none focus:ring-2 focus:ring-offset-0',
            inputSizes[size],
            inputVariants[variant],
            leftIcon && 'pl-10',
            rightIcon && 'pr-10',
            label && (focused || hasValue) && 'pt-6 pb-2',
            error && 'border-error-500 focus:border-error-500 focus:ring-error-500',
            disabled && 'opacity-50 cursor-not-allowed bg-gray-100 dark:bg-gray-800',
            className
          )}
          disabled={disabled || loading}
          onFocus={(e) => {
            setFocused(true)
            props.onFocus?.(e)
          }}
          onBlur={(e) => {
            setFocused(false)
            props.onBlur?.(e)
          }}
          {...props}
        />

        {/* Right Icon / Loading */}
        {(rightIcon || loading) && (
          <div className={cn(
            'absolute right-3 top-1/2 transform -translate-y-1/2',
            size === 'sm' ? 'w-4 h-4' : 'w-5 h-5'
          )}>
            {loading ? (
              <svg className="animate-spin text-primary-500" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
            ) : (
              <span className="text-gray-400 dark:text-gray-500">{rightIcon}</span>
            )}
          </div>
        )}
      </div>

      {/* Helper Text / Error */}
      {(error || helperText) && (
        <p className={cn(
          'mt-2 text-xs',
          error ? 'text-error-600 dark:text-error-400' : 'text-gray-500 dark:text-gray-400'
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  )
})

Input.displayName = 'Input'

// Textarea Component
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  helperText?: string
  resize?: 'none' | 'vertical' | 'horizontal' | 'both'
  fullWidth?: boolean
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(({
  label,
  error,
  helperText,
  resize = 'vertical',
  fullWidth = false,
  className,
  disabled,
  ...props
}, ref) => {
  const [focused, setFocused] = useState(false)
  const hasValue = props.value !== undefined ? String(props.value).length > 0 : false

  return (
    <div className={cn('relative', fullWidth && 'w-full')}>
      {/* Floating Label */}
      {label && (
        <label
          className={cn(
            'absolute left-4 transition-all duration-200 pointer-events-none',
            focused || hasValue
              ? 'top-2 text-xs text-primary-600 dark:text-primary-400 font-medium'
              : 'top-3 text-sm text-gray-500 dark:text-gray-400'
          )}
        >
          {label}
        </label>
      )}

      <textarea
        ref={ref}
        className={cn(
          'w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800',
          'text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500',
          'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
          'transition-all duration-200',
          label && (focused || hasValue) && 'pt-6 pb-2',
          error && 'border-error-500 focus:border-error-500 focus:ring-error-500',
          disabled && 'opacity-50 cursor-not-allowed bg-gray-100 dark:bg-gray-800',
          resize === 'none' && 'resize-none',
          resize === 'vertical' && 'resize-y',
          resize === 'horizontal' && 'resize-x',
          resize === 'both' && 'resize',
          className
        )}
        disabled={disabled}
        onFocus={(e) => {
          setFocused(true)
          props.onFocus?.(e)
        }}
        onBlur={(e) => {
          setFocused(false)
          props.onBlur?.(e)
        }}
        {...props}
      />

      {/* Helper Text / Error */}
      {(error || helperText) && (
        <p className={cn(
          'mt-2 text-xs',
          error ? 'text-error-600 dark:text-error-400' : 'text-gray-500 dark:text-gray-400'
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  )
})

Textarea.displayName = 'Textarea'

// Select Component
interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string
  error?: string
  helperText?: string
  options: Array<{ value: string | number; label: string; disabled?: boolean }>
  placeholder?: string
  fullWidth?: boolean
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(({
  label,
  error,
  helperText,
  options,
  placeholder,
  fullWidth = false,
  className,
  disabled,
  ...props
}, ref) => {
  const [focused, setFocused] = useState(false)
  const hasValue = props.value !== undefined && props.value !== ''

  return (
    <div className={cn('relative', fullWidth && 'w-full')}>
      {/* Floating Label */}
      {label && (
        <label
          className={cn(
            'absolute left-4 transition-all duration-200 pointer-events-none z-10',
            focused || hasValue
              ? 'top-2 text-xs text-primary-600 dark:text-primary-400 font-medium'
              : 'top-3 text-sm text-gray-500 dark:text-gray-400'
          )}
        >
          {label}
        </label>
      )}

      <div className="relative">
        <select
          ref={ref}
          className={cn(
            'w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800',
            'text-gray-900 dark:text-gray-100',
            'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            'transition-all duration-200 appearance-none cursor-pointer',
            label && (focused || hasValue) && 'pt-6 pb-2',
            error && 'border-error-500 focus:border-error-500 focus:ring-error-500',
            disabled && 'opacity-50 cursor-not-allowed bg-gray-100 dark:bg-gray-800',
            className
          )}
          disabled={disabled}
          onFocus={(e) => {
            setFocused(true)
            props.onFocus?.(e)
          }}
          onBlur={(e) => {
            setFocused(false)
            props.onBlur?.(e)
          }}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          ))}
        </select>

        {/* Dropdown Arrow */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Helper Text / Error */}
      {(error || helperText) && (
        <p className={cn(
          'mt-2 text-xs',
          error ? 'text-error-600 dark:text-error-400' : 'text-gray-500 dark:text-gray-400'
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  )
})

Select.displayName = 'Select'
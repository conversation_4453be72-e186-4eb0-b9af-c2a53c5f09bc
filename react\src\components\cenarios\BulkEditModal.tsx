import { useState } from 'react'
import type { BulkEditData } from '@/types/cenarios'

interface BulkEditModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: BulkEditData) => void
}

export function BulkEditModal({ isOpen, onClose, onSave }: BulkEditModalProps) {
  const [formData, setFormData] = useState<BulkEditData>({})

  const handleChange = (field: keyof BulkEditData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = () => {
    const payload: BulkEditData = {}
    if (formData.ncm) payload.ncm = formData.ncm
    if (formData.cst) payload.cst = formData.cst
    if (formData.aliquota !== undefined && formData.aliquota !== null && formData.aliquota !== ('' as any)) {
      payload.aliquota = Number(formData.aliquota)
    }
    onSave(payload)
    setFormData({})
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={onClose}></div>
        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-auto p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Editar Cenários Selecionados</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">NCM</label>
              <input
                type="text"
                value={formData.ncm || ''}
                onChange={e => handleChange('ncm', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">CST</label>
              <input
                type="text"
                value={formData.cst || ''}
                onChange={e => handleChange('cst', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Alíquota (%)</label>
              <input
                type="number"
                step="0.01"
                value={formData.aliquota ?? ''}
                onChange={e => handleChange('aliquota', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>
          <div className="mt-6 flex justify-end space-x-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-500"
            >
              Cancelar
            </button>
            <button
              type="button"
              onClick={handleSave}
              className="px-4 py-2 text-sm bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              Salvar
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
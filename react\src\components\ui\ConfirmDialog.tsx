import React from 'react'
import { Modal } from './Modal'
import { But<PERSON> } from './Button'

interface ConfirmDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  variant?: 'error' | 'warning' | 'success' | 'info'
  loading?: boolean
  icon?: React.ReactNode
}

const variantConfig = {
  error: {
    color: 'error',
    bgColor: 'bg-error-100 dark:bg-error-900/30',
    iconColor: 'text-error-600 dark:text-error-400',
    defaultIcon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
      </svg>
    )
  },
  warning: {
    color: 'warning',
    bgColor: 'bg-warning-100 dark:bg-warning-900/30',
    iconColor: 'text-warning-600 dark:text-warning-400',
    defaultIcon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
    )
  },
  success: {
    color: 'success',
    bgColor: 'bg-success-100 dark:bg-success-900/30',
    iconColor: 'text-success-600 dark:text-success-400',
    defaultIcon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    )
  },
  info: {
    color: 'primary',
    bgColor: 'bg-primary-100 dark:bg-primary-900/30',
    iconColor: 'text-primary-600 dark:text-primary-400',
    defaultIcon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
      </svg>
    )
  }
}

export function ConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirmar',
  cancelText = 'Cancelar',
  variant = 'warning',
  loading = false,
  icon
}: ConfirmDialogProps) {
  const config = variantConfig[variant]

  const handleConfirm = () => {
    onConfirm()
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      showCloseButton={false}
      footer={
        <div className="flex gap-3 justify-end w-full">
          <Button
            variant="ghost"
            onClick={onClose}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            variant={config.color as any}
            onClick={handleConfirm}
            loading={loading}
            glow
          >
            {confirmText}
          </Button>
        </div>
      }
    >
      <div className="text-center py-4">
        {/* Icon */}
        <div className={`w-16 h-16 mx-auto mb-4 ${config.bgColor} rounded-2xl flex items-center justify-center`}>
          <div className={config.iconColor}>
            {icon || config.defaultIcon}
          </div>
        </div>

        {/* Title */}
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
          {title}
        </h3>

        {/* Message */}
        <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
          {message}
        </p>
      </div>
    </Modal>
  )
}

// Hook para usar o ConfirmDialog facilmente
export function useConfirmDialog() {
  const [dialog, setDialog] = React.useState<{
    isOpen: boolean
    title: string
    message: string
    onConfirm: () => void
    variant?: 'error' | 'warning' | 'success' | 'info'
    confirmText?: string
    cancelText?: string
    loading?: boolean
  }>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    variant: 'warning',
    loading: false
  })

  const confirm = React.useCallback((options: {
    title: string
    message: string
    onConfirm: () => void
    variant?: 'error' | 'warning' | 'success' | 'info'
    confirmText?: string
    cancelText?: string
  }) => {
    setDialog({
      isOpen: true,
      ...options,
      loading: false
    })
  }, [])

  const close = React.useCallback(() => {
    setDialog(prev => ({ ...prev, isOpen: false }))
  }, [])

  const setLoading = React.useCallback((loading: boolean) => {
    setDialog(prev => ({ ...prev, loading }))
  }, [])

  const ConfirmDialogComponent = React.useCallback(() => (
    <ConfirmDialog
      isOpen={dialog.isOpen}
      onClose={close}
      onConfirm={dialog.onConfirm}
      title={dialog.title}
      message={dialog.message}
      variant={dialog.variant}
      confirmText={dialog.confirmText}
      cancelText={dialog.cancelText}
      loading={dialog.loading}
    />
  ), [dialog, close])

  return {
    confirm,
    close,
    setLoading,
    ConfirmDialog: ConfirmDialogComponent
  }
}
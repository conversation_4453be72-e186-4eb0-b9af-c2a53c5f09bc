# Regras de Criação de Cenários na Importação

Este documento resume quando o sistema cria ou atualiza cenários fiscais automaticamente durante a importação de XML, separando as regras por tipo de imposto (ICMS, ICMS‑ST, IPI, PIS, COFINS e DIFAL).

## 1. Importação somente gera cenários para notas de saída
- O `XMLImportService` identifica se o XML é de entrada ou saída comparando o CNPJ da empresa com o emitente/destinatário.
- Cenários são gerados apenas se `tipo_nota == '1'` (nota de **saída**).
- Para notas de entrada (`tipo_nota == '0'`) nenhum cenário é criado.

Trechos de referência mostram essa lógica e a redução de 50% no volume de cenários ao criar apenas para notas de saída【F:docs/CENARIOS_SAIDA_AUTOMATICOS_XML.md†L1-L22】【F:docs/OTIMIZACAO_IMPORTACAO_XML.md†L230-L242】.

## 2. Verificação de cenários existentes
- Para cada tributo do item da nota, o serviço chama `criar_cenario_importacao` do `CenarioService`.
- Ele busca um cenário do mesmo cliente, produto e CFOP. Se existir, compara os campos relevantes conforme o tipo de imposto.
- A regra geral é:
  1. **Cenário em produção**
     - Se qualquer campo principal (incluindo NCM) divergir, cria-se um novo cenário com status `incons_*` para análise.
  2. **Cenário não produtivo ou valores idênticos**
     - Os dados do cenário existente são apenas atualizados.
  3. **CFOP inexistente**
     - Quando não há cenário com o CFOP informado, cria-se um novo cenário com status `novo`.

Os códigos que implementam essas regras estão nos métodos de `CenarioService`【F:back/services/cenario_service.py†L50-L135】.

## 3. Comparação específica por tipo de imposto
O `CenarioService` usa funções de consistência para cada tributo:
- **ICMS** e **ICMS‑ST** – Comparam CST, origem, modalidade de base, reduções, alíquotas e NCM.
- **IPI** – Compara CST, alíquota e `ex`.
- **PIS** e **COFINS** – Comparam CST, alíquota e percentual de redução da base.
- **DIFAL** – Compara CST, origem, modalidade de base, reduções e percentuais de ICMS interestadual e FCP.

Qualquer divergência nesses campos gera um cenário `incons_*`; caso contrário o cenário existente é reutilizado ou atualizado【F:back/services/cenario_service.py†L1221-L1455】.

## 4. Resumo final
- Cenários são criados automaticamente somente para notas de saída.
- Se já existe cenário em produção com mesmo CFOP e dados iguais, ele é atualizado; se houver divergência, cria-se um novo cenário `incons_*`.
- CFOP não cadastrado sempre gera cenário `novo`.
- A verificação de divergências considera campos específicos de cada tributo (ICMS, ICMS‑ST, IPI, PIS, COFINS e DIFAL).

Assim o sistema mantém consistência dos cenários fiscais, permitindo identificar diferenças que exigem aprovação manual antes de entrarem em produção.
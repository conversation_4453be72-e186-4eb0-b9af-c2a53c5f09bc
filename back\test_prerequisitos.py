import sys
import types
import os
import pytest
from unittest.mock import patch

# Criar stubs para evitar dependências de Flask nos testes
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'back'))

flask_stub = types.ModuleType("flask")
class DummyBlueprint:
    def __init__(self, *a, **k):
        pass
    def route(self, *a, **k):
        def decorator(f):
            return f
        return decorator

flask_stub.Blueprint = DummyBlueprint
flask_stub.request = None
flask_stub.jsonify = lambda *a, **k: None
flask_stub.current_app = None
sys.modules.setdefault("flask", flask_stub)

flask_jwt_stub = types.ModuleType("flask_jwt_extended")
flask_jwt_stub.jwt_required = lambda *a, **k: (lambda f: f)
flask_jwt_stub.get_jwt_identity = lambda: 1
sys.modules.setdefault("flask_jwt_extended", flask_jwt_stub)

# Stub para SQLAlchemy utilizado nos modelos

fsql_stub = types.ModuleType("flask_sqlalchemy")
class DummySQLAlchemy:
    Model = object
    Column = Integer = String = lambda *a, **k: None
    relationship = lambda *a, **k: None
fsql_stub.SQLAlchemy = lambda *a, **k: DummySQLAlchemy()
sys.modules.setdefault("flask_sqlalchemy", fsql_stub)

# Stub para o pacote models utilizado nos routes
models_stub = types.ModuleType("models")
class DummyQuery:
    def filter_by(self, **k):
        return self
    def filter(self, *a):
        return self
    def count(self):
        return 0
    def all(self):
        return []

class DummyModel:
    query = DummyQuery()

models_stub.AuditoriaSumario = DummyModel
models_stub.Tributo = DummyModel
models_stub.Usuario = models_stub.Cliente = models_stub.Produto = DummyModel
models_stub.TributoHistorico = models_stub.Empresa = DummyModel
models_stub.AuditoriaResultado = models_stub.NotaFiscalItem = DummyModel
models_stub.CenarioICMS = models_stub.CenarioICMSST = DummyModel
models_stub.CenarioIPI = models_stub.CenarioPIS = DummyModel
models_stub.CenarioCOFINS = models_stub.CenarioDIFAL = DummyModel
models_stub.db = DummySQLAlchemy()
sys.modules.setdefault("models", models_stub)

services_stub = types.ModuleType("services")
auditoria_service_stub = types.ModuleType("services.auditoria_service")
class DummyAuditoriaService:
    def __init__(self, *a, **k):
        pass
auditoria_service_stub.AuditoriaService = DummyAuditoriaService
services_stub.auditoria_service = auditoria_service_stub
sys.modules.setdefault("services", services_stub)
sys.modules.setdefault("services.auditoria_service", auditoria_service_stub)

# Stub simples para sqlalchemy
sqlalchemy_stub = types.ModuleType("sqlalchemy")
sqlalchemy_stub.and_ = lambda *a, **k: None
sqlalchemy_stub.or_ = lambda *a, **k: None
sys.modules.setdefault("sqlalchemy", sqlalchemy_stub)

import importlib.util

module_path = os.path.join(os.path.dirname(__file__), 'routes', 'auditoria_routes.py')
spec = importlib.util.spec_from_file_location('auditoria_routes', module_path)
auditoria_routes = importlib.util.module_from_spec(spec)
spec.loader.exec_module(auditoria_routes)
verificar_pre_requisitos_auditoria = auditoria_routes.verificar_pre_requisitos_auditoria

# Helper to create fake query return
class FakeQuery:
    def __init__(self, result):
        self._result = result
    def first(self):
        return self._result

def fake_filter_by_factory(results):
    def fake_filter_by(**kwargs):
        tipo = kwargs.get('tipo_tributo')
        return FakeQuery(results.get(tipo))
    return fake_filter_by


def test_icms_sem_sumario_ipi():
    results = {'ipi': None}
    with patch.object(auditoria_routes.AuditoriaSumario, 'query') as mock_query:
        mock_query.filter_by.side_effect = fake_filter_by_factory(results)
        ok, msg = verificar_pre_requisitos_auditoria(1, 'icms', 0, 2024, 5)
        assert not ok
        assert 'IPI' in msg


def test_icms_ok_com_sumario_ipi():
    results = {'ipi': object()}
    with patch.object(auditoria_routes.AuditoriaSumario, 'query') as mock_query:
        mock_query.filter_by.side_effect = fake_filter_by_factory(results)
        ok, msg = verificar_pre_requisitos_auditoria(1, 'icms', 0, 2024, 5)
        assert ok


def test_pis_requer_sumarios_ipi_icms():
    results = {'ipi': object(), 'icms': None}
    with patch.object(auditoria_routes.AuditoriaSumario, 'query') as mock_query:
        mock_query.filter_by.side_effect = fake_filter_by_factory(results)
        ok, msg = verificar_pre_requisitos_auditoria(1, 'pis', 0, 2024, 5)
        assert not ok
        assert 'IPI' in msg and 'ICMS' in msg


def test_pis_ok_com_sumarios():
    results = {'ipi': object(), 'icms': object()}
    with patch.object(auditoria_routes.AuditoriaSumario, 'query') as mock_query:
        mock_query.filter_by.side_effect = fake_filter_by_factory(results)
        ok, msg = verificar_pre_requisitos_auditoria(1, 'pis', 0, 2024, 5)
        assert ok

from flask import Blueprint, request, jsonify
from werkzeug.utils import secure_filename
import os

from back.models.escritorio import Escritorio

# Futuramente, usaremos o S3 e SQS
# import boto3
# import json

agente_bp = Blueprint('agente_bp', __name__)

# Simulação de upload para o diretório local, conforme a estrutura atual.
# Em produção na AWS, isso será substituído pelo upload para o S3.
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'xml'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@agente_bp.route('/agent/upload', methods=['POST'])
def upload_file_from_agent():
    # 1. Validar Headers
    api_key = request.headers.get('X-API-Key')
    escritorio_id = request.headers.get('X-Escritorio-ID')

    if not api_key or not escritorio_id:
        return jsonify({"error": "Cabeçalhos X-API-Key e X-Escritorio-ID são obrigatórios"}), 401

    # 2. Validar Credenciais
    escritorio = Escritorio.query.filter_by(id=escritorio_id, api_key=api_key).first()

    if not escritorio:
        return jsonify({"error": "Credenciais inválidas ou não encontradas."}), 403

    # 3. Validar e Salvar o Arquivo
    if 'file' not in request.files:
        return jsonify({"error": "Nenhum arquivo enviado"}), 400
    
    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "Nome de arquivo vazio"}), 400

    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        
        # TODO: Substituir esta lógica pelo upload para o S3 e notificação para o SQS
        # Por enquanto, salva localmente para manter a compatibilidade
        save_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(save_path)
        
        # Lógica futura com S3/SQS seria algo como:
        # s3_client.upload_fileobj(file, BUCKET_NAME, s3_key)
        # sqs_client.send_message(QueueUrl=QUEUE_URL, MessageBody=...)

        return jsonify({"message": f"Arquivo {filename} recebido com sucesso para o escritório {escritorio.nome_fantasia}"}), 200
    else:
        return jsonify({"error": "Tipo de arquivo não permitido. Apenas .xml é aceito."}), 400

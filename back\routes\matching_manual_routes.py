"""
Rotas para Matching Manual de Itens
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db
from models.usuario import Usuario
from models.empresa import Empresa
from services.matching_manual_service import MatchingManualService

matching_manual_bp = Blueprint('matching_manual', __name__)


@matching_manual_bp.route('/api/matching-manual/candidatos', methods=['GET'])
@jwt_required()
def buscar_candidatos_matching():
    """
    Busca candidatos XML para matching manual com um item SPED
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404
        
        # Parâmetros da requisição
        empresa_id = request.args.get('empresa_id', type=int)
        sped_item_id = request.args.get('sped_item_id', type=int)
        mes = request.args.get('mes', type=int)
        ano = request.args.get('ano', type=int)
        
        if not all([empresa_id, sped_item_id, mes, ano]):
            return jsonify({
                'success': False,
                'message': 'empresa_id, sped_item_id, mes e ano são obrigatórios'
            }), 400
        
        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({'message': 'Acesso negado'}), 403
        
        # Criar serviço e buscar candidatos
        service = MatchingManualService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )
        
        resultado = service.buscar_candidatos_para_matching(sped_item_id, mes, ano)
        
        if resultado['success']:
            return jsonify(resultado), 200
        else:
            return jsonify(resultado), 400
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@matching_manual_bp.route('/api/matching-manual/salvar', methods=['POST'])
@jwt_required()
def salvar_matching_manual():
    """
    Salva um matching manual temporário
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404
        
        data = request.get_json()
        
        # Validar parâmetros
        empresa_id = data.get('empresa_id')
        sped_item_id = data.get('sped_item_id')
        xml_items_ids = data.get('xml_items_ids', [])
        justificativa = data.get('justificativa', '')
        confianca = data.get('confianca', 5)
        
        if not all([empresa_id, sped_item_id]):
            return jsonify({
                'success': False,
                'message': 'empresa_id e sped_item_id são obrigatórios'
            }), 400
        
        if not xml_items_ids or not isinstance(xml_items_ids, list):
            return jsonify({
                'success': False,
                'message': 'xml_items_ids deve ser uma lista não vazia'
            }), 400
        
        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({'message': 'Acesso negado'}), 403
        
        # Criar serviço e salvar matching
        service = MatchingManualService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )
        
        resultado = service.salvar_matching_manual(
            sped_item_id=sped_item_id,
            xml_items_ids=xml_items_ids,
            justificativa=justificativa,
            confianca=confianca
        )
        
        if resultado['success']:
            return jsonify(resultado), 200
        else:
            return jsonify(resultado), 400
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@matching_manual_bp.route('/api/matching-manual/processar', methods=['POST'])
@jwt_required()
def processar_matching_manual():
    """
    Processa um matching manual temporário, criando os registros definitivos
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404
        
        data = request.get_json()
        
        # Validar parâmetros
        empresa_id = data.get('empresa_id')
        match_temporario_id = data.get('match_temporario_id')
        
        if not all([empresa_id, match_temporario_id]):
            return jsonify({
                'success': False,
                'message': 'empresa_id e match_temporario_id são obrigatórios'
            }), 400
        
        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({'message': 'Acesso negado'}), 403
        
        # Criar serviço e processar matching
        service = MatchingManualService(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )
        
        resultado = service.processar_matching_temporario(match_temporario_id)
        
        if resultado['success']:
            return jsonify(resultado), 200
        else:
            return jsonify(resultado), 400
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@matching_manual_bp.route('/api/matching-manual/cancelar', methods=['POST'])
@jwt_required()
def cancelar_matching_manual():
    """
    Cancela um matching manual temporário
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404
        
        data = request.get_json()
        
        # Validar parâmetros
        match_temporario_id = data.get('match_temporario_id')
        
        if not match_temporario_id:
            return jsonify({
                'success': False,
                'message': 'match_temporario_id é obrigatório'
            }), 400
        
        # Buscar e cancelar match temporário
        from models.matching_manual_temporario import MatchingManualTemporario
        
        match_temporario = MatchingManualTemporario.query.filter_by(
            id=match_temporario_id,
            usuario_id=usuario_id,
            status='pendente'
        ).first()
        
        if not match_temporario:
            return jsonify({
                'success': False,
                'message': 'Match temporário não encontrado ou já processado'
            }), 404
        
        # Cancelar
        match_temporario.status = 'cancelado'
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Match manual cancelado com sucesso'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@matching_manual_bp.route('/api/matching-manual/pendentes', methods=['GET'])
@jwt_required()
def listar_matchings_pendentes():
    """
    Lista matchings manuais pendentes do usuário
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404
        
        empresa_id = request.args.get('empresa_id', type=int)
        
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400
        
        # Verificar permissão
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa or empresa.escritorio_id != usuario.escritorio_id:
            return jsonify({'message': 'Acesso negado'}), 403
        
        # Buscar matchings pendentes
        from models.matching_manual_temporario import MatchingManualTemporario
        
        matchings_pendentes = MatchingManualTemporario.query.filter_by(
            empresa_id=empresa_id,
            usuario_id=usuario_id,
            status='pendente'
        ).order_by(MatchingManualTemporario.data_criacao.desc()).all()
        
        matchings_data = [match.to_dict() for match in matchings_pendentes]
        
        return jsonify({
            'success': True,
            'matchings_pendentes': matchings_data,
            'total': len(matchings_data)
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

import { useState } from 'react'
import { clientesService } from '@/services/clientesService'
import type { Cliente } from '@/types/clientes'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'

interface ClienteBulkEditModalProps {
  isOpen: boolean
  onClose: () => void
  onSaved: () => void
  selectedIds: number[]
  atividades?: string[]
  destinacoes?: string[]
}

export function ClienteBulkEditModal({
  isOpen,
  onClose,
  onSaved,
  selectedIds,
  atividades = [],
  destinacoes = []
}: ClienteBulkEditModalProps) {
  const [form, setForm] = useState<Partial<Cliente>>({})
  const [loading, setLoading] = useState(false)

  if (!isOpen) return null

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target
    setForm((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      await clientesService.bulkUpdateClientes(selectedIds, form)
      onSaved()
      onClose()
    } catch (err: any) {
      alert(err.message || 'Erro ao aplicar edição em massa')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Edição em Massa - ${selectedIds.length} Participante(s)`}
      size="md"
      footer={
        <div className="flex gap-3">
          <Button
            variant="ghost"
            onClick={onClose}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            loading={loading}
            glow
          >
            Aplicar Alterações
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-xl p-4">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="text-sm text-blue-800 dark:text-blue-200">
              <p className="font-semibold mb-1">Edição em massa</p>
              <p>As alterações serão aplicadas a todos os {selectedIds.length} participantes selecionados. Campos em branco manterão os valores atuais.</p>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Atividade
            </label>
            <select
              name="atividade"
              value={form.atividade || ''}
              onChange={handleChange}
              className="modern-select"
            >
              <option value="">Manter valor atual</option>
              {atividades.map((a) => (
                <option key={a} value={a}>
                  {a}
                </option>
              ))}
            </select>
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Destinação
            </label>
            <select
              name="destinacao"
              value={form.destinacao || ''}
              onChange={handleChange}
              className="modern-select"
            >
              <option value="">Manter valor atual</option>
              {destinacoes.map((d) => (
                <option key={d} value={d}>
                  {d}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </Modal>
  )
}
import xml.etree.ElementTree as ET
from datetime import datetime

class XMLProcessor:
    """
    Classe para processar arquivos XML de notas fiscais eletrônicas (NFe)
    """

    def __init__(self, xml_content):
        """
        Inicializa o processador com o conteúdo do XML

        Args:
            xml_content (str): Conteúdo do arquivo XML
        """
        try:
            # Tentar corrigir problemas comuns em XMLs
            xml_content = self._fix_common_xml_issues(xml_content)

            self.root = ET.fromstring(xml_content)
            # Definir o namespace padrão para facilitar as buscas
            self.ns = {'nfe': 'http://www.portalfiscal.inf.br/nfe'}

            # Extrair a NFe do documento
            self.nfe = self.root.find('.//nfe:NFe', self.ns)
            if self.nfe is None:
                self.nfe = self.root  # Se não encontrar, usa a raiz

            # Extrair infNFe
            self.inf_nfe = self.nfe.find('.//nfe:infNFe', self.ns)

            if self.inf_nfe is None:
                raise ValueError("Não foi possível encontrar as informações da NFe no XML")
        except ET.ParseError as e:
            # Capturar erros de parsing e fornecer mensagem mais detalhada
            line_col = str(e).split(':', 1)[0]
            raise ValueError(f"Erro ao analisar XML: tag não correspondente na {line_col}. Verifique se o XML está bem formado.")
        except Exception as e:
            raise ValueError(f"Erro ao processar XML: {str(e)}")

    def _fix_common_xml_issues(self, xml_content):
        """
        Corrige problemas comuns em arquivos XML

        Args:
            xml_content (str): Conteúdo do arquivo XML

        Returns:
            str: Conteúdo do XML corrigido
        """
        # Remover caracteres inválidos no início do arquivo
        xml_content = xml_content.lstrip()

        # Garantir que o arquivo começa com a declaração XML
        if not xml_content.startswith('<?xml'):
            xml_content = '<?xml version="1.0" encoding="UTF-8"?>\n' + xml_content

        # Remover caracteres de controle inválidos
        import re
        xml_content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', xml_content)

        return xml_content

    def _find_text(self, xpath, namespace=None, default=None):
        """
        Busca um texto no XML usando XPath

        Args:
            xpath (str): Caminho XPath para o elemento
            namespace: Namespace para a busca (opcional)
            default: Valor padrão caso não encontre

        Returns:
            str: Texto do elemento ou valor padrão
        """
        # Se namespace não for fornecido, usar o namespace padrão
        ns = namespace if namespace is not None else self.ns

        try:
            element = self.inf_nfe.find(xpath, ns)
            # Verificar se o elemento existe e se tem um atributo text
            if element is not None:
                # Retornar o texto do elemento ou o valor padrão se o texto for None
                return element.text if element.text is not None else default
            return default
        except Exception as e:
            print(f"Erro ao buscar elemento {xpath}: {str(e)}")
            return default

    def _parse_date(self, date_str):
        """
        Converte uma string de data no formato ISO para objeto date

        Args:
            date_str (str): String de data no formato ISO

        Returns:
            date: Objeto date ou None se inválido
        """
        if not date_str:
            return None

        try:
            # Formato: 2025-04-01T13:09:18-03:00
            return datetime.fromisoformat(date_str).date()
        except (ValueError, TypeError):
            return None

    def _parse_number(self, number_str):
        """
        Converte uma string numérica para float

        Args:
            number_str (str): String numérica

        Returns:
            float: Valor numérico ou None se inválido
        """
        if not number_str:
            return None

        try:
            # Substituir vírgula por ponto para números decimais
            number_str = number_str.replace(',', '.')
            return float(number_str)
        except (ValueError, TypeError):
            return None

    def _get_valor_icms(self, icms_grupo):
        """Obtém o valor do ICMS considerando várias tags"""
        valor = self._get_text_from_element(icms_grupo, './/nfe:vICMS')
        if valor is None:
            valor = self._get_text_from_element(icms_grupo, './/nfe:vCredICMSSN')
            if valor is None:
                valor = self._get_text_from_element(icms_grupo, './/nfe:vICMSMonoRet')
        return self._parse_number(valor)

    def _get_aliquota_icms(self, icms_grupo):
        """Obtém a alíquota do ICMS considerando pICMS, pCredSN e adRemICMSRet"""
        aliquota = self._get_text_from_element(icms_grupo, './/nfe:pICMS')
        if aliquota is None:
            aliquota = self._get_text_from_element(icms_grupo, './/nfe:pCredSN')
        if aliquota is None:
            aliquota = self._get_text_from_element(icms_grupo, './/nfe:adRemICMSRet')
        return self._parse_number(aliquota)
    
    def get_emitente(self):
        """
        Extrai informações do emitente

        Returns:
            dict: Dicionário com informações do emitente
        """
        emit = self.inf_nfe.find('.//nfe:emit', self.ns)
        if emit is None:
            return {}

        # Extrair a inscrição estadual como texto, não como objeto
        ie_value = self._find_text('.//nfe:emit/nfe:IE')

        # Extrair CNPJ ou CPF do emitente
        cnpj = self._find_text('.//nfe:emit/nfe:CNPJ')
        cpf = self._find_text('.//nfe:emit/nfe:CPF')

        # Determinar o tipo de pessoa (física ou jurídica)
        tipo_pessoa = 'J' if cnpj else 'F' if cpf else None

        return {
            'cnpj': cnpj,
            'cpf': cpf,
            'tipo_pessoa': tipo_pessoa,
            'nome': self._find_text('.//nfe:emit/nfe:xNome'),
            'fantasia': self._find_text('.//nfe:emit/nfe:xFant'),
            'ie': ie_value,
            'crt': self._find_text('.//nfe:emit/nfe:CRT'),  # Código de Regime Tributário
            'endereco': {
                'logradouro': self._find_text('.//nfe:emit/nfe:enderEmit/nfe:xLgr'),
                'numero': self._find_text('.//nfe:emit/nfe:enderEmit/nfe:nro'),
                'bairro': self._find_text('.//nfe:emit/nfe:enderEmit/nfe:xBairro'),
                'municipio': self._find_text('.//nfe:emit/nfe:enderEmit/nfe:xMun'),
                'uf': self._find_text('.//nfe:emit/nfe:enderEmit/nfe:UF'),
                'cep': self._find_text('.//nfe:emit/nfe:enderEmit/nfe:CEP'),
                'pais': self._find_text('.//nfe:emit/nfe:enderEmit/nfe:xPais'),
                'codigo_pais': self._find_text('.//nfe:emit/nfe:enderEmit/nfe:cPais'),
            }
        }

    def get_destinatario(self):
        """
        Extrai informações do destinatário

        Returns:
            dict: Dicionário com informações do destinatário
        """
        dest = self.inf_nfe.find('.//nfe:dest', self.ns)
        if dest is None:
            return {}

        # Extrair informações de indIEDest e indFinal
        ind_ie_dest = self._find_text('.//nfe:dest/nfe:indIEDest')
        ind_final = self._find_text('.//nfe:ide/nfe:indFinal')

        # Extrair a inscrição estadual como texto, não como objeto
        ie_value = self._find_text('.//nfe:dest/nfe:IE')

        # Extrair CNPJ ou CPF
        cnpj = self._find_text('.//nfe:dest/nfe:CNPJ')
        cpf = self._find_text('.//nfe:dest/nfe:CPF')

        # Determinar o tipo de pessoa (física ou jurídica)
        tipo_pessoa = 'J' if cnpj else 'F' if cpf else None

        return {
            'cnpj': cnpj,
            'cpf': cpf,
            'tipo_pessoa': tipo_pessoa,
            'nome': self._find_text('.//nfe:dest/nfe:xNome'),
            'ie': ie_value,
            'logradouro': self._find_text('.//nfe:dest/nfe:enderDest/nfe:xLgr'),
            'numero': self._find_text('.//nfe:dest/nfe:enderDest/nfe:nro'),
            'bairro': self._find_text('.//nfe:dest/nfe:enderDest/nfe:xBairro'),
            'municipio': self._find_text('.//nfe:dest/nfe:enderDest/nfe:xMun'),
            'uf': self._find_text('.//nfe:dest/nfe:enderDest/nfe:UF'),
            'cep': self._find_text('.//nfe:dest/nfe:enderDest/nfe:CEP'),
            'pais': self._find_text('.//nfe:dest/nfe:enderDest/nfe:xPais'),
            'codigo_pais': self._find_text('.//nfe:dest/nfe:enderDest/nfe:cPais'),
            'ind_ie_dest': ind_ie_dest,
            'ind_final': ind_final,
        }

    def get_info_nfe(self):
        """
        Extrai informações gerais da NFe

        Returns:
            dict: Dicionário com informações da NFe
        """
        ide = self.inf_nfe.find('.//nfe:ide', self.ns)
        if ide is None:
            return {}

        # Extrair datas
        data_emissao = self._find_text('.//nfe:ide/nfe:dhEmi')
        data_saida = self._find_text('.//nfe:ide/nfe:dhSaiEnt')

        # Extrair tipo de operação (0=entrada, 1=saída)
        tipo_operacao = self._find_text('.//nfe:ide/nfe:tpNF')

        return {
            'chave': self.inf_nfe.get('Id', '').replace('NFe', '') if self.inf_nfe is not None else '',
            'numero': self._find_text('.//nfe:ide/nfe:nNF'),
            'serie': self._find_text('.//nfe:ide/nfe:serie'),
            'data_emissao': self._parse_date(data_emissao),
            'data_saida': self._parse_date(data_saida),
            'natureza_operacao': self._find_text('.//nfe:ide/nfe:natOp'),
            'tipo_operacao': tipo_operacao,  # 0=entrada, 1=saída
            'fin_nfe': self._find_text('.//nfe:ide/nfe:finNFe'),
        }

    def get_totais(self):
        """
        Extrai informações dos totais da NFe

        Returns:
            dict: Dicionário com informações dos totais
        """
        total = self.inf_nfe.find('.//nfe:total', self.ns)
        if total is None:
            return {}

        icms_tot = total.find('.//nfe:ICMSTot', self.ns)
        if icms_tot is None:
            return {}

        # Valor do IPI
        valor_ipi = self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vIPI'))
        if valor_ipi is None or valor_ipi == 0:
            valor_ipi_devol = self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vIPIDevol'))
            if valor_ipi_devol is not None:
                valor_ipi = valor_ipi_devol

        return {
            'valor_bc_icms': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vBC')),
            'valor_icms': self._get_valor_icms(icms_tot),
            'valor_bc_icms_st': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vBCST')),
            'valor_icms_st': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vST')),
            'valor_produtos': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vProd')),
            'valor_frete': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vFrete')),
            'valor_seguro': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vSeg')),
            'valor_desconto': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vDesc')),
            'valor_ii': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vII')),
            'valor_ipi': valor_ipi,
            'valor_pis': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vPIS')),
            'valor_cofins': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vCOFINS')),
            'valor_outros': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vOutro')),
            'valor_total_nf': self._parse_number(self._get_text_from_element(icms_tot, './/nfe:vNF')),
        }

    def get_produtos(self):
        """
        Extrai informações de todos os produtos

        Returns:
            list: Lista de dicionários com informações dos produtos
        """
        produtos = []

        # Buscar todos os itens (det)
        itens = self.inf_nfe.findall('.//nfe:det', self.ns)

        # Obter totais da nota para calcular frete proporcional
        totais = self.get_totais()
        valor_frete_total = totais.get('valor_frete', 0) or 0
        valor_produtos_total = totais.get('valor_produtos', 0) or 0

        for item in itens:
            num_item = item.get('nItem', '0')
            prod = item.find('.//nfe:prod', self.ns)

            if prod is None:
                continue

            valor_produto = self._parse_number(self._get_text_from_element(prod, './/nfe:vProd')) or 0

            # Extrair desconto específico do item (se houver)
            valor_desconto_item = self._parse_number(self._get_text_from_element(prod, './/nfe:vDesc')) or 0

            # IMPORTANTE: Não aplicar desconto proporcional automaticamente
            # O desconto proporcional só deve ser aplicado se houver desconto total na nota
            # E se não houver desconto específico no item
            # Por enquanto, vamos comentar esta lógica para evitar descontos incorretos
            # if valor_desconto_item == 0 and valor_desconto_total > 0 and valor_produtos_total > 0:
            #     proporcao = valor_produto / valor_produtos_total
            #     valor_desconto_item = valor_desconto_total * proporcao

            # Calcular frete proporcional para este item
            valor_frete_item = 0
            if valor_produtos_total > 0 and valor_frete_total > 0:
                proporcao = valor_produto / valor_produtos_total
                valor_frete_item = valor_frete_total * proporcao

            # Extrair informações do produto
            produto = {
                'num_item': num_item,
                'codigo': self._get_text_from_element(prod, './/nfe:cProd'),
                'extipi': self._get_text_from_element(prod, './/nfe:EXTIPI'),
                'descricao': self._get_text_from_element(prod, './/nfe:xProd'),
                'ncm': self._get_text_from_element(prod, './/nfe:NCM'),
                'cfop': self._get_text_from_element(prod, './/nfe:CFOP'),
                'unidade_comercial': self._get_text_from_element(prod, './/nfe:uCom'),
                'quantidade': self._parse_number(self._get_text_from_element(prod, './/nfe:qCom')),
                'valor_unitario': self._parse_number(self._get_text_from_element(prod, './/nfe:vUnCom')),
                'valor_total': valor_produto,
                'valor_frete': valor_frete_item,  # Novo campo para frete proporcional
                'valor_desconto': valor_desconto_item,  # Novo campo para desconto proporcional
                'unidade_tributavel': self._get_text_from_element(prod, './/nfe:uTrib'),
                # Novos campos
                'codigo_ean': self._get_text_from_element(prod, './/nfe:cEAN'),
                'codigo_ean_tributavel': self._get_text_from_element(prod, './/nfe:cEANTrib'),
                'unidade_tributaria': self._get_text_from_element(prod, './/nfe:uTrib'),
                'cest': self._get_text_from_element(prod, './/nfe:CEST'),
            }

            # Extrair informações de impostos
            imposto = item.find('.//nfe:imposto', self.ns)
            if imposto is not None:
                produto.update(self._get_impostos(imposto, item))

            produtos.append(produto)

        return produtos

    def _get_text_from_element(self, element, xpath):
        """
        Extrai texto de um elemento usando XPath relativo

        Args:
            element: Elemento XML
            xpath: Caminho XPath relativo

        Returns:
            str: Texto do elemento ou None
        """
        try:
            child = element.find(xpath, self.ns)
            if child is not None:
                return child.text if child.text is not None else None
            return None
        except Exception as e:
            print(f"Erro ao extrair texto do elemento {xpath}: {str(e)}")
            return None

    def _get_impostos(self, imposto, item=None):
        """
        Extrai informações de impostos de um item da NFe

        Args:
            imposto: Elemento XML do imposto
            item: Elemento <det> correspondente (opcional)

        Returns:
            dict: Dicionário com informações dos impostos
        """
        result = {}

        # ICMS - Pode ter diferentes grupos (ICMS00, ICMS10, ICMS20, etc.) ou Simples Nacional (ICMSSN)
        icms = imposto.find('.//nfe:ICMS', self.ns)
        if icms is not None:
            # Primeiro, verificar se é Simples Nacional (ICMSSN)
            icms_grupo = None
            csosn_grupos = ['ICMSSN101', 'ICMSSN102', 'ICMSSN103', 'ICMSSN201', 'ICMSSN202', 'ICMSSN203', 'ICMSSN300', 'ICMSSN400', 'ICMSSN500', 'ICMSSN900']

            for grupo in csosn_grupos:
                icms_grupo = icms.find(f'.//nfe:{grupo}', self.ns)
                if icms_grupo is not None:
                    # Processar dados do Simples Nacional
                    aliquota = self._get_aliquota_icms(icms_grupo)
                    result.update({
                        'icms_origem': self._get_text_from_element(icms_grupo, './/nfe:orig'),
                        'icms_csosn': self._get_text_from_element(icms_grupo, './/nfe:CSOSN'),
                        # Alguns grupos do Simples Nacional podem ter outros campos
                        'icms_mod_bc': self._get_text_from_element(icms_grupo, './/nfe:modBC'),
                        'icms_p_red_bc': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:pRedBC')),
                        'icms_vbc': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:vBC')),
                        'icms_aliquota': aliquota,
                        'icms_valor': self._get_valor_icms(icms_grupo),
                    })
                    print(f"DEBUG - Simples Nacional detectado: {grupo}, CSOSN: {result.get('icms_csosn')}")
                    break

            # Se não for Simples Nacional, processar como ICMS normal
            if icms_grupo is None:
                for grupo in ['ICMS00', 'ICMS10', 'ICMS20', 'ICMS30', 'ICMS40', 'ICMS51', 'ICMS60', 'ICMS61', 'ICMS70', 'ICMS90']:
                    icms_grupo = icms.find(f'.//nfe:{grupo}', self.ns)
                    if icms_grupo is not None:
                        break

                if icms_grupo is not None:
                    # Campos básicos do ICMS
                    result.update({
                        'icms_origem': self._get_text_from_element(icms_grupo, './/nfe:orig'),
                        'icms_cst': self._get_text_from_element(icms_grupo, './/nfe:CST'),
                        'icms_mod_bc': self._get_text_from_element(icms_grupo, './/nfe:modBC'),
                        'icms_p_red_bc': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:pRedBC')),
                        'icms_vbc': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:vBC')),
                        'icms_aliquota': self._get_aliquota_icms(icms_grupo),
                        'icms_valor': self._get_valor_icms(icms_grupo),
                        # Novos campos para ICMS
                        'icms_v_op': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:vICMSOp')),
                        'icms_p_dif': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:pDif')),
                        'icms_v_dif': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:vICMSDif')),
                        # orig, CST, modBC, pRedBC, pICMS, pDif (difal e icms-st nao precisa)
                    })

                    # ICMS-ST (presente em alguns grupos como ICMS10, ICMS30, etc.)
                    if icms_grupo.find('.//nfe:modBCST', self.ns) is not None:
                        result.update({
                            'icms_st_mod_bc': self._get_text_from_element(icms_grupo, './/nfe:modBCST'),
                            'icms_st_p_mva': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:pMVAST')),
                            'icms_st_p_red_bc': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:pRedBCST')),
                            'icms_st_vbc': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:vBCST')),
                            'icms_st_aliquota': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:pICMSST')),
                            'icms_st_valor': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:vICMSST')),
                            'icms_st_vbc_str': self._parse_number(self._get_text_from_element(icms_grupo, './/nfe:vBCSTRet')),
                            # vBCSTRet, pICMSST, pMVAST, modBCST
                        })

        # IPI
        ipi = imposto.find('.//nfe:IPI', self.ns)
        if ipi is not None:
            # Extrair código de enquadramento do IPI
            ipi_codigo_enquadramento = self._get_text_from_element(ipi, './/nfe:cEnq')
            # Valor de IPI devolvido pode estar fora do grupo IPI
            ipi_valor_devol = None
            if item is not None:
                ipi_valor_devol = self._parse_number(
                    self._get_text_from_element(
                        item, './/nfe:impostoDevol//nfe:IPI//nfe:vIPIDevol'
                    )
                )

            ipi_trib = ipi.find('.//nfe:IPITrib', self.ns)
            if ipi_trib is not None:
                ipi_valor = self._parse_number(
                    self._get_text_from_element(ipi_trib, './/nfe:vIPI')
                )
                if ipi_valor is None or ipi_valor == 0:
                    if ipi_valor_devol is not None:
                        ipi_valor = ipi_valor_devol

                result.update({
                    'ipi_cst': self._get_text_from_element(ipi_trib, './/nfe:CST'),
                    'ipi_vbc': self._parse_number(self._get_text_from_element(ipi_trib, './/nfe:vBC')),
                    'ipi_aliquota': self._parse_number(self._get_text_from_element(ipi_trib, './/nfe:pIPI')),
                    'ipi_valor': ipi_valor,
                    'ipi_codigo_enquadramento': ipi_codigo_enquadramento,
                    # CST, pIPI
                })

            # Verificar se há IPI NT (Não Tributado)
            ipi_nt = ipi.find('.//nfe:IPINT', self.ns)
            if ipi_nt is not None:
                result.update({
                    'ipi_cst': self._get_text_from_element(ipi_nt, './/nfe:CST'),
                    'ipi_codigo_enquadramento': ipi_codigo_enquadramento,
                })

            # Verificar se há IPI Outros
            ipi_outros = ipi.find('.//nfe:IPIOutr', self.ns)
            if ipi_outros is not None:
                ipi_valor = self._parse_number(
                    self._get_text_from_element(ipi_outros, './/nfe:vIPI')
                )
                if ipi_valor is None or ipi_valor == 0:
                    if ipi_valor_devol is not None:
                        ipi_valor = ipi_valor_devol
                        
                result.update({
                    'ipi_cst': self._get_text_from_element(ipi_outros, './/nfe:CST'),
                    'ipi_vbc': self._parse_number(self._get_text_from_element(ipi_outros, './/nfe:vBC')),
                    'ipi_aliquota': self._parse_number(self._get_text_from_element(ipi_outros, './/nfe:pIPI')),
                    'ipi_valor': ipi_valor,
                    'ipi_codigo_enquadramento': ipi_codigo_enquadramento,
                })

        # PIS
        pis = imposto.find('.//nfe:PIS', self.ns)
        if pis is not None:
            # Verificar diferentes grupos de PIS
            pis_grupo = None
            for grupo in ['PISAliq', 'PISQtde', 'PISNT', 'PISOutr']:
                pis_grupo = pis.find(f'.//nfe:{grupo}', self.ns)
                if pis_grupo is not None:
                    break

            if pis_grupo is not None:
                result.update({
                    'pis_cst': self._get_text_from_element(pis_grupo, './/nfe:CST'),
                    'pis_vbc': self._parse_number(self._get_text_from_element(pis_grupo, './/nfe:vBC')),
                    'pis_aliquota': self._parse_number(self._get_text_from_element(pis_grupo, './/nfe:pPIS')),
                    'pis_valor': self._parse_number(self._get_text_from_element(pis_grupo, './/nfe:vPIS')),
                    # CST, pPIS
                })

        # COFINS
        cofins = imposto.find('.//nfe:COFINS', self.ns)
        if cofins is not None:
            # Verificar diferentes grupos de COFINS
            cofins_grupo = None
            for grupo in ['COFINSAliq', 'COFINSQtde', 'COFINSNT', 'COFINSOutr']:
                cofins_grupo = cofins.find(f'.//nfe:{grupo}', self.ns)
                if cofins_grupo is not None:
                    break

            if cofins_grupo is not None:
                result.update({
                    'cofins_cst': self._get_text_from_element(cofins_grupo, './/nfe:CST'),
                    'cofins_vbc': self._parse_number(self._get_text_from_element(cofins_grupo, './/nfe:vBC')),
                    'cofins_aliquota': self._parse_number(self._get_text_from_element(cofins_grupo, './/nfe:pCOFINS')),
                    'cofins_valor': self._parse_number(self._get_text_from_element(cofins_grupo, './/nfe:vCOFINS')),
                    # CST, pCOFINS
                })

        # DIFAL (ICMSUFDest)
        icms_uf_dest = imposto.find('.//nfe:ICMSUFDest', self.ns)
        if icms_uf_dest is not None:
            result.update({
                'difal_vbc': self._parse_number(self._get_text_from_element(icms_uf_dest, './/nfe:vBC')),
                'difal_p_fcp_uf_dest': self._parse_number(self._get_text_from_element(icms_uf_dest, './/nfe:pFCPUFDest')),
                'difal_p_icms_uf_dest': self._parse_number(self._get_text_from_element(icms_uf_dest, './/nfe:pICMSUFDest')),
                'difal_p_icms_inter': self._parse_number(self._get_text_from_element(icms_uf_dest, './/nfe:pICMSInter')),
                'difal_p_icms_inter_part': self._parse_number(self._get_text_from_element(icms_uf_dest, './/nfe:pICMSInterPart')),
                'difal_v_fcp_uf_dest': self._parse_number(self._get_text_from_element(icms_uf_dest, './/nfe:vFCPUFDest')),
                'difal_v_icms_uf_dest': self._parse_number(self._get_text_from_element(icms_uf_dest, './/nfe:vICMSUFDest')),
                'difal_v_icms_uf_remet': self._parse_number(self._get_text_from_element(icms_uf_dest, './/nfe:vICMSUFRemet')),
                # pFCPUFDest, pICMSUFDest, pICMSInter, pICMSInterPart
            })

        return result

    def get_protocolo(self):
        """Extrai dados do protocolo da NF-e (cStat, xMotivo, nProt)."""
        try:
            prot = self.root.find('.//nfe:protNFe', self.ns)
            if prot is None:
                prot = self.root.find('.//protNFe')
            if prot is None:
                return {}

            inf_prot = prot.find('.//nfe:infProt', self.ns)
            if inf_prot is None:
                inf_prot = prot.find('.//infProt')
            if inf_prot is None:
                return {}

            def _text(tag):
                el = inf_prot.find(f'nfe:{tag}', self.ns)
                if el is None:
                    el = inf_prot.find(tag)
                return el.text if el is not None else None

            return {
                'cStat': _text('cStat'),
                'xMotivo': _text('xMotivo'),
                'nProt': _text('nProt'),
            }
        except Exception:
            return {}

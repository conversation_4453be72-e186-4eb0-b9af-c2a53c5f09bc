export interface Produto {
    id: number
    codigo: string
    descricao: string
    unidade_comercial?: string
    unidade_tributavel?: string
    unidade_tributaria?: string
    tipo_sped?: string
    ncm?: string
    codigo_ean?: string
    codigo_ean_tributavel?: string
    cest?: string
    classificacao_tributaria?: string
    status?: string
    data_cadastro?: string
  }
  
  export interface Pagination {
    current_page: number
    last_page: number
    per_page: number
    total: number
    from: number
    to: number
    has_next: boolean
    has_prev: boolean
    next_page?: number
    prev_page?: number
  }
  
  export interface ProdutosResponse {
    produtos: Produto[]
    pagination: Pagination
  }
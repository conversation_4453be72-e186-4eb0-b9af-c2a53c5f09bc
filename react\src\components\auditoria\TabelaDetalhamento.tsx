import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Table } from '@/components/ui/Table'
import { Modal } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import { Tooltip } from '@/components/ui/Tooltip'
import { ResultadoInconsistente, auditoriaService } from '@/services/auditoriaService'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { useMutation, useQueryClient } from '@tanstack/react-query'

interface TabelaDetalhamentoProps {
  data: ResultadoInconsistente[]
  loading?: boolean
  tipoTributo: string
}

interface DetalhesModalProps {
  isOpen: boolean
  onClose: () => void
  tipo: 'nota' | 'cenario'
  id: number
  empresaId: number
}

const atividadeOptions = [
  'Indústria ou Equiparado',
  'Comércio Varejista',
  'Comércio Atacadista',
  'Distribuidor',
  'Produtor Rural',
  'Consumidor Final',
  'Não Contribuinte',
  'Órgão Público',
  'Serviços',
]

const destinacaoOptions = [
  'Industrialização',
  'Revenda',
  'Varejista',
  'Ativo Imobilizado',
  'Uso e Consumo',
]

function DetalhesModal({ isOpen, onClose, tipo, id, empresaId }: DetalhesModalProps) {
  const [detalhes, setDetalhes] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [editData, setEditData] = useState({
    atividade: '',
    destinacao: ''
  })
  const [saving, setSaving] = useState(false)

  const queryClient = useQueryClient()

  React.useEffect(() => {
    if (isOpen && id) {
      setLoading(true)
      auditoriaService.obterDetalhes(tipo, id, empresaId)
        .then((data) => {
          setDetalhes(data)
          setEditData({
            atividade: data.cliente?.atividade || '',
            destinacao: data.cliente?.destinacao || ''
          })
        })
        .catch(console.error)
        .finally(() => setLoading(false))
    }
  }, [isOpen, tipo, id, empresaId])

  const handleSave = async () => {
    if (!detalhes?.cliente?.id) return

    setSaving(true)
    try {
      await auditoriaService.atualizarCliente(detalhes.cliente.id, {
        atividade: editData.atividade,
        destinacao: editData.destinacao
      })
      
      // Atualizar os detalhes localmente
      setDetalhes(prev => ({
        ...prev,
        cliente: {
          ...prev.cliente,
          atividade: editData.atividade,
          destinacao: editData.destinacao
        }
      }))
      
      // Invalidar queries para atualizar a tabela
      queryClient.invalidateQueries({ queryKey: ['auditoria-detalhamento'] })
      
      setEditMode(false)
    } catch (error) {
      console.error('Erro ao salvar:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setEditData({
      atividade: detalhes?.cliente?.atividade || '',
      destinacao: detalhes?.cliente?.destinacao || ''
    })
    setEditMode(false)
  }

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title={`Detalhes da ${tipo === 'nota' ? 'Nota Fiscal' : 'Cenário'}`}
      size="lg"
    >
      <div className="space-y-6">
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : detalhes ? (
          <div className="space-y-6">
            {/* Cliente */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-semibold text-gray-900 dark:text-white">Cliente</h4>
                {tipo === 'nota' && (
                  <div className="flex gap-2">
                    {editMode ? (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleCancel}
                          disabled={saving}
                        >
                          Cancelar
                        </Button>
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={handleSave}
                          loading={saving}
                        >
                          Salvar
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditMode(true)}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        }
                      >
                        Editar
                      </Button>
                    )}
                  </div>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Razão Social:</span>
                  <p className="font-medium mt-1">{detalhes.cliente?.razao_social || '-'}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">CNPJ:</span>
                  <p className="font-medium mt-1">{detalhes.cliente?.cnpj || '-'}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Inscrição Estadual:</span>
                  <p className="font-medium mt-1">{detalhes.cliente?.inscricao_estadual || '-'}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">UF:</span>
                  <p className="font-medium mt-1">{detalhes.cliente?.uf || '-'}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Município:</span>
                  <p className="font-medium mt-1">{detalhes.cliente?.municipio || '-'}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Atividade:</span>
                  {editMode ? (
                    <select
                      value={editData.atividade}
                      onChange={(e) => setEditData(prev => ({ ...prev, atividade: e.target.value }))}
                      className="w-full mt-1 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="">Selecione...</option>
                      {atividadeOptions.map(atividade => (
                        <option key={atividade} value={atividade}>
                          {atividade}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <p className="font-medium mt-1">{detalhes.cliente?.atividade || '-'}</p>
                  )}
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Destinação:</span>
                  {editMode ? (
                    <select
                      value={editData.destinacao}
                      onChange={(e) => setEditData(prev => ({ ...prev, destinacao: e.target.value }))}
                      className="w-full mt-1 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="">Selecione...</option>
                      {destinacaoOptions.map(destinacao => (
                        <option key={destinacao} value={destinacao}>
                          {destinacao}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <p className="font-medium mt-1">{detalhes.cliente?.destinacao || '-'}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Produto */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Produto</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Código:</span>
                  <p className="font-medium mt-1">{detalhes.produto?.codigo || '-'}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">CEST:</span>
                  <p className="font-medium mt-1">{detalhes.produto?.cest || '-'}</p>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-600 dark:text-gray-400">Descrição:</span>
                  <p className="font-medium mt-1">{detalhes.produto?.descricao || '-'}</p>
                </div>
              </div>
            </div>

            {/* Dados específicos */}
            {tipo === 'nota' ? (
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Dados da Nota Fiscal</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">NCM:</span>
                    <p className="font-medium mt-1">{detalhes.nota_fiscal_item?.ncm || '-'}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">CFOP:</span>
                    <p className="font-medium mt-1">{detalhes.nota_fiscal_item?.cfop || '-'}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">CST:</span>
                    <p className="font-medium mt-1">{detalhes.tributo?.cst || '-'}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Data de Emissão:</span>
                    <p className="font-medium mt-1">
                      {detalhes.tributo?.data_emissao 
                        ? new Date(detalhes.tributo.data_emissao).toLocaleDateString('pt-BR')
                        : '-'
                      }
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Base de Cálculo:</span>
                    <p className="font-medium mt-1">
                      {detalhes.tributo?.base_calculo 
                        ? new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(detalhes.tributo.base_calculo)
                        : '-'
                      }
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Alíquota:</span>
                    <p className="font-medium mt-1">
                      {detalhes.tributo?.aliquota ? `${detalhes.tributo.aliquota}%` : '-'}
                    </p>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-600 dark:text-gray-400">Valor do Tributo:</span>
                    <p className="font-medium mt-1 text-lg">
                      {detalhes.tributo?.valor 
                        ? new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(detalhes.tributo.valor)
                        : '-'
                      }
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Dados do Cenário</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">ID do Cenário:</span>
                    <p className="font-medium mt-1">{detalhes.cenario?.id || '-'}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Status:</span>
                    <p className="font-medium mt-1">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        detalhes.cenario?.status === 'producao' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      }`}>
                        {detalhes.cenario?.status || '-'}
                      </span>
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">NCM:</span>
                    <p className="font-medium mt-1">{detalhes.cenario?.ncm || '-'}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">CFOP:</span>
                    <p className="font-medium mt-1">{detalhes.cenario?.cfop || '-'}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">CST:</span>
                    <p className="font-medium mt-1">{detalhes.cenario?.cst || '-'}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Alíquota:</span>
                    <p className="font-medium mt-1">
                      {detalhes.cenario?.aliquota ? `${detalhes.cenario.aliquota}%` : '-'}
                    </p>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-600 dark:text-gray-400">Período de Validade:</span>
                    <p className="font-medium mt-1">
                      {detalhes.cenario?.validade_inicio && detalhes.cenario?.validade_fim
                        ? `${new Date(detalhes.cenario.validade_inicio).toLocaleDateString('pt-BR')} - ${new Date(detalhes.cenario.validade_fim).toLocaleDateString('pt-BR')}`
                        : '-'
                      }
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
              <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <p className="text-gray-500 dark:text-gray-400">Erro ao carregar detalhes</p>
          </div>
        )}
      </div>
    </Modal>
  )
}

export function TabelaDetalhamento({ data, loading, tipoTributo }: TabelaDetalhamentoProps) {
  const [selectedIds, setSelectedIds] = useState<number[]>([])
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set())
  const [detalhesModal, setDetalhesModal] = useState<{
    isOpen: boolean
    tipo: 'nota' | 'cenario'
    id: number
  }>({ isOpen: false, tipo: 'nota', id: 0 })
  const [observacoesModal, setObservacoesModal] = useState<{
    isOpen: boolean
    observacoes: string
    data: string
  }>({ isOpen: false, observacoes: '', data: '' })
  const [marcarVistaModal, setMarcarVistaModal] = useState<{
    isOpen: boolean
    ids: number[]
    observacoes: string
  }>({ isOpen: false, ids: [], observacoes: '' })

  const empresaId = useSelectedCompany()
  const queryClient = useQueryClient()

  const marcarVistaMutation = useMutation({
    mutationFn: ({ ids, observacoes }: { ids: number[], observacoes?: string }) => {
      if (ids.length === 1) {
        return auditoriaService.marcarComoVista(ids[0], observacoes)
      } else {
        return auditoriaService.marcarSelecionadosComoVista(ids, observacoes)
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auditoria-detalhamento'] })
      queryClient.invalidateQueries({ queryKey: ['auditoria-dashboard'] }) // Atualizar dashboard também
      setSelectedIds([])
      setMarcarVistaModal({ isOpen: false, ids: [], observacoes: '' })
    },
  })

  // Limpar seleções de registros que foram analisados
  React.useEffect(() => {
    const analyzedIds = data.filter(item => item.analista_visualizou).map(item => item.id)
    setSelectedIds(prev => prev.filter(id => !analyzedIds.includes(id)))
  }, [data])

  const formatCurrency = (value?: number) => {
    if (value === undefined || value === null) return 'R$ 0,00'
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value)
  }

  const formatPercentage = (value?: number) => {
    if (value === undefined || value === null) return '-'
    return `${value.toFixed(2)}%`
  }

  const toggleRowExpansion = (id: number) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedRows(newExpanded)
  }

  const toggleSelectAll = () => {
    const selectableItems = data.filter(item => !item.analista_visualizou)
    const selectableIds = selectableItems.map(item => item.id)
    
    if (selectedIds.length === selectableIds.length && selectableIds.length > 0) {
      setSelectedIds([])
    } else {
      setSelectedIds(selectableIds)
    }
  }

  const toggleSelectRow = (id: number) => {
    const item = data.find(item => item.id === id)
    
    // Não permite selecionar se já foi analisado
    if (item?.analista_visualizou) {
      return
    }
    
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
    )
  }

  const gerarRelatorio = async (id: number) => {
    try {
      const blob = await auditoriaService.gerarRelatorio(id)
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `relatorio-inconsistencia-${id}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Erro ao gerar relatório:', error)
    }
  }

  // Criar dados expandidos para a tabela
  const expandedData = data.flatMap(item => {
    const rows = [{ ...item, type: 'nota' as const }]
    if (expandedRows.has(item.id)) {
      rows.push({ ...item, type: 'cenario' as const })
    }
    return rows
  })

  const columns = [
    {
      key: 'select',
      title: (
        <input
          type="checkbox"
          checked={selectedIds.length === data.filter(item => !item.analista_visualizou).length && data.filter(item => !item.analista_visualizou).length > 0}
          onChange={toggleSelectAll}
          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
        />
      ),
      width: '40px',
      render: (_, record: any) => {
        if (record.type === 'cenario') return null
        
        const isAnalyzed = record.analista_visualizou
        
        return (
          <input
            type="checkbox"
            checked={selectedIds.includes(record.id)}
            onChange={() => toggleSelectRow(record.id)}
            disabled={isAnalyzed}
            className={`rounded border-gray-300 text-primary-600 focus:ring-primary-500 ${
              isAnalyzed ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            title={isAnalyzed ? 'Este registro já foi analisado' : ''}
          />
        )
      },
    },
    {
      key: 'expand',
      title: '',
      width: '40px',
      render: (_, record: any) => {
        if (record.type === 'cenario') return null
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleRowExpansion(record.id)}
            icon={
              expandedRows.has(record.id) ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              )
            }
          />
        )
      },
    },
    {
      key: 'origem',
      title: 'Origem',
      width: '80px',
      render: (_, record: any) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          record.type === 'cenario' 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        }`}>
          {record.type === 'cenario' ? 'Cenário' : 'NFe'}
        </span>
      ),
    },
    {
      key: 'numero',
      title: 'Número',
      width: '120px',
      render: (_, record: any) => record.type === 'cenario' ? record.cenario.numero : record.numero,
    },
    {
      key: 'cfop',
      title: 'CFOP',
      width: '100px',
      render: (_, record: any) => {
        const notaCfop = record.cfop
        const cenarioCfop = record.cenario?.cfop
        const isDifferent = notaCfop !== cenarioCfop
        const value = record.type === 'cenario' ? cenarioCfop : notaCfop
        
        return (
          <span className={isDifferent && record.type === 'cenario' ? 'text-green-600 font-bold' : 
                          isDifferent && record.type === 'nota' ? 'text-red-600 font-bold' : ''}>
            {value || '-'}
          </span>
        )
      },
    },
    {
      key: 'produto_numero',
      title: 'Produto',
      width: '100px',
      render: (_, record: any) => record.produto_numero || '-',
    },
    {
      key: 'produto_descricao',
      title: 'Descrição',
      render: (_, record: any) => (
        <Tooltip content={record.produto_descricao || '-'}>
          <span className="truncate max-w-xs block">
            {record.produto_descricao || '-'}
          </span>
        </Tooltip>
      ),
    },
    {
      key: 'ncm',
      title: 'NCM',
      width: '120px',
      render: (_, record: any) => {
        const notaNcm = record.ncm
        const cenarioNcm = record.cenario?.ncm
        const isDifferent = notaNcm !== cenarioNcm
        const value = record.type === 'cenario' ? cenarioNcm : notaNcm
        
        return (
          <span className={isDifferent && record.type === 'cenario' ? 'text-green-600 font-bold' : 
                          isDifferent && record.type === 'nota' ? 'text-red-600 font-bold' : ''}>
            {value || '-'}
          </span>
        )
      },
    },
    {
      key: 'cest',
      title: 'CEST',
      width: '100px',
      render: (_, record: any) => record.cest || '-',
    },
    {
      key: 'cst',
      title: 'CST',
      width: '80px',
      render: (_, record: any) => {
        const notaCst = record.cst
        const cenarioCst = record.cenario?.cst
        const isDifferent = notaCst !== cenarioCst
        const value = record.type === 'cenario' ? cenarioCst : notaCst
        
        return (
          <span className={isDifferent && record.type === 'cenario' ? 'text-green-600 font-bold' : 
                          isDifferent && record.type === 'nota' ? 'text-red-600 font-bold' : ''}>
            {value || '-'}
          </span>
        )
      },
    },
    {
      key: 'base_calculo',
      title: 'Base Cálculo',
      align: 'right' as const,
      render: (_, record: any) => {
        const isDifferent = record.comparacao?.base_calculo_diferente
        const value = record.type === 'cenario' ? record.cenario.base_calculo : record.base_calculo
        
        return (
          <span className={isDifferent && record.type === 'cenario' ? 'text-green-600' : 
                          isDifferent && record.type === 'nota' ? 'text-red-600' : ''}>
            {formatCurrency(value)}
          </span>
        )
      },
    },
    {
      key: 'aliquota',
      title: 'Alíquota',
      align: 'right' as const,
      render: (_, record: any) => {
        const isDifferent = record.comparacao?.aliquota_diferente
        const value = record.type === 'cenario' ? record.cenario.aliquota : record.aliquota
        
        return (
          <span className={isDifferent && record.type === 'cenario' ? 'text-green-600' : 
                          isDifferent && record.type === 'nota' ? 'text-red-600' : ''}>
            {formatPercentage(value)}
          </span>
        )
      },
    },
    {
      key: 'valor',
      title: 'Valor',
      align: 'right' as const,
      render: (_, record: any) => {
        const isDifferent = record.comparacao?.valor_diferente
        const value = record.type === 'cenario' ? record.cenario.valor : record.valor
        
        return (
          <span className={isDifferent && record.type === 'cenario' ? 'text-green-600' : 
                          isDifferent && record.type === 'nota' ? 'text-red-600' : ''}>
            {formatCurrency(value)}
          </span>
        )
      },
    },
    {
      key: 'status',
      title: 'Status',
      width: '120px',
      render: (_, record: any) => {
        if (record.type === 'cenario') {
          return (
            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Cenário
            </span>
          )
        }
        return (
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Inconsistente
          </span>
        )
      },
    },
    {
      key: 'acoes',
      title: 'Ações',
      width: '150px',
      render: (_, record: any) => {
        if (record.type === 'cenario') {
          return (
            <div className="flex gap-1">
              <Tooltip content="Visualizar detalhes">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setDetalhesModal({ isOpen: true, tipo: 'cenario', id: record.id })}
                  icon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  }
                />
              </Tooltip>
            </div>
          )
        }

        return (
          <div className="flex gap-1">
            <Tooltip content="Visualizar detalhes">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDetalhesModal({ isOpen: true, tipo: 'nota', id: record.id })}
                icon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                }
              />
            </Tooltip>
            
            <Tooltip content="Gerar relatório PDF">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => gerarRelatorio(record.id)}
                icon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                }
              />
            </Tooltip>

            {record.analista_visualizou ? (
              <Tooltip content="Ver observações da análise">
                <Button
                  variant="success"
                  size="sm"
                  onClick={() => setObservacoesModal({
                    isOpen: true,
                    observacoes: record.observacoes_analista || '',
                    data: record.data_visualizacao ? new Date(record.data_visualizacao).toLocaleDateString('pt-BR') : 'N/A'
                  })}
                  icon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  }
                />
              </Tooltip>
            ) : (
              <Tooltip content="Marcar como analisada">
                <Button
                  variant="warning"
                  size="sm"
                  onClick={() => setMarcarVistaModal({ isOpen: true, ids: [record.id], observacoes: '' })}
                  icon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  }
                />
              </Tooltip>
            )}
          </div>
        )
      },
    },
  ]

  return (
    <div className="space-y-4">
      {/* Botão para marcar selecionados */}
      {selectedIds.length > 0 && (
        <div className="flex justify-end">
          <Button
            variant="primary"
            onClick={() => setMarcarVistaModal({ isOpen: true, ids: selectedIds, observacoes: '' })}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            }
          >
            Marcar {selectedIds.length} selecionados como analisados
          </Button>
        </div>
      )}

      {/* Tabela */}
      <Card>
        <Table
          data={expandedData}
          columns={columns}
          loading={loading}
          emptyText="Nenhum resultado inconsistente encontrado"
          rowClassName={(record: any) => 
            record.type === 'cenario' 
              ? 'bg-green-50 dark:bg-green-900/10' 
              : ''
          }
        />
      </Card>

      {/* Modal de detalhes */}
      <DetalhesModal
        isOpen={detalhesModal.isOpen}
        onClose={() => setDetalhesModal({ isOpen: false, tipo: 'nota', id: 0 })}
        tipo={detalhesModal.tipo}
        id={detalhesModal.id}
        empresaId={empresaId!}
      />

      {/* Modal de observações */}
      <Modal
        isOpen={observacoesModal.isOpen}
        onClose={() => setObservacoesModal({ isOpen: false, observacoes: '', data: '' })}
        title="Observações da Análise"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Data da análise:
            </label>
            <p className="text-sm text-gray-600 dark:text-gray-400">{observacoesModal.data}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Observações:
            </label>
            <p className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              {observacoesModal.observacoes || 'Nenhuma observação registrada.'}
            </p>
          </div>
        </div>
      </Modal>

      {/* Modal para marcar como vista */}
      <Modal
        isOpen={marcarVistaModal.isOpen}
        onClose={() => setMarcarVistaModal({ isOpen: false, ids: [], observacoes: '' })}
        title={`Marcar ${marcarVistaModal.ids.length > 1 ? `${marcarVistaModal.ids.length} inconsistências` : 'inconsistência'} como analisada`}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Observações (opcional):
            </label>
            <textarea
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              rows={4}
              placeholder="Digite suas observações sobre a análise..."
              value={marcarVistaModal.observacoes}
              onChange={(e) => setMarcarVistaModal(prev => ({ ...prev, observacoes: e.target.value }))}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="ghost"
              onClick={() => setMarcarVistaModal({ isOpen: false, ids: [], observacoes: '' })}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              loading={marcarVistaMutation.isPending}
              onClick={() => marcarVistaMutation.mutate({
                ids: marcarVistaModal.ids,
                observacoes: marcarVistaModal.observacoes || undefined
              })}
            >
              Marcar como Analisada
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
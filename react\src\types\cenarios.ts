export interface CenarioBase {
  id: number
  empresa_id: number
  escritorio_id: number
  cliente_id: number
  produto_id: number
  direcao: 'entrada' | 'saida'
  tipo_operacao: string
  cfop: string
  ncm: string
  status: 'novo' | 'producao' | 'inconsistente'
  data_inicio_vigencia?: string
  data_fim_vigencia?: string
  ativo: boolean
  data_criacao: string
  data_atualizacao: string
  
  // Relacionamentos
  cliente?: {
    id: number
    razao_social: string
    cnpj: string
    uf: string
    atividade?: string
    destinacao?: string
    simples_nacional?: boolean
  }
  produto?: {
    id: number
    descricao: string
    codigo: string
    cest?: string
  }
}

export interface CenarioICMS extends CenarioBase {
  origem: string
  cst: string
  mod_bc: string
  p_red_bc?: number
  aliquota?: number
  p_dif?: number
  incluir_frete: boolean
  incluir_desconto: boolean
}

export interface CenarioICMSST extends CenarioBase {
  origem: string
  cst: string
  mod_bc: string
  p_red_bc?: number
  aliquota?: number
  icms_st_mod_bc: string
  icms_st_aliquota?: number
  icms_st_p_mva?: number
  icms_st_p_red_bc?: number
  incluir_frete: boolean
  incluir_desconto: boolean
}

export interface CenarioIPI extends CenarioBase {
  cst: string
  aliquota?: number
  ex?: string
  incluir_frete: boolean
  incluir_desconto: boolean
}

export interface CenarioPIS extends CenarioBase {
  cst: string
  aliquota?: number
  p_red_bc?: number
  incluir_frete: boolean
  incluir_desconto: boolean
}

export interface CenarioCOFINS extends CenarioBase {
  cst: string
  aliquota?: number
  p_red_bc?: number
  incluir_frete: boolean
  incluir_desconto: boolean
}

export interface CenarioDIFAL extends CenarioBase {
  origem: string
  cst: string
  mod_bc: string
  p_red_bc?: number
  aliquota?: number
  p_fcp_uf_dest?: number
  p_icms_uf_dest?: number
  p_icms_inter?: number
  p_icms_inter_part?: number
}

export type TipoTributo = 'icms' | 'icms_st' | 'ipi' | 'pis' | 'cofins' | 'difal'

export type CenarioTributo = CenarioICMS | CenarioICMSST | CenarioIPI | CenarioPIS | CenarioCOFINS | CenarioDIFAL

export interface CenariosResponse {
  success: boolean
  cenarios: CenarioTributo[]
  pagination: {
    total: number
    page: number
    per_page: number
    pages: number
  }
}

export interface FiltrosCenarios {
  empresa_id?: number
  cliente_id?: number
  produto_id?: number
  cfop?: string
  ncm?: string
  cst?: string
  atividade?: string
  destinacao?: string
  status?: string
  direcao?: 'entrada' | 'saida'
  search?: string
}

export interface BulkEditData {
  ncm?: string
  cst?: string
  aliquota?: number
}

export interface OpcoesFiltro {
  clientes: Array<{ id: number; razao_social: string; cnpj: string }>
  produtos: Array<{ id: number; descricao: string; codigo: string }>
  cfops: string[]
  ncms: string[]
  csts: string[]
  status: string[]
}
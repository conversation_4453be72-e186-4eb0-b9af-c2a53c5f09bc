import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Modal } from '@/components/ui/Modal'

interface GlobalSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  totalFilteredCount: number
  visibleCount: number
  selectedCount: number
  actionType: 'production' | 'inconsistent' | 'delete' | 'edit'
  onConfirm: (applyToAll: boolean) => void
}

const ACTION_LABELS = {
  production: {
    title: 'Enviar para Produção',
    description: 'enviar para produção',
    icon: '✅',
    color: 'success'
  },
  inconsistent: {
    title: 'Marcar como Inconsistente', 
    description: 'marcar como inconsistente',
    icon: '⚠️',
    color: 'warning'
  },
  delete: {
    title: 'Excluir Cenários',
    description: 'excluir',
    icon: '🗑️',
    color: 'danger'
  },
  edit: {
    title: 'Editar em Massa',
    description: 'editar',
    icon: '✏️',
    color: 'blue'
  }
} as const

export function GlobalSelectionModal({
  isOpen,
  onClose,
  totalFilteredCount,
  visibleCount,
  selectedCount,
  actionType,
  onConfirm
}: GlobalSelectionModalProps) {
  const [applyToAll, setApplyToAll] = useState(false)
  const action = ACTION_LABELS[actionType]
  const hiddenCount = totalFilteredCount - visibleCount

  const handleConfirm = () => {
    onConfirm(applyToAll)
    onClose()
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={action.title}>
      <div className="space-y-6">
        {/* Informações sobre a seleção */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <div className="flex items-start gap-3">
            <div className="text-2xl">{action.icon}</div>
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100">
                Ação em Cenários Filtrados
              </h3>
              <div className="text-sm text-blue-700 dark:text-blue-300 mt-1 space-y-1">
                <p>• <strong>{selectedCount}</strong> cenários selecionados (visíveis)</p>
                <p>• <strong>{visibleCount}</strong> cenários visíveis no total</p>
                <p>• <strong>{totalFilteredCount}</strong> cenários encontrados com os filtros aplicados</p>
                {hiddenCount > 0 && (
                  <p className="text-orange-700 dark:text-orange-300">
                    • <strong>{hiddenCount}</strong> cenários não visíveis (não carregados ainda)
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Opções de aplicação */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900 dark:text-white">
            Escolha o escopo da ação:
          </h4>
          
          <div className="space-y-3">
            {/* Opção 1: Apenas selecionados */}
            <label className="flex items-start gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
              <input
                type="radio"
                name="scope"
                value="selected"
                checked={!applyToAll}
                onChange={() => setApplyToAll(false)}
                className="mt-1"
              />
              <div>
                <div className="font-medium text-gray-900 dark:text-white">
                  Apenas cenários selecionados ({selectedCount})
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {action.description} apenas os {selectedCount} cenários que você selecionou manualmente
                </div>
              </div>
            </label>

            {/* Opção 2: Todos os filtrados */}
            {hiddenCount > 0 && (
              <label className="flex items-start gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
                <input
                  type="radio"
                  name="scope"
                  value="all"
                  checked={applyToAll}
                  onChange={() => setApplyToAll(true)}
                  className="mt-1"
                />
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    Todos os cenários filtrados ({totalFilteredCount})
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {action.description} TODOS os {totalFilteredCount} cenários que atendem aos filtros aplicados,
                    incluindo os {hiddenCount} não visíveis
                  </div>
                  <div className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                    ⚠️ Esta ação afetará cenários que você não pode ver na tela atual
                  </div>
                </div>
              </label>
            )}
          </div>
        </div>

        {/* Resumo da ação */}
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <div className="text-sm">
            <strong>Resumo:</strong> Você vai {action.description}{' '}
            <strong className={`text-${action.color}-600 dark:text-${action.color}-400`}>
              {applyToAll ? totalFilteredCount : selectedCount} cenários
            </strong>
            {applyToAll && hiddenCount > 0 && (
              <span className="text-orange-600 dark:text-orange-400">
                {' '}(incluindo {hiddenCount} não visíveis)
              </span>
            )}
          </div>
        </div>

        {/* Botões de ação */}
        <div className="flex justify-end gap-3">
          <Button variant="ghost" onClick={onClose}>
            Cancelar
          </Button>
          <Button 
            variant={action.color as any}
            onClick={handleConfirm}
            disabled={selectedCount === 0}
          >
            {action.title}
          </Button>
        </div>
      </div>
    </Modal>
  )
}

import { useState, useRef, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useImportacao } from '@/hooks/useImportacao'
import { importacaoService } from '@/services/importacaoService'
import { ProgressBar } from '@/components/importacao/ProgressBar'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'
import { Pagination } from '@/components/ui/Table'
import { HelpButton, HelpModal } from '@/components/ui'
import {
  FileSpreadsheet,
  Upload,
  Info,
  Layers,
  Zap,
  CheckCircle2,
  XCircle,
  FileCode,
  FileUp,
  FileArchive,
} from 'lucide-react'

type ImportMode = 'individual' | 'batch' | 'optimized'
type TabType = 'xml' | 'sped'

export function ImportacaoPage() {
  const [activeTab, setActiveTab] = useState<TabType>('xml')
  const [historyTab, setHistoryTab] = useState<TabType>('xml')
  const [isImporting, setIsImporting] = useState(false)
  const [isHelpOpen, setIsHelpOpen] = useState(false)
  const [importResult, setImportResult] = useState<{
    type: 'success' | 'error' | 'warning'
    message: string
    totals?: {
      notas: number
      itens: number
      clientes: number
      produtos: number
    }
  } | null>(null)

  const xmlFileRef = useRef<HTMLInputElement>(null)
  const spedFileRef = useRef<HTMLInputElement>(null)

  const {
    progress,
    startImport,
    finishImport,
    generateImportId,
    updateProgress,
  } = useImportacao()

  const {
    data: xmlImports,
    isLoading: loadingXML,
    refetch: refetchXML,
  } = useQuery({
    queryKey: ['importacoes-xml'],
    queryFn: () => importacaoService.getImportacoesXML(),
    enabled: historyTab === 'xml',
  })

  const {
    data: spedImports,
    isLoading: loadingSPED,
    refetch: refetchSPED,
  } = useQuery({
    queryKey: ['importacoes-sped'],
    queryFn: () => importacaoService.getImportacoesSPED(),
    enabled: historyTab === 'sped',
  })

  const ITEMS_PER_PAGE = 20
  const [xmlPage, setXmlPage] = useState(1)
  const [spedPage, setSpedPage] = useState(1)

  useEffect(() => {
    setXmlPage(1)
  }, [xmlImports])

  useEffect(() => {
    setSpedPage(1)
  }, [spedImports])

  const paginatedXmlImports = xmlImports?.slice(
    (xmlPage - 1) * ITEMS_PER_PAGE,
    xmlPage * ITEMS_PER_PAGE
  )

  const paginatedSpedImports = spedImports?.slice(
    (spedPage - 1) * ITEMS_PER_PAGE,
    spedPage * ITEMS_PER_PAGE
  )

  const handleXMLImport = async (mode: ImportMode) => {
    const fileInput = xmlFileRef.current
    if (!fileInput?.files?.length) {
      setImportResult({
        type: 'error',
        message: 'Selecione pelo menos um arquivo para importar.',
      })
      return
    }

    setIsImporting(true)
    setImportResult(null)

    try {
      const files = fileInput.files
      const importId = generateImportId()

      if (mode === 'individual' && files.length === 1) {
        const result = await importacaoService.importarXML(files[0])
        const isSuccess =
          result.success !== false &&
          !result.message?.toLowerCase().includes('erro')
        setImportResult({
          type: isSuccess ? 'success' : 'error',
          message: result.message,
        })
      } else {
        const totalFiles = Array.from(files).reduce((total, file) => {
          if (file.name.endsWith('.zip')) {
            return total + 10 // Estimativa
          }
          return total + 1
        }, 0)

        startImport(importId, totalFiles)

        const result =
          mode === 'batch'
            ? await importacaoService.importarXMLBatch(files, importId)
            : await importacaoService.importarXMLOtimizado(files, importId)

        if (result.total_files) {
          updateProgress({ total: result.total_files })
        }

        if (result.import_id && result.import_id !== importId) {
          finishImport(importId, false)
          startImport(result.import_id, totalFiles)
        }

        if (result.success === false) {
          setImportResult({
            type: 'error',
            message: result.message,
          })
        }
      }

      refetchXML()

      if (fileInput) {
        fileInput.value = ''
      }
    } catch (error: any) {
      console.error('Erro na importação XML:', error)
      setImportResult({
        type: 'error',
        message: error.message || 'Erro desconhecido na importação.',
      })
    } finally {
      setIsImporting(false)
    }
  }

  const handleSPEDImport = async () => {
    const fileInput = spedFileRef.current
    if (!fileInput?.files?.length) {
      setImportResult({
        type: 'error',
        message: 'Selecione um arquivo SPED para importar.',
      })
      return
    }

    setIsImporting(true)
    setImportResult(null)

    const importId = generateImportId()
    startImport(importId, 0)

    try {
      const result = await importacaoService.importarSPED(
        fileInput.files[0],
        importId
      )

      const isSuccess =
        result.success !== false &&
        !result.message?.toLowerCase().includes('erro')

      setImportResult({
        type: isSuccess ? 'success' : 'error',
        message: result.message,
        totals: result.totais,
      })

      if (!isSuccess) {
        finishImport(importId, false)
      }

      refetchSPED()

      if (fileInput) {
        fileInput.value = ''
      }
    } catch (error: any) {
      console.error('Erro na importação SPED:', error)
      setImportResult({
        type: 'error',
        message: error.message || 'Erro desconhecido na importação.',
      })
      finishImport(importId, false)
    } finally {
      setIsImporting(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR')
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sucesso':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
            Sucesso
          </span>
        )
      case 'erro':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
            Erro
          </span>
        )
      case 'processando':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
            Processando
          </span>
        )
      default:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
            {status}
          </span>
        )
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center gap-2">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Importação de Arquivos
          </h1>
          <HelpButton onClick={() => setIsHelpOpen(true)} />
        </div>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Importe arquivos XML e SPED para o sistema de auditoria fiscal
        </p>
      </div>

      <Card className="p-2 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex items-center gap-2">
          <div className="flex flex-1 gap-2">
            <Button
              variant={activeTab === 'xml' ? 'primary' : 'ghost'}
              size="md"
              onClick={() => setActiveTab('xml')}
              className="flex-1 justify-center"
              icon={<FileCode className="w-4 h-4" />}
              glow={activeTab === 'xml'}
            >
              Importação XML
            </Button>

            <Button
              variant={activeTab === 'sped' ? 'primary' : 'ghost'}
              size="md"
              onClick={() => setActiveTab('sped')}
              className="flex-1 justify-center"
              icon={<FileSpreadsheet className="w-4 h-4" />}
              glow={activeTab === 'sped'}
            >
              Importação SPED
            </Button>
          </div>
        </div>
      </Card>

      {activeTab === 'xml' && (
        <div className="space-y-6">
          <Card className="overflow-hidden" gradient>
            <div className="bg-gradient-to-r ">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-xl flex items-center justify-center">
                  <FileCode className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white gradient-text">
                    Importar Arquivo XML
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mt-1">
                    Selecione arquivos XML ou ZIP para importar os dados fiscais
                  </p>
                </div>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="space-y-4">
                <div className="relative">
                  <input
                    ref={xmlFileRef}
                    type="file"
                    id="xml-file"
                    accept=".xml,.zip"
                    multiple
                    disabled={isImporting}
                    className="w-full px-4 py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100 dark:file:bg-primary-900/30 dark:file:text-primary-400 transition-all duration-200 hover:border-primary-400 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20"
                  />
                  <div className="absolute top-3 right-3">
                    <FileUp className="w-5 h-5 text-gray-400" />
                  </div>
                </div>

                <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-700">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                      <Info className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="text-sm text-blue-800 dark:text-blue-200">
                      <p className="font-semibold mb-2">💡 Suporte completo a múltiplos arquivos:</p>
                      <ul className="space-y-1 text-blue-700 dark:text-blue-300">
                        <li>• <strong>XML Individual:</strong> Selecione um arquivo .xml</li>
                        <li>• <strong>ZIP Único:</strong> Selecione um arquivo .zip com múltiplos XMLs</li>
                        <li>• <strong>Múltiplos ZIPs:</strong> Selecione vários arquivos .zip</li>
                        <li>• <strong>Múltiplos XMLs:</strong> Selecione vários arquivos .xml</li>
                        <li>• <strong>Misto:</strong> Combine XMLs e ZIPs na mesma importação</li>
                      </ul>
                      <p className="mt-2 text-xs">
                        O sistema identificará automaticamente as empresas pelos CNPJs dos emitentes.
                      </p>
                    </div>
                  </div>
                </Card>
              </div>

              <div className="flex flex-wrap gap-3">
                <Button
                  variant="secondary"
                  size="md"
                  onClick={() => handleXMLImport('individual')}
                  disabled={isImporting}
                  loading={isImporting}
                  icon={<Upload className="w-4 h-4" />}
                  glow
                >
                  Importar Individual
                </Button>

                <Button
                  variant="primary"
                  size="md"
                  onClick={() => handleXMLImport('batch')}
                  disabled={isImporting}
                  loading={isImporting}
                  icon={<Layers className="w-4 h-4" />}
                >
                  Importar em Lote
                </Button>

              </div>
            </div>
          </Card>

          <ProgressBar progress={progress} />

          {importResult && (
            <div className={`p-4 rounded-lg ${
              importResult.type === 'success'
                ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                : importResult.type === 'warning'
                  ? 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800'
                  : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
            }`}>
              <div className="flex">
                {importResult.type === 'success' ? (
                  <CheckCircle2 className="w-5 h-5 text-green-400" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-400" />
                )}
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    importResult.type === 'success'
                      ? 'text-green-800 dark:text-green-200'
                        : importResult.type === 'warning'
                        ? 'text-yellow-800 dark:text-yellow-200'
                        : 'text-red-800 dark:text-red-200'
                  }`}>
                    {importResult.message}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === 'sped' && (
        <div className="space-y-6">
          <Card className="overflow-hidden" gradient>
            <div className="">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-warning-100 dark:bg-warning-900/30 rounded-xl flex items-center justify-center">
                  <FileSpreadsheet className="w-6 h-6 text-warning-600 dark:text-warning-400" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white gradient-text">
                    Importar Arquivo SPED
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mt-1">
                    Selecione um arquivo SPED (.txt) para importar as notas de entrada
                  </p>
                </div>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="space-y-4">
                <div className="relative">
                  <input
                    ref={spedFileRef}
                    type="file"
                    id="sped-file"
                    accept=".txt"
                    disabled={isImporting}
                    className="w-full px-4 py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-warning-50 file:text-warning-700 hover:file:bg-warning-100 dark:file:bg-warning-900/30 dark:file:text-warning-400 transition-all duration-200 hover:border-warning-400 focus:border-warning-500 focus:ring-2 focus:ring-warning-500/20"
                  />
                  <div className="absolute top-3 right-3">
                    <FileUp className="w-5 h-5 text-gray-400" />
                  </div>
                </div>

                <Card className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border-amber-200 dark:border-amber-700">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                      <Info className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                    </div>
                    <div className="text-sm text-amber-800 dark:text-amber-200">
                      <p className="font-semibold mb-2">ℹ️ Informações importantes sobre importação SPED:</p>
                      <ul className="space-y-1 text-amber-700 dark:text-amber-300">
                        <li>• <strong>Identificação automática:</strong> O sistema identifica a empresa pelo CNPJ no arquivo</li>
                        <li>• <strong>Pré-requisito:</strong> A empresa deve estar cadastrada no sistema</li>
                        <li>• <strong>Filtro automático:</strong> Apenas notas de entrada (IND_OPER = 0) são importadas</li>
                        <li>• <strong>Formato:</strong> Arquivo de texto (.txt) no padrão SPED Fiscal</li>
                      </ul>
                    </div>
                  </div>
                </Card>
              </div>

              <div className="flex justify-start">
                <Button
                  variant="warning"
                  size="lg"
                  onClick={handleSPEDImport}
                  disabled={isImporting}
                  loading={isImporting}
                  icon={<Upload className="w-5 h-5" />}
                  glow
                >
                  {isImporting ? 'Importando SPED...' : 'Importar Arquivo SPED'}
                </Button>
              </div>
            </div>
          </Card>

          <ProgressBar progress={progress} />

          {importResult && activeTab === 'sped' && (
            <div className={`p-4 rounded-lg ${
              importResult.type === 'success'
                ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
            }`}>
              <div className="flex">
                {importResult.type === 'success' ? (
                  <CheckCircle2 className="w-5 h-5 text-green-400" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-400" />
                )}
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    importResult.type === 'success'
                      ? 'text-green-800 dark:text-green-200'
                      : 'text-red-800 dark:text-red-200'
                  }`}>
                    {importResult.message}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="card">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Histórico de Importações
          </h3>
        </div>

        <div className="px-6 pt-4">
          <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg w-fit">
            <button
              onClick={() => setHistoryTab('xml')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                historyTab === 'xml'
                  ? 'bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              XML
            </button>
            <button
              onClick={() => setHistoryTab('sped')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                historyTab === 'sped'
                  ? 'bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              SPED
            </button>
          </div>
        </div>

        <div className="p-6">
          {historyTab === 'xml' && (
            <div>
              {loadingXML ? (
                <div className="text-center py-8">
                  <LoadingSpinner className="mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    Carregando histórico XML...
                  </p>
                </div>
              ) : xmlImports && xmlImports.length > 0 ? (
                <>
                  <TableScrollContainer>
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          ID
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Arquivo
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Nota Fiscal
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Data Importação
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {paginatedXmlImports?.map((importacao) => (
                        <tr key={importacao.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {importacao.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {importacao.arquivo}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {importacao.nota_fiscal}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {formatDate(importacao.data_importacao)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getStatusBadge(importacao.status)}
                          </td>
                        </tr>
                      ))}
                      </tbody>
                    </table>
                  </TableScrollContainer>
                  <Pagination
                    current={xmlPage}
                    total={xmlImports.length}
                    pageSize={ITEMS_PER_PAGE}
                    onChange={setXmlPage}
                  />
                </>
              ) : (
                <div className="text-center py-8">
                  <FileArchive className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Nenhuma importação XML encontrada
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Importe alguns arquivos XML para ver o histórico aqui.
                  </p>
                </div>
              )}
            </div>
          )}

          {historyTab === 'sped' && (
            <div>
              {loadingSPED ? (
                <div className="text-center py-8">
                  <LoadingSpinner className="mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    Carregando histórico SPED...
                  </p>
                </div>
              ) : spedImports && spedImports.length > 0 ? (
                <>
                  <TableScrollContainer>
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          ID
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Arquivo
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Registros
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Data Importação
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {paginatedSpedImports?.map((importacao) => (
                        <tr key={importacao.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {importacao.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {importacao.arquivo}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {importacao.registros_processados}/{importacao.total_registros}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {formatDate(importacao.data_importacao)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getStatusBadge(importacao.status)}
                          </td>
                        </tr>
                      ))}
                      </tbody>
                    </table>
                  </TableScrollContainer>
                  <Pagination
                    current={spedPage}
                    total={spedImports.length}
                    pageSize={ITEMS_PER_PAGE}
                    onChange={setSpedPage}
                  />
                </>              ) : (
                <div className="text-center py-8">
                  <FileArchive className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Nenhuma importação SPED encontrada
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Importe alguns arquivos SPED para ver o histórico aqui.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      <HelpModal
        isOpen={isHelpOpen}
        onClose={() => setIsHelpOpen(false)}
        title="Ajuda na Importação"
        tabs={[
          {
            label: 'XMLs',
            content: (
              <div className="space-y-4 text-sm text-gray-600 dark:text-gray-300">
                <div>
                  <p className="font-semibold">Formatos aceitos</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Arquivos <strong>.xml</strong></li>
                    <li>Arquivos <strong>.zip</strong> com XMLs</li>
                  </ul>
                </div>
                <div>
                  <p className="font-semibold">Modos de importação</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>
                      <strong>Individual:</strong> processa um único arquivo.
                    </li>
                    <li>
                      <strong>Lote:</strong> envia vários arquivos e processa
                      sequencialmente.
                    </li>
                    <li>
                      <strong>Otimizado:</strong> indicado para grandes
                      volumes, processa em paralelo.
                    </li>
                  </ul>
                </div>
                <div>
                  <p className="font-semibold">Resultados</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Mensagem de sucesso, alerta ou erro da importação.</li>
                    <li>
                      Totais de notas, itens, clientes e produtos
                      importados.
                    </li>
                  </ul>
                </div>
              </div>
            ),
          },
          {
            label: 'SPED',
            content: (
              <div className="space-y-4 text-sm text-gray-600 dark:text-gray-300">
                <div>
                  <p className="font-semibold">Formatos aceitos</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Arquivo de texto <strong>.txt</strong> do SPED Fiscal.</li>
                  </ul>
                </div>
                <div>
                  <p className="font-semibold">Modos de importação</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>
                      A importação do SPED ocorre em modo <strong>otimizado</strong>
                      e aceita um arquivo por vez.
                    </li>
                  </ul>
                </div>
                <div>
                  <p className="font-semibold">Resultados</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Mensagem indicando sucesso ou erros do processamento.</li>
                    <li>Totais de registros processados.</li>
                  </ul>
                </div>
              </div>
            ),
          },
        ]}
      />
    </div>
  )
}
import api from './authService'

export interface ApuracaoHistoricoResponse {
  success: boolean
  dados: {
    labels: string[]
    [key: string]: any
  }
  message?: string
}

export const apuracaoService = {
  async getHistoricoICMS(empresaId: number, ano: number, mes: number) {
    const params = new URLSearchParams({ ano: ano.toString(), mes: mes.toString() })
    const response = await api.get(`/apuracao/${empresaId}/historico?${params}`)
    return response.data as ApuracaoHistoricoResponse
  },
  async getHistoricoICMSST(empresaId: number, ano: number, mes: number) {
    const params = new URLSearchParams({ ano: ano.toString(), mes: mes.toString(), tipo: 'icms_st' })
    const response = await api.get(`/apuracao/${empresaId}/historico?${params}`)
    return response.data as ApuracaoHistoricoResponse
  },
  async getHistoricoIPI(empresaId: number, ano: number, mes: number) {
    const params = new URLSearchParams({ ano: ano.toString(), mes: mes.toString() })
    const response = await api.get(`/apuracao/${empresaId}/historico/ipi?${params}`)
    return response.data as ApuracaoHistoricoResponse
  },
  async getHistoricoPIS(empresaId: number, ano: number, mes: number) {
    const params = new URLSearchParams({ ano: ano.toString(), mes: mes.toString() })
    const response = await api.get(`/apuracao/${empresaId}/historico/pis?${params}`)
    return response.data as ApuracaoHistoricoResponse
  },
  async getHistoricoCOFINS(empresaId: number, ano: number, mes: number) {
    const params = new URLSearchParams({ ano: ano.toString(), mes: mes.toString() })
    const response = await api.get(`/apuracao/${empresaId}/historico/cofins?${params}`)
    return response.data as ApuracaoHistoricoResponse
  }
}

export default apuracaoService
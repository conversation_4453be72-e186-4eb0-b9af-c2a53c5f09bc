import { useState } from 'react'
import {
  ipiValidationService,
  IPIValidationResult,
} from '@/services/ipiValidationService'
import { IPIValidationModal } from './IPIValidationModal'
import { CFOPCSTValidationModal } from './CFOPCSTValidationModal'

interface Props {
  empresaId?: number
  status: string
  onRefresh?: () => void
  onFilter?: (ids: number[]) => void
  onClearFilters?: () => void
}

export function IpiValidationActions({
  empresaId,
  status,
  onRefresh,
  onFilter,
  onClearFilters,
}: Props) {
  const [loadingIPI, setLoadingIPI] = useState(false)
  const [loadingCFOP, setLoadingCFOP] = useState(false)
  const [ipiResult, setIpiResult] = useState<IPIValidationResult | null>(null)
  const [cfopResult, setCfopResult] = useState<IPIValidationResult | null>(null)
  const [showIPIModal, setShowIPIModal] = useState(false)
  const [showCFOPModal, setShowCFOPModal] = useState(false)

  if (!empresaId) return null

  const analyzeIPI = async () => {
    setLoadingIPI(true)
    try {
      const result = await ipiValidationService.validateIPI(empresaId, status)
      setIpiResult(result)
      setShowIPIModal(true)
    } catch (e) {
      console.error(e)
      alert('Erro na análise de IPI')
    } finally {
      setLoadingIPI(false)
    }
  }

  const validateCFOPCST = async () => {
    setLoadingCFOP(true)
    try {
      const result = await ipiValidationService.validateCFOPCST(
        empresaId,
        status
      )
      setCfopResult(result)
      setShowCFOPModal(true)
    } catch (e) {
      console.error(e)
      alert('Erro na validação CFOP x CST')
    } finally {
      setLoadingCFOP(false)
    }
  }

  return (
    <div className="flex gap-3">
      <button
        onClick={analyzeIPI}
        disabled={loadingIPI}
        className="warning-button"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V12a1 1 0 102 0V9a1 1 0 01.293-.707L13.586 6H12a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293A1 1 0 0112 9v3a3 3 0 11-6 0V9a1 1 0 01.293-.707L8.586 6H7v1a1 1 0 01-2 0V4z" clipRule="evenodd" />
        </svg>
        {loadingIPI ? 'Analisando...' : 'Analisar IPI'}
      </button>
      <button
        onClick={validateCFOPCST}
        disabled={loadingCFOP}
        className="analysis-button"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
        {loadingCFOP ? 'Validando...' : 'Validar CFOP x CST'}
      </button>

      <IPIValidationModal
        isOpen={showIPIModal}
        result={ipiResult}
        onClose={() => setShowIPIModal(false)}
        onApplied={onRefresh}
        onFilter={onFilter}
        onClearFilters={onClearFilters}
      />
      <CFOPCSTValidationModal
        isOpen={showCFOPModal}
        result={cfopResult}
        onClose={() => setShowCFOPModal(false)}
        onApplied={onRefresh}
        onFilter={onFilter}
        onClearFilters={onClearFilters}
      />
    </div>
  )
}
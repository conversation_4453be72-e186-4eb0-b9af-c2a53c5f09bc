# Funcionamento da Importação de XML e SPED

Este documento resume como o sistema realiza a importação de XMLs de notas fiscais e de arquivos SPED. Também explica onde os dados são gravados no banco de dados e como diferenciamos XMLs de entrada e de saída.

## Importação de XML

- O serviço `XMLImportService` lê cada arquivo XML através do `XMLProcessor`.
- Durante o processamento é determinado se a empresa é emitente ou destinatária:
  - Se o CNPJ no XML corresponder ao da empresa e a tag `tpNF` indicar **1**, o documento é tratado como **saída**.
  - Caso a empresa seja destinatária ou `tpNF` seja **0**, o XML é considerado de **entrada**.
- Todas as informações obtidas são registradas na tabela `importacao_xml`, incluindo o campo `tipo_nota` ("0" para entrada e "1" para saída).
- Clientes e produtos são criados ou atualizados nas tabelas `cliente` e `produto`.
- Itens das notas são armazenados em `nota_fiscal_item` e seus tributos em `tributo`.

## Importação de SPED

- O `SPEDImportService` utiliza o `SPEDProcessor` para interpretar o arquivo `.txt` do SPED.
- Participantes (fornecedores) são unificados na tabela `cliente`, contendo campos como `cod_part` e `cpf`.
- As notas extraídas são salvas em `nota_entrada` e seus itens em `item_nota_entrada`.
- Produtos são registrados em `produto_entrada`.
- Um resumo geral da operação fica na tabela `importacao_sped`, com a contagem de notas, itens, clientes e produtos processados.

## Diferenciação de XML de Entrada e Saída

- O tipo de documento é identificado no momento da importação do XML:
  - **Saída**: quando o CNPJ emitente é o da empresa e `tpNF` = 1.
  - **Entrada**: quando a empresa é destinatária ou `tpNF` = 0.
- Esta informação fica registrada no campo `tipo_nota` da tabela `importacao_xml`, permitindo filtrar posteriormente as notas de entrada ou saída.

## Conclusão

- A importação de XML grava dados em `importacao_xml`, `cliente`, `produto`, `nota_fiscal_item` e `tributo`.
- A importação do SPED utiliza `cliente`, `produto_entrada`, `nota_entrada`, `item_nota_entrada` e registra o resumo em `importacao_sped`.
- A distinção entre XML de entrada e de saída é baseada na correspondência do CNPJ da empresa e no valor da tag `tpNF`.

🎉 ZIP processado: 192 sucessos, 0 falhas em 197.55s

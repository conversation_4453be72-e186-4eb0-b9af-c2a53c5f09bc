import React from 'react'
import { Card } from '@/components/ui/Card'
import { But<PERSON> } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'

interface ProgressoAuditoriaProps {
  isVisible: boolean
  progress: number
  message: string
  currentStep?: string
  totalSteps?: number
  onCancel?: () => void
}

export function ProgressoAuditoria({
  isVisible,
  progress,
  message,
  currentStep,
  totalSteps,
  onCancel
}: ProgressoAuditoriaProps) {
  if (!isVisible) return null

  return (
    <Card className="p-4 border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-blue-600 dark:text-blue-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                Gerando Auditoria Comparativa
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {message}
              </p>
            </div>
          </div>
          
          {onCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
            >
              Cancelar
            </Button>
          )}
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-blue-700 dark:text-blue-300">
              Progresso: {Math.round(progress)}%
            </span>
            {currentStep && totalSteps && (
              <span className="text-blue-600 dark:text-blue-400">
                Etapa {currentStep} de {totalSteps}
              </span>
            )}
          </div>
          
          <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-3">
            <div
              className="bg-blue-600 dark:bg-blue-400 h-3 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center gap-2">
          <Badge variant="info" size="sm">
            Em Processamento
          </Badge>
          <span className="text-xs text-blue-600 dark:text-blue-400">
            Aguarde enquanto processamos os dados...
          </span>
        </div>
      </div>
    </Card>
  )
}
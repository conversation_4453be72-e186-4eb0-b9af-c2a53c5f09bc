-- Migração para adicionar suporte ao Simples Nacional
-- Data: 2024-12-19

-- 1. Adicionar campos CSOSN e CRT à tabela importacao_xml
ALTER TABLE importacao_xml 
ADD COLUMN IF NOT EXISTS csosn VARCHAR(3),
ADD COLUMN IF NOT EXISTS crt_emitente VARCHAR(1);

-- 2. Adicionar campos do Simples Nacional à tabela tributo
ALTER TABLE tributo 
ADD COLUMN IF NOT EXISTS icms_csosn VARCHAR(3),
ADD COLUMN IF NOT EXISTS icms_p_cred_sn NUMERIC(10, 4),
ADD COLUMN IF NOT EXISTS icms_v_cred_sn NUMERIC(10, 2);

-- 3. Comentários para documentação
COMMENT ON COLUMN importacao_xml.csosn IS 'CSOSN do Simples Nacional (informativo)';
COMMENT ON COLUMN importacao_xml.crt_emitente IS 'CRT do emitente (1=Simples Nacional)';
COMMENT ON COLUMN tributo.icms_csosn IS 'CSOSN para empresas do Simples Nacional';
COMMENT ON COLUMN tributo.icms_p_cred_sn IS 'Percentual de crédito do ICMS no Simples Nacional';
COMMENT ON COLUMN tributo.icms_v_cred_sn IS 'Valor do crédito do ICMS no Simples Nacional';

-- 4. Verificar se as colunas foram criadas corretamente
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('importacao_xml', 'tributo') 
  AND column_name IN ('csosn', 'crt_emitente', 'icms_csosn', 'icms_p_cred_sn', 'icms_v_cred_sn')
ORDER BY table_name, column_name;

import { useState, useEffect, useRef } from 'react'
import { Link } from 'react-router-dom'
import { AlertTriangle, ClipboardCheck, Factory, Landmark, Play, Recycle, Scale, Users } from 'lucide-react'
import { auditoriaService } from '@/services/auditoriaService'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { useFilterStore } from '@/store/filterStore'
import { useAuditoriaWebSocket } from '@/hooks/useAuditoriaWebSocket'
import { useAuditoriaSumarios } from '@/hooks/useAuditoriaSumarios'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { HelpButton, HelpModal } from '@/components/ui'
import { ResumoAuditoriaTributo } from '@/components/auditoria/ResumoAuditoriaTributo'

interface TributoCard {
  tipo: string
  nome: string
  icon: React.ReactNode
  iconColor: string
  iconBg: string
}

const TRIBUTOS: TributoCard[] = [
  {
    tipo: 'icms',
    nome: 'ICMS',
    icon: <Landmark className="w-6 h-6" />,
    iconColor: 'text-blue-600',
    iconBg: 'bg-blue-100',
  },
  {
    tipo: 'icms-st',
    nome: 'ICMS-ST',
    icon: <Recycle className="w-6 h-6" />,
    iconColor: 'text-purple-600',
    iconBg: 'bg-purple-100',
  },
  {
    tipo: 'difal',
    nome: 'DIFAL',
    icon: <Scale className="w-6 h-6" />,
    iconColor: 'text-indigo-600',
    iconBg: 'bg-indigo-100',
  },
  {
    tipo: 'ipi',
    nome: 'IPI',
    icon: <Factory className="w-6 h-6" />,
    iconColor: 'text-green-600',
    iconBg: 'bg-green-100',
  },
  {
    tipo: 'pis',
    nome: 'PIS',
    icon: <Users className="w-6 h-6" />,
    iconColor: 'text-orange-600',
    iconBg: 'bg-orange-100',
  },
  {
    tipo: 'cofins',
    nome: 'COFINS',
    icon: <Landmark className="w-6 h-6" />,
    iconColor: 'text-red-600',
    iconBg: 'bg-red-100',
  },
]

export function AuditoriaSaidaPage() {
  const empresaId = useSelectedCompany()
  const { selectedYear, selectedMonth } = useFilterStore()
  const [executando, setExecutando] = useState(false)
  const [progresso, setProgresso] = useState<string>('')
  const [tributoEmExecucao, setTributoEmExecucao] = useState<string>('')
  const [auditoriasCompletas, setAuditoriasCompletas] = useState<Set<string>>(new Set())
  const [auditoriasAtivas, setAuditoriasAtivas] = useState<Set<string>>(new Set())
  const [helpOpen, setHelpOpen] = useState(false)

  // Hook para buscar sumários de auditoria
  const { sumariosMap, isLoading: isLoadingSumarios, error: errorSumarios, refetch: refetchSumarios } = useAuditoriaSumarios({
    empresaId,
    year: selectedYear,
    month: selectedMonth,
    enabled: !!empresaId
  })

  // Mapa para armazenar as promises de conclusão de cada auditoria
  const auditoriasPromises = useRef<Map<string, { resolve: () => void, reject: (error: Error) => void }>>(new Map())

  // Configurar WebSocket usando o hook existente
  const {
    isConnected,
    subscribeToAudit,
    unsubscribeFromAudit,
    getAuditStatus,
    isAuditProcessing
  } = useAuditoriaWebSocket({
    onProgress: (progress) => {
      console.log('Progresso da auditoria:', progress)
      if (progress.progress !== undefined) {
        setProgresso(`${progress.current_step || 'Processando'}: ${progress.progress}%`)
      }
    },
    onComplete: (results) => {
      console.log('Auditoria concluída:', results)
      // Marcar como completa quando receber o evento de conclusão
      if (results.audit_id) {
        console.log(`✅ Marcando auditoria ${results.audit_id} como completa`)
        setAuditoriasCompletas(prev => {
          const newSet = new Set([...prev, results.audit_id])
          console.log(`📊 Auditorias completas: ${newSet.size}/6`)
          return newSet
        })

        // Resolver a promise correspondente se existir
        const promise = auditoriasPromises.current.get(results.audit_id)
        if (promise) {
          console.log(`🎯 Resolvendo promise para auditoria ${results.audit_id}`)
          promise.resolve()
          auditoriasPromises.current.delete(results.audit_id)
        }

        // Recarregar sumários quando uma auditoria for concluída
        refetchSumarios()
      }
    },
    onError: (error) => {
      console.error('Erro na auditoria:', error)
      alert(`Erro na auditoria: ${error}`)
      setExecutando(false)
    }
  })

  const executarTodos = async () => {
    if (!empresaId) {
      alert('Selecione uma empresa para executar a auditoria.')
      return
    }

    if (!isConnected) {
      alert('WebSocket não está conectado. Tente novamente em alguns segundos.')
      return
    }

    try {
      setExecutando(true)
      setProgresso('Iniciando auditorias...')
      setTributoEmExecucao('')
      setAuditoriasCompletas(new Set())
      setAuditoriasAtivas(new Set())

      const ordem = ['ipi', 'icms', 'icms-st', 'difal', 'pis', 'cofins']

      for (const tributo of ordem) {
        setTributoEmExecucao(tributo)
        setProgresso(`Iniciando auditoria de ${tributo.toUpperCase()}...`)
        console.log(`Iniciando auditoria de ${tributo.toUpperCase()}...`)

        // Executar auditoria e obter audit_id
        const response = await auditoriaService.executar({
          empresaId,
          tipoTributo: tributo,
          tipoOperacao: 'saida',
          year: selectedYear,
          month: selectedMonth,
        })

        if (response.audit_id) {
          // Inscrever-se na auditoria via WebSocket
          subscribeToAudit(response.audit_id)
          setAuditoriasAtivas(prev => new Set([...prev, response.audit_id]))

          // Aguardar conclusão via WebSocket
          await aguardarConclusaoWebSocket(response.audit_id, tributo)
          console.log(`Auditoria de ${tributo.toUpperCase()} concluída`)

          // Desinscrever-se da auditoria
          unsubscribeFromAudit(response.audit_id)
          setAuditoriasAtivas(prev => {
            const newSet = new Set(prev)
            newSet.delete(response.audit_id)
            return newSet
          })
        }

        // Pequena pausa entre auditorias para evitar sobrecarga
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      setTributoEmExecucao('')
      setProgresso('Todas as auditorias concluídas!')
      alert('Todas as auditorias foram executadas com sucesso!')
    } catch (error: any) {
      const message = error.response?.data?.message || error.message
      alert(`Erro ao executar auditoria: ${message}`)
      setProgresso('Erro na execução das auditorias')
      setTributoEmExecucao('')
    } finally {
      setExecutando(false)
      setAuditoriasAtivas(new Set())
    }
  }

  const aguardarConclusaoWebSocket = async (auditId: string, tipoTributo: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      console.log(`⏳ Aguardando conclusão da auditoria ${auditId} (${tipoTributo.toUpperCase()})`)

      const timeout = setTimeout(() => {
        // Limpar a promise do mapa se der timeout
        auditoriasPromises.current.delete(auditId)
        reject(new Error(`Timeout aguardando conclusão da auditoria de ${tipoTributo.toUpperCase()}`))
      }, 300000) // 5 minutos de timeout

      // Armazenar a promise no mapa para ser resolvida pelo callback onComplete
      auditoriasPromises.current.set(auditId, {
        resolve: () => {
          console.log(`🎯 Promise resolvida para ${auditId} (${tipoTributo.toUpperCase()})`)
          clearTimeout(timeout)
          resolve()
        },
        reject: (error: Error) => {
          console.log(`❌ Promise rejeitada para ${auditId} (${tipoTributo.toUpperCase()}):`, error.message)
          clearTimeout(timeout)
          reject(error)
        }
      })

      // Verificar se já está completa (caso o evento tenha chegado antes desta função ser chamada)
      const status = getAuditStatus(auditId)
      if (status?.status === 'completed') {
        console.log(`✅ Auditoria ${auditId} (${tipoTributo.toUpperCase()}) já estava completa`)
        auditoriasPromises.current.delete(auditId)
        clearTimeout(timeout)
        resolve()
      } else if (status?.status === 'error') {
        console.log(`❌ Auditoria ${auditId} (${tipoTributo.toUpperCase()}) já tinha erro:`, status.error)
        auditoriasPromises.current.delete(auditId)
        clearTimeout(timeout)
        reject(new Error(status.error || 'Erro na auditoria'))
      }
    })
  }

  return (
    <div className="space-y-6">
      <div className="relative overflow-hidden bg-gradient-to-r rounded-2xl">
        <div className="relative z-10">
          <div className="flex items-start justify-between mb-2">
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-3xl text-gray-900 font-bold">Auditoria de Saída</h1>
                <HelpButton onClick={() => setHelpOpen(true)} />
              </div>
              <p className="text-gray-500 mt-1">
                Execute auditorias por tributo ou para todos os impostos
              </p>
              <p className="text-sm text-gray-400 mt-1">
                Período: {String(selectedMonth).padStart(2, '0')}/{selectedYear}
              </p>
            </div>
          </div>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      <div className="space-y-4">
        <Button
          onClick={executarTodos}
          disabled={executando}
          icon={<Play className="w-4 h-4" />}
        >
          {executando ? 'Executando...' : 'Executar Auditoria de Todos os Impostos'}
        </Button>

        {/* Indicador de conexão WebSocket */}
        <div className="flex items-center gap-2 text-sm">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-gray-600 dark:text-gray-400">
            WebSocket: {isConnected ? 'Conectado' : 'Desconectado'}
          </span>
        </div>

        {/* Progresso das auditorias */}
        {executando && (
          <Card className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Progresso da Auditoria
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {auditoriasCompletas.size}/6 concluídas
                </span>
              </div>
              {tributoEmExecucao && (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Executando:</span> {
                  TRIBUTOS.find(t => t.tipo === tributoEmExecucao)?.nome || tributoEmExecucao.toUpperCase()
                }
                </div>
              )}
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(auditoriasCompletas.size / 6) * 100}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {progresso}
              </p>
            </div>
          </Card>
        )}
      </div>

      {/* Erro ao carregar sumários */}
      {errorSumarios && (
        <Card className="p-4 border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <AlertTriangle className="w-4 h-4" />
            <span className="text-sm">
              Erro ao carregar dados de auditoria: {errorSumarios}
            </span>
          </div>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {TRIBUTOS.map((tributo) => {
          // Normalizar o tipo do tributo para buscar no mapa de sumários
          const tipoTributoNormalizado = tributo.tipo.replace('-', '_')
          const sumario = sumariosMap[tipoTributoNormalizado]

          return (
            <Link key={tributo.tipo} to={`/fiscal/auditoria/saida/${tributo.tipo}`} className="block">
              <Card hover className="p-6">
                <div className="flex flex-col gap-4">
                  {/* Header do card */}
                  <div className="flex flex-col items-center gap-3">
                    <div className={`w-12 h-12 ${tributo.iconBg} rounded-xl flex items-center justify-center`}>
                      <div className={tributo.iconColor}>{tributo.icon}</div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white text-center">
                      {tributo.nome}
                    </h3>
                  </div>

                  {/* Resumo da auditoria */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <ResumoAuditoriaTributo
                      sumario={sumario}
                      isLoading={isLoadingSumarios}
                    />
                  </div>
                </div>
              </Card>
            </Link>
          )
        })}
      </div>

      <HelpModal
        isOpen={helpOpen}
        onClose={() => setHelpOpen(false)}
        title="Ajuda - Auditoria de Saída"
      >
        <div className="space-y-4 text-gray-600 dark:text-gray-400 text-sm">
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
              Visão Geral do Processo
            </h3>
            <p>
              Esta página permite executar auditorias de saída tributo a tributo
              ou todas de uma vez. O progresso em tempo real é exibido durante a
              execução e o indicador mostra a conexão com o WebSocket.
            </p>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
              Cartões de Tributo
            </h3>
            <p>
              Cada cartão representa um imposto auditável. Clique em um cartão
              para acessar a auditoria específica de cada tributo.
            </p>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
              Estados de Execução
            </h3>
            <p>
              O painel de progresso indica o tributo em execução e quantos já
              foram concluídos. O indicador verde ou vermelho informa se o
              WebSocket está conectado.
            </p>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
              Ícones e Cores
            </h3>
            <p className="mb-2">
              Os ícones e cores ajudam a identificar rapidamente cada tributo:
            </p>
            <ul className="space-y-1">
              {TRIBUTOS.map((t) => (
                <li key={t.tipo} className="flex items-center gap-2">
                  <div
                    className={`w-5 h-5 ${t.iconBg} rounded flex items-center justify-center`}
                  >
                    <div className={`${t.iconColor}`}>{t.icon}</div>
                  </div>
                  <span>{t.nome}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </HelpModal>
    </div>
  )
}
# Implementação do Suporte ao Simples Nacional

## Resumo das Mudanças Implementadas

### 1. **Correção do Erro de Endpoint Duplicado**
- ✅ Removido endpoint duplicado `editar_dados_sped` em `auditoria_comparativa_routes.py`
- ✅ Aplicação agora inicia sem erros

### 2. **Expansão da Tabela de Auditoria Comparativa**
- ✅ Adicionados todos os campos solicitados na tabela:
  - Nota Fiscal, Emissão, Nome/Razão Social, Regime Parceiro
  - Produto, Descrição, NCM NFe, NCM SPED
  - Tipo, Descrição Tipo, Produto Nota
  - Origem SPED, Origem Nota, CFOP SPED, CFOP Nota
  - CST SPED, CST Nota, CSOSN Nota
  - Match, Status, Ações

### 3. **Suporte ao Simples Nacional na Importação XML**

#### **Modelos de Dados Atualizados:**
- ✅ **ImportacaoXML**: Adicionados campos `csosn` e `crt_emitente`
- ✅ **Tributo**: Adicionado campo `icms_csosn` (pCredSN e vCredICMSSN agora preenchem `icms_aliquota` e `icms_valor`)
- ✅ **Cliente**: Campo `simples_nacional` já existia

#### **XMLProcessor Atualizado:**
- ✅ Extração do campo `CRT` do emitente
- ✅ Suporte para tags do Simples Nacional:
  - `ICMSSN101`, `ICMSSN102`, `ICMSSN103`
  - `ICMSSN201`, `ICMSSN202`, `ICMSSN203`
  - `ICMSSN300`, `ICMSSN400`, `ICMSSN500`, `ICMSSN900`
- ✅ Extração de campos específicos:
  - `<orig>` → `icms_origem`
  - `<CSOSN>` → `icms_csosn`
  - `<pCredSN>` → `icms_aliquota`
  - `<vCredICMSSN>` → `icms_valor`

#### **XMLImportService Atualizado:**
- ✅ Detecção automática de notas de entrada com CRT do emitente
- ✅ Atualização do status `simples_nacional` do cliente baseado no CRT
- ✅ Salvamento dos dados do Simples Nacional nos tributos
- ✅ Registro dos dados informativos na tabela `importacao_xml`

### 4. **Lógica de Funcionamento**

#### **Para Notas de Entrada (tipo_nota = '0'):**
1. Sistema detecta que é nota de entrada
2. Extrai o CRT do emitente do XML
3. Se CRT = '1', marca o cliente como Simples Nacional
4. Extrai dados dos impostos ICMS do Simples Nacional
5. Salva CSOSN e grava pCredSN/vCredICMSSN em `icms_aliquota` e `icms_valor`
6. Registra dados informativos na importação

#### **Campos Salvos:**
- **Cliente**: `simples_nacional = true/false` baseado no CRT
- **Tributo**: Todos os campos de ICMS + campos específicos do SN
- **ImportacaoXML**: `crt_emitente` e `csosn` para referência

### 5. **Migração do Banco de Dados**
- ✅ Script `db/migration_simples_nacional.sql` criado
- ✅ Adiciona colunas necessárias com segurança (IF NOT EXISTS)
- ✅ Inclui comentários para documentação
- ✅ Script `db/migration_remove_icms_sn_columns.sql` remove colunas antigas e migra os dados

### 6. **Campos de Edição Expandidos**
- ✅ Modal de edição ICMS inclui:
  - CFOP, NCM, Origem de ICMS
  - CST, CSOSN (Simples Nacional)
  - % Crédito ICMS (Simples Nacional)
  - Base de cálculo, alíquota, valor, % redução

## Como Testar

### 1. **Executar Migração:**
```sql
-- Executar o arquivo db/migration_simples_nacional.sql
-- Em seguida executar db/migration_remove_icms_sn_columns.sql
```

### 2. **Importar XML de Entrada com Simples Nacional:**
- XML deve ter emitente com `<CRT>1</CRT>`
- Produtos devem ter tags como `<ICMSSN101>`, `<ICMSSN102>`, etc.
- Sistema deve detectar automaticamente e processar

### 3. **Verificar Resultados:**
- Cliente deve ter `simples_nacional = true`
- Tributo deve ter `icms_csosn`, `icms_aliquota`, `icms_valor` preenchidos
- ImportacaoXML deve ter `crt_emitente = '1'` e `csosn` preenchido

### 4. **Auditoria Comparativa:**
- Acessar `/auditoria/entrada/auditoria`
- Verificar se tabela mostra todos os campos solicitados
- Testar modal de edição com novos campos

## Arquivos Modificados

### Backend:
- `back/models/importacao_xml.py`
- `back/models/tributo.py`
- `back/services/xml_import_service.py`
- `back/utils/xml_processor.py`
- `back/routes/auditoria_comparativa_routes.py`

### Frontend:
- `front/static/js/auditoria_comparativa.js`

### Banco de Dados:
- `db/migration_simples_nacional.sql`

## Próximos Passos

1. **Executar migração do banco**
2. **Testar importação com XMLs do Simples Nacional**
3. **Verificar auditoria comparativa**
4. **Implementar exportação SPED** (próxima fase)

Todas as mudanças foram implementadas mantendo compatibilidade com o sistema existente e seguindo as melhores práticas de desenvolvimento.

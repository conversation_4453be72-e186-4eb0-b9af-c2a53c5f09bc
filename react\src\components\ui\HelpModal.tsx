import React, { useState } from 'react'
import { Modal } from './Modal'
import { But<PERSON> } from './Button'

interface HelpTab {
  label: string
  content: React.ReactNode
}

interface HelpModalProps extends Omit<React.ComponentProps<typeof Modal>, 'children'> {
  tabs?: HelpTab[]
  children?: React.ReactNode
}

export function HelpModal({ tabs, children, ...modalProps }: HelpModalProps) {
  const [activeTab, setActiveTab] = useState(0)

  return (
    <Modal {...modalProps}>
      {tabs && tabs.length > 0 ? (
        <div className="space-y-4">
          <div className="flex gap-2 border-b pb-2">
            {tabs.map((tab, index) => (
              <Button
                key={index}
                variant={activeTab === index ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab(index)}
              >
                {tab.label}
              </Button>
            ))}
          </div>
          <div>{tabs[activeTab]?.content}</div>
        </div>
      ) : (
        children
      )}
    </Modal>
  )
}

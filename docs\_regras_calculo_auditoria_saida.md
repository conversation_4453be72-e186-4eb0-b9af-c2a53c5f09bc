# Regras de Auditoria de Impostos de Saída

Este documento consolida as principais regras utilizadas no cálculo de tributos de **saída** no sistema de auditoria fiscal. As informações se baseiam nas implementações descritas em `TributoCalculationService` e na documentação existente.

## Regras Gerais

1. A auditoria segue a ordem: **IPI → ICMS → ICMS-ST → PIS → COFINS → DIFAL**.
2. Apenas cenários em produção (`status='producao'` e `ativo=true`) são considerados.
3. A data de emissão da nota deve estar dentro do período de vigência do cenário.
4. Diferenças de até **R$ 0,01** são toleradas no comparativo nota × cenário.
5. Clientes classificados como **uso/consumo** ou **ativo imobilizado** podem ter o valor do IPI somado à base de ICMS ou ICMS-ST.
6. Cada tributo é auditado de forma independente e os resultados são gravados em `auditoria_resultado` e `auditoria_sumario`.

## Regras por Tributo

### IPI
- Somente calculado se o CST do cenário for `50`.
- Base de cálculo: valor da mercadoria, podendo incluir frete e/ou desconto conforme o cenário.
- Valor do IPI = base de cálculo × alíquota / 100.

### ICMS
- CSTs tributados: `00`, `10`, `20` e `70`.
- A base pode incluir frete, descontos e IPI (para clientes de uso/consumo ou ativo).
- Caso exista redução da base (`p_red_bc`), aplica-se o percentual antes da alíquota.
- Valor do ICMS = base de cálculo × alíquota / 100.

### ICMS-ST
- CSTs com substituição: `10` e `70`.
- A base parte do valor da mercadoria e pode incluir frete, desconto e IPI.
- Aplica-se a **MVA** (`icms_st_p_mva`) e eventuais reduções de base se o CST não for `10`.
- Valor do ICMS-ST é obtido subtraindo o ICMS próprio do valor total após a alíquota de ST.

### PIS
- CSTs tributados: `01`, `02`, `1` e `2`; CST `04` resulta em base e alíquota zero.
- A base subtrai o valor do ICMS e pode incluir frete/desconto conforme o cenário.
- Pode haver redução de base via `p_red_bc`.
- Valor do PIS = base de cálculo × alíquota / 100.

### COFINS
- Segue a mesma lógica do PIS (CSTs, descontos, frete, redução e alíquotas).
- Valor do COFINS = base de cálculo × alíquota / 100.

### DIFAL
- Calculado apenas se o cenário possuir os percentuais necessários (`p_icms_uf_dest`, `p_icms_inter`).
- Pode haver redução da base (`p_red_bc`) antes de aplicar a diferença de alíquotas.
- Aplica-se a partilha interestadual (`p_icms_inter_part`) quando informada.
- Valor do DIFAL considera também o FCP (`p_fcp_uf_dest`) quando presente.

---

Estas regras servem como guia para o cálculo e a conferência automática dos tributos de **saída** no sistema de auditoria fiscal.
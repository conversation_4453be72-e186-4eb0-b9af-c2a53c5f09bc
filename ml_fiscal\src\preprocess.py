from sklearn.preprocessing import LabelEncoder, StandardScaler
from db import load_data

def preprocess():
    # 1) Carrega tudo do banco (cenários + clientes + produtos)
    df = load_data()

    # 1.1) Remove cenários 'novo' - mantém só produção e todos os 'incons_*'
    df = df[df['status'] != 'novo']

    # 2) Defina suas colunas categóricas:
    cat_cols = [
        'cliente_id', 'produto_id', 'cfop', 'ncm',
        'uf', 'codigo_pais', 'cnae', 'atividade',
        'destinacao', 'natureza_juridica', 'simples_nacional',
        'cliente_descricao', 'produto_descricao',
        'cest', 'unidade_comercial'
    ]
    # Ajuste a lista conforme nomes exatos das colunas no seu DataFrame

    # 3) Crie um LabelEncoder para cada coluna categórica
    encoders = {}
    for col in cat_cols:
        enc = LabelEncoder()
        df[col] = df[col].fillna('')          # imputar string vazia se faltar
        enc.fit(df[col])
        df[col] = enc.transform(df[col])
        encoders[col] = enc

    # 4) Colunas numéricas (mantemos as antigas)
    num_cols = ['aliquota', 'p_red_bc']
    df[num_cols] = df[num_cols].fillna(0)

    # 5) Normalização
    scaler = StandardScaler().fit(df[num_cols])
    df[num_cols] = scaler.transform(df[num_cols])

    # 6) Rótulo final
   #  df['label'] = (df['status'] == 'inconsistente').astype(int)
    df['label'] = df['status'].str.startswith('incons').astype(int)

    return df, cat_cols, num_cols, encoders, scaler

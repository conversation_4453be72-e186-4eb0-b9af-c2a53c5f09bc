import json
from pathlib import Path

DEFAULT_PAIRS = {
    "1902": ["5902"],
    "1124": ["5124", "6124"],
}

CONFIG_PATH = Path(__file__).resolve().parents[1] / "config" / "cfop_pairs.json"


def load_cfop_pair_rules():
    """Carrega regras de pareamento de CFOP a partir de JSON.
    Retorna dicion\u00e1rio no formato {cfop: [cfop_pareados]}.
    """
    data = {}
    if CONFIG_PATH.exists():
        try:
            with open(CONFIG_PATH, "r", encoding="utf-8") as f:
                data = json.load(f) or {}
        except Exception as e:
            print(f"Erro ao carregar regras de CFOP: {e}")
    if not isinstance(data, dict):
        data = DEFAULT_PAIRS
    # Gerar mapeamento sim\u00e9trico
    pairs = {}
    for cfop, relacionados in (data or DEFAULT_PAIRS).items():
        rel_list = relacionados if isinstance(relacionados, list) else [relacionados]
        pairs.setdefault(str(cfop), set()).update(str(r) for r in rel_list)
        for r in rel_list:
            pairs.setdefault(str(r), set()).add(str(cfop))
    return {k: sorted(v) for k, v in pairs.items()}
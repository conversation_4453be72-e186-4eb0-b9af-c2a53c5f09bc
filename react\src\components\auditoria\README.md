# Dashboard de Auditoria - Componentes React

Este diretório contém os componentes React para o dashboard de auditoria fiscal, baseado na funcionalidade existente do frontend HTML/CSS/JavaScript.

## Componentes Implementados

### 1. AuditoriaDashboardPage
**Localização:** `src/pages/AuditoriaDashboardPage.tsx`

Página principal do dashboard de auditoria com:
- Sistema de tabs (Resumo/Detalhamento)
- Integração com APIs do backend
- Estados de loading, erro e dados vazios
- Queries otimizadas com React Query

### 2. CardsResumo
**Localização:** `src/components/auditoria/CardsResumo.tsx`

Cards de resumo da auditoria com:
- Card de Total (valor total, notas, operações)
- Card de Conformidade (valores conformes, percentual)
- Card de Inconsistências (valores inconsistentes, clicável)
- Cards de Valores a Maior/Menor
- Resumo detalhado de inconsistências analisadas/não analisadas

### 3. FiltrosAvancados
**Localização:** `src/components/auditoria/FiltrosAvancados.tsx`

Sistema de filtros avançados com:
- Filtros simples: Análise, Atividade, Destinação, Número NF, Produto
- Filtros dropdown múltiplos: CFOP, NCM, CST, Alíquota
- Busca em tempo real nos dropdowns
- Botões de seleção/limpeza
- Integração com opções do backend

### 4. TabelaDetalhamento
**Localização:** `src/components/auditoria/TabelaDetalhamento.tsx`

Tabela de detalhamento com funcionalidades avançadas:
- Expansão de linhas (Nota/Cenário)
- Seleção múltipla com checkboxes
- Comparação visual entre nota e cenário (cores)
- Ações por linha: visualizar, relatório PDF, marcar como analisada
- Modal de detalhes (cliente, produto, tributo/cenário)
- Modal de observações da análise
- Modal para marcar como analisada (individual/bulk)

## Funcionalidades Implementadas

### Sistema de Tabs
- **Resumo:** Cards visuais com métricas principais
- **Detalhamento:** Tabela com filtros avançados e ações

### Filtros Inteligentes
- Filtros relacionados entre CFOP, NCM, CST e Alíquota
- Busca em tempo real
- Múltipla seleção
- Persistência de estado

### Visualização de Dados
- Comparação visual entre valores da nota e cenário
- Cores diferenciadas para inconsistências
- Tooltips informativos
- Formatação de moeda e percentuais

### Ações do Usuário
- Marcar inconsistências como analisadas
- Adicionar observações
- Gerar relatórios PDF
- Visualizar detalhes completos

### Estados da Interface
- Loading states
- Estados de erro
- Dados vazios
- Feedback visual para ações

## Integração com Backend

### APIs Utilizadas
- `GET /api/auditoria/dashboard` - Dados do dashboard
- `GET /api/auditoria/dashboard/filtros` - Opções de filtros
- `GET /api/auditoria/dashboard/detalhamento` - Dados detalhados
- `POST /api/auditoria/resultados/{id}/marcar-vista` - Marcar como analisada
- `POST /api/auditoria/resultados/marcar-vista-bulk` - Marcar múltiplas
- `GET /api/auditoria/detalhes/{tipo}/{id}` - Detalhes de nota/cenário
- `GET /api/auditoria/relatorio/{id}` - Gerar relatório PDF

### Tipos TypeScript
Todos os tipos estão definidos em `src/services/auditoriaService.ts`:
- `DashboardSumario`
- `ResultadoInconsistente`
- `DashboardResponse`
- `FiltroOptions`

## Dependências

### Componentes UI Reutilizados
- `Card`, `StatsCard` - Layout e cards
- `Button` - Botões com variantes
- `Table` - Tabela responsiva
- `Modal` - Modais
- `Input` - Campos de entrada
- `Dropdown` - Dropdown com múltipla seleção
- `LoadingSpinner` - Indicador de carregamento
- `Tooltip` - Tooltips

### Hooks e Stores
- `useSelectedCompany` - Empresa selecionada
- `useFilterStore` - Filtros de ano/mês
- `useQuery`, `useMutation` - React Query

### Utilitários
- `cn` - Concatenação de classes CSS
- Formatação de moeda e percentuais

## Como Usar

```tsx
import { AuditoriaDashboardPage } from '@/pages/AuditoriaDashboardPage'

// A página é acessada via rota com parâmetro tipoTributo
// Exemplo: /auditoria/dashboard/icms
```

## Melhorias Futuras

1. **Performance**
   - Virtualização da tabela para grandes datasets
   - Debounce nos filtros
   - Cache inteligente

2. **UX/UI**
   - Drag & drop para reordenar colunas
   - Exportação para Excel/CSV
   - Gráficos e visualizações

3. **Funcionalidades**
   - Filtros salvos
   - Comparação entre períodos
   - Alertas automáticos

## Compatibilidade

- ✅ Funcionalidade equivalente ao frontend atual
- ✅ Todas as APIs do backend suportadas
- ✅ Responsivo (mobile/desktop)
- ✅ Dark mode
- ✅ Acessibilidade básica
-- Migration to add service authentication tables for XML service optimization

-- Create service_authentication table to store portal authentication data
CREATE TABLE IF NOT EXISTS service_authentication (
    id SERIAL PRIMARY KEY,
    portal_user_id VARCHAR(50) NOT NULL,
    portal_office_id VARCHAR(50) NOT NULL,
    office_cnpj VARCHAR(20) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    user_email VARCHAR(255) NOT NULL,
    user_role VARCHAR(50) NOT NULL,
    jwt_token TEXT NOT NULL,
    decoded_payload JSONB NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(portal_office_id)
);

-- Add indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_service_auth_portal_office_id ON service_authentication(portal_office_id);
CREATE INDEX IF NOT EXISTS idx_service_auth_expires_at ON service_authentication(expires_at);
CREATE INDEX IF NOT EXISTS idx_service_auth_is_active ON service_authentication(is_active);
CREATE INDEX IF NOT EXISTS idx_service_auth_created_at ON service_authentication(created_at);

-- Add comments for documentation
COMMENT ON TABLE service_authentication IS 'Stores authentication data for XML monitoring service portal integration';
COMMENT ON COLUMN service_authentication.portal_user_id IS 'User ID from the portal system';
COMMENT ON COLUMN service_authentication.portal_office_id IS 'Office ID from the portal system';
COMMENT ON COLUMN service_authentication.office_cnpj IS 'CNPJ of the office for identification';
COMMENT ON COLUMN service_authentication.jwt_token IS 'JWT token received from portal authentication';
COMMENT ON COLUMN service_authentication.decoded_payload IS 'Decoded JWT payload stored as JSONB';
COMMENT ON COLUMN service_authentication.expires_at IS 'Token expiration timestamp';
COMMENT ON COLUMN service_authentication.is_active IS 'Flag to indicate if authentication is currently active';
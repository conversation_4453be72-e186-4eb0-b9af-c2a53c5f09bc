import { useState, useEffect } from 'react'
import { auditoriaService, SumarioAuditoria } from '@/services/auditoriaService'

interface UseAuditoriaSumariosParams {
  empresaId: number | null
  year: number
  month: number
  enabled?: boolean
}

interface UseAuditoriaSumariosReturn {
  sumarios: SumarioAuditoria[]
  sumariosMap: Record<string, SumarioAuditoria>
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useAuditoriaSumarios({
  empresaId,
  year,
  month,
  enabled = true
}: UseAuditoriaSumariosParams): UseAuditoriaSumariosReturn {
  const [sumarios, setSumarios] = useState<SumarioAuditoria[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchSumarios = async () => {
    if (!empresaId || !enabled) {
      setSumarios([])
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await auditoriaService.obterSumarios(empresaId, year, month)
      
      if (response.success) {
        setSumarios(response.sumarios)
      } else {
        setError(response.message || 'Erro ao carregar sumários de auditoria')
        setSumarios([])
      }
    } catch (err: any) {
      console.error('Erro ao buscar sumários de auditoria:', err)
      setError(err.response?.data?.message || err.message || 'Erro ao carregar dados')
      setSumarios([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchSumarios()
  }, [empresaId, year, month, enabled])

  // Criar um mapa de sumários por tipo de tributo para acesso rápido
  const sumariosMap = sumarios.reduce((acc, sumario) => {
    acc[sumario.tipo_tributo] = sumario
    return acc
  }, {} as Record<string, SumarioAuditoria>)

  return {
    sumarios,
    sumariosMap,
    isLoading,
    error,
    refetch: fetchSumarios
  }
}

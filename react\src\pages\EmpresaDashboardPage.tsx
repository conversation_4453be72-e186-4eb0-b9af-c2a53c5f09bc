import { useState, useEffect } from 'react'
import { useParams, useSearchParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { useFilterStore } from '@/store/filterStore'
import { dashboardService, EmpresaDetails } from '@/services/dashboardService'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ResumoGeral } from '@/components/empresa/ResumoGeral'
import { TributoCard } from '@/components/empresa/TributoCard'
import { InconsistenciasGraficos } from '@/components/empresa/InconsistenciasGraficos'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { HelpButton, HelpModal } from '@/components/ui'

// Lucide React Icons
import {
  AlertCircle,
  RefreshCw,
  Home,
  ChevronRight,
  Building2,
  CheckCircle,
  Calendar,
  Upload,
  Download,
  BarChart3,
  FileText,
  AlertTriangle,
  Package,
  Check,
} from 'lucide-react'

export function EmpresaDashboardPage() {
  const { empresaId } = useParams<{ empresaId: string }>()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const tipo = (searchParams.get('tipo') as 'saida' | 'entrada') || 'saida'
  const { selectedYear, selectedMonth, setCompany } = useFilterStore()
  const [activeTab, setActiveTab] = useState<'auditorias' | 'inconsistencias'>(
    'auditorias'
  )
  const [isHelpOpen, setIsHelpOpen] = useState(false)

  const {
    data: empresaData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [
      'empresa-dashboard',
      empresaId,
      tipo,
      selectedYear,
      selectedMonth,
    ],
    queryFn: () => {
      const id = parseInt(empresaId!)
      return tipo === 'entrada'
        ? dashboardService.getEmpresaDetailsEntrada(
          id,
          selectedYear,
          selectedMonth
        )
        : dashboardService.getEmpresaDetails(id, selectedYear, selectedMonth)
    },
    enabled: !!empresaId,
  }) as {
    data: EmpresaDetails | undefined
    isLoading: boolean
    error: any
    refetch: () => void
  }

  useEffect(() => {
    if (empresaId) {
      setCompany(parseInt(empresaId))
    }
  }, [empresaId, setCompany])

  const formatCNPJ = (cnpj: string) => {
    if (!cnpj) return ''
    return cnpj.replace(
      /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
      '$1.$2.$3/$4-$5'
    )
  }

  const handleNavigateToAuditoria = (tributo: string) => {
    if (!empresaId) return
    const params = new URLSearchParams({
      empresaId,
      year: String(selectedYear),
      month: String(selectedMonth),
    })
    navigate(`/fiscal/auditoria/saida/${tributo}?${params.toString()}`)
  }

  const handleMarcarNaoAplicavel = async (tributo: string) => {
    // TODO: Implementar API call para marcar como não aplicável
    console.log('Marcar como não aplicável:', tributo)
    // Após sucesso, refetch dos dados
    // refetch()
  }

  const handleReverterStatus = async (tributo: string) => {
    // TODO: Implementar API call para reverter status
    console.log('Reverter status:', tributo)
    // Após sucesso, refetch dos dados
    // refetch()
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">
            Carregando dashboard da empresa...
          </p>
        </div>
      </div>
    )
  }

  if (error || !empresaData?.success) {
    return (
      <Card className="border-error-200 dark:border-error-800 bg-gradient-to-r from-error-50 to-error-100 dark:from-error-900/20 dark:to-error-800/20">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-error-100 dark:bg-error-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
            <AlertCircle className="w-6 h-6 text-error-600 dark:text-error-400" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-error-800 dark:text-error-200 mb-1">
              ⚠️ Erro ao carregar dashboard da empresa
            </h3>
            <p className="text-error-700 dark:text-error-300 mb-4">
              {empresaData?.message ||
                'Verifique se a empresa existe e tente novamente.'}
            </p>
            <Button
              variant="error"
              size="sm"
              onClick={() => window.location.reload()}
              icon={<RefreshCw className="w-4 h-4" />}
            >
              Tentar Novamente
            </Button>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb Moderno */}
      <Card className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-0">
        <nav className="flex items-center gap-2 text-sm">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/fiscal/dashboard')}
            icon={<Home className="w-4 h-4" />}
          >
            Dashboard
          </Button>
          <ChevronRight className="w-4 h-4 text-gray-400" />
          <div className="flex items-center gap-2 text-gray-900 dark:text-white font-medium">
            <Building2 className="w-4 h-4" />
            {empresaData.empresa.razao_social}
          </div>
        </nav>
      </Card>

      {/* Header Hero */}
      <div className="relative overflow-hidden text-gray-900">
        <div className="relative z-10">
          <HelpButton
            className="absolute top-4 right-4 z-10"
            onClick={() => setIsHelpOpen(true)}
          />
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                  <CheckCircle className="w-8 h-8" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold mb-1">
                    {empresaData.empresa.razao_social}
                  </h1>
                  <p className="text-gray-500 text-lg">Dashboard da Empresa</p>
                </div>
              </div>

              <div className="flex items-center gap-4 text-gray-400">
                <div className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  <span className="font-medium">
                    CNPJ: {formatCNPJ(empresaData.empresa.cnpj)}
                  </span>
                </div>
                <div className="w-px h-4 bg-primary-300"></div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  <span className="font-medium">
                    Período: {selectedMonth}/{selectedYear}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Background decorations */}
        <div className="absolute top-0 right-0 -mt-8 -mr-8 w-40 h-40 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 -mb-12 -ml-12 w-48 h-48 bg-white/5 rounded-full blur-3xl"></div>
      </div>

      {/* Modern Tabs - Tipo Dashboard */}
      <Card className="p-2 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex gap-2">
          <Button
            variant={tipo === 'saida' ? 'primary' : 'ghost'}
            size="md"
            onClick={() =>
              navigate(`/fiscal/dashboard/empresa/${empresaId}?tipo=saida`)
            }
            className="flex-1 justify-center"
            icon={<Upload className="w-5 h-5" />}
            glow={tipo === 'saida'}
          >
            Saída
          </Button>

          <Button
            variant={tipo === 'entrada' ? 'primary' : 'ghost'}
            size="md"
            onClick={() =>
              navigate(`/fiscal/dashboard/empresa/${empresaId}?tipo=entrada`)
            }
            className="flex-1 justify-center"
            icon={<Download className="w-5 h-5" />}
            glow={tipo === 'entrada'}
          >
            Entrada
          </Button>
        </div>
      </Card>

      {/* Modern Tabs - Conteúdo */}
      <Card className="p-2 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex gap-2">
          <Button
            variant={activeTab === 'auditorias' ? 'primary' : 'ghost'}
            size="md"
            onClick={() => setActiveTab('auditorias')}
            className="flex-1 justify-center"
            icon={<Check className="w-5 h-5" />}
            glow={activeTab === 'auditorias'}
          >
            Auditorias
          </Button>

          {tipo === 'saida' && (
            <Button
              variant={activeTab === 'inconsistencias' ? 'primary' : 'ghost'}
              size="md"
              onClick={() => setActiveTab('inconsistencias')}
              className="flex-1 justify-center"
              icon={<BarChart3 className="w-5 h-5" />}
              glow={activeTab === 'inconsistencias'}
            >
              Inconsistências
            </Button>
          )}
        </div>
      </Card>

      {/* Conteúdo das Tabs */}
      {activeTab === 'auditorias' && (
        <div className="space-y-6">
          {/* Resumo Geral (apenas para saída) */}
          {tipo === 'saida' && empresaData.total_geral && (
            <ResumoGeral totalGeral={empresaData.total_geral} />
          )}

          {/* Cards dos Tributos */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                  <Package className="w-5 h-5 mr-2" />
                  Tributos
                </h4>
                {tipo === 'saida' && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Clique em um card para acessar a auditoria específica do
                    tributo
                  </p>
                )}
              </div>

              {tipo === 'saida' && (
                <button className="btn btn-primary btn-sm">
                  <Download className="w-4 h-4 mr-1" />
                  Relatório Geral
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {empresaData.cards_tributos.map((card) => (
                <TributoCard
                  key={card.tipo_tributo}
                  card={card}
                  tipo={tipo}
                  onNavigateToAuditoria={handleNavigateToAuditoria}
                  onMarcarNaoAplicavel={handleMarcarNaoAplicavel}
                  onReverterStatus={handleReverterStatus}
                />
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'inconsistencias' && tipo === 'saida' && (
        <InconsistenciasGraficos
          empresaId={parseInt(empresaId!)}
          year={selectedYear}
          month={selectedMonth}
        />
      )}

      <HelpModal
        isOpen={isHelpOpen}
        onClose={() => setIsHelpOpen(false)}
        title="Ajuda do Dashboard da Empresa"
        size="lg"
        tabs={[
          {
            label: 'Auditorias',
            content: (
              <div className="space-y-2">
                <p>
                  A aba Auditorias apresenta um resumo dos tributos e o
                  status de cada auditoria realizada para a empresa.
                </p>
              </div>
            )
          },
          {
            label: 'Inconsistências',
            content: (
              <div className="space-y-2">
                <p>
                  Em Inconsistências você encontra gráficos que destacam os
                  principais erros identificados nas notas fiscais de saída.
                </p>
              </div>
            )
          },
          {
            label: 'Cards de Tributos',
            content: (
              <div className="space-y-2">
                <p>
                  Cada card de tributo permite acessar a auditoria
                  específica daquele imposto. Basta clicar no card desejado
                  para visualizar os detalhes.
                </p>
              </div>
            )
          }
        ]}
      />
    </div>
  )
}
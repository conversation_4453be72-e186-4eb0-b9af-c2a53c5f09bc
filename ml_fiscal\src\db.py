import pandas as pd
from sqlalchemy import create_engine

def load_data():
    engine = create_engine("*********************************************************")
    query = """
        SELECT
            cenario.cliente_id,
            c.uf,
            c.codigo_pais,
            c.cnae,
            c.atividade,
            c.destina<PERSON>o,
            c.natureza_juridica,
            c.simples_nacional,
            c.descricao   AS cliente_descricao,

            cenario.produto_id,
            p.descricao   AS produto_descricao,
            p.cest,
            p.unidade_comercial,

            cenario.cfop,
            cenario.ncm,
            cenario.aliquota,
            cenario.p_red_bc,
            cenario.status
        FROM cenario_icms AS cenario
        JOIN cliente AS c
          ON c.id = cenario.cliente_id
        JOIN produto AS p
          ON p.id = cenario.produto_id;
    """
    df = pd.read_sql(query, engine)
    return df

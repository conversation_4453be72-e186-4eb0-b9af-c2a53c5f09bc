-- Migração para Sistema de Edição SPED e Histórico de Matching Inteligente
-- Data: 2025-06-17
-- Descrição: Adiciona campos necessários para edição de dados SPED e histórico de matching

-- =====================================================
-- 1. VERIFICAR E ADICIONAR CAMPOS NA TABELA item_nota_entrada
-- =====================================================

-- Campos ICMS-ST que podem estar faltando
ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS p_mva_st DECIMAL(10,4);

ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS valor_bc_icms_st DECIMAL(15,2);

ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS aliquota_icms_st DECIMAL(10,4);

ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS valor_icms_st DECIMAL(15,2);

-- Campos de redução de base de cálculo
ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS p_red_bc_icms DECIMAL(10,4);

-- Campos IPI que podem estar faltando
ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS valor_bc_ipi DECIMAL(15,2);

ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS aliquota_ipi DECIMAL(10,4);

ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS valor_ipi DECIMAL(15,2);

-- Campos PIS que podem estar faltando
ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS valor_bc_pis DECIMAL(15,2);

ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS aliquota_pis DECIMAL(10,4);

ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS valor_pis DECIMAL(15,2);

-- Campos COFINS que podem estar faltando
ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS valor_bc_cofins DECIMAL(15,2);

ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS aliquota_cofins DECIMAL(10,4);

ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS valor_cofins DECIMAL(15,2);

-- =====================================================
-- 2. VERIFICAR E ADICIONAR CAMPOS NA TABELA auditoria_comparativa_impostos
-- =====================================================

-- Campos para controle de alterações SPED
ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN IF NOT EXISTS sped_alterado BOOLEAN DEFAULT FALSE;

ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN IF NOT EXISTS historico_alteracoes JSONB;

-- Campos SPED ICMS que podem estar faltando
ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN IF NOT EXISTS sped_icms_reducao DECIMAL(10,4);

ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN IF NOT EXISTS sped_icms_csosn VARCHAR(3);

ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN IF NOT EXISTS sped_icms_credito DECIMAL(15,2);

-- Campos SPED ICMS-ST que podem estar faltando
ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN IF NOT EXISTS sped_icms_st_mva DECIMAL(10,4);

ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN IF NOT EXISTS sped_icms_st_reducao DECIMAL(10,4);

-- Campos SPED IPI que podem estar faltando
ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN IF NOT EXISTS sped_ipi_reducao DECIMAL(10,4);

-- =====================================================
-- 3. ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para busca rápida no histórico de matching
CREATE INDEX IF NOT EXISTS idx_historico_matching_empresa_cliente 
ON historico_matching_aprendizado(empresa_id, cliente_id);

CREATE INDEX IF NOT EXISTS idx_historico_matching_produtos 
ON historico_matching_aprendizado(xml_codigo_produto, sped_codigo_produto);

CREATE INDEX IF NOT EXISTS idx_historico_matching_acao 
ON historico_matching_aprendizado(acao_usuario);

-- Índices para auditoria comparativa
CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_empresa_alterado 
ON auditoria_comparativa_impostos(empresa_id, sped_alterado);

CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_status_tributos 
ON auditoria_comparativa_impostos USING GIN(status_tributos);

-- =====================================================
-- 4. COMENTÁRIOS PARA DOCUMENTAÇÃO
-- =====================================================

COMMENT ON COLUMN auditoria_comparativa_impostos.sped_alterado IS 
'Indica se os dados SPED foram alterados manualmente pelo usuário';

COMMENT ON COLUMN auditoria_comparativa_impostos.historico_alteracoes IS 
'Histórico JSON das alterações feitas nos dados SPED';

COMMENT ON COLUMN auditoria_comparativa_impostos.sped_icms_reducao IS 
'Percentual de redução da base de cálculo ICMS (SPED)';

COMMENT ON COLUMN auditoria_comparativa_impostos.sped_icms_csosn IS 
'CSOSN para empresas do Simples Nacional (SPED)';

COMMENT ON COLUMN auditoria_comparativa_impostos.sped_icms_credito IS 
'Valor do crédito de ICMS para Simples Nacional (SPED)';

-- =====================================================
-- 6. VERIFICAÇÃO FINAL
-- =====================================================

-- Verificar se as tabelas principais existem
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'auditoria_comparativa_impostos') THEN
        RAISE EXCEPTION 'Tabela auditoria_comparativa_impostos não encontrada. Execute primeiro a migração principal.';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'historico_matching_aprendizado') THEN
        RAISE EXCEPTION 'Tabela historico_matching_aprendizado não encontrada. Execute primeiro a migração principal.';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'item_nota_entrada') THEN
        RAISE EXCEPTION 'Tabela item_nota_entrada não encontrada. Execute primeiro a migração do SPED.';
    END IF;
    
    RAISE NOTICE 'Migração do Sistema de Edição SPED concluída com sucesso!';
END $$;

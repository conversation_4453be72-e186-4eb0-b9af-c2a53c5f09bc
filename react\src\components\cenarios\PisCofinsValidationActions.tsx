import { useState } from 'react'
import { pisCofinsValidationService, PisCofinsValidationResult } from '@/services/pisCofinsValidationService'
import { PisCofinsValidationModal } from './PisCofinsValidationModal'

interface Props {
  empresaId?: number
  status: string
  tributo: 'PIS' | 'COFINS' 
  onRefresh?: () => void
  onFilter?: (ids: number[]) => void
  onClearFilters?: () => void
}

export function PisCofinsValidationActions({
  empresaId,
  status,
  tributo,
  onRefresh,
  onFilter,
  onClearFilters
}: Props) {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<PisCofinsValidationResult | null>(null)
  const [showModal, setShowModal] = useState(false)

  if (!empresaId) return null

  const analyze = async () => {
    setLoading(true)
    try {
      const r = await pisCofinsValidationService.validate(
        empresaId,
        tributo,
        status
      )
      setResult(r)
      setShowModal(true)
    } catch (e) {
      console.error(e)
      alert('Erro na análise PIS/COFINS')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex gap-2">
      <button onClick={analyze} disabled={loading} className="analysis-button">
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V12a1 1 0 102 0V9a1 1 0 01.293-.707L13.586 6H12a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293A1 1 0 0112 9v3a3 3 0 11-6 0V9a1 1 0 01.293-.707L8.586 6H7v1a1 1 0 01-2 0V4z" clipRule="evenodd" />
        </svg>
        {loading ? 'Analisando...' : `Analisar ${tributo}`}
      </button>

      <PisCofinsValidationModal
        isOpen={showModal}
        result={result}
        onClose={() => setShowModal(false)}
        onApplied={onRefresh}
        onFilter={onFilter}
        onClearFilters={onClearFilters}
      />
    </div>
  )
}
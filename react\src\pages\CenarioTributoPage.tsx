import { useState } from 'react'
import { useParams, Link } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { cenariosService } from '@/services/cenariosService'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { CenarioTable } from '@/components/cenarios/CenarioTable'
import { IpiValidationActions } from '@/components/cenarios/IpiValidationActions'
import { ICMSSTValidationActions } from '@/components/cenarios/ICMSSTValidationActions'
import { PisCofinsValidationActions } from '@/components/cenarios/PisCofinsValidationActions'
import { Card, StatsCard } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { HelpButton, HelpModal } from '@/components/ui'
import type { TipoTributo, CenarioTributo } from '@/types/cenarios'

const TRIBUTO_NAMES: Record<TipoTributo, string> = {
  icms: 'ICMS',
  icms_st: 'ICMS-ST',
  ipi: 'IPI',
  pis: 'PIS',
  cofins: 'COFINS',
  difal: 'DIFAL',
}

const TRIBUTO_DESCRIPTIONS: Record<TipoTributo, string> = {
  icms: 'Imposto sobre Circulação de Mercadorias e Serviços',
  icms_st: 'ICMS Substituição Tributária',
  ipi: 'Imposto sobre Produtos Industrializados',
  pis: 'Programa de Integração Social',
  cofins: 'Contribuição para o Financiamento da Seguridade Social',
  difal: 'Diferencial de Alíquota do ICMS',
}

export function CenarioTributoPage() {
  const { tipoTributo } = useParams<{ tipoTributo: TipoTributo }>()
  const [activeTab, setActiveTab] = useState<
    'novo' | 'producao' | 'inconsistente'
  >('novo')
  const empresaId = useSelectedCompany() // Usar o hook para pegar empresa do header
  const [filteredCenarios, setFilteredCenarios] = useState<CenarioTributo[] | null>(null)
  const [isHelpOpen, setIsHelpOpen] = useState(false)

  if (!tipoTributo || !TRIBUTO_NAMES[tipoTributo]) {
    return (
      <Card className="text-center py-12">
        <div className="w-16 h-16 bg-error-100 dark:bg-error-900/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-error-600 dark:text-error-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Tipo de tributo inválido
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          O tipo de tributo solicitado não foi encontrado ou não é válido.
        </p>
        <Button
          variant="primary"
          as={Link}
          to="/fiscal/cenarios/saida"
          icon={
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          }
        >
          Voltar para Cenários
        </Button>
      </Card>
    )
  }

  // Query para buscar cenários da aba ativa
  const {
    data: cenarios,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['cenarios', tipoTributo, activeTab, empresaId],
    queryFn: async () => {
      if (!empresaId)
        return {
          cenarios: [],
          pagination: { total: 0, page: 1, per_page: 50, pages: 0 },
        }

      const result = await cenariosService.getCenarios(tipoTributo, {
        empresa_id: empresaId,
        status: activeTab,
        direcao: 'saida', // Sistema atual só tem saída
      })

      return result
    },
    enabled: !!empresaId,
    refetchOnWindowFocus: false,
  })

  // Query para contadores
  const { data: contadores, isLoading: loadingContadores } = useQuery({
    queryKey: ['cenarios-contadores', tipoTributo, empresaId],
    queryFn: async () => {
      if (!empresaId)
        return {
          success: false,
          counts: { novo: 0, producao: 0, inconsistente: 0 },
        }

      const result = await cenariosService.getContadores(
        tipoTributo,
        empresaId,
        'saida'
      )

      return result
    },
    enabled: !!empresaId,
    refetchOnWindowFocus: false,
  })

  if (error) {
    return (
      <Card className="border-error-200 dark:border-error-800 bg-gradient-to-r from-error-50 to-error-100 dark:from-error-900/20 dark:to-error-800/20">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-error-100 dark:bg-error-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
            <svg className="w-6 h-6 text-error-600 dark:text-error-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-error-800 dark:text-error-200 mb-1">
              Erro ao carregar cenários
            </h3>
            <p className="text-error-700 dark:text-error-300 mb-4">
              Ocorreu um erro ao carregar os cenários. Verifique sua conexão e tente novamente.
            </p>
            <Button
              variant="error"
              size="sm"
              onClick={() => window.location.reload()}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              }
            >
              Tentar Novamente
            </Button>
          </div>
        </div>
      </Card>
    )
  }

  const displayedCenarios = filteredCenarios || cenarios?.cenarios

  const handleFilter = async (ids: number[]) => {
    if (!tipoTributo || !empresaId) return
    const data = await cenariosService.getCenariosByIds(
      tipoTributo,
      ids,
      empresaId,
      'saida'
    )
    setFilteredCenarios(data)
  }

  const handleClearFilters = () => setFilteredCenarios(null)

  const helpTabs = [
    {
      label: 'Novos',
      content: (
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold">Critérios</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li>Cenários recém-criados ou importados</li>
              <li>Ainda não validados ou aplicados</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold">Ações disponíveis</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li>Revisar informações e ajustar regras</li>
              <li>Validar para mover à produção</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold">Impacto nos cenários</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li>Alterações não afetam cálculos enquanto permanecerem como novos</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      label: 'Produção',
      content: (
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold">Critérios</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li>Cenários validados e ativos</li>
              <li>Aplicados na apuração fiscal</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold">Ações disponíveis</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li>Monitorar resultados e ajustar parâmetros</li>
              <li>Remover da produção se necessário</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold">Impacto nos cenários</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li>Alterações refletem imediatamente na apuração e relatórios</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      label: 'Inconsistentes',
      content: (
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold">Critérios</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li>Dados incompletos ou divergentes</li>
              <li>Falhas identificadas em validações</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold">Ações disponíveis</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li>Analisar mensagens de erro</li>
              <li>Corrigir campos e reenviar para validação</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold">Impacto nos cenários</h4>
            <ul className="list-disc pl-4 space-y-1">
              <li>Não utilizados até a correção das inconsistências</li>
            </ul>
          </div>
        </div>
      )
    }
  ]

  return (
    <div className="space-y-6">
      {/* Breadcrumb Moderno */}
      <Card className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-0">
        <nav className="flex items-center gap-2 text-sm">
          <Button
            variant="ghost"
            size="sm"
            as={Link}
            to="/fiscal/cenarios/saida"
            icon={
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V12a1 1 0 102 0V9a1 1 0 01.293-.707L13.586 6H12a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293A1 1 0 0112 9v3a3 3 0 11-6 0V9a1 1 0 01.293-.707L8.586 6H7v1a1 1 0 01-2 0V4z" clipRule="evenodd" />
              </svg>
            }
          >
            Cenários
          </Button>
          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
          </svg>
          <span className="text-gray-900 dark:text-white font-medium">
            {TRIBUTO_NAMES[tipoTributo]}
          </span>
        </nav>
      </Card>

      {/* Header Simples */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <svg className="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V12a1 1 0 102 0V9a1 1 0 01.293-.707L13.586 6H12a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293A1 1 0 0112 9v3a3 3 0 11-6 0V9a1 1 0 01.293-.707L8.586 6H7v1a1 1 0 01-2 0V4z" clipRule="evenodd" />
            </svg>
            Cenários {TRIBUTO_NAMES[tipoTributo]}
            <HelpButton onClick={() => setIsHelpOpen(true)} className="ml-2" />
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {TRIBUTO_DESCRIPTIONS[tipoTributo]}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          {tipoTributo === 'ipi' && (
            <IpiValidationActions
              empresaId={empresaId || undefined}
              status={activeTab}
              onRefresh={() => refetch()}
              onFilter={handleFilter}
              onClearFilters={handleClearFilters}
            />
          )}
          {tipoTributo === 'icms_st' && (
            <ICMSSTValidationActions
              empresaId={empresaId || undefined}
              status={activeTab}
              onRefresh={() => refetch()}
              onFilter={handleFilter}
              onClearFilters={handleClearFilters}
            />
          )}
          {(tipoTributo === 'pis' || tipoTributo === 'cofins') && (
               <PisCofinsValidationActions
               empresaId={empresaId || undefined}
               status={activeTab}
               tributo={tipoTributo === 'pis' ? 'PIS' : 'COFINS'}
               onRefresh={() => refetch()}
               onFilter={handleFilter}
               onClearFilters={handleClearFilters}
             />
          )}
        </div>
      </div>

      {/* Stats Cards Modernos */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="Cenários Novos"
          value={(contadores?.counts.novo || 0).toLocaleString()}
          change={{
            value: 12,
            type: 'increase',
            period: 'vs semana anterior'
          }}
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          }
          color="blue"
          loading={loadingContadores}
        />

        <StatsCard
          title="Em Produção"
          value={(contadores?.counts.producao || 0).toLocaleString()}
          change={{
            value: 8,
            type: 'increase',
            period: 'vs semana anterior'
          }}
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          }
          color="success"
          loading={loadingContadores}
        />

        <StatsCard
          title="Inconsistentes"
          value={(contadores?.counts.inconsistente || 0).toLocaleString()}
          change={{
            value: 3,
            type: 'decrease',
            period: 'vs semana anterior'
          }}
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          }
          color="error"
          loading={loadingContadores}
        />
      </div>

      {/* Modern Tabs Container */}
      <Card className="overflow-hidden" gradient>
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 p-2 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center gap-2">
            <div className="flex flex-1 gap-2">
              <Button
                variant={activeTab === 'novo' ? 'blue' : 'ghost'}
                size="md"
                onClick={() => setActiveTab('novo')}
                className="flex-1 justify-center"
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                }
                glow={activeTab === 'novo'}
              >
                Novos ({contadores?.counts.novo || 0})
              </Button>

              <Button
                variant={activeTab === 'producao' ? 'success' : 'ghost'}
                size="md"
                onClick={() => setActiveTab('producao')}
                className="flex-1 justify-center"
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                }
                glow={activeTab === 'producao'}
              >
                Produção ({contadores?.counts.producao || 0})
              </Button>

              <Button
                variant={activeTab === 'inconsistente' ? 'error' : 'ghost'}
                size="md"
                onClick={() => setActiveTab('inconsistente')}
                className="flex-1 justify-center"
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                }
                glow={activeTab === 'inconsistente'}
              >
                Inconsistentes ({contadores?.counts.inconsistente || 0})
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Loading State Moderno */}
          {(isLoading || loadingContadores) && (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center mb-4">
                <LoadingSpinner className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Carregando cenários {TRIBUTO_NAMES[tipoTributo]}...
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Aguarde enquanto buscamos os dados mais recentes
              </p>
            </div>
          )}

          {/* Content - Tabela com novo design */}
          {!isLoading && !loadingContadores && (
            <CenarioTable
              cenarios={displayedCenarios || []}
              tipoTributo={tipoTributo}
              isLoading={isLoading}
              status={activeTab}
              totalCount={filteredCenarios ? filteredCenarios.length : cenarios?.pagination?.total || 0}
            />
          )}
        </div>

        <HelpModal
          isOpen={isHelpOpen}
          onClose={() => setIsHelpOpen(false)}
          title="Ajuda"
          size="lg"
          tabs={helpTabs}
        />
      </Card>
    </div>
  )
}
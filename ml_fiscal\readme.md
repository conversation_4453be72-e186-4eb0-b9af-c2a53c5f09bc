# Fiscal Auditoria – Pipeline de ML para Cenários

Este repositório contém o código para treinar e avaliar um modelo em PyTorch que, dado um cenário fiscal (cliente, produto, CFOP, NCM, etc.), classifica se ele deve ir para **produção** ou é **inconsistente**.

## Estrutura de pastas

fiscal_auditoria/
├── src/
│ ├── db.py # Conexão e extração dos dados do PostgreSQL
│ ├── preprocess.py # Limpeza, encoding e normalização do DataFrame
│ ├── dataset.py # Dataset/DataLoader para PyTorch
│ ├── model.py # Definição do modelo (MLP + embeddings)
│ ├── train.py # Loop de treino e validação, salva checkpoint
│ └── evaluate.py # Carrega modelo salvo e gera métricas
├── requirements.txt
└── README.md # Este arquivo

python
---

## Descrição dos módulos

### src/db.py  
Abre a conexão com o banco e traz um **DataFrame** pandas via SQL:
```python
from sqlalchemy import create_engine
import pandas as pd

def load_data():
    engine = create_engine("postgresql://user:senha@host:porta/banco")
    query = """
      SELECT cliente_id, produto_id, cfop, ncm,
             aliquota, p_red_bc, status
      FROM cenario_icms;
    """
    df = pd.read_sql(query, engine)
    return df
Roda essa consulta sobre todos os registros atuais de cenários.

src/preprocess.py
Converte o DataFrame bruto em features numéricas e labels:

LabelEncoder nas colunas cliente_id, produto_id, cfop, ncm.

Imputação de zeros em aliquota e p_red_bc.

StandardScaler para média=0, desvio=1.

Cria df['label'] = 0 (produção) ou 1 (inconsistente).

python
df, cat_cols, num_cols, encoders, scaler = preprocess()
src/dataset.py
Define CenarioDataset e get_loaders():

Transforma pandas→tensores (X_cat, X_num, y).

Separa treino (80%) / validação (20%) sem embaralhar a ordem temporal.

Cria batches de tamanho 64 via DataLoader.

src/model.py
Um MLP simples com embeddings para cada campo categórico:

python
class CenarioModel(nn.Module):
    def __init__(..., cardinalities, emb_dims, n_num):
        # embeddings + 2 camadas FC + sigmoid final
    def forward(self, x_cat, x_num):
        # concat embeddings + numéricas → ReLU → sigmoid → probabilidade
src/train.py
Loop de treino/validação:

Carrega e pré‑processa os dados via preprocess().

Instancia o modelo, optimizer = Adam(lr=1e-3), criterion = BCELoss().

Roda 10 épocas, imprimindo Train loss e Val loss.

Salva best_model.pth ao final.

src/evaluate.py
Recarrega best_model.pth e gera:

AUC-ROC

Precision/Recall/F1

Matriz de confusão

Novas features sugeridas
Para melhorar a capacidade de acerto na alíquota, inclua informações de cliente, produto (e, opcionalmente, tributo):

Cliente

uf (estado)

codigo_pais

cnae

atividade

destinacao

simples_nacional

natureza_juridica

descricao

Produto

descricao

cest

unidade_comercial

cfop (já disponível)

ncm (já disponível)

Tributo (caso usemos antes de gerar cenários)

data_emissao

icms_origem, icms_cst, icms_mod_bc, icms_p_red_bc

icms_aliquota, icms_p_dif

(E campos análogos para ICMS-ST, IPI, PIS, COFINS e DIFAL)

Quando usar tributo vs. cenários

Após a criação de cenários: use apenas as tabelas cenario_*.

Antes da criação de cenários: leia diretamente de tributo para formar suas features.


| Data            | Fase                                         | Atividade                                                                                                                               |
| --------------- | -------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------- |
| **29 Jul 2025** | **1. Query enriquecida**                     | Atualizar `src/db.py` para fazer `JOIN` com `cliente` e `produto` e trazer colunas adicionais (uf, código\_pais, cnae, etc.)            |
| **30 Jul 2025** | **2. Pré‑processamento**                     | Em `src/preprocess.py`, incluir as novas colunas em `cat_cols` e `num_cols` e ajustar imputação/encoding/scaling                        |
| **31 Jul 2025** | **3. Teste de pipeline**                     | Executar um script rápido para verificar se o DataFrame vem corretamente com todas as colunas e encoders funcionando                    |
| **01 Ago 2025** | **4. Ajuste de modelo e dataset**            | Em `src/dataset.py` e `src/model.py`, aumentar `cardinalities`/`emb_dims` para os novos campos categóricos; ajustar dimensão de entrada |
| **02 Ago 2025** | **5. Treino e avaliação inicial**            | Rodar `python src/train.py` e `python src/evaluate.py`, checar métricas (AUC, matriz de confusão, precision/recall)                     |
| **03 Ago 2025** | **6. Hiperparâmetros e feature engineering** | Se necessário, ajustar learning rate, batch size, adicionar dropout, explorar encoding de texto (`descricao`)                           |
| **04 Ago 2025** | **7. Integração e entrega**                  | Integrar o modelo ao fluxo real de criação de cenários e validar resultados junto ao time fiscal                                        |


# Como tornar a detecção ainda mais robusta: feature de "diferença"
Para faliciar que o modelo exatamente veja "isso mudou de 12 para 15", podemos criar uma nova coluna:
# no src/db.py, ao buscar os cenários novos, faça um LEFT JOIN
# com o cenário atual em produção para obter a aliquota de referência:

WITH prod_base AS (
  SELECT cliente_id, produto_id, cfop, ncm,
         MAX(data_inicio_vigencia) AS ultima_vi
  FROM cenario_icms
  WHERE status = 'producao'
  GROUP BY cliente_id, produto_id, cfop, ncm
)
SELECT c.*,
       p.aliquota AS aliquota_producao
FROM cenario_importacao_novos AS c
LEFT JOIN prod_base AS b
  ON c.cliente_id    = b.cliente_id
 AND c.produto_id    = b.produto_id
 AND c.cfop          = b.cfop
 AND c.ncm           = b.ncm
LEFT JOIN cenario_icms AS p
  ON p.cliente_id    = b.cliente_id
 AND p.produto_id    = b.produto_id
 AND p.cfop          = b.cfop
 AND p.ncm           = b.ncm
 AND p.data_inicio_vigencia = b.ultima_vi;
# Isso acrescenta ao df uma coluna aliquota_producao
## No preprocess.py, então:
# após carregar o df…
df['delta_aliquota'] = df['aliquota'] - df['aliquota_producao']
# imputar e normalizar
df['delta_aliquota'] = df['delta_aliquota'].fillna(0)
from sklearn.preprocessing import StandardScaler
sc = StandardScaler().fit(df[['delta_aliquota']])
df['delta_aliquota'] = sc.transform(df[['delta_aliquota']])
# e depois inclua 'delta_aliquota' em num_cols
num_cols = ['aliquota', 'p_red_bc', 'delta_aliquota']

Dessa forma, o vetor enviado ao modelo fica algo como:

…	aliquota	p_red_bc	delta_aliquota	…
…	–0.02	–0.03	+2.5/σ_delta	…

e o modelo explicitamente vê o quanto a alíquota diverge do que era “normal”.

Resumo
Sem o delta, o modelo já pode detectar anomalias de alíquota por aprender padrões absolutos.

Com o delta, você fornece uma pista direta, acelerando o aprendizado e aumentando a confiança nas predições (“inconsistente se delta_aliquota for grande”).
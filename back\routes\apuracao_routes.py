from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import (
    db,
    Usuario,
    Empresa,
    ApuracaoICMS,
    ApuracaoIPI,
    IpiApuracaoGeral,
)
from services.apuracao_pis_cofins_service import ApuracaoPisCofinsService
from datetime import datetime

apuracao_bp = Blueprint('apuracao_bp', __name__)

@apuracao_bp.route('/api/apuracao/<int:empresa_id>', methods=['GET'])
@jwt_required()
def obter_apuracao(empresa_id):
    ano = request.args.get('ano', type=int)
    mes = request.args.get('mes', type=int)
    if not ano or not mes:
        return jsonify({'message': 'Ano e mês são obrigatórios'}), 400

    usuario_id = get_jwt_identity()
    usuario = db.session.get(Usuario, usuario_id)
    if not usuario:
        return jsonify({'message': 'Usuário não encontrado'}), 404

    empresa = db.session.get(Empresa, empresa_id)
    if not empresa:
        return jsonify({'message': 'Empresa não encontrada'}), 404

    if usuario.tipo_usuario == 'empresa' and usuario.empresa_id != empresa.id:
        return jsonify({'message': 'Sem permissão'}), 403
    if usuario.tipo_usuario == 'escritorio' and usuario.escritorio_id != empresa.escritorio_id:
        return jsonify({'message': 'Sem permissão'}), 403
    if not (usuario.is_admin or usuario.tipo_usuario in ['admin', 'escritorio'] or (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)):
        return jsonify({'message': 'Sem permissão'}), 403

    icms = ApuracaoICMS.query.filter_by(empresa_id=empresa_id, ano=ano, mes=mes, tipo='icms').first()
    icms_st = ApuracaoICMS.query.filter_by(empresa_id=empresa_id, ano=ano, mes=mes, tipo='icms_st').first()
    ipi = ApuracaoIPI.query.filter_by(empresa_id=empresa_id, ano=ano, mes=mes).all()
    ipi_geral = IpiApuracaoGeral.query.filter_by(empresa_id=empresa_id, ano=ano, mes=mes).first()

    return jsonify({
        'success': True,
        'icms': icms.to_dict() if icms else None,
        'icms_st': icms_st.to_dict() if icms_st else None,
        'ipi': [r.to_dict() for r in ipi],
        'ipi_geral': ipi_geral.to_dict() if ipi_geral else None
    })


@apuracao_bp.route('/api/apuracao/<int:empresa_id>/historico', methods=['GET'])
@jwt_required()
def obter_apuracao_historico(empresa_id):
    """Retorna histórico de ICMS ou ICMS-ST dos últimos 12 meses."""
    try:
        ano = request.args.get('ano', type=int, default=datetime.now().year)
        mes = request.args.get('mes', type=int, default=datetime.now().month)
        tipo = request.args.get('tipo', 'icms')
        if tipo:
            tipo = tipo.lower()
        if tipo not in ['icms', 'icms_st']:
            tipo = 'icms'
            
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({'message': 'Empresa não encontrada'}), 404

        if usuario.tipo_usuario == 'empresa' and usuario.empresa_id != empresa.id:
            return jsonify({'message': 'Sem permissão'}), 403
        if (
            usuario.tipo_usuario == 'escritorio'
            and usuario.escritorio_id != empresa.escritorio_id
        ):
            return jsonify({'message': 'Sem permissão'}), 403
        if not (
            usuario.is_admin
            or usuario.tipo_usuario in ['admin', 'escritorio']
            or (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)
        ):
            return jsonify({'message': 'Sem permissão'}), 403

        meses_nomes = [
            'Jan',
            'Fev',
            'Mar',
            'Abr',
            'Mai',
            'Jun',
            'Jul',
            'Ago',
            'Set',
            'Out',
            'Nov',
            'Dez',
        ]

        meses = []
        ano_atual, mes_atual = ano, mes
        for _ in range(12):
            label = f"{meses_nomes[mes_atual - 1]} {ano_atual}"
            meses.append({'ano': ano_atual, 'mes': mes_atual, 'label': label})
            if mes_atual == 1:
                ano_atual -= 1
                mes_atual = 12
            else:
                mes_atual -= 1
        meses.reverse()

        dados = {
            'labels': [m['label'] for m in meses],
            'vl_tot_debitos': [0] * 12,
            'vl_tot_creditos': [0] * 12,
            'vl_icms_recolher': [0] * 12,
            'vl_sld_credor_transportar': [0] * 12,
            'vl_icms_a_recolher': [0] * 12,
        }

        for idx, m in enumerate(meses):
            apur = ApuracaoICMS.query.filter_by(
                empresa_id=empresa_id,
                ano=m['ano'],
                mes=m['mes'],
                tipo=tipo,
            ).first()
            if apur:
                dados['vl_tot_debitos'][idx] = float(apur.vl_tot_debitos or 0)
                dados['vl_tot_creditos'][idx] = float(apur.vl_tot_creditos or 0)
                rec = float(getattr(apur, 'vl_icms_recolher', 0) or 0)
                cred = float(getattr(apur, 'vl_sld_credor_transportar', 0) or 0)
                dados['vl_icms_recolher'][idx] = rec
                dados['vl_sld_credor_transportar'][idx] = cred
                dados['vl_icms_a_recolher'][idx] = rec if rec != 0 else -cred
                print(
                    f"Histórico {m['mes']}/{m['ano']} - debitos={apur.vl_tot_debitos} "
                    f"creditos={apur.vl_tot_creditos} recolher={rec} cred_trans={cred}"
                )
            else:
                print(f"Histórico {m['mes']}/{m['ano']} - sem registros")

        print('Série ICMS a Recolher:', dados['vl_icms_a_recolher'])

        return (
            jsonify(
                {
                    'success': True,
                    'empresa': {
                        'id': empresa.id,
                        'razao_social': empresa.razao_social
                        or empresa.nome_fantasia
                        or empresa.nome,
                        'cnpj': empresa.cnpj,
                    },
                    'periodo': {
                        'inicio': meses[0]['label'],
                        'fim': meses[-1]['label'],
                    },
                    'dados': dados,
                }
            ),
            200,
        )
    except Exception as e:
        print(f'Erro ao obter histórico de apuração: {e}')
        return jsonify({'success': False, 'message': 'Erro interno do servidor'}), 500


@apuracao_bp.route('/api/apuracao/<int:empresa_id>/historico/ipi', methods=['GET'])
@jwt_required()
def obter_apuracao_historico_ipi(empresa_id):
    """Retorna histórico de IPI dos últimos 12 meses."""
    try:
        ano = request.args.get('ano', type=int, default=datetime.now().year)
        mes = request.args.get('mes', type=int, default=datetime.now().month)

        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({'message': 'Empresa não encontrada'}), 404

        if usuario.tipo_usuario == 'empresa' and usuario.empresa_id != empresa.id:
            return jsonify({'message': 'Sem permissão'}), 403
        if (
            usuario.tipo_usuario == 'escritorio'
            and usuario.escritorio_id != empresa.escritorio_id
        ):
            return jsonify({'message': 'Sem permissão'}), 403
        if not (
            usuario.is_admin
            or usuario.tipo_usuario in ['admin', 'escritorio']
            or (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)
        ):
            return jsonify({'message': 'Sem permissão'}), 403

        meses_nomes = [
            'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
            'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
        ]

        meses = []
        ano_atual, mes_atual = ano, mes
        for _ in range(12):
            label = f"{meses_nomes[mes_atual - 1]} {ano_atual}"
            meses.append({'ano': ano_atual, 'mes': mes_atual, 'label': label})
            if mes_atual == 1:
                ano_atual -= 1
                mes_atual = 12
            else:
                mes_atual -= 1
        meses.reverse()

        dados = {
            'labels': [m['label'] for m in meses],
            'vl_deb_ipi': [0] * 12,
            'vl_cred_ipi': [0] * 12,
            'vl_sc_ipi': [0] * 12,
            'vl_sd_ipi': [0] * 12,
            'vl_ipi_a_recolher': [0] * 12,
        }

        for idx, m in enumerate(meses):
            apur = IpiApuracaoGeral.query.filter_by(
                empresa_id=empresa_id,
                ano=m['ano'],
                mes=m['mes'],
            ).first()
            if apur:
                dados['vl_deb_ipi'][idx] = float(apur.vl_deb_ipi or 0)
                dados['vl_cred_ipi'][idx] = float(apur.vl_cred_ipi or 0)
                sd = float(apur.vl_sd_ipi or 0)
                sc = float(apur.vl_sc_ipi or 0)
                dados['vl_sd_ipi'][idx] = sd
                dados['vl_sc_ipi'][idx] = sc
                dados['vl_ipi_a_recolher'][idx] = sd if sd != 0 else -sc
            
        return (
            jsonify(
                {
                    'success': True,
                    'empresa': {
                        'id': empresa.id,
                        'razao_social': empresa.razao_social
                        or empresa.nome_fantasia
                        or empresa.nome,
                        'cnpj': empresa.cnpj,
                    },
                    'periodo': {
                        'inicio': meses[0]['label'],
                        'fim': meses[-1]['label'],
                    },
                    'dados': dados,
                }
            ),
            200,
        )
    except Exception as e:
        print(f'Erro ao obter histórico de apuração IPI: {e}')
        return jsonify({'success': False, 'message': 'Erro interno do servidor'}), 500


@apuracao_bp.route('/api/apuracao/<int:empresa_id>/historico/pis', methods=['GET'])
@jwt_required()
def obter_apuracao_historico_pis(empresa_id):
    try:
        ano = request.args.get('ano', type=int, default=datetime.now().year)
        mes = request.args.get('mes', type=int, default=datetime.now().month)

        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({'message': 'Empresa não encontrada'}), 404

        if usuario.tipo_usuario == 'empresa' and usuario.empresa_id != empresa.id:
            return jsonify({'message': 'Sem permissão'}), 403
        if usuario.tipo_usuario == 'escritorio' and usuario.escritorio_id != empresa.escritorio_id:
            return jsonify({'message': 'Sem permissão'}), 403
        if not (
            usuario.is_admin
            or usuario.tipo_usuario in ['admin', 'escritorio']
            or (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)
        ):
            return jsonify({'message': 'Sem permissão'}), 403

        meses_nomes = ['Jan','Fev','Mar','Abr','Mai','Jun','Jul','Ago','Set','Out','Nov','Dez']
        meses = []
        ano_atual, mes_atual = ano, mes
        for _ in range(12):
            label = f"{meses_nomes[mes_atual - 1]} {ano_atual}"
            meses.append({'ano': ano_atual, 'mes': mes_atual, 'label': label})
            if mes_atual == 1:
                ano_atual -= 1
                mes_atual = 12
            else:
                mes_atual -= 1
        meses.reverse()

        dados = {
            'labels': [m['label'] for m in meses],
            'vl_tot_debitos': [0] * 12,
            'vl_tot_creditos': [0] * 12,
            'vl_pis_a_recolher': [0] * 12,
        }

        service = ApuracaoPisCofinsService(empresa_id)
        for idx, m in enumerate(meses):
            res = service.calcular_pis(m['ano'], m['mes'])
            dados['vl_tot_debitos'][idx] = res['vl_tot_debitos']
            dados['vl_tot_creditos'][idx] = res['vl_tot_creditos']
            dados['vl_pis_a_recolher'][idx] = res['vl_pis_a_recolher']

        return (
            jsonify({
                'success': True,
                'empresa': {
                    'id': empresa.id,
                    'razao_social': empresa.razao_social or empresa.nome_fantasia or empresa.nome,
                    'cnpj': empresa.cnpj,
                },
                'periodo': {'inicio': meses[0]['label'], 'fim': meses[-1]['label']},
                'dados': dados,
            }),
            200,
        )
    except Exception as e:
        print(f'Erro ao obter histórico de apuração PIS: {e}')
        return jsonify({'success': False, 'message': 'Erro interno do servidor'}), 500


@apuracao_bp.route('/api/apuracao/<int:empresa_id>/historico/cofins', methods=['GET'])
@jwt_required()
def obter_apuracao_historico_cofins(empresa_id):
    try:
        ano = request.args.get('ano', type=int, default=datetime.now().year)
        mes = request.args.get('mes', type=int, default=datetime.now().month)

        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        if not usuario:
            return jsonify({'message': 'Usuário não encontrado'}), 404

        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({'message': 'Empresa não encontrada'}), 404

        if usuario.tipo_usuario == 'empresa' and usuario.empresa_id != empresa.id:
            return jsonify({'message': 'Sem permissão'}), 403
        if usuario.tipo_usuario == 'escritorio' and usuario.escritorio_id != empresa.escritorio_id:
            return jsonify({'message': 'Sem permissão'}), 403
        if not (
            usuario.is_admin
            or usuario.tipo_usuario in ['admin', 'escritorio']
            or (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)
        ):
            return jsonify({'message': 'Sem permissão'}), 403

        meses_nomes = ['Jan','Fev','Mar','Abr','Mai','Jun','Jul','Ago','Set','Out','Nov','Dez']
        meses = []
        ano_atual, mes_atual = ano, mes
        for _ in range(12):
            label = f"{meses_nomes[mes_atual - 1]} {ano_atual}"
            meses.append({'ano': ano_atual, 'mes': mes_atual, 'label': label})
            if mes_atual == 1:
                ano_atual -= 1
                mes_atual = 12
            else:
                mes_atual -= 1
        meses.reverse()

        dados = {
            'labels': [m['label'] for m in meses],
            'vl_tot_debitos': [0] * 12,
            'vl_tot_creditos': [0] * 12,
            'vl_cofins_a_recolher': [0] * 12,
        }

        service = ApuracaoPisCofinsService(empresa_id)
        for idx, m in enumerate(meses):
            res = service.calcular_cofins(m['ano'], m['mes'])
            dados['vl_tot_debitos'][idx] = res['vl_tot_debitos']
            dados['vl_tot_creditos'][idx] = res['vl_tot_creditos']
            dados['vl_cofins_a_recolher'][idx] = res['vl_cofins_a_recolher']

        return (
            jsonify({
                'success': True,
                'empresa': {
                    'id': empresa.id,
                    'razao_social': empresa.razao_social or empresa.nome_fantasia or empresa.nome,
                    'cnpj': empresa.cnpj,
                },
                'periodo': {'inicio': meses[0]['label'], 'fim': meses[-1]['label']},
                'dados': dados,
            }),
            200,
        )
    except Exception as e:
        print(f'Erro ao obter histórico de apuração COFINS: {e}')
        return jsonify({'success': False, 'message': 'Erro interno do servidor'}), 500
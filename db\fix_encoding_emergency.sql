-- =====================================================
-- CORREÇÃO EMERGENCIAL DE ENCODING
-- Sistema de Auditoria Fiscal
-- =====================================================

-- Definir encoding correto
SET client_encoding = 'UTF8';

-- Verificar se há problemas de encoding nas tabelas de cenários
-- e corrigir dados corrompidos

-- Função para limpar caracteres problemáticos
CREATE OR REPLACE FUNCTION clean_text(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
    IF input_text IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Remover caracteres problemáticos e manter apenas ASCII válido
    RETURN regexp_replace(input_text, '[^\x20-\x7E]', '', 'g');
END;
$$ LANGUAGE plpgsql;

-- Limpar dados problemáticos nas tabelas de cenários
UPDATE cenario_icms SET 
    status = clean_text(status),
    cst = clean_text(cst),
    cfop = clean_text(cfop),
    ncm = clean_text(ncm)
WHERE status ~ '[^\x20-\x7E]' 
   OR cst ~ '[^\x20-\x7E]' 
   OR cfop ~ '[^\x20-\x7E]' 
   OR ncm ~ '[^\x20-\x7E]';

UPDATE cenario_icms_st SET 
    status = clean_text(status),
    cst = clean_text(cst),
    cfop = clean_text(cfop),
    ncm = clean_text(ncm)
WHERE status ~ '[^\x20-\x7E]' 
   OR cst ~ '[^\x20-\x7E]' 
   OR cfop ~ '[^\x20-\x7E]' 
   OR ncm ~ '[^\x20-\x7E]';

UPDATE cenario_ipi SET 
    status = clean_text(status),
    cst = clean_text(cst),
    cfop = clean_text(cfop),
    ncm = clean_text(ncm)
WHERE status ~ '[^\x20-\x7E]' 
   OR cst ~ '[^\x20-\x7E]' 
   OR cfop ~ '[^\x20-\x7E]' 
   OR ncm ~ '[^\x20-\x7E]';

UPDATE cenario_pis SET 
    status = clean_text(status),
    cst = clean_text(cst),
    cfop = clean_text(cfop),
    ncm = clean_text(ncm)
WHERE status ~ '[^\x20-\x7E]' 
   OR cst ~ '[^\x20-\x7E]' 
   OR cfop ~ '[^\x20-\x7E]' 
   OR ncm ~ '[^\x20-\x7E]';

UPDATE cenario_cofins SET 
    status = clean_text(status),
    cst = clean_text(cst),
    cfop = clean_text(cfop),
    ncm = clean_text(ncm)
WHERE status ~ '[^\x20-\x7E]' 
   OR cst ~ '[^\x20-\x7E]' 
   OR cfop ~ '[^\x20-\x7E]' 
   OR ncm ~ '[^\x20-\x7E]';

UPDATE cenario_difal SET 
    status = clean_text(status),
    cst = clean_text(cst),
    cfop = clean_text(cfop),
    ncm = clean_text(ncm)
WHERE status ~ '[^\x20-\x7E]' 
   OR cst ~ '[^\x20-\x7E]' 
   OR cfop ~ '[^\x20-\x7E]' 
   OR ncm ~ '[^\x20-\x7E]';

-- Garantir que todos os status sejam válidos
UPDATE cenario_icms SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_icms_st SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_ipi SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_pis SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_cofins SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;
UPDATE cenario_difal SET status = 'novo' WHERE status NOT IN ('novo', 'producao', 'inconsistente') OR status IS NULL;

-- Remover a função temporária
DROP FUNCTION IF EXISTS clean_text(TEXT);

-- Commit das alterações
COMMIT;

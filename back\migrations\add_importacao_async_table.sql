-- Migração para adicionar tabela de importações assíncronas
-- Execute este script no banco de dados

CREATE TABLE IF NOT EXISTS importacao_async (
    id VARCHAR(36) PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    escritorio_id INTEGER NOT NULL,
    tipo VARCHAR(20) NOT NULL DEFAULT 'xml_batch',
    status VARCHAR(20) NOT NULL DEFAULT 'iniciando',
    
    -- Progresso
    total_arquivos INTEGER DEFAULT 0,
    arquivos_processados INTEGER DEFAULT 0,
    arquivos_sucesso INTEGER DEFAULT 0,
    arquivos_erro INTEGER DEFAULT 0,
    porcentagem INTEGER DEFAULT 0,
    
    -- Dados da importação
    dados_entrada TEXT,
    resultado TEXT,
    mensagem_atual VARCHAR(500),
    mensagem_erro TEXT,
    
    -- Timestamps
    data_inicio DATETIME DEFAULT CURRENT_TIMESTAMP,
    data_fim DATETIME,
    data_ultima_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (usuario_id) REFERENCES usuario(id) ON DELETE CASCADE,
    FOREIGN KEY (escritorio_id) REFERENCES escritorio(id) ON DELETE CASCADE
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_importacao_async_usuario ON importacao_async(usuario_id);
CREATE INDEX IF NOT EXISTS idx_importacao_async_status ON importacao_async(status);
CREATE INDEX IF NOT EXISTS idx_importacao_async_tipo ON importacao_async(tipo);
CREATE INDEX IF NOT EXISTS idx_importacao_async_data_inicio ON importacao_async(data_inicio);

-- Comentários para documentação
COMMENT ON TABLE importacao_async IS 'Controle de importações assíncronas (XML em lote, SPED, etc.)';
COMMENT ON COLUMN importacao_async.id IS 'UUID único da importação';
COMMENT ON COLUMN importacao_async.tipo IS 'Tipo de importação: xml_batch, sped, etc.';
COMMENT ON COLUMN importacao_async.status IS 'Status: iniciando, processando, concluido, erro';
COMMENT ON COLUMN importacao_async.dados_entrada IS 'JSON com dados de entrada da importação';
COMMENT ON COLUMN importacao_async.resultado IS 'JSON com resultado final da importação';

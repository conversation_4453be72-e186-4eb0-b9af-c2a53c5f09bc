import { useEffect, useState, useRef } from 'react'
import { io as ioClient } from 'socket.io-client'

interface AuditoriaProgress {
  audit_id: string
  status: 'processing' | 'completed' | 'error' | 'cancelled'
  progress?: number
  percentage?: number
  processed?: number
  total?: number
  message?: string
  current_step?: string
  total_steps?: number
  results?: any
  error?: string
}

interface UseAuditoriaWebSocketProps {
  onProgress?: (progress: AuditoriaProgress) => void
  onComplete?: (results: any) => void
  onError?: (error: string) => void
}

// Declarar tipos globais para o WebSocket do frontend antigo
declare global {
  interface Window {
    auditoriaSocket: any
    io: any
    setupAuditoriaWebSocketWithStatus: () => any
  }
}

export function useAuditoriaWebSocket({
                                        onProgress,
                                        onComplete,
                                        onError,
                                      }: UseAuditoriaWebSocketProps = {}) {
  const [isConnected, setIsConnected] = useState(false)
  const [auditoriaStatus, setAuditoriaStatus] = useState<{
    [auditId: string]: AuditoriaProgress
  }>({})
  const socketRef = useRef<any>(null)
  const activeAuditIds = useRef<Set<string>>(new Set())

  const mapStatus = (status?: string): AuditoriaProgress['status'] => {
    if (!status) return 'processing'
    const normalized = status.toLowerCase()
    switch (normalized) {
      case 'concluido':
      case 'completed':
        return 'completed'
      case 'erro':
      case 'error':
        return 'error'
      case 'cancelado':
      case 'cancelled':
        return 'cancelled'
      default:
        return 'processing'
    }
  }

  useEffect(() => {
    // Tentar usar o WebSocket global do frontend antigo
    // Handlers individuais para cada instância do hook
    const handleProgress = (data: any) => {
      console.log('📊 Progresso da auditoria:', data)

      let auditId = data.audit_id
      if (!auditId && activeAuditIds.current.size === 1) {
        auditId = Array.from(activeAuditIds.current)[0]
      }

      const formatted: AuditoriaProgress = {
        audit_id: auditId,
        status: mapStatus(data.status),
        progress: data.progress ?? data.porcentagem ?? data.percentage,
        percentage: data.percentage ?? data.progress ?? data.porcentagem,
        processed: data.processed ?? data.auditados,
        total: data.total ?? data.total_itens,
        message: data.message ?? data.mensagem,
        current_step: data.current_step ?? data.etapa,
        total_steps: data.total_steps ?? data.total,
        results: data.results,
        error: data.error,
      }

      if (formatted.audit_id) {
        setAuditoriaStatus((prev) => ({
          ...prev,
          [formatted.audit_id]: formatted,
        }))
      }

      onProgress?.(formatted)
    }

    const handleComplete = (data: any) => {
      console.log('✅ Auditoria concluída:', data)

      let auditId = data.audit_id
      if (!auditId && activeAuditIds.current.size === 1) {
        auditId = Array.from(activeAuditIds.current)[0]
      }

      const formatted: AuditoriaProgress = {
        audit_id: auditId,
        status: 'completed',
        message: data.message ?? data.mensagem,
        results: data.results,
      }

      if (formatted.audit_id) {
        setAuditoriaStatus((prev) => ({
          ...prev,
          [formatted.audit_id]: formatted,
        }))
      }

      // Passar os dados completos incluindo audit_id
      onComplete?.({ ...data, audit_id: formatted.audit_id })
    }

    const handleError = (data: any) => {
      console.log('❌ Erro na auditoria:', data)

      let auditId = data.audit_id
      if (!auditId && activeAuditIds.current.size === 1) {
        auditId = Array.from(activeAuditIds.current)[0]
      }

      const formatted: AuditoriaProgress = {
        audit_id: auditId,
        status: 'error',
        message: data.message ?? data.mensagem,
        error: data.error,
      }

      if (formatted.audit_id) {
        setAuditoriaStatus((prev) => ({
          ...prev,
          [formatted.audit_id]: formatted,
        }))
      }

      onError?.(
        data.message || data.mensagem || 'Erro desconhecido na auditoria'
      )
    }

    const setupEventListeners = () => {
      if (!socketRef.current) return

      // Eventos de progresso da auditoria
      socketRef.current.on('audit_progress', handleProgress)

      // Evento de conclusão da auditoria
      socketRef.current.on('audit_complete', handleComplete)

      // Evento de erro na auditoria
      socketRef.current.on('audit_error', handleError)
    }

    const initializeWebSocket = () => {
      // Verificar se o WebSocket global já existe
      if (window.auditoriaSocket) {
        console.log('🔌 Usando WebSocket global existente')
        socketRef.current = window.auditoriaSocket
        setIsConnected(true)
        setupEventListeners()
        return
      }

      // Tentar criar usando a função global
      if (window.setupAuditoriaWebSocketWithStatus) {
        console.log('🔌 Criando WebSocket usando função global')
        const socket = window.setupAuditoriaWebSocketWithStatus()
        if (socket) {
          socketRef.current = socket
          window.auditoriaSocket = socket
          setIsConnected(true)
          setupEventListeners()
          return
        }
      }

      // Fallback: criar WebSocket diretamente se Socket.IO estiver disponível
      const ioInstance = window.io || ioClient
      if (ioInstance) {
        console.log('🔌 Criando WebSocket diretamente')
        const socket = ioInstance({
          autoConnect: true,
          reconnection: true,
          reconnectionAttempts: 5,
          timeout: 60000,
          forceNew: false,
          path: '/fiscal/socket.io',
        })

        socketRef.current = socket
        window.auditoriaSocket = socket

        socket.on('connect', () => {
          console.log('🔌 WebSocket conectado para auditoria')
          setIsConnected(true)
        })

        socket.on('disconnect', () => {
          console.log('🔌 WebSocket desconectado')
          setIsConnected(false)
        })

        setupEventListeners()
      } else {
        console.warn('Socket.IO não está disponível')
      }
    }

    // Tentar inicializar imediatamente
    initializeWebSocket()

    // Se não conseguiu, tentar novamente após um tempo
    const retryTimeout = setTimeout(() => {
      if (!socketRef.current) {
        console.log('🔄 Tentando reconectar WebSocket...')
        initializeWebSocket()
      }
    }, 2000)

    // Cleanup
    return () => {
      clearTimeout(retryTimeout)
      // Não desconectar o WebSocket global, apenas remover nossos listeners
      if (socketRef.current) {
        socketRef.current.off('audit_progress', handleProgress)
        socketRef.current.off('audit_complete', handleComplete)
        socketRef.current.off('audit_error', handleError)
      }
    }
  }, [onProgress, onComplete, onError])

  const subscribeToAudit = (auditId: string) => {
    if (socketRef.current) {
      console.log('🎯 Inscrevendo-se na auditoria:', auditId)
      // Usar o mesmo formato do frontend antigo
      socketRef.current.emit('join_audit', {
        token: localStorage.getItem('token'),
        audit_id: auditId,
      })
      activeAuditIds.current.add(auditId)
    }
  }

  const unsubscribeFromAudit = (auditId: string) => {
    if (socketRef.current) {
      console.log('🎯 Desinscrevendo-se da auditoria:', auditId)
      socketRef.current.emit('leave_audit', { audit_id: auditId })
      activeAuditIds.current.delete(auditId)
    }
  }

  const getAuditStatus = (auditId: string) => {
    return auditoriaStatus[auditId]
  }

  const isAuditProcessing = (auditId: string) => {
    const status = auditoriaStatus[auditId]
    return status?.status === 'processing'
  }

  return {
    isConnected,
    auditoriaStatus,
    subscribeToAudit,
    unsubscribeFromAudit,
    getAuditStatus,
    isAuditProcessing,
  }
}
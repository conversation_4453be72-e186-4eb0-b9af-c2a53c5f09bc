"""
Serviço para Auditoria Comparativa de Impostos
Gerencia o processo completo de matching e auditoria entre XML e SPED
"""

import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from sqlalchemy import and_, or_, text
from models import db
from models.auditoria_comparativa_impostos import AuditoriaComparativaImpostos, HistoricoMatchingAprendizado
from models.nota_fiscal_item import NotaFiscalItem
from models.item_nota_entrada import ItemNotaEntrada
from models.produto import Produto
from models.produto_entrada import ProdutoEntrada
from models.tributo import Tributo
from models.nota_entrada import NotaEntrada
from models.importacao_xml import ImportacaoXML
from services.item_matching_service import ItemMatchingService
from services.sugestoes_inteligentes_service import SugestoesInteligentesService
from services.transactional import transactional_session

class AuditoriaComparativaService:
    def __init__(self, empresa_id: int, escritorio_id: int, usuario_id: int):
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id
        self.matching_service = ItemMatchingService(empresa_id, escritorio_id)
        self.sugestoes_service = SugestoesInteligentesService(empresa_id, escritorio_id)

    def gerar_auditoria_comparativa(self, chave_nf: str, force_recalculate: bool = False) -> Dict[str, Any]:
        """
        Gera auditoria comparativa completa para uma nota fiscal

        Args:
            chave_nf: Chave da nota fiscal
            force_recalculate: Se True, força recálculo mesmo se já existir

        Returns:
            Dict com resultado da auditoria
        """
        try:
            # Verificar se já existe auditoria para esta nota
            if not force_recalculate:
                existing = db.session.query(AuditoriaComparativaImpostos).filter(
                    AuditoriaComparativaImpostos.empresa_id == self.empresa_id,
                    AuditoriaComparativaImpostos.chave_nf == chave_nf
                ).first()

                if existing:
                    return {
                        'success': True,
                        'message': 'Auditoria já existe para esta nota',
                        'auditoria_id': existing.id,
                        'force_recalculate_required': True
                    }

            # Executar matching entre XML e SPED
            matching_result = self.matching_service.find_matches_for_note(chave_nf, force_recalculate)

            if not matching_result['success']:
                return matching_result

            # Criar registros de auditoria baseados nos matches
            auditorias_criadas = []

            for match in matching_result['matches']:
                auditoria = self._create_auditoria_record(match, chave_nf)
                if auditoria:
                    auditorias_criadas.append(auditoria)

            # Salvar no banco
            if auditorias_criadas:
                with transactional_session():
                    db.session.add_all(auditorias_criadas)

            return {
                'success': True,
                'message': f'Auditoria comparativa gerada com sucesso',
                'chave_nf': chave_nf,
                'total_registros': len(auditorias_criadas),
                'matching_stats': matching_result['statistics'],
                'auditorias': [a.to_dict() for a in auditorias_criadas]
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'Erro ao gerar auditoria comparativa: {str(e)}'
            }

    def gerar_auditoria_comparativa_periodo(self, mes: int, ano: int, force_recalculate: bool = False, progress_callback=None) -> Dict[str, Any]:
        """
        Gera auditoria comparativa para todas as notas de um período (mês/ano)

        Args:
            mes: Mês de referência
            ano: Ano de referência
            force_recalculate: Se True, força recálculo mesmo se já existir
            progress_callback: Função callback para reportar progresso

        Returns:
            Dict com resultado da auditoria
        """
        try:
            from models.importacao_xml import ImportacaoXML
            from sqlalchemy import func

            # Buscar todas as notas XML de entrada do período
            notas_xml = db.session.query(ImportacaoXML).filter(
                ImportacaoXML.empresa_id == self.empresa_id,
                ImportacaoXML.tipo_nota == '0',  # Entrada
                func.extract('month', ImportacaoXML.data_entrada) == mes,
                func.extract('year', ImportacaoXML.data_entrada) == ano
            ).all()

            if not notas_xml:
                return {
                    'success': False,
                    'message': 'Nenhuma nota XML encontrada para o período especificado'
                }

            total_notas = len(notas_xml)

            # Se for recálculo, remover registros existentes deste período
            if force_recalculate:
                chaves_periodo = [n.chave_nf for n in notas_xml]
                if chaves_periodo:
                    with transactional_session():
                        db.session.query(AuditoriaComparativaImpostos).filter(
                            AuditoriaComparativaImpostos.empresa_id == self.empresa_id,
                            AuditoriaComparativaImpostos.chave_nf.in_(chaves_periodo)
                        ).delete(synchronize_session=False)

            # Enviar progresso inicial
            if progress_callback:
                progress_callback({
                    'etapa': 'Iniciando processamento',
                    'progresso': 0,
                    'total': total_notas,
                    'porcentagem': 0,
                    'mensagem': f'Encontradas {total_notas} notas para processar',
                    'status': 'iniciando'
                })

            # Processar cada nota
            total_auditorias = 0
            total_registros = 0
            notas_processadas = 0
            notas_com_erro = 0
            batch_size = 10  # Processar em lotes de 10 notas para evitar timeout
            batch_registros = []  # Buffer para registros do lote atual

            estatisticas_gerais = {
                'total_xml_items': 0,
                'total_sped_items': 0,
                'total_matches': 0,
                'direct_matches': 0,
                'embedding_matches': 0,
                'unmatched_xml_items': 0,
                'unmatched_sped_items': 0,
                'high_confidence_matches': 0,
                'medium_confidence_matches': 0,
                'low_confidence_matches': 0,
                'confidence_sum': 0,
                'confidence_count': 0
            }

            for i, nota_xml in enumerate(notas_xml, 1):
                try:
                    # Enviar progresso da nota atual
                    if progress_callback:
                        porcentagem = int((i / total_notas) * 100)
                        progress_callback({
                            'etapa': 'Processando notas',
                            'progresso': i,
                            'total': total_notas,
                            'porcentagem': porcentagem,
                            'mensagem': f'Processando nota {i} de {total_notas} - NF: {nota_xml.numero_nf}',
                            'status': 'processando',
                            'nota_atual': nota_xml.numero_nf,
                            'chave_atual': nota_xml.chave_nf[:8] + '...'
                        })

                    # Verificar se já existe auditoria para esta nota
                    if not force_recalculate:
                        existing = db.session.query(AuditoriaComparativaImpostos).filter(
                            AuditoriaComparativaImpostos.empresa_id == self.empresa_id,
                            AuditoriaComparativaImpostos.chave_nf == nota_xml.chave_nf
                        ).first()

                        if existing:
                            notas_processadas += 1
                            if progress_callback:
                                porcentagem = int((i / total_notas) * 100)
                                progress_callback({
                                    'etapa': 'Processando notas',
                                    'progresso': i,
                                    'total': total_notas,
                                    'porcentagem': porcentagem,
                                    'mensagem': f'Nota {i} de {total_notas} já processada',
                                    'status': 'processando',
                                    'nota_atual': nota_xml.numero_nf,
                                    'total_matches': estatisticas_gerais['total_matches']
                                })
                            continue

                    # Executar matching para esta nota
                    matching_result = self.matching_service.find_matches_for_note(nota_xml.chave_nf, force_recalculate)

                    if matching_result['success']:
                        # Criar registros de auditoria
                        auditorias_criadas = []

                        for match in matching_result['matches']:
                            auditoria = self._create_auditoria_record(match, nota_xml.chave_nf)
                            if auditoria:
                                auditorias_criadas.append(auditoria)

                        # Adicionar ao buffer do lote
                        if auditorias_criadas:
                            batch_registros.extend(auditorias_criadas)
                            total_registros += len(auditorias_criadas)

                        # Acumular estatísticas
                        stats = matching_result.get('statistics', {})
                        estatisticas_gerais['total_xml_items'] += stats.get('total_xml_items', 0)
                        estatisticas_gerais['total_sped_items'] += stats.get('total_sped_items', 0)
                        estatisticas_gerais['total_matches'] += stats.get('total_matches', 0)
                        estatisticas_gerais['direct_matches'] += stats.get('direct_matches', 0)
                        estatisticas_gerais['embedding_matches'] += stats.get('embedding_matches', 0)
                        estatisticas_gerais['unmatched_xml_items'] += stats.get('unmatched_xml_items', 0)
                        estatisticas_gerais['unmatched_sped_items'] += stats.get('unmatched_sped_items', 0)
                        estatisticas_gerais['high_confidence_matches'] += stats.get('high_confidence_matches', 0)
                        estatisticas_gerais['medium_confidence_matches'] += stats.get('medium_confidence_matches', 0)
                        estatisticas_gerais['low_confidence_matches'] += stats.get('low_confidence_matches', 0)

                        # Acumular confiança média ponderada
                        if stats.get('average_confidence', 0) > 0:
                            total_matches_nota = stats.get('total_matches', 0)
                            if total_matches_nota > 0:
                                estatisticas_gerais['confidence_sum'] += stats['average_confidence'] * total_matches_nota
                                estatisticas_gerais['confidence_count'] += total_matches_nota

                        notas_processadas += 1
                        if progress_callback:
                            porcentagem = int((i / total_notas) * 100)
                            progress_callback({
                                'etapa': 'Processando notas',
                                'progresso': i,
                                'total': total_notas,
                                'porcentagem': porcentagem,
                                'mensagem': f'Nota {i} de {total_notas} processada',
                                'status': 'processando',
                                'nota_atual': nota_xml.numero_nf,
                                'total_matches': estatisticas_gerais['total_matches']
                            })
                    else:
                        notas_com_erro += 1
                        if progress_callback:
                            porcentagem = int((i / total_notas) * 100)
                            progress_callback({
                                'etapa': 'Processando notas',
                                'progresso': i,
                                'total': total_notas,
                                'porcentagem': porcentagem,
                                'mensagem': f'Erro ao processar nota {i} de {total_notas}',
                                'status': 'processando',
                                'nota_atual': nota_xml.numero_nf,
                                'total_matches': estatisticas_gerais['total_matches']
                            })

                    # Commit em lotes para evitar timeout
                    if len(batch_registros) >= batch_size or i == total_notas:
                        if batch_registros:
                            try:
                                with transactional_session():
                                    db.session.add_all(batch_registros)
                                print(f"Lote salvo: {len(batch_registros)} registros (nota {i}/{total_notas})")
                                batch_registros = []  # Limpar buffer
                            except Exception as commit_error:
                                print(f"Erro ao salvar lote: {str(commit_error)}")
                                # Tentar salvar registros individualmente
                                for registro in batch_registros:
                                    try:
                                        with transactional_session():
                                            db.session.add(registro)
                                    except Exception as individual_error:
                                        print(f"Erro ao salvar registro individual: {str(individual_error)}")
                                batch_registros = []

                except Exception as e:
                    print(f"Erro ao processar nota {nota_xml.chave_nf}: {str(e)}")
                    notas_com_erro += 1
                    # Em caso de erro, ainda tentar salvar o lote atual
                    if batch_registros:
                        try:
                            with transactional_session():
                                db.session.add_all(batch_registros)
                            batch_registros = []
                        except Exception:
                            batch_registros = []
                    continue

            # Enviar progresso de finalização
            if progress_callback:
                progress_callback({
                    'etapa': 'Finalizando processamento',
                    'progresso': total_notas,
                    'total': total_notas,
                    'porcentagem': 95,
                    'mensagem': 'Processamento concluído!',
                    'status': 'finalizando'
                })

            # Garantir que todos os registros foram salvos
            if batch_registros:
                try:
                    with transactional_session():
                        db.session.add_all(batch_registros)
                    print(f"Lote final salvo: {len(batch_registros)} registros")
                except Exception as final_commit_error:
                    print(f"Erro no commit final: {str(final_commit_error)}")

            total_auditorias = notas_processadas

            # Calcular estatísticas finais
            avg_confidence = 0
            if estatisticas_gerais['confidence_count'] > 0:
                avg_confidence = estatisticas_gerais['confidence_sum'] / estatisticas_gerais['confidence_count']

            match_rate = 0
            if estatisticas_gerais['total_xml_items'] > 0 or estatisticas_gerais['total_sped_items'] > 0:
                total_items = max(estatisticas_gerais['total_xml_items'], estatisticas_gerais['total_sped_items'])
                match_rate = (estatisticas_gerais['total_matches'] / total_items) * 100 if total_items > 0 else 0

            # Formatar estatísticas para o frontend
            matching_stats = {
                'total_matches': estatisticas_gerais['total_matches'],
                'match_rate': match_rate,
                'direct_matches': estatisticas_gerais['direct_matches'],
                'embedding_matches': estatisticas_gerais['embedding_matches'],
                'unmatched_xml_items': estatisticas_gerais['unmatched_xml_items'],
                'unmatched_sped_items': estatisticas_gerais['unmatched_sped_items'],
                'average_confidence': avg_confidence,
                'high_confidence_matches': estatisticas_gerais['high_confidence_matches'],
                'medium_confidence_matches': estatisticas_gerais['medium_confidence_matches'],
                'low_confidence_matches': estatisticas_gerais['low_confidence_matches']
            }

            # Enviar progresso final
            if progress_callback:
                progress_callback({
                    'etapa': 'Concluído',
                    'progresso': total_notas,
                    'total': total_notas,
                    'porcentagem': 100,
                    'mensagem': f'Processamento concluído! {notas_processadas} notas processadas, {total_registros} registros criados',
                    'status': 'concluido',
                    'estatisticas': matching_stats,
                    'total_matches': estatisticas_gerais['total_matches']
                })

            return {
                'success': True,
                'message': f'Auditoria comparativa gerada para o período {mes:02d}/{ano}',
                'periodo': f'{mes:02d}/{ano}',
                'total_notas_encontradas': len(notas_xml),
                'total_notas_processadas': notas_processadas,
                'total_notas_com_erro': notas_com_erro,
                'total_auditorias': total_auditorias,
                'total_registros': total_registros,
                'matching_stats': matching_stats
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'Erro ao gerar auditoria comparativa do período: {str(e)}'
            }

    def _create_auditoria_record(self, match: Dict, chave_nf: str) -> Optional[AuditoriaComparativaImpostos]:
        """
        Cria registro de auditoria baseado no match
        """
        try:
            auditoria = AuditoriaComparativaImpostos()
            
            # Dados básicos
            auditoria.empresa_id = self.empresa_id
            auditoria.escritorio_id = self.escritorio_id
            auditoria.chave_nf = chave_nf
            auditoria.match_type = match['match_type']
            # Converter numpy types para Python types e validar precisão
            confidence_score = match['confidence_score']
            if hasattr(confidence_score, 'item'):  # numpy scalar
                confidence_score = float(confidence_score.item())
            elif confidence_score is not None:
                confidence_score = float(confidence_score)

            # Validar que o confidence_score não excede o limite do campo DECIMAL(6,4)
            if confidence_score is not None:
                if confidence_score > 99.9999:
                    print(f"AVISO: confidence_score {confidence_score} excede limite, ajustando para 99.9999")
                    confidence_score = 99.9999
                elif confidence_score < 0:
                    print(f"AVISO: confidence_score {confidence_score} é negativo, ajustando para 0")
                    confidence_score = 0

            auditoria.confidence_score = confidence_score
            auditoria.match_details = match.get('details', {})
            auditoria.usuario_criacao = self.usuario_id
            
            # Dados XML
            xml_item = match.get('xml_item')
            if xml_item:
                auditoria.xml_item_id = xml_item['id']
                auditoria.numero_nf = xml_item['numero_nf']
                auditoria.cliente_id = xml_item.get('cliente_id')  # Salvar cliente_id

                # Se cliente_id não veio no match, buscar diretamente do banco
                if not auditoria.cliente_id:
                    from models.nota_fiscal_item import NotaFiscalItem
                    xml_item_db = db.session.get(NotaFiscalItem, xml_item['id'])
                    if xml_item_db:
                        auditoria.cliente_id = xml_item_db.cliente_id
                auditoria.xml_descricao = xml_item['descricao']
                auditoria.xml_quantidade = xml_item['quantidade']
                auditoria.xml_valor_unitario = xml_item['valor_unitario']
                auditoria.xml_valor_total = xml_item['valor_total']
                auditoria.xml_unidade = xml_item['unidade']
                auditoria.xml_ncm = xml_item['ncm']
                auditoria.xml_cfop = xml_item['cfop']

                # Buscar dados tributários XML do item original
                xml_tributos = self._get_xml_item_tributos(xml_item['id'])
                if xml_tributos:
                    auditoria.xml_icms_valor = xml_tributos.get('icms_valor')
                    # Adicionar dados de origem e CSOSN que estavam faltando
                    auditoria.xml_icms_origem = xml_tributos.get('icms_origem')
                    auditoria.xml_icms_cst = xml_tributos.get('icms_cst')
                    auditoria.xml_icms_csosn = xml_tributos.get('icms_csosn')
                    auditoria.xml_icms_bc = xml_tributos.get('icms_vbc')
                    auditoria.xml_icms_aliquota = xml_tributos.get('icms_aliquota')
                    # Dados de outros tributos XML
                    auditoria.xml_ipi_valor = xml_tributos.get('ipi_valor')
                    auditoria.xml_ipi_bc = xml_tributos.get('ipi_vbc')
                    auditoria.xml_ipi_aliquota = xml_tributos.get('ipi_aliquota')
                    auditoria.xml_ipi_cst = xml_tributos.get('ipi_cst')
                    auditoria.xml_pis_valor = xml_tributos.get('pis_valor')
                    auditoria.xml_pis_bc = xml_tributos.get('pis_vbc')
                    auditoria.xml_pis_aliquota = xml_tributos.get('pis_aliquota')
                    auditoria.xml_pis_cst = xml_tributos.get('pis_cst')
                    auditoria.xml_cofins_valor = xml_tributos.get('cofins_valor')
                    auditoria.xml_cofins_bc = xml_tributos.get('cofins_vbc')
                    auditoria.xml_cofins_aliquota = xml_tributos.get('cofins_aliquota')
                    auditoria.xml_cofins_cst = xml_tributos.get('cofins_cst')
                    # ICMS-ST XML
                    auditoria.xml_icms_st_valor = xml_tributos.get('icms_st_valor')
                    auditoria.xml_icms_st_bc = xml_tributos.get('icms_st_vbc')
                    auditoria.xml_icms_st_aliquota = xml_tributos.get('icms_st_aliquota')
                    auditoria.xml_icms_st_cst = xml_tributos.get('icms_st_cst')
            
            # Dados SPED
            sped_item = match.get('sped_item')
            if sped_item:
                auditoria.sped_item_id = sped_item['id']
                if not auditoria.numero_nf:
                    auditoria.numero_nf = sped_item['numero_nf']
                
                auditoria.sped_descricao = sped_item['descricao']
                auditoria.sped_quantidade = sped_item['quantidade']
                auditoria.sped_valor_item = sped_item['valor_item']
                auditoria.sped_unidade = sped_item['unidade']
                auditoria.sped_cfop = sped_item['cfop']
                auditoria.sped_cod_item = sped_item['cod_item']
                auditoria.sped_ncm = sped_item.get('ncm', '')  # NCM do SPED
                
                # Dados tributários SPED - ICMS
                cst_icms_raw = sped_item.get('cst_icms')
                origem_icms = sped_item.get('origem_icms')
                if not origem_icms and cst_icms_raw:
                    origem_icms = cst_icms_raw[0]
                    auditoria.sped_cst_icms = cst_icms_raw[1:] if len(cst_icms_raw) == 3 else cst_icms_raw
                else:
                    auditoria.sped_cst_icms = cst_icms_raw
                auditoria.sped_icms_origem = origem_icms
                auditoria.sped_icms_bc = sped_item.get('valor_bc_icms')
                auditoria.sped_icms_aliquota = sped_item.get('aliquota_icms')
                auditoria.sped_icms_valor = sped_item.get('valor_icms')
                
                # ICMS-ST
                auditoria.sped_icms_st_bc = sped_item.get('valor_bc_icms_st')
                auditoria.sped_icms_st_aliquota = sped_item.get('aliquota_st')
                auditoria.sped_icms_st_valor = sped_item.get('valor_icms_st')
                
                # IPI
                auditoria.sped_cst_ipi = sped_item.get('cst_ipi')
                auditoria.sped_ipi_bc = sped_item.get('valor_bc_ipi')
                auditoria.sped_ipi_aliquota = sped_item.get('aliquota_ipi')
                auditoria.sped_ipi_valor = sped_item.get('valor_ipi')
                
                # PIS
                auditoria.sped_cst_pis = sped_item.get('cst_pis')
                auditoria.sped_pis_bc = sped_item.get('valor_bc_pis')
                auditoria.sped_pis_aliquota = sped_item.get('aliquota_pis')
                auditoria.sped_pis_valor = sped_item.get('valor_pis')
                
                # COFINS
                auditoria.sped_cst_cofins = sped_item.get('cst_cofins')
                auditoria.sped_cofins_bc = sped_item.get('valor_bc_cofins')
                auditoria.sped_cofins_aliquota = sped_item.get('aliquota_cofins')
                auditoria.sped_cofins_valor = sped_item.get('valor_cofins')
            
            # Determinar status inicial baseado no tipo de match
            if match['match_type'] in ['unmatched_xml', 'unmatched_sped']:
                auditoria.status_auditoria = 'pendente'
            elif match['confidence_score'] >= 0.9:
                auditoria.status_auditoria = 'em_analise'
            else:
                auditoria.status_auditoria = 'pendente'

            # Tentar auto-aprovar tributos com base no histórico
            for trib in ['icms', 'icms_st', 'ipi', 'pis', 'cofins']:
                self._tentar_auto_aprovacao(auditoria, trib)

            return auditoria

        except Exception as e:
            print(f"Erro ao criar registro de auditoria: {str(e)}")
            return None

    def _get_xml_item_tributos(self, xml_item_id: int) -> Optional[Dict]:
        """
        Busca dados tributários do item XML na tabela tributo
        """
        try:
            from models.tributo import Tributo
            from models.nota_fiscal_item import NotaFiscalItem

            # Buscar o item XML
            xml_item = db.session.get(NotaFiscalItem, xml_item_id)
            if not xml_item:
                return None

            # Buscar tributos relacionados ao item XML
            tributo = db.session.query(Tributo).filter(
                Tributo.empresa_id == xml_item.empresa_id,
                Tributo.cliente_id == xml_item.cliente_id,
                Tributo.produto_id == xml_item.produto_id,
                Tributo.data_emissao == xml_item.data_emissao,
                Tributo.numero_nf == xml_item.numero_nf
            ).first()

            if not tributo:
                return None

            return {
                # ICMS
                'icms_valor': float(tributo.icms_valor) if tributo.icms_valor else None,
                'icms_vbc': float(tributo.icms_vbc) if tributo.icms_vbc else None,
                'icms_aliquota': float(tributo.icms_aliquota) if tributo.icms_aliquota else None,
                'icms_origem': tributo.icms_origem,
                'icms_cst': tributo.icms_cst,
                'icms_csosn': tributo.icms_csosn,
                # IPI
                'ipi_valor': float(tributo.ipi_valor) if tributo.ipi_valor else None,
                'ipi_vbc': float(tributo.ipi_vbc) if tributo.ipi_vbc else None,
                'ipi_aliquota': float(tributo.ipi_aliquota) if tributo.ipi_aliquota else None,
                'ipi_cst': tributo.ipi_cst,
                # PIS
                'pis_valor': float(tributo.pis_valor) if tributo.pis_valor else None,
                'pis_vbc': float(tributo.pis_vbc) if tributo.pis_vbc else None,
                'pis_aliquota': float(tributo.pis_aliquota) if tributo.pis_aliquota else None,
                'pis_cst': tributo.pis_cst,
                # COFINS
                'cofins_valor': float(tributo.cofins_valor) if tributo.cofins_valor else None,
                'cofins_vbc': float(tributo.cofins_vbc) if tributo.cofins_vbc else None,
                'cofins_aliquota': float(tributo.cofins_aliquota) if tributo.cofins_aliquota else None,
                'cofins_cst': tributo.cofins_cst,
                # ICMS-ST
                'icms_st_valor': float(tributo.icms_st_valor) if tributo.icms_st_valor else None,
                'icms_st_vbc': float(tributo.icms_st_vbc) if tributo.icms_st_vbc else None,
                'icms_st_aliquota': float(tributo.icms_st_aliquota) if tributo.icms_st_aliquota else None,
                'icms_st_cst': tributo.icms_cst  # ICMS-ST usa o mesmo CST do ICMS
            }
        except Exception as e:
            print(f"Erro ao buscar dados tributários XML: {str(e)}")
            return None

    def _tentar_auto_aprovacao(self, auditoria: AuditoriaComparativaImpostos, tributo: str) -> None:
        """Tenta auto-aprovar o tributo do item com base no histórico."""
        try:
            if not auditoria.xml_item_id or not auditoria.sped_item_id:
                return

            xml_item = db.session.get(NotaFiscalItem, auditoria.xml_item_id)
            sped_item = db.session.get(ItemNotaEntrada, auditoria.sped_item_id)

            if not xml_item or not sped_item:
                return

            xml_codigo = None
            if xml_item and xml_item.produto:
                xml_codigo = xml_item.produto.codigo

            sped_codigo = getattr(sped_item, 'cod_item', None)

            if not xml_codigo or not sped_codigo:
                return

            sugestoes = self.sugestoes_service._buscar_sugestoes_historico(
                xml_codigo, sped_codigo, auditoria.cliente_id, tributo
            )
            pode_auto, motivo = self.sugestoes_service._verificar_auto_aprovacao(
                auditoria, tributo, sugestoes
            )

            if pode_auto:
                score = self.sugestoes_service._calcular_score_confianca(sugestoes)
                auditoria.set_status_tributo(tributo, 'aprovado', self.usuario_id,
                                           f'Auto-aprovado: {motivo}')
                self.sugestoes_service._registrar_auto_aprovacao(
                    auditoria,
                    tributo,
                    self.usuario_id,
                    {
                        'sugestoes_historico': sugestoes,
                        'score_confianca': score
                    }
                )
        except Exception as e:
            print(f"Erro ao tentar auto-aprovar: {str(e)}")

    def get_auditoria_by_nota(self, chave_nf: str, tributo_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        Busca auditoria comparativa de uma nota específica
        
        Args:
            chave_nf: Chave da nota fiscal
            tributo_filter: Filtro por tributo específico (icms, icms_st, ipi, pis, cofins)
            
        Returns:
            Dict com dados da auditoria
        """
        try:
            query = db.session.query(AuditoriaComparativaImpostos).filter(
                AuditoriaComparativaImpostos.empresa_id == self.empresa_id,
                AuditoriaComparativaImpostos.chave_nf == chave_nf
            )
            
            auditorias = query.all()
            
            if not auditorias:
                return {
                    'success': False,
                    'message': 'Nenhuma auditoria encontrada para esta nota'
                }
            
            # Converter para dict e aplicar filtro de tributo se necessário
            auditorias_dict = []
            for auditoria in auditorias:
                auditoria_data = auditoria.to_dict()
                
                # Verificar se NCM possui IPI NT na tabela tipi
                auditoria_data['is_ncm_nt'] = self._is_ncm_nt(auditoria.xml_ncm)

                # Aplicar filtro de tributo se especificado
                if tributo_filter:
                    auditoria_data = self._filter_by_tributo(auditoria_data, tributo_filter)
                
                auditorias_dict.append(auditoria_data)
            
            # Calcular estatísticas
            stats = self._calculate_auditoria_stats(auditorias)
            
            return {
                'success': True,
                'chave_nf': chave_nf,
                'auditorias': auditorias_dict,
                'statistics': stats,
                'total_registros': len(auditorias)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao buscar auditoria: {str(e)}'
            }

    def _filter_by_tributo(self, auditoria_data: Dict, tributo: str) -> Dict:
        """Filtra dados da auditoria por tributo específico.

        A aba "PIS/COFINS" no frontend solicita o tributo ``pis`` à API e
        espera receber os dados de **ambos** (PIS e COFINS). Por isso, quando o
        filtro for ``pis`` devemos retornar as duas estruturas. Para os demais
        tributos o comportamento permanece o mesmo (retorna apenas o tributo
        solicitado).
        """
        
        tributos = auditoria_data.get('tributos', {})
        if tributo not in tributos:
            return auditoria_data

        # Caso especial: tab PIS/COFINS
        if tributo in ('pis', 'pis_cofins'):
            pis_data = tributos.get('pis')
            cofins_data = tributos.get('cofins')
            auditoria_data['tributos'] = {}
            if pis_data is not None:
                auditoria_data['tributos']['pis'] = pis_data
            if cofins_data is not None:
                auditoria_data['tributos']['cofins'] = cofins_data
        else:
            auditoria_data['tributos'] = {tributo: tributos[tributo]}
        return auditoria_data

    def get_auditoria_by_periodo(self, mes: int, ano: int, tributo_filter: Optional[str] = None, 
                               load_all: bool = False, page: int = 1, per_page: int = 100) -> Dict[str, Any]:
        """
        Busca auditoria comparativa de um período (mês/ano)

        Args:
            mes: Mês de referência
            ano: Ano de referência
            tributo_filter: Filtro por tributo específico (icms, icms_st, ipi, pis, cofins)

        Returns:
            Dict com dados da auditoria
        """
        try:
            from models.importacao_xml import ImportacaoXML
            from sqlalchemy import func

            print(f"DEBUG - Buscando auditoria para empresa {self.empresa_id}, período {mes:02d}/{ano}, tributo: {tributo_filter}")

            # Buscar todas as chaves de notas do período
            notas_periodo = db.session.query(ImportacaoXML.chave_nf).filter(
                ImportacaoXML.empresa_id == self.empresa_id,
                ImportacaoXML.tipo_nota == '0',  # Entrada
                func.extract('month', ImportacaoXML.data_entrada) == mes,
                func.extract('year', ImportacaoXML.data_entrada) == ano
            ).distinct().all()

            print(f"DEBUG - Encontradas {len(notas_periodo)} notas no período")

            if not notas_periodo:
                return {
                    'success': False,
                    'message': 'Nenhuma nota encontrada para o período especificado'
                }

            chaves_nf = [nota.chave_nf for nota in notas_periodo]

            # Buscar auditorias para todas as notas do período com dados complementares
            from models.cliente import Cliente
            from models.produto import Produto
            from models.produto_entrada import ProdutoEntrada
            from models.empresa import Empresa
            from utils import check_pis_cofins_rules

            query = db.session.query(
                AuditoriaComparativaImpostos,
                Cliente.razao_social.label('cliente_nome'),
                Cliente.simples_nacional.label('cliente_simples_nacional'),
                Cliente.uf.label('cliente_uf'),
                Produto.descricao.label('xml_produto_descricao'),
                Produto.codigo.label('xml_codigo_produto'),
                ProdutoEntrada.descricao.label('sped_produto_descricao'),
                ProdutoEntrada.tipo_item.label('sped_tipo_item'),
                ImportacaoXML.data_emissao,
                ImportacaoXML.fin_nfe,
                NotaEntrada.ind_oper,
                NotaEntrada.ind_emit,
                Tributo.icms_st_p_mva.label('mva_nota')
            ).outerjoin(
                # Join direto com cliente_id da auditoria (se disponível) ou via NotaFiscalItem
                Cliente, AuditoriaComparativaImpostos.cliente_id == Cliente.id
            ).outerjoin(
                NotaFiscalItem, AuditoriaComparativaImpostos.xml_item_id == NotaFiscalItem.id
            ).outerjoin(
                Produto, NotaFiscalItem.produto_id == Produto.id
            ).outerjoin(
                Tributo, Tributo.nota_fiscal_item_id == AuditoriaComparativaImpostos.xml_item_id
            ).outerjoin(
                ItemNotaEntrada, AuditoriaComparativaImpostos.sped_item_id == ItemNotaEntrada.id
            ).outerjoin(
                NotaEntrada, ItemNotaEntrada.nota_entrada_id == NotaEntrada.id
            ).outerjoin(
                ProdutoEntrada, ItemNotaEntrada.produto_entrada_id == ProdutoEntrada.id
            ).outerjoin(
                ImportacaoXML, AuditoriaComparativaImpostos.chave_nf == ImportacaoXML.chave_nf
            ).filter(
                AuditoriaComparativaImpostos.empresa_id == self.empresa_id,
                AuditoriaComparativaImpostos.chave_nf.in_(chaves_nf)
            )

            results = query.all()

            empresa = db.session.get(Empresa, self.empresa_id)
            empresa_dict = empresa.to_dict() if empresa else {}

            print(f"DEBUG - Encontrados {len(results)} registros de auditoria")

            if not results:
                return {
                    'success': False,
                    'message': 'Nenhuma auditoria encontrada para o período especificado'
                }

            # Converter para dict e aplicar filtro de tributo se necessário
            auditorias_dict = []
            auditorias_only = []

            for result in results:
                auditoria = result[0]  # AuditoriaComparativaImpostos
                cliente_nome = result[1]  # cliente_nome
                cliente_simples_nacional = result[2]  # cliente_simples_nacional
                cliente_uf = result[3]  # cliente_uf
                xml_produto_descricao = result[4]  # xml_produto_descricao
                xml_codigo_produto = result[5]  # xml_codigo_produto
                sped_produto_descricao = result[6]  # sped_produto_descricao
                sped_tipo_item = result[7]  # sped_tipo_item
                data_emissao = result[8]  # data_emissao
                fin_nfe = result[9]  # fin_nfe
                ind_oper = result[10]  # ind_oper
                ind_emit = result[11]  # ind_emit
                mva_nota = result[12]  # mva_nota

                auditorias_only.append(auditoria)
                auditoria_data = auditoria.to_dict()
                auditoria_data['is_ncm_nt'] = self._is_ncm_nt(auditoria.xml_ncm)

                # Mapear tipo de item para descrição
                tipo_item_descricao = {
                    '00': 'Mercadoria para Revenda',
                    '01': 'Matéria Prima',
                    '02': 'Embalagem',
                    '03': 'Produto em Processo',
                    '04': 'Produto Acabado',
                    '05': 'Subproduto',
                    '06': 'Produto Intermediário',
                    '07': 'Material de uso e consumo',
                    '08': 'Ativo Imobilizado',
                    '09': 'Serviços',
                    '10': 'Outros Insumos',
                    '99': 'Outras'
                }.get(sped_tipo_item, sped_tipo_item or 'N/A')

                # Adicionar dados complementares
                auditoria_data.update({
                    'cliente_nome': cliente_nome,
                    'parceiro_nome': cliente_nome,  # Usar nome do cliente ao invés do emitente
                    'parceiro_simples_nacional': cliente_simples_nacional,
                    'regime_parceiro': 'Simples Nacional' if cliente_simples_nacional else 'Regime Normal',
                    'cliente_uf': cliente_uf,
                    'data_emissao': data_emissao.isoformat() if data_emissao else None,
                    'xml_produto_descricao': xml_produto_descricao,
                    'xml_codigo_produto': xml_codigo_produto,
                    'sped_produto_descricao': sped_produto_descricao,
                    'tipo_produto': sped_tipo_item,
                    'descricao_tipo_produto': tipo_item_descricao,
                    'codigo_produto': xml_codigo_produto or auditoria.sped_cod_item,
                    'produto_nota': xml_produto_descricao,
                    'sped_origem': auditoria.sped_icms_origem,
                    'fin_nfe': fin_nfe,
                    'ind_oper': ind_oper,
                    'ind_emit': ind_emit,
                    'mva_nota': float(mva_nota) if mva_nota is not None else None
                })


                # Aplicar filtro de tributo se especificado (mas sempre incluir o registro)
                if tributo_filter:
                    auditoria_data = self._filter_by_tributo(auditoria_data, tributo_filter)
                
                # Verificar regras de PIS/COFINS (esqueleto)
                auditoria_data['regras_pis_cofins'] = check_pis_cofins_rules(
                    empresa_dict, auditoria_data
                )

                # Sempre incluir o registro na lista
                auditorias_dict.append(auditoria_data)

            # Calcular estatísticas do período
            stats = self._calculate_auditoria_stats(auditorias_only)

            print(f"DEBUG - Retornando {len(auditorias_dict)} registros para o frontend")

            return {
                'success': True,
                'auditorias': auditorias_dict,
                'total_registros': len(auditorias_dict),
                'periodo': f'{mes:02d}/{ano}',
                'total_notas_periodo': len(chaves_nf),
                'statistics': stats
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao buscar auditoria do período: {str(e)}'
            }
    def _is_ncm_nt(self, ncm: str) -> bool:
        """Verifica se um NCM possui aliquota NT na tabela TIPI"""
        if not ncm:
            return False
        if not hasattr(self, '_ncm_nt_cache'):
            self._ncm_nt_cache = {}
        if ncm in self._ncm_nt_cache:
            return self._ncm_nt_cache[ncm]
        try:
            result = db.session.execute(
                text("SELECT aliquota FROM tipi WHERE ncm = :ncm LIMIT 1"),
                {'ncm': ncm}
            ).fetchone()
            is_nt = bool(result and result[0] and str(result[0]).strip().upper() == 'NT')
        except Exception:
            is_nt = False
        self._ncm_nt_cache[ncm] = is_nt
        return is_nt

    def _calculate_auditoria_stats(self, auditorias: List[AuditoriaComparativaImpostos]) -> Dict:
        """
        Calcula estatísticas da auditoria
        """
        total = len(auditorias)
        if total == 0:
            return {
                'total_matches': 0,
                'match_rate': 0,
                'direct_matches': 0,
                'embedding_matches': 0,
                'unmatched_xml_items': 0,
                'unmatched_sped_items': 0,
                'average_confidence': 0,
                'high_confidence_matches': 0,
                'medium_confidence_matches': 0,
                'low_confidence_matches': 0
            }

        # Contar por tipo de match
        direct_matches = 0
        embedding_matches = 0
        unmatched_xml = 0
        unmatched_sped = 0
        confidence_scores = []
        status_counts = {}

        for auditoria in auditorias:
            # Tipos de match
            match_type = auditoria.match_type or 'unknown'
            if match_type == 'direct':
                direct_matches += 1
            elif match_type == 'embedding':
                embedding_matches += 1
            elif match_type == 'unmatched_xml':
                unmatched_xml += 1
            elif match_type == 'unmatched_sped':
                unmatched_sped += 1

            # Scores de confiança (apenas para matches válidos)
            if auditoria.confidence_score and match_type in ['direct', 'embedding']:
                confidence_scores.append(float(auditoria.confidence_score))

            # Status
            status = auditoria.status_auditoria or 'unknown'
            status_counts[status] = status_counts.get(status, 0) + 1

        # Calcular médias e totais
        total_matches = direct_matches + embedding_matches
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        match_rate = (total_matches / total) * 100 if total > 0 else 0

        return {
            'total_matches': total_matches,
            'match_rate': match_rate,
            'direct_matches': direct_matches,
            'embedding_matches': embedding_matches,
            'unmatched_xml_items': unmatched_xml,
            'unmatched_sped_items': unmatched_sped,
            'average_confidence': avg_confidence,
            'high_confidence_matches': len([s for s in confidence_scores if s >= 0.8]),
            'medium_confidence_matches': len([s for s in confidence_scores if 0.6 <= s < 0.8]),
            'low_confidence_matches': len([s for s in confidence_scores if s < 0.6]),
            'total_registros': total,
            'status_counts': status_counts
        }

    def editar_dados_sped(self, auditoria_id: int, dados_sped: Dict[str, Any]) -> Dict[str, Any]:
        """
        Edita dados SPED de um registro de auditoria

        Args:
            auditoria_id: ID do registro de auditoria
            dados_sped: Novos dados SPED a serem salvos

        Returns:
            Dict com resultado da operação
        """
        try:
            auditoria = db.session.get(AuditoriaComparativaImpostos, auditoria_id)

            if not auditoria:
                return {
                    'success': False,
                    'message': 'Registro de auditoria não encontrado'
                }

            # Verificar permissão
            if auditoria.empresa_id != self.empresa_id:
                return {
                    'success': False,
                    'message': 'Acesso negado'
                }

            # Teste simples - atualizar apenas um campo para verificar se funciona
            if 'cfop' in dados_sped:
                auditoria.sped_cfop = dados_sped['cfop']
                print(f"TESTE - CFOP atualizado para: {dados_sped['cfop']}")

            if 'ncm' in dados_sped:
                auditoria.sped_ncm = dados_sped['ncm']
                print(f"TESTE - NCM atualizado para: {dados_sped['ncm']}")

            # Marcar como alterado
            auditoria.sped_alterado = True
            auditoria.data_atualizacao = datetime.now()
            auditoria.usuario_atualizacao = self.usuario_id

            # Salvar estado anterior para histórico
            estado_anterior = self._get_sped_data_snapshot(auditoria)

            # Atualizar dados SPED
            self._update_sped_fields(auditoria, dados_sped)

            # Marcar como alterado e registrar histórico
            auditoria.sped_alterado = True
            auditoria.usuario_atualizacao = self.usuario_id

            # Adicionar ao histórico de alterações
            historico_entry = {
                'timestamp': datetime.now().isoformat(),
                'usuario_id': self.usuario_id,
                'estado_anterior': estado_anterior,
                'alteracoes': dados_sped,
                'motivo': dados_sped.get('motivo_alteracao', 'Correção manual')
            }

            if auditoria.historico_alteracoes:
                historico = auditoria.historico_alteracoes.copy()
                historico.append(historico_entry)
            else:
                historico = [historico_entry]

            auditoria.historico_alteracoes = historico

            print(f"DEBUG SERVICE - Antes do commit. Auditoria alterada: {auditoria.sped_alterado}")
            print(f"DEBUG SERVICE - Campos SPED atualizados: cfop={auditoria.sped_cfop}, ncm={auditoria.sped_ncm}")

            db.session.commit()

            print(f"DEBUG SERVICE - Commit realizado com sucesso")

            return {
                'success': True,
                'message': 'Dados SPED atualizados com sucesso',
                'auditoria': auditoria.to_dict()
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'Erro ao editar dados SPED: {str(e)}'
            }

    def salvar_historico_matching_com_dados_atualizados(self, auditoria: AuditoriaComparativaImpostos,
                                                       tributo: str, acao: str, usuario_id: int,
                                                       feedback: str = '',
                                                       aprovacao_massa: bool = False) -> Dict[str, Any]:
        """
        Salva histórico de matching com dados SPED atualizados para aprendizado futuro

        Args:
            auditoria: Registro de auditoria comparativa
            tributo: Tributo específico (icms, icms_st, ipi, pis, cofins)
            acao: Ação realizada (aprovado, rejeitado, corrigido)
            usuario_id: ID do usuário
            feedback: Feedback opcional do usuário

        Returns:
            Dict com resultado da operação
        """
        try:
            from models.auditoria_comparativa_impostos import HistoricoMatchingAprendizado

            # Buscar dados dos itens XML e SPED
            xml_item = db.session.get(NotaFiscalItem, auditoria.xml_item_id) if auditoria.xml_item_id else None
            sped_item = db.session.get(ItemNotaEntrada, auditoria.sped_item_id) if auditoria.sped_item_id else None

            if not xml_item or not sped_item:
                return {
                    'success': False,
                    'message': 'Itens XML ou SPED não encontrados'
                }

            # Criar registro de histórico
            historico = HistoricoMatchingAprendizado()
            historico.empresa_id = auditoria.empresa_id
            historico.escritorio_id = auditoria.escritorio_id
            historico.xml_item_id = auditoria.xml_item_id
            historico.sped_item_id = auditoria.sped_item_id
            historico.cliente_id = auditoria.cliente_id

            # Códigos dos produtos
            historico.xml_codigo_produto = xml_item.produto.codigo if xml_item and xml_item.produto else None
            historico.sped_codigo_produto = sped_item.cod_item if sped_item else None

            # Dados do matching
            historico.match_type_original = auditoria.match_type
            historico.confidence_score_original = auditoria.confidence_score
            if aprovacao_massa:
                historico.acao_usuario = f'{acao}_{tributo}_massa'
            else:
                historico.acao_usuario = f'{acao}_{tributo}'
            historico.usuario_id = usuario_id
            historico.feedback_usuario = f'[{tributo.upper()}] {feedback}' if feedback else f'[{tributo.upper()}] {acao.title()}'

            if tributo == 'pis_cofins':
                dados_sped = {
                    'pis': self._get_sped_data_snapshot(auditoria, 'pis'),
                    'cofins': self._get_sped_data_snapshot(auditoria, 'cofins')
                }
                dados_xml = {
                    'pis': self._get_xml_data_snapshot(auditoria, 'pis'),
                    'cofins': self._get_xml_data_snapshot(auditoria, 'cofins')
                }
            else:
                dados_sped = {
                    tributo: self._get_sped_data_snapshot(auditoria, tributo)
                }
                dados_xml = {
                    tributo: self._get_xml_data_snapshot(auditoria, tributo)
                }

            # Características do match com dados SPED atualizados
            caracteristicas = {
                'tributo': tributo,
                'timestamp': datetime.now().isoformat(),
                'confidence_score': float(auditoria.confidence_score) if auditoria.confidence_score else None,
                'match_type': auditoria.match_type,
                'xml_codigo_produto': historico.xml_codigo_produto,
                'sped_codigo_produto': historico.sped_codigo_produto,
                'cliente_id': auditoria.cliente_id,
                'dados_sped_atualizados': dados_sped,
                'dados_xml': dados_xml,
                'sped_foi_alterado': auditoria.sped_alterado,
                'historico_alteracoes': auditoria.historico_alteracoes
            }

            if aprovacao_massa:
                caracteristicas['aprovacao_massa'] = True

            historico.caracteristicas_match = caracteristicas

            db.session.add(historico)
            db.session.commit()

            return {
                'success': True,
                'message': 'Histórico de matching salvo com sucesso',
                'historico_id': historico.id
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'Erro ao salvar histórico de matching: {str(e)}'
            }

    def _get_sped_data_snapshot(self, auditoria: AuditoriaComparativaImpostos, tributo: str | None = None) -> Dict:
        """Cria snapshot dos dados SPED atuais para histórico.

        Se ``tributo`` for informado, retorna somente os dados desse tributo.
        """
        dados = {
            'icms': {
                'cst': auditoria.sped_cst_icms,
                'origem': auditoria.sped_icms_origem,
                'cfop': auditoria.sped_cfop,
                'ncm': auditoria.sped_ncm,
                'csosn': auditoria.sped_icms_csosn,
                'bc': float(auditoria.sped_icms_bc) if auditoria.sped_icms_bc else None,
                'aliquota': float(auditoria.sped_icms_aliquota) if auditoria.sped_icms_aliquota else None,
                'valor': float(auditoria.sped_icms_valor) if auditoria.sped_icms_valor else None,
                'reducao': float(auditoria.sped_icms_reducao) if auditoria.sped_icms_reducao else None
            },
            'icms_st': {
                'bc': float(auditoria.sped_icms_st_bc) if auditoria.sped_icms_st_bc else None,
                'aliquota': float(auditoria.sped_icms_st_aliquota) if auditoria.sped_icms_st_aliquota else None,
                'valor': float(auditoria.sped_icms_st_valor) if auditoria.sped_icms_st_valor else None,
                'mva': float(auditoria.sped_icms_st_mva) if auditoria.sped_icms_st_mva else None,
                'reducao': float(auditoria.sped_icms_st_reducao) if auditoria.sped_icms_st_reducao else None,
                'cfop': auditoria.sped_cfop,
                'ncm': auditoria.sped_ncm,
                'origem': auditoria.sped_icms_origem,
            },
            'ipi': {
                'cst': auditoria.sped_cst_ipi,
                'bc': float(auditoria.sped_ipi_bc) if auditoria.sped_ipi_bc else None,
                'aliquota': float(auditoria.sped_ipi_aliquota) if auditoria.sped_ipi_aliquota else None,
                'valor': float(auditoria.sped_ipi_valor) if auditoria.sped_ipi_valor else None,
                'reducao': float(auditoria.sped_ipi_reducao) if auditoria.sped_ipi_reducao else None
            },
            'pis': {
                'cst': auditoria.sped_cst_pis,
                'bc': float(auditoria.sped_pis_bc) if auditoria.sped_pis_bc else None,
                'aliquota': float(auditoria.sped_pis_aliquota) if auditoria.sped_pis_aliquota else None,
                'valor': float(auditoria.sped_pis_valor) if auditoria.sped_pis_valor else None
            },
            'cofins': {
                'cst': auditoria.sped_cst_cofins,
                'bc': float(auditoria.sped_cofins_bc) if auditoria.sped_cofins_bc else None,
                'aliquota': float(auditoria.sped_cofins_aliquota) if auditoria.sped_cofins_aliquota else None,
                'valor': float(auditoria.sped_cofins_valor) if auditoria.sped_cofins_valor else None
            }
        }

        return dados[tributo] if tributo else dados

    def _get_xml_data_snapshot(self, auditoria: AuditoriaComparativaImpostos, tributo: str | None = None) -> Dict:
        """Cria snapshot dos dados XML atuais para histórico.

        Se ``tributo`` for informado, retorna apenas os dados desse tributo.
        """
        dados = {
            'icms': {
                'origem': auditoria.xml_icms_origem,
                'cfop': auditoria.xml_cfop,
                'ncm': auditoria.xml_ncm,
                'csosn': auditoria.xml_icms_csosn,
                'aliquota': float(auditoria.xml_icms_aliquota) if auditoria.xml_icms_aliquota else None,
            },
            'icms_st': {
                'cfop': auditoria.xml_cfop,
                'ncm': auditoria.xml_ncm,
                'aliquota': float(auditoria.xml_icms_st_aliquota) if auditoria.xml_icms_st_aliquota else None,
                'origem': auditoria.xml_icms_origem,
            },
            'ipi': {
                'cfop': auditoria.xml_cfop,
                'ncm': auditoria.xml_ncm,
                'aliquota': float(auditoria.xml_ipi_aliquota) if auditoria.xml_ipi_aliquota else None,
            },
            'pis': {
                'cfop': auditoria.xml_cfop,
                'ncm': auditoria.xml_ncm,
                'aliquota': float(auditoria.xml_pis_aliquota) if auditoria.xml_pis_aliquota else None,
            },
            'cofins': {
                'cfop': auditoria.xml_cfop,
                'ncm': auditoria.xml_ncm,
                'aliquota': float(auditoria.xml_cofins_aliquota) if auditoria.xml_cofins_aliquota else None,
            }
        }

        return dados[tributo] if tributo else dados

    def _update_sped_fields(self, auditoria: AuditoriaComparativaImpostos, dados: Dict[str, Any]):
        """
        Atualiza campos SPED no registro de auditoria e nas tabelas originais
        """
        print(f"DEBUG _update_sped_fields - Dados recebidos: {dados}")

        # Campos básicos do item
        if 'cfop' in dados:
            print(f"DEBUG - Atualizando CFOP: {dados['cfop']}")
            auditoria.sped_cfop = dados['cfop']
        if 'ncm' in dados:
            print(f"DEBUG - Atualizando NCM: {dados['ncm']}")
            auditoria.sped_ncm = dados['ncm']
        if 'origem_icms' in dados:
            print(f"DEBUG - Atualizando origem ICMS: {dados['origem_icms']}")
            auditoria.sped_icms_origem = dados['origem_icms']
        if 'tipo_item' in dados:
            print(f"DEBUG - Atualizando tipo item: {dados['tipo_item']}")
            auditoria.sped_tipo_item = dados['tipo_item']

        # ICMS
        if 'icms' in dados:
            print(f"DEBUG - Processando dados ICMS: {dados['icms']}")
            icms = dados['icms']

            # Valores antes da atualização
            print(f"DEBUG - Valores ANTES - CST: {auditoria.sped_cst_icms}, BC: {auditoria.sped_icms_bc}, Aliq: {auditoria.sped_icms_aliquota}, Valor: {auditoria.sped_icms_valor}")

            if 'cst' in icms:
                print(f"DEBUG - Atualizando CST ICMS: {icms['cst']}")
                cst_raw = icms['cst']
                if 'origem_icms' not in dados and cst_raw:
                    auditoria.sped_icms_origem = cst_raw[0]
                    auditoria.sped_cst_icms = cst_raw[1:] if len(cst_raw) == 3 else cst_raw
                else:
                    auditoria.sped_cst_icms = cst_raw
            if 'bc' in icms:
                print(f"DEBUG - Atualizando BC ICMS: {icms['bc']}")
                auditoria.sped_icms_bc = icms['bc']
            if 'aliquota' in icms:
                print(f"DEBUG - Atualizando alíquota ICMS: {icms['aliquota']}")
                auditoria.sped_icms_aliquota = icms['aliquota']
            if 'valor' in icms:
                print(f"DEBUG - Atualizando valor ICMS: {icms['valor']}")
                auditoria.sped_icms_valor = icms['valor']
            if 'reducao' in icms:
                print(f"DEBUG - Atualizando redução ICMS: {icms['reducao']}")
                auditoria.sped_icms_reducao = icms['reducao']
            if 'credito_icms' in icms:
                print(f"DEBUG - Atualizando crédito ICMS: {icms['credito_icms']}")
                auditoria.sped_icms_credito = icms['credito_icms']

            # Valores depois da atualização
            print(f"DEBUG - Valores DEPOIS - CST: {auditoria.sped_cst_icms}, BC: {auditoria.sped_icms_bc}, Aliq: {auditoria.sped_icms_aliquota}, Valor: {auditoria.sped_icms_valor}")
            if 'reducao' in icms:
                print(f"DEBUG - Atualizando redução ICMS: {icms['reducao']}")
                auditoria.sped_icms_reducao = icms['reducao']
            if 'csosn' in icms:
                print(f"DEBUG - Atualizando CSOSN: {icms['csosn']}")
                auditoria.sped_icms_csosn = icms['csosn']
            if 'credito_icms' in icms:
                print(f"DEBUG - Atualizando crédito ICMS: {icms['credito_icms']}")
                auditoria.sped_icms_credito = icms['credito_icms']

        # ICMS-ST
        if 'icms_st' in dados:
            icms_st = dados['icms_st']
            if 'bc' in icms_st:
                auditoria.sped_icms_st_bc = icms_st['bc']
            if 'aliquota' in icms_st:
                auditoria.sped_icms_st_aliquota = icms_st['aliquota']
            if 'valor' in icms_st:
                auditoria.sped_icms_st_valor = icms_st['valor']
            if 'mva' in icms_st:
                auditoria.sped_icms_st_mva = icms_st['mva']
            if 'reducao' in icms_st:
                auditoria.sped_icms_st_reducao = icms_st['reducao']

        # IPI
        if 'ipi' in dados:
            ipi = dados['ipi']
            if 'cst' in ipi:
                auditoria.sped_cst_ipi = ipi['cst']
            if 'bc' in ipi:
                auditoria.sped_ipi_bc = ipi['bc']
            if 'aliquota' in ipi:
                auditoria.sped_ipi_aliquota = ipi['aliquota']
            if 'valor' in ipi:
                auditoria.sped_ipi_valor = ipi['valor']
            if 'reducao' in ipi:
                auditoria.sped_ipi_reducao = ipi['reducao']

        # PIS
        if 'pis' in dados:
            pis = dados['pis']
            if 'cst' in pis:
                auditoria.sped_cst_pis = pis['cst']
            if 'bc' in pis:
                auditoria.sped_pis_bc = pis['bc']
            if 'aliquota' in pis:
                auditoria.sped_pis_aliquota = pis['aliquota']
            if 'valor' in pis:
                auditoria.sped_pis_valor = pis['valor']

        # COFINS
        if 'cofins' in dados:
            cofins = dados['cofins']
            if 'cst' in cofins:
                auditoria.sped_cst_cofins = cofins['cst']
            if 'bc' in cofins:
                auditoria.sped_cofins_bc = cofins['bc']
            if 'aliquota' in cofins:
                auditoria.sped_cofins_aliquota = cofins['aliquota']
            if 'valor' in cofins:
                auditoria.sped_cofins_valor = cofins['valor']

        # Atualizar tabelas originais SPED
        self._update_original_sped_tables(auditoria, dados)

    def _update_original_sped_tables(self, auditoria: AuditoriaComparativaImpostos, dados: Dict[str, Any]):
        """
        Atualiza as tabelas originais do SPED (item_nota_entrada e produto_entrada)
        """
        try:
            # Atualizar produto_entrada se temos sped_item_id
            if auditoria.sped_item_id:
                item_nota_entrada = db.session.get(ItemNotaEntrada, auditoria.sped_item_id)
                if item_nota_entrada:
                    # Atualizar campos do item_nota_entrada
                    if 'cfop' in dados:
                        item_nota_entrada.cfop = dados['cfop']
                    if 'origem_icms' in dados:
                        item_nota_entrada.origem_icms = dados['origem_icms']

                    # Atualizar campos tributários específicos
                    if 'icms' in dados:
                        icms = dados['icms']
                        if 'cst' in icms:
                            cst_raw = icms['cst']
                            if 'origem_icms' not in dados and cst_raw:
                                item_nota_entrada.origem_icms = cst_raw[0]
                                item_nota_entrada.cst_icms = cst_raw[1:] if len(cst_raw) == 3 else cst_raw
                            else:
                                item_nota_entrada.cst_icms = cst_raw
                        if 'bc' in icms:
                            item_nota_entrada.valor_bc_icms = icms['bc']
                        if 'aliquota' in icms:
                            item_nota_entrada.aliquota_icms = icms['aliquota']
                        if 'valor' in icms:
                            item_nota_entrada.valor_icms = icms['valor']
                        if 'reducao' in icms:
                            item_nota_entrada.p_red_bc_icms = icms['reducao']

                    if 'icms_st' in dados:
                        icms_st = dados['icms_st']
                        if 'bc' in icms_st:
                            item_nota_entrada.valor_bc_icms_st = icms_st['bc']
                        if 'aliquota' in icms_st:
                            item_nota_entrada.aliquota_icms_st = icms_st['aliquota']
                        if 'valor' in icms_st:
                            item_nota_entrada.valor_icms_st = icms_st['valor']
                        if 'mva' in icms_st:
                            item_nota_entrada.p_mva_st = icms_st['mva']

                    if 'ipi' in dados:
                        ipi = dados['ipi']
                        if 'cst' in ipi:
                            item_nota_entrada.cst_ipi = ipi['cst']
                        if 'bc' in ipi:
                            item_nota_entrada.valor_bc_ipi = ipi['bc']
                        if 'aliquota' in ipi:
                            item_nota_entrada.aliquota_ipi = ipi['aliquota']
                        if 'valor' in ipi:
                            item_nota_entrada.valor_ipi = ipi['valor']

                    if 'pis' in dados:
                        pis = dados['pis']
                        if 'cst' in pis:
                            item_nota_entrada.cst_pis = pis['cst']
                        if 'bc' in pis:
                            item_nota_entrada.valor_bc_pis = pis['bc']
                        if 'aliquota' in pis:
                            item_nota_entrada.aliquota_pis = pis['aliquota']
                        if 'valor' in pis:
                            item_nota_entrada.valor_pis = pis['valor']

                    if 'cofins' in dados:
                        cofins = dados['cofins']
                        if 'cst' in cofins:
                            item_nota_entrada.cst_cofins = cofins['cst']
                        if 'bc' in cofins:
                            item_nota_entrada.valor_bc_cofins = cofins['bc']
                        if 'aliquota' in cofins:
                            item_nota_entrada.aliquota_cofins = cofins['aliquota']
                        if 'valor' in cofins:
                            item_nota_entrada.valor_cofins = cofins['valor']

                    # Atualizar produto_entrada se necessário
                    if item_nota_entrada.produto_entrada_id:
                        produto_entrada = db.session.get(ProdutoEntrada, item_nota_entrada.produto_entrada_id)
                        if produto_entrada:
                            if 'ncm' in dados:
                                produto_entrada.ncm = dados['ncm']
                            if 'tipo_item' in dados:
                                produto_entrada.tipo_item = dados['tipo_item']

        except Exception as e:
            print(f"Erro ao atualizar tabelas originais SPED: {str(e)}")
            # Não interromper o processo principal, apenas logar o erro

    def count_auditoria_by_periodo(self, mes: int, ano: int, tributo_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        Conta o número de registros de auditoria comparativa para um período
        
        Args:
            mes: Mês de referência
            ano: Ano de referência
            tributo_filter: Filtro por tributo específico
            
        Returns:
            Dict com contagem de registros
        """
        try:
            from models.importacao_xml import ImportacaoXML
            from sqlalchemy import func

            # Buscar todas as chaves de notas do período
            notas_periodo = db.session.query(ImportacaoXML.chave_nf).filter(
                ImportacaoXML.empresa_id == self.empresa_id,
                ImportacaoXML.tipo_nota == '0',  # Entrada
                func.extract('month', ImportacaoXML.data_entrada) == mes,
                func.extract('year', ImportacaoXML.data_entrada) == ano
            ).distinct().all()

            if not notas_periodo:
                return {
                    'success': True,
                    'count': 0
                }

            chaves_nf = [nota.chave_nf for nota in notas_periodo]

            # Contar auditorias para as notas do período
            query = db.session.query(AuditoriaComparativaImpostos).filter(
                AuditoriaComparativaImpostos.empresa_id == self.empresa_id,
                AuditoriaComparativaImpostos.chave_nf.in_(chaves_nf)
            )

            # Aplicar filtro de tributo se especificado
            if tributo_filter:
                if tributo_filter == 'icms':
                    query = query.filter(AuditoriaComparativaImpostos.xml_icms_valor.isnot(None))
                elif tributo_filter == 'icms_st':
                    query = query.filter(AuditoriaComparativaImpostos.xml_icms_st_valor.isnot(None))
                elif tributo_filter == 'ipi':
                    query = query.filter(AuditoriaComparativaImpostos.xml_ipi_valor.isnot(None))
                elif tributo_filter == 'pis':
                    query = query.filter(AuditoriaComparativaImpostos.xml_pis_valor.isnot(None))
                elif tributo_filter == 'cofins':
                    query = query.filter(AuditoriaComparativaImpostos.xml_cofins_valor.isnot(None))

            count = query.count()

            return {
                'success': True,
                'count': count
            }

        except Exception as e:
            logger.error(f"Error counting auditoria by periodo: {str(e)}")
            return {
                'success': False,
                'message': f'Erro ao contar auditoria: {str(e)}',
                'count': 0
            }

    def get_auditoria_by_periodo_batch(self, mes: int, ano: int, tributo_filter: Optional[str] = None, 
                                     offset: int = 0, batch_size: int = 1000) -> Dict[str, Any]:
        """
        Busca auditoria comparativa de um período em lotes
        
        Args:
            mes: Mês de referência
            ano: Ano de referência
            tributo_filter: Filtro por tributo específico
            offset: Offset para paginação
            batch_size: Tamanho do lote
            
        Returns:
            Dict com dados do lote
        """
        try:
            from models.importacao_xml import ImportacaoXML
            from models.cliente import Cliente
            from models.produto import Produto
            from models.produto_entrada import ProdutoEntrada
            from sqlalchemy import func

            # Buscar todas as chaves de notas do período
            notas_periodo = db.session.query(ImportacaoXML.chave_nf).filter(
                ImportacaoXML.empresa_id == self.empresa_id,
                ImportacaoXML.tipo_nota == '0',  # Entrada
                func.extract('month', ImportacaoXML.data_entrada) == mes,
                func.extract('year', ImportacaoXML.data_entrada) == ano
            ).distinct().all()

            if not notas_periodo:
                return {
                    'success': True,
                    'auditorias': [],
                    'has_more': False
                }

            chaves_nf = [nota.chave_nf for nota in notas_periodo]

            # Buscar auditorias em lote com dados complementares
            query = db.session.query(
                AuditoriaComparativaImpostos,
                Cliente.razao_social.label('cliente_nome'),
                Cliente.simples_nacional.label('cliente_simples_nacional'),
                Cliente.uf.label('cliente_uf'),
                Produto.descricao.label('xml_produto_descricao'),
                Produto.codigo.label('xml_codigo_produto'),
                ProdutoEntrada.descricao.label('sped_produto_descricao'),
                ProdutoEntrada.tipo_item.label('sped_tipo_item'),
                ImportacaoXML.data_emissao,
                ImportacaoXML.fin_nfe
            ).outerjoin(
                Cliente, AuditoriaComparativaImpostos.cliente_id == Cliente.id
            ).outerjoin(
                NotaFiscalItem, AuditoriaComparativaImpostos.xml_item_id == NotaFiscalItem.id
            ).outerjoin(
                Produto, NotaFiscalItem.produto_id == Produto.id
            ).outerjoin(
                ItemNotaEntrada, AuditoriaComparativaImpostos.sped_item_id == ItemNotaEntrada.id
            ).outerjoin(
                ProdutoEntrada, ItemNotaEntrada.produto_entrada_id == ProdutoEntrada.id
            ).outerjoin(
                ImportacaoXML, AuditoriaComparativaImpostos.chave_nf == ImportacaoXML.chave_nf
            ).filter(
                AuditoriaComparativaImpostos.empresa_id == self.empresa_id,
                AuditoriaComparativaImpostos.chave_nf.in_(chaves_nf)
            )

            # Aplicar filtro de tributo se especificado
            if tributo_filter:
                if tributo_filter == 'icms':
                    query = query.filter(AuditoriaComparativaImpostos.xml_icms_valor.isnot(None))
                elif tributo_filter == 'icms_st':
                    query = query.filter(AuditoriaComparativaImpostos.xml_icms_st_valor.isnot(None))
                elif tributo_filter == 'ipi':
                    query = query.filter(AuditoriaComparativaImpostos.xml_ipi_valor.isnot(None))
                elif tributo_filter == 'pis':
                    query = query.filter(AuditoriaComparativaImpostos.xml_pis_valor.isnot(None))
                elif tributo_filter == 'cofins':
                    query = query.filter(AuditoriaComparativaImpostos.xml_cofins_valor.isnot(None))

            # Aplicar offset e limit
            results = query.order_by(AuditoriaComparativaImpostos.id.desc()).offset(offset).limit(batch_size + 1).all()

            # Verificar se há mais registros
            has_more = len(results) > batch_size
            if has_more:
                results = results[:-1]  # Remover o registro extra

            # Processar resultados
            auditorias_data = []
            for result in results:
                auditoria = result[0]  # AuditoriaComparativaImpostos object
                
                auditoria_dict = {
                    'id': auditoria.id,
                    'chave_nf': auditoria.chave_nf,
                    'cliente_id': auditoria.cliente_id,
                    'cliente_nome': result.cliente_nome,
                    'cliente_simples_nacional': result.cliente_simples_nacional,
                    'cliente_uf': result.cliente_uf,
                    'xml_produto_descricao': result.xml_produto_descricao,
                    'xml_codigo_produto': result.xml_codigo_produto,
                    'sped_produto_descricao': result.sped_produto_descricao,
                    'sped_tipo_item': result.sped_tipo_item,
                    'data_emissao': result.data_emissao.isoformat() if result.data_emissao else None,
                    'fin_nfe': result.fin_nfe,
                    
                    # Dados XML
                    'xml_icms_valor': float(auditoria.xml_icms_valor) if auditoria.xml_icms_valor else None,
                    'xml_icms_st_valor': float(auditoria.xml_icms_st_valor) if auditoria.xml_icms_st_valor else None,
                    'xml_ipi_valor': float(auditoria.xml_ipi_valor) if auditoria.xml_ipi_valor else None,
                    'xml_pis_valor': float(auditoria.xml_pis_valor) if auditoria.xml_pis_valor else None,
                    'xml_cofins_valor': float(auditoria.xml_cofins_valor) if auditoria.xml_cofins_valor else None,
                    
                    # Dados SPED
                    'sped_icms_valor': float(auditoria.sped_icms_valor) if auditoria.sped_icms_valor else None,
                    'sped_icms_st_valor': float(auditoria.sped_icms_st_valor) if auditoria.sped_icms_st_valor else None,
                    'sped_ipi_valor': float(auditoria.sped_ipi_valor) if auditoria.sped_ipi_valor else None,
                    'sped_pis_valor': float(auditoria.sped_pis_valor) if auditoria.sped_pis_valor else None,
                    'sped_cofins_valor': float(auditoria.sped_cofins_valor) if auditoria.sped_cofins_valor else None,
                    
                    # Status de matching
                    'status_matching': auditoria.status_matching,
                    'confidence_score': float(auditoria.confidence_score) if auditoria.confidence_score else None,
                    
                    # Timestamps
                    'data_criacao': auditoria.data_criacao.isoformat() if auditoria.data_criacao else None,
                    'data_atualizacao': auditoria.data_atualizacao.isoformat() if auditoria.data_atualizacao else None
                }
                
                auditorias_data.append(auditoria_dict)

            return {
                'success': True,
                'auditorias': auditorias_data,
                'has_more': has_more
            }

        except Exception as e:
            logger.error(f"Error getting auditoria by periodo batch: {str(e)}")
            return {
                'success': False,
                'message': f'Erro ao buscar auditoria em lote: {str(e)}',
                'auditorias': [],
                'has_more': False
            }
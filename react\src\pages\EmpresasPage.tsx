import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Building, Plus, Pencil, Trash2 } from 'lucide-react'
import { empresasService } from '@/services/empresasService'
import { EmpresaModal } from '@/components/empresas/EmpresaModal'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { HelpButton } from '@/components/ui/HelpButton'
import { HelpModal } from '@/components/ui/HelpModal'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'
import type { Empresa } from '@/types/empresas'

export function EmpresasPage() {
  const { data: empresas = [], isLoading, refetch } = useQuery({
    queryKey: ['empresas'],
    queryFn: empresasService.getEmpresas
  })

  const [modalOpen, setModalOpen] = useState(false)
  const [selectedEmpresa, setSelectedEmpresa] = useState<Empresa | null>(null)
  const [helpModalOpen, setHelpModalOpen] = useState(false)

  const handleNew = () => {
    setSelectedEmpresa(null)
    setModalOpen(true)
  }

  const handleEdit = (empresa: Empresa) => {
    setSelectedEmpresa(empresa)
    setModalOpen(true)
  }

  const handleDelete = async (empresa: Empresa) => {
    if (!confirm(`Excluir a empresa ${empresa.razao_social}?`)) return
    await empresasService.deleteEmpresa(empresa.id)
    refetch()
  }

  const handleSaved = () => {
    refetch()
  }

  const formatCNPJ = (cnpj: string) => {
    return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5')
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
          <Building className="w-6 h-6" />
          Empresas
          <HelpButton onClick={() => setHelpModalOpen(true)} />
        </h1>
        <Button
          variant="primary"
          onClick={handleNew}
          icon={<Plus className="w-4 h-4" />}
          glow
        >
          Nova Empresa
        </Button>
      </div>

      <Card>
        {isLoading ? (
          <div className="flex justify-center p-6">
            <LoadingSpinner />
          </div>
        ) : empresas.length > 0 ? (
          <TableScrollContainer>
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead>
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">
                    Razão Social
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">
                    CNPJ
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">
                    Cidade/UF
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">
                    Tributação
                  </th>
                  <th className="px-4 py-3 text-right text-sm font-semibold text-gray-900 dark:text-gray-100">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                {empresas.map((empresa) => (
                  <tr
                    key={empresa.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                      {empresa.razao_social || '-'}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                      {empresa.cnpj ? formatCNPJ(empresa.cnpj) : '-'}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                      {empresa.cidade && empresa.estado
                        ? `${empresa.cidade}/${empresa.estado}`
                        : '-'}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                      {empresa.tributacao || '-'}
                    </td>
                    <td className="px-4 py-2 text-right text-sm">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(empresa)}
                          icon={<Pencil className="w-4 h-4" />}
                          className="text-primary-600 hover:text-primary-800 hover:bg-primary-50 dark:hover:bg-primary-900/20"
                        >
                          Editar
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(empresa)}
                          icon={<Trash2 className="w-4 h-4" />}
                          className="text-danger-600 hover:text-danger-800 hover:bg-danger-50 dark:hover:bg-danger-900/20"
                        >
                          Excluir
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </TableScrollContainer>
        ) : (
          <div className="p-6 text-center text-gray-600 dark:text-gray-400">
            Nenhuma empresa cadastrada.
          </div>
        )}
      </Card>

      <EmpresaModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        onSaved={handleSaved}
        empresa={selectedEmpresa}
      />
      <HelpModal
        isOpen={helpModalOpen}
        onClose={() => setHelpModalOpen(false)}
        title="Empresas"
      >
        <div className="space-y-4 text-gray-700 dark:text-gray-300">
          <p>
            A página de empresas permite cadastrar e gerenciar as empresas utilizadas nas auditorias.
          </p>
          <div>
            <h3 className="font-semibold mb-1">Campos da tabela</h3>
            <ul className="list-disc pl-5 space-y-1">
              <li>
                <strong>Razão Social:</strong> nome cadastrado da empresa.
              </li>
              <li>
                <strong>CNPJ:</strong> número de inscrição no CNPJ.
              </li>
              <li>
                <strong>Cidade/UF:</strong> município e estado da empresa.
              </li>
              <li>
                <strong>Tributação:</strong> regime tributário da empresa.
              </li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-1">Ações disponíveis</h3>
            <ul className="list-disc pl-5 space-y-1">
              <li>
                <strong>Novo:</strong> cadastra uma nova empresa.
              </li>
              <li>
                <strong>Editar:</strong> altera os dados da empresa selecionada.
              </li>
              <li>
                <strong>Excluir:</strong> remove a empresa da lista.
              </li>
            </ul>
          </div>
        </div>
      </HelpModal>
    </div>
  )
}
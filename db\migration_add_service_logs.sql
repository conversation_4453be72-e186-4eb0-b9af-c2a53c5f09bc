-- Migration to add service logs table for XML service optimization

-- Create service_logs table to track service operations and authentication events
CREATE TABLE IF NOT EXISTS service_logs (
    id SERIAL PRIMARY KEY,
    service_auth_id INTEGER REFERENCES service_authentication(id) ON DELETE SET NULL,
    portal_office_id VARCHAR(50) NOT NULL,
    operation_type VARCHAR(100) NOT NULL,
    operation_details JSONB,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    error_message TEXT,
    request_data JSONB,
    response_data JSONB,
    execution_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3
);

-- Add indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_service_logs_service_auth_id ON service_logs(service_auth_id);
CREATE INDEX IF NOT EXISTS idx_service_logs_portal_office_id ON service_logs(portal_office_id);
CREATE INDEX IF NOT EXISTS idx_service_logs_operation_type ON service_logs(operation_type);
CREATE INDEX IF NOT EXISTS idx_service_logs_status ON service_logs(status);
CREATE INDEX IF NOT EXISTS idx_service_logs_created_at ON service_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_service_logs_completed_at ON service_logs(completed_at);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_service_logs_office_status ON service_logs(portal_office_id, status);
CREATE INDEX IF NOT EXISTS idx_service_logs_office_operation ON service_logs(portal_office_id, operation_type);
CREATE INDEX IF NOT EXISTS idx_service_logs_status_created ON service_logs(status, created_at);

-- Add comments for documentation
COMMENT ON TABLE service_logs IS 'Logs all service operations and authentication events for monitoring and debugging';
COMMENT ON COLUMN service_logs.service_auth_id IS 'Reference to the authentication record used for this operation';
COMMENT ON COLUMN service_logs.portal_office_id IS 'Office ID from portal system for tracking';
COMMENT ON COLUMN service_logs.operation_type IS 'Type of operation performed (auth, xml_fetch, data_sync, etc.)';
COMMENT ON COLUMN service_logs.operation_details IS 'Additional details about the operation stored as JSONB';
COMMENT ON COLUMN service_logs.status IS 'Operation status: pending, success, failed, retrying';
COMMENT ON COLUMN service_logs.error_message IS 'Error message if operation failed';
COMMENT ON COLUMN service_logs.request_data IS 'Request payload stored as JSONB';
COMMENT ON COLUMN service_logs.response_data IS 'Response data stored as JSONB';
COMMENT ON COLUMN service_logs.execution_time_ms IS 'Operation execution time in milliseconds';
COMMENT ON COLUMN service_logs.retry_count IS 'Number of retry attempts made';
COMMENT ON COLUMN service_logs.max_retries IS 'Maximum number of retries allowed for this operation';
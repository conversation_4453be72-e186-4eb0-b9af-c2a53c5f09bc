from .escritorio import db
from sqlalchemy.sql import func

class NotasFaltantes(db.Model):
    __tablename__ = 'notas_faltantes'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.<PERSON>umn(db.Integer, db.<PERSON>ey('escritorio.id'), nullable=False)
    chave_nf = db.Column(db.String(44), nullable=False)
    numero_nf = db.Column(db.String(20))
    data_emissao = db.Column(db.Date)
    data_entrada = db.Column(db.Date)
    origem = db.Column(db.String(10), nullable=False)  # 'XML' ou 'SPED'
    status = db.Column(db.String(20), default='faltante')  # faltante, encontrado, cancelado
    mes_referencia = db.Column(db.Integer, nullable=False)
    ano_referencia = db.Column(db.Integer, nullable=False)
    data_identificacao = db.Column(db.DateTime, server_default=func.now())
    data_resolucao = db.Column(db.DateTime)
    observacoes = db.Column(db.Text)
    
    # Relacionamentos
    empresa = db.relationship('Empresa', backref='notas_faltantes')
    escritorio = db.relationship('Escritorio', backref='notas_faltantes')
    
    # Constraint única
    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'chave_nf', 'origem', name='uq_notas_faltantes_empresa_chave_origem'),
    )
    
    def __repr__(self):
        return f"<NotasFaltantes {self.numero_nf} - {self.origem}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'chave_nf': self.chave_nf,
            'numero_nf': self.numero_nf,
            'data_emissao': self.data_emissao.isoformat() if self.data_emissao else None,
            'data_entrada': self.data_entrada.isoformat() if self.data_entrada else None,
            'origem': self.origem,
            'status': self.status,
            'mes_referencia': self.mes_referencia,
            'ano_referencia': self.ano_referencia,
            'data_identificacao': self.data_identificacao.isoformat() if self.data_identificacao else None,
            'data_resolucao': self.data_resolucao.isoformat() if self.data_resolucao else None,
            'observacoes': self.observacoes
        }


class HistoricoAlteracaoXML(db.Model):
    __tablename__ = 'historico_alteracao_xml'
    
    id = db.Column(db.Integer, primary_key=True)
    importacao_xml_id = db.Column(db.Integer, db.ForeignKey('importacao_xml.id'), nullable=False)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)
    data_anterior = db.Column(db.Date)
    data_nova = db.Column(db.Date)
    motivo = db.Column(db.String(255))
    data_alteracao = db.Column(db.DateTime, server_default=func.now())
    
    # Relacionamentos
    importacao_xml = db.relationship('ImportacaoXML', backref='historico_alteracoes')
    usuario = db.relationship('Usuario', backref='alteracoes_xml')
    
    def __repr__(self):
        return f"<HistoricoAlteracaoXML {self.importacao_xml_id} - {self.data_alteracao}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'importacao_xml_id': self.importacao_xml_id,
            'usuario_id': self.usuario_id,
            'data_anterior': self.data_anterior.isoformat() if self.data_anterior else None,
            'data_nova': self.data_nova.isoformat() if self.data_nova else None,
            'motivo': self.motivo,
            'data_alteracao': self.data_alteracao.isoformat() if self.data_alteracao else None
        }


class HistoricoAuditoriaEntrada(db.Model):
    __tablename__ = 'historico_auditoria_entrada'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'), nullable=False)
    cliente_id = db.Column(db.Integer, db.ForeignKey('cliente.id'))
    produto_id = db.Column(db.Integer, db.ForeignKey('produto.id'))
    cfop = db.Column(db.String(10))
    ncm = db.Column(db.String(20))
    
    # Padrões identificados
    icms_aliquota_padrao = db.Column(db.Numeric(5, 2))
    icms_st_aliquota_padrao = db.Column(db.Numeric(5, 2))
    ipi_aliquota_padrao = db.Column(db.Numeric(5, 2))
    pis_aliquota_padrao = db.Column(db.Numeric(5, 4))
    cofins_aliquota_padrao = db.Column(db.Numeric(5, 4))
    
    # Frequência de uso
    frequencia_uso = db.Column(db.Integer, default=1)
    ultima_utilizacao = db.Column(db.DateTime, server_default=func.now())
    
    # Status de confiabilidade
    confiabilidade = db.Column(db.Numeric(3, 2), default=0.5)  # 0.0 a 1.0
    aprovado_usuario = db.Column(db.Boolean, default=False)
    
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    data_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    empresa = db.relationship('Empresa', backref='historico_auditoria_entrada')
    cliente = db.relationship('Cliente', backref='historico_auditoria_entrada')
    produto = db.relationship('Produto', backref='historico_auditoria_entrada')
    
    # Constraint única
    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'cliente_id', 'produto_id', 'cfop', 'ncm', 
                          name='uq_historico_auditoria_entrada_empresa_cliente_produto_cfop_ncm'),
    )
    
    def __repr__(self):
        return f"<HistoricoAuditoriaEntrada {self.empresa_id} - {self.cfop} - {self.ncm}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'cliente_id': self.cliente_id,
            'produto_id': self.produto_id,
            'cfop': self.cfop,
            'ncm': self.ncm,
            'icms_aliquota_padrao': float(self.icms_aliquota_padrao) if self.icms_aliquota_padrao else None,
            'icms_st_aliquota_padrao': float(self.icms_st_aliquota_padrao) if self.icms_st_aliquota_padrao else None,
            'ipi_aliquota_padrao': float(self.ipi_aliquota_padrao) if self.ipi_aliquota_padrao else None,
            'pis_aliquota_padrao': float(self.pis_aliquota_padrao) if self.pis_aliquota_padrao else None,
            'cofins_aliquota_padrao': float(self.cofins_aliquota_padrao) if self.cofins_aliquota_padrao else None,
            'frequencia_uso': self.frequencia_uso,
            'ultima_utilizacao': self.ultima_utilizacao.isoformat() if self.ultima_utilizacao else None,
            'confiabilidade': float(self.confiabilidade) if self.confiabilidade else None,
            'aprovado_usuario': self.aprovado_usuario,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None
        }

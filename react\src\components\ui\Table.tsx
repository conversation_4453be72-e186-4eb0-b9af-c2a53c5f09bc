import React, { useEffect, useRef, useState, useCallback } from 'react'
import { cn } from '@/utils/cn'
import { Button } from './Button'

export interface Column<T> {
  key: keyof T | string
  title: string
  render?: (value: any, record: T, index: number) => React.ReactNode
  width?: string
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  className?: string
}

interface TableProps<T> {
  data: T[]
  columns: Column<T>[]
  loading?: boolean
  emptyMessage?: string
  className?: string
  rowClassName?: (record: T, index: number) => string
  onRowClick?: (record: T, index: number) => void
  striped?: boolean
  hoverable?: boolean
  bordered?: boolean
  size?: 'sm' | 'md' | 'lg'
  filterRow?: React.ReactNode
  showTopScrollbar?: boolean // Nova propriedade para mostrar scrollbar superior
}

interface PaginationProps {
  current: number
  total: number
  pageSize: number
  onChange: (page: number) => void
  showSizeChanger?: boolean
  onShowSizeChange?: (current: number, size: number) => void
  pageSizeOptions?: number[]
  showTotal?: (total: number, range: [number, number]) => React.ReactNode
}

const tableSizes = {
  sm: 'text-xs',
  md: 'text-sm',
  lg: 'text-base',
}

const cellPadding = {
  sm: 'px-3 py-2',
  md: 'px-4 py-3',
  lg: 'px-6 py-4',
}

export function Table<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  emptyMessage = 'Nenhum dado encontrado',
  className,
  rowClassName,
  onRowClick,
  striped = true,
  hoverable = true,
  bordered = false,
  size = 'md',
  filterRow,
  showTopScrollbar = true, // Habilitar por padrão
}: TableProps<T>) {
  const getValue = (record: T, key: keyof T | string): any => {
    if (typeof key === 'string' && key.includes('.')) {
      return key.split('.').reduce((obj, k) => obj?.[k], record)
    }
    return record[key as keyof T]
  }

  // Referências para os elementos de scroll
  const topScrollbarRef = useRef<HTMLDivElement>(null)
  const tableContainerRef = useRef<HTMLDivElement>(null)
  const [showTopScroll, setShowTopScroll] = useState(false)
  const contentWidthRef = useRef(0)

  // Sincronizar scroll entre top scrollbar e tabela
  const syncScroll = useCallback((source: 'top' | 'main') => {
    const topScrollbar = topScrollbarRef.current
    const tableContainer = tableContainerRef.current

    if (!topScrollbar || !tableContainer) return

    if (source === 'top') {
      tableContainer.scrollLeft = topScrollbar.scrollLeft
    } else {
      topScrollbar.scrollLeft = tableContainer.scrollLeft
    }
  }, [])

  // Verificar se a tabela é larga o suficiente para precisar de scrollbar
  const checkScrollbarNeeded = useCallback(() => {
    const tableContainer = tableContainerRef.current
    if (!tableContainer) return false

    // Forçar reflow para obter medidas corretas
    const table = tableContainer.querySelector('table')
    if (!table) return false

    // Calcular largura do conteúdo
    const contentWidth = table.offsetWidth
    const containerWidth = tableContainer.clientWidth
    
    // Armazenar largura do conteúdo para usar na barra superior
    contentWidthRef.current = contentWidth
    
    return contentWidth > containerWidth
  }, [])

  // Atualizar visibilidade da barra de rolagem superior
  const updateScrollbarVisibility = useCallback(() => {
    if (!showTopScrollbar) {
      setShowTopScroll(false)
      return
    }

    const needsScrollbar = checkScrollbarNeeded()
    setShowTopScroll(needsScrollbar)
  }, [showTopScrollbar, checkScrollbarNeeded])

  // Efeito para adicionar event listeners
  useEffect(() => {
    const topScrollbar = topScrollbarRef.current
    const tableContainer = tableContainerRef.current

    if (!topScrollbar || !tableContainer || !showTopScroll) return

    const handleTopScroll = () => syncScroll('top')
    const handleTableScroll = () => syncScroll('main')

    topScrollbar.addEventListener('scroll', handleTopScroll)
    tableContainer.addEventListener('scroll', handleTableScroll)

    return () => {
      topScrollbar.removeEventListener('scroll', handleTopScroll)
      tableContainer.removeEventListener('scroll', handleTableScroll)
    }
  }, [showTopScroll, syncScroll])

  // Efeito para verificar quando a tabela muda de tamanho
  useEffect(() => {
    updateScrollbarVisibility()
    
    // Adicionar resize observer para detectar mudanças no tamanho
    const tableContainer = tableContainerRef.current
    if (!tableContainer) return

    const resizeObserver = new ResizeObserver(() => {
      updateScrollbarVisibility()
    })

    resizeObserver.observe(tableContainer)
    
    // Observar também a tabela em si
    const table = tableContainer.querySelector('table')
    if (table) {
      resizeObserver.observe(table)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [data, columns, showTopScrollbar, updateScrollbarVisibility])

  if (loading) {
    return (
      <div className="w-full">
        <div className="animate-pulse">
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded-t-xl mb-2"></div>
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className="h-16 bg-gray-100 dark:bg-gray-800 mb-1 rounded"
            ></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={cn('w-full overflow-hidden', className)}>
      {/* Scrollbar superior (opcional) */}
      {showTopScroll && showTopScrollbar && (
        <div 
          ref={topScrollbarRef}
          className="w-full overflow-x-auto"
          style={{ 
            height: '16px',
            marginBottom: '4px',
            overflowY: 'hidden',
            cursor: 'pointer'
          }}
        >
          <div 
            style={{ 
              height: '1px',
              width: `${contentWidthRef.current}px`
            }}
          />
        </div>
      )}

      {/* Tabela principal com scrollbar inferior */}
      <div 
        ref={tableContainerRef}
        className="overflow-x-auto"
      >
        <table className="w-full">
          {/* Header */}
          <thead className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
            <tr>
              {columns.map((column, index) => (
                <th
                  key={String(column.key)}
                  className={cn(
                    'font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 whitespace-nowrap',
                    cellPadding[size],
                    tableSizes[size],
                    column.align === 'center' && 'text-center',
                    column.align === 'right' && 'text-right',
                    column.className,
                    index === 0 && 'rounded-tl-xl',
                    index === columns.length - 1 && 'rounded-tr-xl'
                  )}
                  style={{ width: column.width }}
                >
                  <div className="flex items-center gap-2">
                    {column.title}
                    {column.sortable && (
                      <svg
                        className="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                        />
                      </svg>
                    )}
                  </div>
                </th>
              ))}
            </tr>
            {filterRow}
          </thead>

          {/* Body */}
          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            {data.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className={cn(
                    'text-center text-gray-500 dark:text-gray-400 py-12',
                    cellPadding[size]
                  )}
                >
                  <div className="flex flex-col items-center gap-3">
                    <svg
                      className="w-12 h-12 text-gray-300 dark:text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1}
                        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
                      />
                    </svg>
                    <span className="font-medium">{emptyMessage}</span>
                  </div>
                </td>
              </tr>
            ) : (
              data.map((record, index) => (
                <tr
                  key={index}
                  className={cn(
                    'transition-colors duration-150',
                    striped &&
                      index % 2 === 1 &&
                      'bg-gray-50/50 dark:bg-gray-800/50',
                    hoverable &&
                      'hover:bg-primary-50 dark:hover:bg-primary-900/20',
                    onRowClick && 'cursor-pointer',
                    bordered && 'border-b border-gray-200 dark:border-gray-700',
                    rowClassName?.(record, index)
                  )}
                  onClick={() => onRowClick?.(record, index)}
                >
                  {columns.map((column) => (
                    <td
                      key={String(column.key)}
                      className={cn(
                        'text-gray-900 dark:text-gray-100 whitespace-nowrap',
                        cellPadding[size],
                        tableSizes[size],
                        column.align === 'center' && 'text-center',
                        column.align === 'right' && 'text-right',
                        column.className
                      )}
                    >
                      {column.render
                        ? column.render(
                            getValue(record, column.key),
                            record,
                            index
                          )
                        : getValue(record, column.key)}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export function Pagination({
  current,
  total,
  pageSize,
  onChange,
  showSizeChanger = false,
  onShowSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showTotal,
}: PaginationProps) {
  const totalPages = Math.ceil(total / pageSize)
  const startItem = (current - 1) * pageSize + 1
  const endItem = Math.min(current * pageSize, total)

  const getVisiblePages = () => {
    const delta = 2
    const range = []
    const rangeWithDots = []

    for (
      let i = Math.max(2, current - delta);
      i <= Math.min(totalPages - 1, current + delta);
      i++
    ) {
      range.push(i)
    }

    if (current - delta > 2) {
      rangeWithDots.push(1, '...')
    } else {
      rangeWithDots.push(1)
    }

    rangeWithDots.push(...range)

    if (current + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages)
    } else {
      rangeWithDots.push(totalPages)
    }

    return rangeWithDots
  }

  if (totalPages <= 1) return null

  return (
    <div className="flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 sm:px-6">
      <div className="flex items-center gap-4">
        {showTotal && (
          <div className="text-sm text-gray-700 dark:text-gray-300">
            {showTotal(total, [startItem, endItem])}
          </div>
        )}

        {showSizeChanger && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Mostrar:
            </span>
            <select
              value={pageSize}
              onChange={(e) => onShowSizeChange?.(1, Number(e.target.value))}
              className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {pageSizeOptions.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          disabled={current <= 1}
          onClick={() => onChange(current - 1)}
          icon={
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          }
        >
          Anterior
        </Button>

        <div className="flex items-center gap-1">
          {getVisiblePages().map((page, index) => (
            <React.Fragment key={index}>
              {page === '...' ? (
                <span className="px-3 py-2 text-gray-500">...</span>
              ) : (
                <button
                  onClick={() => onChange(page as number)}
                  className={cn(
                    'px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    page === current
                      ? 'bg-primary-500 text-white shadow-md'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  )}
                >
                  {page}
                </button>
              )}
            </React.Fragment>
          ))}
        </div>

        <Button
          variant="ghost"
          size="sm"
          disabled={current >= totalPages}
          onClick={() => onChange(current + 1)}
          icon={
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          }
          iconPosition="right"
        >
          Próximo
        </Button>
      </div>
    </div>
  )
}
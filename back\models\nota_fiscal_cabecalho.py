from .escritorio import db
from sqlalchemy.sql import func

class NotaFiscalCabecalho(db.Model):
    __tablename__ = 'nota_fiscal_cabecalho'

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON>ger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.Integer, db.Foreign<PERSON>ey('escritorio.id'), nullable=False)
    chave_nf = db.Column(db.String(44), unique=True, nullable=False)
    numero_nf = db.Column(db.String(20), nullable=False)
    data_emissao = db.Column(db.Date, nullable=False)
    
    # Totais da NF
    valor_total_nf = db.Column(db.Numeric(15, 2), nullable=False)  # vNF
    valor_produtos = db.Column(db.Numeric(15, 2))  # v<PERSON>rod
    valor_frete = db.Column(db.Numeric(15, 2))     # vFrete
    valor_seguro = db.Column(db.Numeric(15, 2))    # vSeg
    valor_desconto = db.Column(db.Numeric(15, 2))  # vDesc
    valor_outros = db.Column(db.Numeric(15, 2))    # vOutro
    
    # Dados de auditoria
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    data_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())

    # Relacionamentos
    empresa = db.relationship('Empresa', backref='notas_fiscais_cabecalho', lazy=True)
    escritorio = db.relationship('Escritorio', backref='notas_fiscais_cabecalho', lazy=True)
    itens = db.relationship('NotaFiscalItem', backref='cabecalho', lazy=True)

    def __repr__(self):
        return f"<NotaFiscalCabecalho {self.chave_nf}>"

    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'chave_nf': self.chave_nf,
            'numero_nf': self.numero_nf,
            'data_emissao': self.data_emissao.isoformat() if self.data_emissao else None,
            'valor_total_nf': float(self.valor_total_nf) if self.valor_total_nf is not None else None,
            'valor_produtos': float(self.valor_produtos) if self.valor_produtos is not None else None,
            'valor_frete': float(self.valor_frete) if self.valor_frete is not None else None,
            'valor_seguro': float(self.valor_seguro) if self.valor_seguro is not None else None,
            'valor_desconto': float(self.valor_desconto) if self.valor_desconto is not None else None,
            'valor_outros': float(self.valor_outros) if self.valor_outros is not None else None,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None
        }

import torch
from sklearn.metrics import roc_auc_score, classification_report, confusion_matrix
from preprocess import preprocess
from dataset import get_loaders
from model import CenarioModel

def load_trained_model(encoders, num_cols, path="best_model.pth"):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    # Mesma lógica de instanciar
    cardinalities = [len(encoders[col].classes_) for col in encoders]
    emb_dims      = [min(50, (c+1)//2) for c in cardinalities]
    model = CenarioModel(cardinalities, emb_dims, n_num=len(num_cols))
    model.load_state_dict(torch.load(path, map_location=device))
    model.to(device).eval()
    return model, device

if __name__ == "__main__":
    # Recarrega dados e separa loaders
    df, cat_cols, num_cols, encoders, scaler = preprocess()
    _, val_loader = get_loaders(df, cat_cols, num_cols)

    # Carrega o modelo que você salvou ao final do treino (ainda vamos salvar isso)
    model, device = load_trained_model(encoders, num_cols, path="best_model.pth")

    # Gera preds
    all_labels, all_preds = [], []
    with torch.no_grad():
        for Xc, Xn, y in val_loader:
            Xc, Xn = Xc.to(device), Xn.to(device)
            probs = model(Xc, Xn).cpu().numpy()
            all_preds.extend(probs)
            all_labels.extend(y.numpy())

    # Métricas
    preds_bin = [1 if p>0.5 else 0 for p in all_preds]
    print("AUC-ROC:", roc_auc_score(all_labels, all_preds))
    print(classification_report(all_labels, preds_bin, digits=4))
    print("Matriz de confusão:\n",
      confusion_matrix(all_labels, preds_bin, labels=[0, 1]))

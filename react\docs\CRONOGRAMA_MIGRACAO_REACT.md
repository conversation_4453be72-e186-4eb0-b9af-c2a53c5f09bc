# Cronograma de Migração Frontend para React

## Visão Geral

Migração incremental do frontend atual (Jinja2 + jQuery) para React, mantendo a funcionalidade existente e melhorando a experiência do usuário.

## Estrutura Atual Analisada

### Templates Existentes
- `front/templates/index.html` - Página de autenticação
- `front/templates/dashboard.html` - Dashboard principal (arquivo muito grande, ~1000+ linhas)
- `front/templates/escritorios.html` - Gestão de escritórios

### JavaScript Modules (47 arquivos)
- **Core**: `common.js`, `dashboard.js`, `auth.js`
- **Auditoria**: `auditoria_comparativa.js`, `auditoria_dashboard.js`, `auditoria_entrada.js`
- **Funcionalidades**: `importacao.js`, `cenarios.js`, `clientes.js`, `produtos.js`
- **Validações**: `ipi_validation.js`, `icms_st_validation.js`, `pis_cofins_validation.js`
- **UI Components**: `chatbot_ia.js`, `custom-dropdown.js`, `common-ui.js`

### CSS Modules (11 arquivos)
- **Base**: `main.css`, `dashboard.css`, `animations.css`
- **Componentes**: `auditoria_comparativa.css`, `chatbot_ia.css`, `custom-dropdown.css`
- **Páginas**: `login.css`, `portal-auth.css`, `dashboard-empresa.css`

## Fases da Migração

### Fase 1: Setup e Infraestrutura (Semana 1-2)
**Objetivo**: Preparar ambiente React e estrutura base

#### 1.1 Setup do Projeto React
- [ ] Criar pasta `react/` na raiz
- [ ] Configurar Vite + React + TypeScript
- [ ] Setup de roteamento (React Router)
- [ ] Configurar Tailwind CSS ou styled-components
- [ ] Setup de gerenciamento de estado (Zustand/Redux)

#### 1.2 Estrutura Base
```
react/
├── src/
│   ├── components/          # Componentes reutilizáveis
│   ├── pages/              # Páginas principais
│   ├── hooks/              # Custom hooks
│   ├── services/           # API calls
│   ├── store/              # Estado global
│   ├── types/              # TypeScript types
│   ├── utils/              # Utilitários
│   └── styles/             # Estilos globais
├── public/
└── package.json
```

#### 1.3 Configuração de Build
- [ ] Configurar build para produção
- [ ] Setup de proxy para API Flask
- [ ] Configurar hot reload
- [ ] Setup de testes (Vitest + Testing Library)

**Entregáveis**: Ambiente React funcionando, estrutura base criada

---

### Fase 2: Autenticação e Layout Base (Semana 3-4)
**Objetivo**: Migrar sistema de auth e layout principal

#### 2.1 Sistema de Autenticação
- [ ] Componente `LoginPage` (migrar `index.html`)
- [ ] Hook `useAuth` para gerenciar autenticação
- [ ] Service `authService` para API calls
- [ ] Proteção de rotas com `PrivateRoute`

#### 2.2 Layout Principal
- [ ] Componente `DashboardLayout`
- [ ] Componente `Header` com seletores
- [ ] Componente `Sidebar` com navegação
- [ ] Hook `useTheme` para dark/light mode
- [ ] Componente `UserProfile` dropdown

#### 2.3 Componentes Base
- [ ] `LoadingSpinner`
- [ ] `ErrorBoundary`
- [ ] `CustomDropdown`
- [ ] `Modal` genérico

**Entregáveis**: Login funcional, layout base responsivo

---

### Fase 3: Dashboard Principal (Semana 5-6)
**Objetivo**: Migrar dashboard principal com cards e estatísticas

#### 3.1 Dashboard Overview
- [ ] Página `DashboardPage`
- [ ] Componente `DashboardCards`
- [ ] Componente `CompanyList`
- [ ] Hook `useDashboardData`

#### 3.2 Seletores e Filtros
- [ ] Componente `CompanySelector`
- [ ] Componente `YearSelector`
- [ ] Componente `MonthSelector`
- [ ] Context `FilterContext`

#### 3.3 Gráficos e Visualizações
- [ ] Setup Chart.js ou Recharts
- [ ] Componente `DashboardCharts`
- [ ] Componente `StatusCards`

**Entregáveis**: Dashboard principal funcional com dados dinâmicos

---

### Fase 4: Módulo de Importação (Semana 7-8)
**Objetivo**: Migrar funcionalidade de importação de arquivos

#### 4.1 Interface de Importação
- [ ] Página `ImportacaoPage`
- [ ] Componente `FileUpload` com drag & drop
- [ ] Componente `ImportProgress`
- [ ] Hook `useFileUpload`

#### 4.2 WebSocket Integration
- [ ] Hook `useWebSocket` para real-time updates
- [ ] Componente `ImportStatus`
- [ ] Notificações de progresso

#### 4.3 Histórico de Importações
- [ ] Componente `ImportHistory`
- [ ] Tabela com paginação
- [ ] Filtros de busca

**Entregáveis**: Sistema de importação completo com feedback real-time

---

### Fase 5: Módulo de Auditoria (Semana 9-11)
**Objetivo**: Migrar sistema de auditoria comparativa

#### 5.1 Auditoria Base
- [ ] Página `AuditoriaPage`
- [ ] Componente `TributoTabs`
- [ ] Componente `AuditoriaFilters`

#### 5.2 Auditoria Comparativa
- [ ] Componente `AuditoriaComparativa`
- [ ] Componente `ComparisonTable`
- [ ] Componente `DivergenceHighlight`
- [ ] Hook `useAuditoriaData`

#### 5.3 Validações Específicas
- [ ] Componente `IPIValidation`
- [ ] Componente `ICMSSTValidation`
- [ ] Componente `PISCOFINSValidation`

#### 5.4 Matching Manual
- [ ] Componente `MatchingManual`
- [ ] Modal de seleção de candidatos
- [ ] Interface de aprovação/rejeição

**Entregáveis**: Sistema de auditoria completo e funcional

---

### Fase 6: Gestão de Cenários (Semana 12-13)
**Objetivo**: Migrar módulo de cenários fiscais

#### 6.1 Interface de Cenários
- [ ] Página `CenariosPage`
- [ ] Componente `CenarioCard`
- [ ] Componente `CenarioModal`

#### 6.2 Editor de Cenários
- [ ] Componente `CenarioEditor`
- [ ] Formulários dinâmicos
- [ ] Validação de regras

#### 6.3 Histórico e Versionamento
- [ ] Componente `CenarioHistory`
- [ ] Comparação de versões

**Entregáveis**: Gestão completa de cenários fiscais

---

### Fase 7: Módulos Auxiliares (Semana 14-15)
**Objetivo**: Migrar páginas de apoio

#### 7.1 Gestão de Clientes
- [ ] Página `ClientesPage`
- [ ] Componente `ClienteTable`
- [ ] Formulários CRUD

#### 7.2 Gestão de Produtos
- [ ] Página `ProdutosPage`
- [ ] Componente `ProdutoTable`
- [ ] Editor de produtos

#### 7.3 Gestão de Usuários
- [ ] Página `UsuariosPage`
- [ ] Componente `UserManagement`
- [ ] Controle de permissões

**Entregáveis**: Módulos de gestão completos

---

### Fase 8: Chatbot e Features Avançadas (Semana 16-17)
**Objetivo**: Migrar funcionalidades avançadas

#### 8.1 Chatbot IA
- [ ] Componente `ChatbotIA`
- [ ] Interface de chat
- [ ] Integração com OpenAI

#### 8.2 Apuração
- [ ] Página `ApuracaoPage`
- [ ] Componentes de cálculo
- [ ] Relatórios dinâmicos

#### 8.3 Gestão de XMLs
- [ ] Página `GestaoXMLs`
- [ ] Visualizador de XML
- [ ] Ferramentas de análise

**Entregáveis**: Features avançadas funcionais

---

### Fase 9: Otimização e Polimento (Semana 18-19)
**Objetivo**: Otimizar performance e UX

#### 9.1 Performance
- [ ] Code splitting por rotas
- [ ] Lazy loading de componentes
- [ ] Otimização de bundle
- [ ] Caching de dados

#### 9.2 Acessibilidade
- [ ] ARIA labels
- [ ] Navegação por teclado
- [ ] Contraste de cores
- [ ] Screen reader support

#### 9.3 Testes
- [ ] Testes unitários (>80% coverage)
- [ ] Testes de integração
- [ ] Testes E2E (Playwright)

**Entregáveis**: Aplicação otimizada e testada

---

### Fase 10: Deploy e Migração Final (Semana 20)
**Objetivo**: Deploy em produção e migração completa

#### 10.1 Deploy
- [ ] Configurar build de produção
- [ ] Setup de CI/CD
- [ ] Testes em staging

#### 10.2 Migração
- [ ] Backup do sistema atual
- [ ] Deploy gradual (feature flags)
- [ ] Monitoramento de erros

#### 10.3 Documentação
- [ ] Documentação técnica
- [ ] Guia de desenvolvimento
- [ ] Treinamento da equipe

**Entregáveis**: Sistema React em produção

---

## Tecnologias Recomendadas

### Core Stack
- **React 18** - Framework principal
- **TypeScript** - Type safety
- **Vite** - Build tool rápido
- **React Router v6** - Roteamento

### Estado e Dados
- **Zustand** - Gerenciamento de estado (mais simples que Redux)
- **React Query** - Cache e sincronização de dados
- **Axios** - HTTP client

### UI e Styling
- **Tailwind CSS** - Utility-first CSS
- **Headless UI** - Componentes acessíveis
- **Framer Motion** - Animações
- **React Hook Form** - Formulários

### Gráficos e Visualização
- **Recharts** - Gráficos React-native
- **Chart.js** - Gráficos avançados (se necessário)

### Testes
- **Vitest** - Test runner
- **Testing Library** - Testes de componentes
- **Playwright** - Testes E2E

### Desenvolvimento
- **ESLint + Prettier** - Code quality
- **Husky** - Git hooks
- **Storybook** - Documentação de componentes

---

## Estratégia de Migração

### Abordagem Incremental
1. **Coexistência**: React e Jinja2 funcionando juntos
2. **Rota por rota**: Migrar uma página por vez
3. **Feature flags**: Controlar qual versão mostrar
4. **Rollback**: Possibilidade de voltar ao sistema anterior

### Integração com Flask
- Manter APIs Flask existentes
- React consome APIs via `/fiscal/api/*`
- WebSocket mantido para real-time
- Autenticação JWT preservada

### Considerações de UX
- Manter funcionalidades existentes
- Melhorar responsividade
- Adicionar loading states
- Melhorar feedback visual
- Manter atalhos de teclado

---

## Riscos e Mitigações

### Riscos Técnicos
- **Complexidade da migração**: Migração incremental reduz risco
- **Performance**: Otimização contínua e monitoramento
- **Bugs**: Testes automatizados e QA rigoroso

### Riscos de Negócio
- **Downtime**: Deploy gradual com rollback
- **Treinamento**: Documentação e sessões de treinamento
- **Resistência**: Envolver equipe no processo

---

## Métricas de Sucesso

### Performance
- Tempo de carregamento < 2s
- First Contentful Paint < 1s
- Bundle size otimizado

### Qualidade
- Cobertura de testes > 80%
- Zero bugs críticos
- Acessibilidade WCAG AA

### UX
- Redução de cliques para tarefas comuns
- Feedback positivo dos usuários
- Redução de tempo para completar fluxos

---

## Próximos Passos

1. **Aprovação do cronograma** pela equipe
2. **Setup do ambiente React** (Fase 1)
3. **Definição de padrões** de código e arquitetura
4. **Início da migração** com autenticação

**Estimativa total**: 20 semanas (~5 meses)
**Recursos necessários**: 1-2 desenvolvedores frontend
**Investimento**: Alto, mas com retorno em manutenibilidade e UX
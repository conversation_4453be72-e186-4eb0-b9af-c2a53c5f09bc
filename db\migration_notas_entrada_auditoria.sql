-- Migration: Sistema de Auditoria de Notas de Entrada
-- Criação de tabelas e campos necessários para o sistema de auditoria de entrada

-- 1. Adicionar campos para controle de entrada/saída nos XMLs
ALTER TABLE importacao_xml ADD COLUMN IF NOT EXISTS tipo_nota VARCHAR(1) DEFAULT '0'; -- 0=entrada, 1=saída
ALTER TABLE importacao_xml ADD COLUMN IF NOT EXISTS data_entrada DATE; -- Data de entrada modificável
ALTER TABLE importacao_xml ADD COLUMN IF NOT EXISTS data_emissao_original DATE; -- Backup da data original
ALTER TABLE importacao_xml ADD COLUMN IF NOT EXISTS status_validacao VARCHAR(20) DEFAULT 'pendente'; -- pendente, validado, cancelado
ALTER TABLE importacao_xml ADD COLUMN IF NOT EXISTS observacoes_validacao TEXT;

-- 2. Tabela para histórico de alterações de datas
CREATE TABLE IF NOT EXISTS historico_alteracao_xml (
    id SERIAL PRIMARY KEY,
    importacao_xml_id INTEGER REFERENCES importacao_xml(id) NOT NULL,
    usuario_id INTEGER REFERENCES usuario(id) NOT NULL,
    data_anterior DATE,
    data_nova DATE,
    motivo VARCHAR(255),
    data_alteracao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Tabela para controle de notas faltantes (XML x SPED)
CREATE TABLE IF NOT EXISTS notas_faltantes (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    chave_nf VARCHAR(44) NOT NULL,
    numero_nf VARCHAR(20),
    data_emissao DATE,
    data_entrada DATE,
    origem VARCHAR(10) NOT NULL, -- 'XML' ou 'SPED'
    status VARCHAR(20) DEFAULT 'faltante', -- faltante, encontrado, cancelado
    mes_referencia INTEGER NOT NULL,
    ano_referencia INTEGER NOT NULL,
    data_identificacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_resolucao TIMESTAMP,
    observacoes TEXT,
    UNIQUE (empresa_id, chave_nf, origem)
);

-- 4. Tabela para auditoria de entrada (comparação SPED x XML x Cenários)
CREATE TABLE IF NOT EXISTS auditoria_entrada (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    usuario_id INTEGER REFERENCES usuario(id),
    chave_nf VARCHAR(44) NOT NULL,
    numero_nf VARCHAR(20),
    cliente_id INTEGER REFERENCES cliente(id),
    produto_id INTEGER REFERENCES produto(id),
    
    -- Dados do XML
    xml_data_emissao DATE,
    xml_data_entrada DATE,
    xml_cfop VARCHAR(10),
    xml_ncm VARCHAR(20),
    xml_valor_total DECIMAL(15,2),
    xml_quantidade DECIMAL(15,4),
    
    -- Dados do SPED
    sped_data_documento DATE,
    sped_data_entrada_saida DATE,
    sped_cfop VARCHAR(10),
    sped_ncm VARCHAR(20),
    sped_valor_item DECIMAL(15,2),
    sped_quantidade DECIMAL(15,4),
    
    -- Tributos XML
    xml_icms_aliquota DECIMAL(5,2),
    xml_icms_valor DECIMAL(15,2),
    xml_icms_st_aliquota DECIMAL(5,2),
    xml_icms_st_valor DECIMAL(15,2),
    xml_ipi_aliquota DECIMAL(5,2),
    xml_ipi_valor DECIMAL(15,2),
    xml_pis_aliquota DECIMAL(5,4),
    xml_pis_valor DECIMAL(15,2),
    xml_cofins_aliquota DECIMAL(5,4),
    xml_cofins_valor DECIMAL(15,2),
    
    -- Tributos SPED
    sped_icms_aliquota DECIMAL(5,2),
    sped_icms_valor DECIMAL(15,2),
    sped_icms_st_aliquota DECIMAL(5,2),
    sped_icms_st_valor DECIMAL(15,2),
    sped_ipi_aliquota DECIMAL(5,2),
    sped_ipi_valor DECIMAL(15,2),
    sped_pis_aliquota DECIMAL(5,4),
    sped_pis_valor DECIMAL(15,2),
    sped_cofins_aliquota DECIMAL(5,4),
    sped_cofins_valor DECIMAL(15,2),
    
    -- Tributos Cenário (valores esperados)
    cenario_icms_aliquota DECIMAL(5,2),
    cenario_icms_valor DECIMAL(15,2),
    cenario_icms_st_aliquota DECIMAL(5,2),
    cenario_icms_st_valor DECIMAL(15,2),
    cenario_ipi_aliquota DECIMAL(5,2),
    cenario_ipi_valor DECIMAL(15,2),
    cenario_pis_aliquota DECIMAL(5,4),
    cenario_pis_valor DECIMAL(15,2),
    cenario_cofins_aliquota DECIMAL(5,4),
    cenario_cofins_valor DECIMAL(15,2),
    
    -- Status de auditoria por tributo
    status_icms VARCHAR(20) DEFAULT 'pendente', -- pendente, conforme, divergente, auditado
    status_icms_st VARCHAR(20) DEFAULT 'pendente',
    status_ipi VARCHAR(20) DEFAULT 'pendente',
    status_pis VARCHAR(20) DEFAULT 'pendente',
    status_cofins VARCHAR(20) DEFAULT 'pendente',
    
    -- Observações por tributo
    obs_icms TEXT,
    obs_icms_st TEXT,
    obs_ipi TEXT,
    obs_pis TEXT,
    obs_cofins TEXT,
    
    -- Controle geral
    status_geral VARCHAR(20) DEFAULT 'pendente', -- pendente, em_auditoria, auditado
    data_auditoria TIMESTAMP,
    usuario_auditoria INTEGER REFERENCES usuario(id),
    
    mes_referencia INTEGER NOT NULL,
    ano_referencia INTEGER NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE (empresa_id, chave_nf, produto_id)
);

-- 5. Tabela para histórico de auditoria (machine learning)
CREATE TABLE IF NOT EXISTS historico_auditoria_entrada (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id),
    produto_id INTEGER REFERENCES produto(id),
    cfop VARCHAR(10),
    ncm VARCHAR(20),
    
    -- Padrões identificados
    icms_aliquota_padrao DECIMAL(5,2),
    icms_st_aliquota_padrao DECIMAL(5,2),
    ipi_aliquota_padrao DECIMAL(5,2),
    pis_aliquota_padrao DECIMAL(5,4),
    cofins_aliquota_padrao DECIMAL(5,4),
    
    -- Frequência de uso
    frequencia_uso INTEGER DEFAULT 1,
    ultima_utilizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Status de confiabilidade
    confiabilidade DECIMAL(3,2) DEFAULT 0.5, -- 0.0 a 1.0
    aprovado_usuario BOOLEAN DEFAULT FALSE,
    
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE (empresa_id, cliente_id, produto_id, cfop, ncm)
);

-- 6. Adicionar campos na tabela nota_fiscal_item para controle de entrada
ALTER TABLE nota_fiscal_item ADD COLUMN IF NOT EXISTS tipo_nota VARCHAR(1) DEFAULT '1'; -- 0=entrada, 1=saída
ALTER TABLE nota_fiscal_item ADD COLUMN IF NOT EXISTS data_entrada DATE; -- Para notas de entrada

-- 7. Índices para performance
CREATE INDEX IF NOT EXISTS idx_importacao_xml_tipo_nota ON importacao_xml(tipo_nota);
CREATE INDEX IF NOT EXISTS idx_importacao_xml_data_entrada ON importacao_xml(data_entrada);
CREATE INDEX IF NOT EXISTS idx_notas_faltantes_empresa_mes_ano ON notas_faltantes(empresa_id, mes_referencia, ano_referencia);
CREATE INDEX IF NOT EXISTS idx_auditoria_entrada_empresa_mes_ano ON auditoria_entrada(empresa_id, mes_referencia, ano_referencia);
CREATE INDEX IF NOT EXISTS idx_auditoria_entrada_chave_nf ON auditoria_entrada(chave_nf);
CREATE INDEX IF NOT EXISTS idx_historico_auditoria_entrada_empresa ON historico_auditoria_entrada(empresa_id, cliente_id, produto_id);

-- 8. Comentários nas tabelas
COMMENT ON TABLE historico_alteracao_xml IS 'Histórico de alterações de datas nos XMLs importados';
COMMENT ON TABLE notas_faltantes IS 'Controle de notas que estão no XML mas não no SPED ou vice-versa';
COMMENT ON TABLE auditoria_entrada IS 'Auditoria comparativa entre XML, SPED e Cenários para notas de entrada';
COMMENT ON TABLE historico_auditoria_entrada IS 'Histórico de padrões de auditoria para machine learning';

COMMIT;

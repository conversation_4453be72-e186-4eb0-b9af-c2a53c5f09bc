# Etapa de build
FROM node:18-alpine AS build

WORKDIR /app

# Copiar arquivos de configuração primeiro para aproveitar o cache
COPY package*.json ./

# Instalar todas as dependências (incluindo devDependencies)
RUN npm ci

# Copiar o restante do código
COPY . .

# Build da aplicação
RUN npm run build

# Etapa de produção
FROM nginx:alpine

# Copiar os arquivos buildados para o diretório padrão do nginx
COPY --from=build /app/dist /usr/share/nginx/html

# Copiar a configuração do nginx
COPY nginx.conf /etc/nginx/nginx.conf

# Expor a porta 80
EXPOSE 80

# Comando para iniciar o nginx
CMD ["nginx", "-g", "daemon off;"]
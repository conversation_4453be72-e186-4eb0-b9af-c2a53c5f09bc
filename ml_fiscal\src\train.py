# src/train.py
import torch
import torch.optim as optim
import torch.nn as nn
from preprocess import preprocess
from dataset import get_loaders
from model import CenarioModel

def train_epoch(loader, model, optimizer, criterion, device):
    model.train()
    total_loss = 0
    for Xc, Xn, y in loader:
        Xc, Xn, y = Xc.to(device), Xn.to(device), y.to(device)
        optimizer.zero_grad()
        pred = model(Xc, Xn)
        loss = criterion(pred, y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item() * y.size(0)
    return total_loss / len(loader.dataset)

def eval_epoch(loader, model, criterion, device):
    model.eval()
    total_loss = 0
    with torch.no_grad():
        for Xc, Xn, y in loader:
            Xc, Xn, y = Xc.to(device), Xn.to(device), y.to(device)
            pred = model(Xc, Xn)
            loss = criterion(pred, y)
            total_loss += loss.item() * y.size(0)
    return total_loss / len(loader.dataset)

if __name__ == "__main__":
    df, cat_cols, num_cols, encoders, scaler = preprocess()
    train_loader, val_loader = get_loaders(df, cat_cols, num_cols)

    cardinalities = [len(encoders[col].classes_) for col in cat_cols]
    # Ajuste para centenas ou milhares de valores únicos, como a descrição do produto (substituir (c+1)//2 por ([min(50, max(5, int(c**0.25))) for c in cardinalities]))
    emb_dims      = [min(50, (c + 1)//2) for c in cardinalities]
    model         = CenarioModel(cardinalities, emb_dims, n_num=len(num_cols))

    device    = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    optimizer = optim.Adam(model.parameters(), lr=1e-3)
    criterion = nn.BCELoss()   # espera entrada em [0,1]; nosso forward já aplica sigmoid

    for epoch in range(1, 11):
        tr_loss  = train_epoch(train_loader, model, optimizer, criterion, device)
        val_loss = eval_epoch (val_loader,   model, criterion, device)
        print(f"Epoch {epoch}: Train {tr_loss:.4f} – Val {val_loss:.4f}")
        torch.save(model.state_dict(), "best_model.pth")

# BLOCO HTTPS (PRINCIPAL)
server {
    listen 443 ssl;
    server_name www.audittei.com.br audittei.com.br;

    ssl_certificate /etc/letsencrypt/live/www.audittei.com.br/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.audittei.com.br/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    gzip on;
    gzip_min_length 256;
    gzip_types text/plain text/css application/json application/javascript text/javascript application/x-javascript;
    gzip_vary on;

    # Static fiscal frontend
    location /fiscal/static/ {
        alias /home/<USER>/audittei/fiscal/front/static/;
        access_log off;
        add_header Expires 0d;
        add_header Cache-Control "no-cache, no-store, must-revalidate" ;
        add_header Pragma "no-cache";
        gzip_static off;
    }

    # Flask + API + JS router
    location /fiscal/ {
        proxy_pass http://localhost:5000/fiscal/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Script-Name /fiscal;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_buffering off;
        client_max_body_size 100M;
    }

    # WebSocket para o fiscal
    location /fiscal/socket.io {
        proxy_pass http://localhost:5000/fiscal/socket.io;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_buffering off;
    }

    # Novo frontend React servido diretamente pelo Nginx
    location / {
        root /home/<USER>/audittei/front;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # Configurações de cache para arquivos estáticos
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }

    access_log /var/log/nginx/audittei.access.log;
    error_log /var/log/nginx/audittei.error.log;
}
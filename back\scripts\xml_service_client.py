#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Serviço Cliente de Monitoramento e Envio de XMLs
Sistema de auditoria fiscal - Cliente executável
"""

import os
import sys
import time
import json
import shutil
import zipfile
import requests
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime, timedelta
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import logging

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xml_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ConfigManager:
    """Gerenciador de configurações persistentes"""
    
    CONFIG_FILE = "xml_service_config.json"
    
    @classmethod
    def load_config(cls):
        """Carrega configurações do arquivo"""
        default_config = {
            "api_key": "",
            "monitor_directory": "",
            "api_endpoint": "https://api.audittei.com/fiscal/api/service-upload",
            "escritorio_id": 1,
            "retry_attempts": 5,
            "retry_interval": 30,
            "failure_cooldown_hours": 2,
            "max_failure_days": 5
        }
        
        if os.path.exists(cls.CONFIG_FILE):
            try:
                with open(cls.CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Merge com configurações padrão para garantir todas as chaves
                    default_config.update(config)
                    return default_config
            except Exception as e:
                logger.error(f"Erro ao carregar configurações: {e}")
                return default_config
        return default_config
    
    @classmethod
    def save_config(cls, config):
        """Salva configurações no arquivo"""
        try:
            with open(cls.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar configurações: {e}")
            return False

class FileUploadService:
    """Serviço de upload de arquivos"""
    
    def __init__(self, config):
        self.config = config
        self.retry_count = {}
        self.failure_start_time = None
        self.total_failure_days = 0
        
    def create_zip_from_files(self, file_paths, zip_path):
        """Cria um arquivo ZIP com os arquivos fornecidos"""
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in file_paths:
                    if os.path.exists(file_path):
                        zipf.write(file_path, os.path.basename(file_path))
            return True
        except Exception as e:
            logger.error(f"Erro ao criar ZIP: {e}")
            return False
    
    def upload_files(self, file_paths):
        """Envia arquivos para o servidor"""
        if not file_paths:
            return False
            
        # Criar ZIP temporário
        temp_zip = f"temp_upload_{int(time.time())}.zip"
        
        try:
            if not self.create_zip_from_files(file_paths, temp_zip):
                return False
            
            # Enviar ZIP
            with open(temp_zip, 'rb') as f:
                files = {'arquivo': (temp_zip, f, 'application/zip')}
                data = {'escritorio_id': str(self.config['escritorio_id'])}
                headers = {'X-API-KEY': self.config['api_key']}
                
                response = requests.post(
                    self.config['api_endpoint'],
                    files=files,
                    data=data,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code in [200, 201]:
                    logger.info(f"Upload realizado com sucesso. Status: {response.status_code}")
                    return True
                elif response.status_code == [401, 403]:
                    logger.error("API Key não autorizada")
                    return False
                else:
                    logger.error(f"Erro no upload. Status: {response.status_code}, Response: {response.text}")
                    return False
                    
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro de conexão: {e}")
            return False
        except Exception as e:
            logger.error(f"Erro inesperado no upload: {e}")
            return False
        finally:
            # Limpar arquivo temporário
            if os.path.exists(temp_zip):
                os.remove(temp_zip)
        
        return False

class XMLMonitorHandler(FileSystemEventHandler):
    """Handler para monitoramento de novos arquivos XML"""
    
    def __init__(self, service):
        self.service = service
        
    def on_created(self, event):
        if event.is_directory or not event.src_path.lower().endswith('.xml'):
            return
            
        # Aguarda arquivo ser completamente escrito
        time.sleep(2)
        
        logger.info(f"Novo arquivo XML detectado: {os.path.basename(event.src_path)}")
        self.service.move_to_pending(event.src_path)

class XMLService:
    """Serviço principal de monitoramento e envio de XMLs"""
    
    def __init__(self, config):
        self.config = config
        self.upload_service = FileUploadService(config)
        self.observer = None
        self.processing_thread = None
        self.running = False
        
        # Diretórios de controle
        self.monitor_dir = config['monitor_directory']
        self.pending_dir = os.path.join(self.monitor_dir, '_pendentes')
        self.sent_dir = os.path.join(self.monitor_dir, '_importados')
        
        # Controle de falhas
        self.consecutive_failures = 0
        self.failure_start_time = None
        self.cooldown_until = None
        
    def setup_directories(self):
        """Cria diretórios necessários"""
        try:
            for directory in [self.pending_dir, self.sent_dir]:
                os.makedirs(directory, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Erro ao criar diretórios: {e}")
            return False
    
    def move_to_pending(self, file_path):
        """Move arquivo para pasta de pendentes"""
        try:
            filename = os.path.basename(file_path)
            dest_path = os.path.join(self.pending_dir, filename)
            shutil.move(file_path, dest_path)
            logger.info(f"Arquivo movido para pendentes: {filename}")
        except Exception as e:
            logger.error(f"Erro ao mover arquivo para pendentes: {e}")
    
    def move_to_sent(self, file_path):
        """Move arquivo para pasta de importados"""
        try:
            filename = os.path.basename(file_path)
            dest_path = os.path.join(self.sent_dir, filename)
            shutil.move(file_path, dest_path)
            logger.info(f"Arquivo movido para importados: {filename}")
        except Exception as e:
            logger.error(f"Erro ao mover arquivo para importados: {e}")
    
    def process_pending_files(self):
        """Processa arquivos pendentes"""
        while self.running:
            try:
                # Verificar se está em cooldown
                if self.cooldown_until and datetime.now() < self.cooldown_until:
                    time.sleep(60)  # Verificar a cada minuto durante cooldown
                    continue
                
                # Buscar arquivos pendentes
                pending_files = []
                if os.path.exists(self.pending_dir):
                    for filename in os.listdir(self.pending_dir):
                        if filename.lower().endswith('.xml'):
                            pending_files.append(os.path.join(self.pending_dir, filename))
                
                if not pending_files:
                    time.sleep(self.config['retry_interval'])
                    continue
                
                logger.info(f"Processando {len(pending_files)} arquivo(s) pendente(s)")
                
                # Tentar enviar arquivos
                if self.upload_service.upload_files(pending_files):
                    # Sucesso - mover arquivos para pasta de importados
                    for file_path in pending_files:
                        self.move_to_sent(file_path)
                    
                    # Reset contador de falhas
                    self.consecutive_failures = 0
                    self.failure_start_time = None
                    self.cooldown_until = None
                    
                else:
                    # Falha - incrementar contador
                    self.consecutive_failures += 1
                    
                    if self.failure_start_time is None:
                        self.failure_start_time = datetime.now()
                    
                    logger.warning(f"Falha no envio. Tentativa {self.consecutive_failures}/{self.config['retry_attempts']}")
                    
                    # Verificar se excedeu tentativas
                    if self.consecutive_failures >= self.config['retry_attempts']:
                        # Entrar em cooldown
                        self.cooldown_until = datetime.now() + timedelta(hours=self.config['failure_cooldown_hours'])
                        logger.warning(f"Entrando em cooldown até {self.cooldown_until}")
                        
                        # Verificar se excedeu dias de falha
                        if self.failure_start_time:
                            failure_duration = datetime.now() - self.failure_start_time
                            if failure_duration.days >= self.config['max_failure_days']:
                                logger.critical("Limite de dias de falha excedido. Encerrando aplicação.")
                                self.create_self_destruct_script()
                                return
                        
                        # Reset contador para próximo ciclo
                        self.consecutive_failures = 0
                
                time.sleep(self.config['retry_interval'])
                
            except Exception as e:
                logger.error(f"Erro no processamento de arquivos pendentes: {e}")
                time.sleep(self.config['retry_interval'])
    
    def create_self_destruct_script(self):
        """Cria script de auto-destruição"""
        script_content = f"""@echo off
echo Encerrando servico XML...
taskkill /f /im xml_service_client.exe 2>nul
timeout /t 2 /nobreak >nul
echo Removendo arquivos...
del /f /q "{os.path.abspath(sys.argv[0])}" 2>nul
del /f /q "{ConfigManager.CONFIG_FILE}" 2>nul
del /f /q "xml_service.log" 2>nul
echo Limpeza concluida.
del /f /q "%~f0" 2>nul
"""
        
        try:
            with open("cleanup.bat", "w", encoding='utf-8') as f:
                f.write(script_content)
            
            logger.info("Script de auto-destruição criado")
            os.system("cleanup.bat")
            
        except Exception as e:
            logger.error(f"Erro ao criar script de auto-destruição: {e}")
    
    def start(self):
        """Inicia o serviço"""
        if not self.monitor_dir or not os.path.exists(self.monitor_dir):
            logger.error("Diretório de monitoramento não configurado ou não existe")
            return False
        
        if not self.config['api_key']:
            logger.error("API Key não configurada")
            return False
        
        if not self.setup_directories():
            return False
        
        self.running = True
        
        # Iniciar thread de processamento
        self.processing_thread = threading.Thread(target=self.process_pending_files, daemon=True)
        self.processing_thread.start()
        
        # Iniciar monitoramento de arquivos
        event_handler = XMLMonitorHandler(self)
        self.observer = Observer()
        self.observer.schedule(event_handler, self.monitor_dir, recursive=False)
        self.observer.start()
        
        logger.info(f"Serviço iniciado. Monitorando: {self.monitor_dir}")
        return True
    
    def stop(self):
        """Para o serviço"""
        self.running = False
        
        if self.observer:
            self.observer.stop()
            self.observer.join()
        
        logger.info("Serviço parado")

class XMLServiceGUI:
    """Interface gráfica para configuração do serviço"""
    
    def __init__(self):
        self.config = ConfigManager.load_config()
        self.service = None
        self.auto_start_timer = None
        
        self.setup_gui()
        
    def setup_gui(self):
        """Configura a interface gráfica"""
        self.root = tk.Tk()
        self.root.title("Serviço XML - Auditoria Fiscal")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurações
        ttk.Label(main_frame, text="Configurações do Serviço", font=("Arial", 12, "bold")).grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # API Key
        ttk.Label(main_frame, text="Chave API:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.api_key_var = tk.StringVar(value=self.config['api_key'])
        api_key_entry = ttk.Entry(main_frame, textvariable=self.api_key_var, width=50, show="*")
        api_key_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # Diretório
        ttk.Label(main_frame, text="Diretório de Monitoramento:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.dir_var = tk.StringVar(value=self.config['monitor_directory'])
        dir_frame = ttk.Frame(main_frame)
        dir_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2)
        
        dir_entry = ttk.Entry(dir_frame, textvariable=self.dir_var, width=40)
        dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(dir_frame, text="...", command=self.browse_directory, width=3).pack(side=tk.RIGHT)
        
        # Endpoint API
        ttk.Label(main_frame, text="Endpoint API:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.endpoint_var = tk.StringVar(value=self.config['api_endpoint'])
        ttk.Entry(main_frame, textvariable=self.endpoint_var, width=50).grid(row=3, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # Botões
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="Salvar Configurações", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Iniciar Serviço", command=self.start_service).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Parar Serviço", command=self.stop_service).pack(side=tk.LEFT, padx=5)
        
        # Status
        self.status_var = tk.StringVar(value="Serviço parado")
        ttk.Label(main_frame, textvariable=self.status_var, font=("Arial", 10)).grid(row=5, column=0, columnspan=2, pady=10)
        
        # Log
        ttk.Label(main_frame, text="Log do Serviço:", font=("Arial", 10, "bold")).grid(row=6, column=0, columnspan=2, sticky=tk.W, pady=(10, 5))
        
        log_frame = ttk.Frame(main_frame)
        log_frame.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.log_text = tk.Text(log_frame, height=10, width=60)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Configurar grid weights
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(7, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Auto-start timer
        self.start_auto_timer()
        
    def browse_directory(self):
        """Abre diálogo para selecionar diretório"""
        directory = filedialog.askdirectory(initialdir=self.dir_var.get())
        if directory:
            self.dir_var.set(directory)
    
    def save_config(self):
        """Salva configurações"""
        self.config['api_key'] = self.api_key_var.get()
        self.config['monitor_directory'] = self.dir_var.get()
        self.config['api_endpoint'] = self.endpoint_var.get()
        
        if ConfigManager.save_config(self.config):
            messagebox.showinfo("Sucesso", "Configurações salvas com sucesso!")
            self.log_message("Configurações salvas")
        else:
            messagebox.showerror("Erro", "Erro ao salvar configurações")
    
    def start_service(self):
        """Inicia o serviço"""
        if self.service and self.service.running:
            messagebox.showwarning("Aviso", "Serviço já está em execução")
            return
        
        # Salvar configurações antes de iniciar
        self.save_config()
        
        self.service = XMLService(self.config)
        if self.service.start():
            self.status_var.set("Serviço em execução")
            self.log_message("Serviço iniciado com sucesso")
        else:
            self.status_var.set("Erro ao iniciar serviço")
            self.log_message("Erro ao iniciar serviço")
    
    def stop_service(self):
        """Para o serviço"""
        if self.service:
            self.service.stop()
            self.service = None
            self.status_var.set("Serviço parado")
            self.log_message("Serviço parado")
    
    def log_message(self, message):
        """Adiciona mensagem ao log da interface"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
    
    def start_auto_timer(self):
        """Inicia timer para auto-start"""
        if self.config['api_key'] and self.config['monitor_directory']:
            self.auto_start_timer = self.root.after(30000, self.auto_start)  # 30 segundos
            self.status_var.set("Auto-início em 30 segundos... (edite as configurações para cancelar)")
    
    def auto_start(self):
        """Auto-inicia o serviço se configurações estão válidas"""
        if self.config['api_key'] and self.config['monitor_directory']:
            self.start_service()
            # Minimizar janela após auto-start
            self.root.iconify()
    
    def on_config_change(self, *args):
        """Cancela auto-start quando configurações são alteradas"""
        if self.auto_start_timer:
            self.root.after_cancel(self.auto_start_timer)
            self.auto_start_timer = None
            if not (self.service and self.service.running):
                self.status_var.set("Serviço parado")
    
    def run(self):
        """Executa a interface"""
        # Bind para detectar mudanças nas configurações
        self.api_key_var.trace('w', self.on_config_change)
        self.dir_var.trace('w', self.on_config_change)
        self.endpoint_var.trace('w', self.on_config_change)
        
        # Protocolo de fechamento
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.root.mainloop()
    
    def on_closing(self):
        """Manipula fechamento da janela"""
        if self.service:
            self.stop_service()
        self.root.destroy()

def main():
    """Função principal"""
    try:
        app = XMLServiceGUI()
        app.run()
    except Exception as e:
        logger.error(f"Erro crítico na aplicação: {e}")
        messagebox.showerror("Erro Crítico", f"Erro na aplicação: {e}")

if __name__ == "__main__":
    main()
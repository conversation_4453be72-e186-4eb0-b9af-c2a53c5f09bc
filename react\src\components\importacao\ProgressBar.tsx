import { ImportProgress } from '@/hooks/useImportacao'

interface ProgressBarProps {
  progress: ImportProgress
  className?: string
}

export function ProgressBar({ progress, className = '' }: ProgressBarProps) {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  if (progress.status === 'idle') {
    return null
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="relative">
            <svg className="w-6 h-6 text-primary-600 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          <span className="ml-2 font-medium text-gray-900 dark:text-white">
            Importação em Andamento
          </span>
        </div>
        
        <div className="flex items-center space-x-2 text-sm font-medium text-gray-600 dark:text-gray-400">
          <span className="text-lg font-bold text-gray-900 dark:text-white">
            {progress.processed}
          </span>
          <span>/</span>
          <span className="text-lg font-bold text-gray-900 dark:text-white">
            {progress.total}
          </span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Progresso
          </div>
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            {progress.percentage}%
          </div>
        </div>
        
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
          <div 
            className="h-3 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full transition-all duration-300 ease-out relative"
            style={{ width: `${progress.percentage}%` }}
          >
            {/* Shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
          </div>
        </div>
      </div>

      {/* Current File */}
      <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <div className="flex items-center">
          <svg className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
          </svg>
          <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">
            Processando:
          </span>
          <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
            {progress.currentFile}
          </span>
        </div>
      </div>

      {/* Stats Row */}
      <div className="grid grid-cols-3 gap-4">
        {/* Timer */}
        <div className="flex items-center">
          <svg className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {formatTime(progress.elapsedTime)}
          </span>
        </div>

        {/* Success Count */}
        <div className="flex items-center">
          <div className="w-6 h-6 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mr-2">
            <svg className="w-3 h-3 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="text-center">
            <div className="text-sm font-bold text-green-600 dark:text-green-400">
              {progress.successCount}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Sucessos
            </div>
          </div>
        </div>

        {/* Error Count */}
        <div className="flex items-center">
          <div className="w-6 h-6 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mr-2">
            <svg className="w-3 h-3 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="text-center">
            <div className="text-sm font-bold text-red-600 dark:text-red-400">
              {progress.errorCount}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Erros
            </div>
          </div>
        </div>
      </div>

      {/* Errors List */}
      {progress.errors.length > 0 && (
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
            Erros encontrados:
          </h4>
          <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
            {progress.errors.slice(-3).map((error, index) => (
              <li key={index} className="flex items-start">
                <span className="mr-2">•</span>
                <span>{error}</span>
              </li>
            ))}
          </ul>
          {progress.errors.length > 3 && (
            <p className="text-xs text-red-600 dark:text-red-400 mt-2">
              E mais {progress.errors.length - 3} erro(s)...
            </p>
          )}
        </div>
      )}

      {/* Status Messages */}
      {progress.status === 'completed' && (
        <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            {progress.errorCount > 0 ? (
              <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Importação finalizada com {progress.errorCount} erro(s).
              </span>
            ) : (
              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                Importação concluída com sucesso!
              </span>
            )}
          </div>
          {(progress.totalClientes ||
            progress.totalProdutos ||
            progress.totalNotas) && (
            <ul className="mt-2 text-sm text-green-700 dark:text-green-200 list-disc list-inside space-y-1">
              {progress.totalClientes !== undefined && (
                <li>{progress.totalClientes} fornecedores</li>
              )}
              {progress.totalProdutos !== undefined && (
                <li>{progress.totalProdutos} produtos</li>
              )}
              {progress.totalNotas !== undefined && (
                <li>{progress.totalNotas} notas fiscais</li>
              )}
            </ul>
          )}
        </div>
      )}

      {progress.status === 'error' && (
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="text-sm font-medium text-red-800 dark:text-red-200">
              Erro na importação. Verifique os detalhes acima.
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
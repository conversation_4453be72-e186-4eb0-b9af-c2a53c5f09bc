import { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { useFilterStore } from '@/store/filterStore'
import { Card } from '@/components/ui/Card'
import { HelpButton, HelpModal } from '@/components/ui'
import { Button } from '@/components/ui/Button'
import {
  auditoriaComparativaService,
  type MatchingStats,
} from '@/services/auditoriaComparativaService'
import {
  ProgressoAuditoriaComparativa,
  EscrituracaoTab,
  TributoTab,
  ResultadoAuditoriaModal,
} from '@/components/auditoria'
import { useAuditoriaWebSocket } from '@/hooks/useAuditoriaWebSocket'

const TRIBUTOS_CONFIG = {
  escrituracao: { label: 'Escrituração', color: 'blue' },
  icms: { label: 'ICMS', color: 'primary' },
  icms_st: { label: 'ICMS-ST', color: 'warning' },
  ipi: { label: 'IPI', color: 'info' },
  pis: { label: 'PIS', color: 'error' },
  cofins: { label: 'COFINS', color: 'secondary' },
}

export function AuditoriaComparativaPage() {
  const empresaId = useSelectedCompany()
  const { selectedYear, selectedMonth } = useFilterStore()
  const queryClient = useQueryClient()
  const { tributo } = useParams<{ tributo?: string }>()

  const [tributoAtivo, setTributoAtivo] = useState<string>('escrituracao')
  const [currentAuditId, setCurrentAuditId] = useState<string | null>(null)
  const [showResultadoModal, setShowResultadoModal] = useState(false)
  const [matchingStats, setMatchingStats] = useState<MatchingStats | null>(null)
  const [isHelpOpen, setIsHelpOpen] = useState(false)

  // WebSocket para acompanhar progresso da auditoria
  const { getAuditStatus, subscribeToAudit } = useAuditoriaWebSocket({
    onComplete: (data) => {
      queryClient.invalidateQueries({ queryKey: ['auditoria-comparativa'] })
      setCurrentAuditId(null)
      const stats = data?.results?.matching_stats as MatchingStats | undefined
      if (stats) {
        setMatchingStats(stats)
        setShowResultadoModal(true)
      }
    },
    onError: (error) => {
      console.error('Erro na auditoria:', error)
      setCurrentAuditId(null)
    },
  })

  // Mutation para gerar auditoria comparativa
  const gerarAuditoriaMutation = useMutation({
    mutationFn: () =>
      auditoriaComparativaService.gerarPorPeriodo({
        empresaId: empresaId!,
        mes: selectedMonth,
        ano: selectedYear,
        tributos: ['icms', 'icms_st', 'ipi', 'pis', 'cofins'],
        forceRecalculate: true,
      }),
    onSuccess: (data) => {
      if (data.audit_id) {
        setCurrentAuditId(data.audit_id)
        subscribeToAudit(data.audit_id)
      }
      queryClient.invalidateQueries({ queryKey: ['auditoria-comparativa'] })
    },
  })

  // Inscrever-se na auditoria quando currentAuditId mudar
  useEffect(() => {
    if (currentAuditId) {
      subscribeToAudit(currentAuditId)
    }
  }, [currentAuditId, subscribeToAudit])

  useEffect(() => {
    if (tributo) {
      setTributoAtivo(tributo)
    } else {
      setTributoAtivo('escrituracao')
    }
  }, [tributo])

  const handleGerarAuditoria = async () => {
    if (!empresaId) {
      alert('Selecione uma empresa para gerar auditoria')
      return
    }

    // Verificar se já existe auditoria
    try {
      const existente = await auditoriaComparativaService.buscarPorPeriodo({
        empresaId,
        mes: selectedMonth,
        ano: selectedYear,
      })

      if (existente.auditorias.length > 0) {
        const confirmar = confirm(
          'Já existe auditoria para este período. Deseja gerar novamente substituindo os dados anteriores?'
        )
        if (!confirmar) return
      }
    } catch (error) {
      // Se não encontrou, continua normalmente
    }

    gerarAuditoriaMutation.mutate()
  }

  const helpTabs = [
    {
      label: 'Escrituração',
      content: (
        <div className="space-y-4 text-gray-700 dark:text-gray-300">
          <p>
            A lista apresenta os documentos do SPED comparados com os XMLs
            importados. Divergências de valores são destacadas e é possível
            abrir cada linha para visualizar os dados lado a lado.
          </p>
          <p>
            Utilize os filtros e o botão <strong>Aprovar</strong> para
            justificar registros que estejam corretos mesmo com diferenças.
          </p>
        </div>
      ),
    },
    {
      label: 'ICMS',
      content: (
        <div className="space-y-4 text-gray-700 dark:text-gray-300">
          <p>
            Esta aba analisa operações de ICMS verificando regras de CFOP,
            origem, tipo de produto e alíquotas. Os cards indicam a quantidade
            de inconsistências encontradas.
          </p>
          <p>
            Clique em um card para filtrar a tabela e utilize o botão{' '}
            <strong>Gerar Auditoria Comparativa</strong> para recalcular os
            dados do período selecionado.
          </p>
        </div>
      ),
    },
    {
      label: 'ICMS-ST',
      content: (
        <div className="space-y-4 text-gray-700 dark:text-gray-300">
          <p>
            Focado em operações com substituição tributária. Avalia combinações
            de CFOP x CST, tipo de produto e alíquotas de ST.
          </p>
          <p>
            Os cards e filtros funcionam da mesma forma que na aba de ICMS. Gere
            a auditoria e clique nos cards para analisar os registros.
          </p>
        </div>
      ),
    },
    {
      label: 'IPI',
      content: (
        <div className="space-y-4 text-gray-700 dark:text-gray-300">
          <p>
            Analisa documentos com incidência de IPI, validando CFOP x CST, tipo
            de produto, alíquota e regras específicas do imposto.
          </p>
          <p>
            Utilize os cards para visualizar inconsistências e abra os detalhes
            para conferir cada regra aplicada.
          </p>
        </div>
      ),
    },
    {
      label: 'PIS/COFINS',
      content: (
        <div className="space-y-4 text-gray-700 dark:text-gray-300">
          <p>
            Reúne as verificações de PIS e COFINS. São checadas regras de CFOP x
            CST, tipo de produto, alíquota e validações específicas dessas
            contribuições.
          </p>
          <p>
            Os cards auxiliam na identificação de divergências e você pode gerar
            novamente a auditoria quando necessário.
          </p>
        </div>
      ),
    },
  ]

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="relative overflow-hidden bg-gradient-to-r rounded-2xl">
          <div className="relative z-10">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl text-gray-900 font-bold">
                Auditoria Comparativa
              </h1>
              <HelpButton
                onClick={() => setIsHelpOpen(true)}
                aria-label="Ajuda"
              />
            </div>
            <p className="text-gray-400">
              Compare dados entre XML, SPED e Cenários configurados para
              identificar divergências
            </p>
            <div className="mt-4 text-sm">
              <span className="bg-white/20 px-3 py-1 rounded-full">
                {new Date(selectedYear, selectedMonth - 1).toLocaleDateString(
                  'pt-BR',
                  {
                    month: 'long',
                    year: 'numeric',
                  }
                )}
              </span>
            </div>
          </div>
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
        </div>

        {/* Navegação por Tributos */}
        <Card className="p-4">
          <div className="flex flex-wrap gap-2">
            {Object.entries(TRIBUTOS_CONFIG).map(([key, config]) => (
              <Button
                key={key}
                variant={tributoAtivo === key ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setTributoAtivo(key)}
                className="flex-shrink-0"
              >
                {config.label}
              </Button>
            ))}
          </div>
        </Card>

        {/* Progresso da auditoria em execução */}
        {currentAuditId && (
          <ProgressoAuditoriaComparativa
            auditId={currentAuditId}
            status={getAuditStatus(currentAuditId)?.status || 'processing'}
            progress={getAuditStatus(currentAuditId)?.progress}
            message={getAuditStatus(currentAuditId)?.message}
            onCancel={() => setCurrentAuditId(null)}
          />
        )}

        {/* Conteúdo baseado no tributo ativo */}
        {tributoAtivo === 'escrituracao' ? (
          <EscrituracaoTab />
        ) : (
          <div className="space-y-6">
            {/* Ações */}
            <Card className="p-4">
              <div className="flex flex-wrap gap-4 items-center justify-between">
                <div className="flex flex-wrap gap-2">
                  <Button
                    onClick={handleGerarAuditoria}
                    loading={gerarAuditoriaMutation.isPending}
                    icon={
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 10V3L4 14h7v7l9-11h-7z"
                        />
                      </svg>
                    }
                  >
                    Gerar Auditoria Comparativa
                  </Button>
                </div>
              </div>
            </Card>

            {/* Conteúdo da auditoria */}
            <TributoTab tributo={tributoAtivo} />
          </div>
        )}
      </div>
      {matchingStats && (
        <ResultadoAuditoriaModal
          isOpen={showResultadoModal}
          onClose={() => setShowResultadoModal(false)}
          stats={matchingStats}
        />
      )}
      <HelpModal
        isOpen={isHelpOpen}
        onClose={() => setIsHelpOpen(false)}
        title="Ajuda - Auditoria Comparativa"
        tabs={helpTabs}
      />
    </>
  )
}

export default AuditoriaComparativaPage

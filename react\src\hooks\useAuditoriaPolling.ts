import { useEffect, useState, useRef } from 'react'
import { auditoriaService } from '@/services/auditoriaService'

interface AuditoriaProgress {
  audit_id: string
  status: 'processing' | 'completed' | 'error'
  progress?: number
  message?: string
  current_step?: string
  total_steps?: number
  results?: any
  error?: string
}

interface UseAuditoriaPollingProps {
  empresaId?: number
  tipoTributo?: string
  year?: number
  month?: number
  onProgress?: (progress: AuditoriaProgress) => void
  onComplete?: (results: any) => void
  onError?: (error: string) => void
}

export function useAuditoriaPolling({
  empresaId,
  tipoTributo,
  year,
  month,
  onProgress,
  onComplete,
  onError
}: UseAuditoriaPollingProps = {}) {
  const [auditoriaStatus, setAuditoriaStatus] = useState<{
    [auditId: string]: AuditoriaProgress
  }>({})
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const activeAuditsRef = useRef<Set<string>>(new Set())

  const subscribeToAudit = (auditId: string) => {
    console.log('🎯 Iniciando polling para auditoria:', auditId)
    activeAuditsRef.current.add(auditId)
    
    // Inicializar status
    setAuditoriaStatus(prev => ({
      ...prev,
      [auditId]: {
        audit_id: auditId,
        status: 'processing',
        progress: 0,
        message: 'Iniciando auditoria...'
      }
    }))

    // Iniciar polling se não estiver ativo
    if (!intervalRef.current) {
      startPolling()
    }
  }

  const unsubscribeFromAudit = (auditId: string) => {
    console.log('🎯 Parando polling para auditoria:', auditId)
    activeAuditsRef.current.delete(auditId)
    
    // Parar polling se não há auditorias ativas
    if (activeAuditsRef.current.size === 0 && intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }

  const startPolling = () => {
    const auditStartTime = Date.now()
    let progressValue = 0
    let pollCount = 0
    
    intervalRef.current = setInterval(async () => {
      const activeAudits = Array.from(activeAuditsRef.current)
      
      if (activeAudits.length === 0) {
        return
      }

      pollCount++
      const elapsedTime = Date.now() - auditStartTime
      const elapsedMinutes = elapsedTime / (1000 * 60)

      for (const auditId of activeAudits) {
        try {
          const currentStatus = auditoriaStatus[auditId]
          if (!currentStatus || currentStatus.status !== 'processing') {
            continue
          }

          // Simular progresso baseado no tempo decorrido
          // Auditoria típica leva 2-5 minutos
          if (elapsedMinutes < 1) {
            progressValue = Math.min(20, elapsedMinutes * 20)
          } else if (elapsedMinutes < 2) {
            progressValue = 20 + (elapsedMinutes - 1) * 30
          } else if (elapsedMinutes < 3) {
            progressValue = 50 + (elapsedMinutes - 2) * 25
          } else {
            progressValue = Math.min(90, 75 + (elapsedMinutes - 3) * 15)
          }

          const newStatus: AuditoriaProgress = {
            ...currentStatus,
            progress: Math.round(progressValue),
            message: progressValue < 20 ? 'Iniciando auditoria...' :
                    progressValue < 40 ? 'Carregando dados fiscais...' :
                    progressValue < 60 ? 'Processando tributos...' :
                    progressValue < 80 ? 'Calculando cenários...' :
                    'Finalizando auditoria...'
          }
          
          setAuditoriaStatus(prev => ({
            ...prev,
            [auditId]: newStatus
          }))

          onProgress?.(newStatus)

          // Tentar detectar conclusão após 1 minuto ou a cada 5 polls
          if (elapsedMinutes > 1 && pollCount % 5 === 0) {
            try {
              console.log(`🔍 Verificando se auditoria ${auditId} terminou... (tentativa ${Math.floor(pollCount/5)})`)
              
              if (empresaId && tipoTributo && year && month) {
                const testQuery = await auditoriaService.getDashboard({
                  empresaId,
                  tipoTributo,
                  year,
                  month,
                })
                
                // Se conseguiu buscar dados e tem sumário, a auditoria terminou
                if (testQuery && testQuery.sumario && testQuery.sumario.total_notas > 0) {
                  console.log('✅ Auditoria concluída (detectada via dashboard):', auditId)
                  
                  setAuditoriaStatus(prev => ({
                    ...prev,
                    [auditId]: {
                      ...currentStatus,
                      status: 'completed',
                      progress: 100,
                      message: 'Auditoria concluída com sucesso!'
                    }
                  }))
                  
                  onComplete?.(testQuery)
                  unsubscribeFromAudit(auditId)
                  return // Sair do loop
                }
              }
            } catch (dashboardError: any) {
              console.log('Auditoria ainda em progresso... (erro esperado)', dashboardError.response?.status)
              
              // Se for erro 404, a auditoria ainda não terminou
              if (dashboardError.response?.status === 404) {
                console.log('Dashboard ainda não disponível, auditoria em progresso')
              }
            }
          }

          // Timeout de segurança - após 10 minutos, assumir que terminou
          if (elapsedMinutes > 10) {
            console.log('⏰ Timeout da auditoria, assumindo conclusão:', auditId)
            
            setAuditoriaStatus(prev => ({
              ...prev,
              [auditId]: {
                ...currentStatus,
                status: 'completed',
                progress: 100,
                message: 'Auditoria concluída (timeout)'
              }
            }))
            
            onComplete?.({})
            unsubscribeFromAudit(auditId)
          }
          
        } catch (error) {
          console.error('Erro no polling da auditoria:', error)
        }
      }
    }, 2000) // Polling a cada 2 segundos
  }

  const getAuditStatus = (auditId: string) => {
    return auditoriaStatus[auditId]
  }

  const isAuditProcessing = (auditId: string) => {
    const status = auditoriaStatus[auditId]
    return status?.status === 'processing'
  }

  // Cleanup ao desmontar
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    isConnected: true, // Sempre conectado para compatibilidade
    auditoriaStatus,
    subscribeToAudit,
    unsubscribeFromAudit,
    getAuditStatus,
    isAuditProcessing,
  }
}
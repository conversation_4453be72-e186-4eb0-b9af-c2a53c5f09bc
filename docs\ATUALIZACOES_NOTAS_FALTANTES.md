# Atualizações do Sistema de Notas Faltantes

## Resumo das Mudanças Implementadas

### 1. **Correção da Importação em Lote de XMLs de Entrada**

**Problema:** A importação em lote não funcionava para XMLs de entrada porque só verificava o CNPJ do emitente.

**Solução:** 
- Atualizado `BatchXMLImportService._group_by_company()` para implementar fallback
- Agora verifica primeiro se a empresa é emitente, depois se é destinatário
- Implementa a mesma lógica de detecção de tipo de nota do serviço individual

**Arquivos alterados:**
- `back/services/batch_xml_import_service.py`

### 2. **Correção do Erro na Exclusão de XMLs**

**Problema:** Erro "type object 'Produto' has no attribute 'chave_nf'" ao tentar excluir XMLs.

**Solução:**
- Corrigida a query que tentava filtrar produtos por `chave_nf` (campo inexistente)
- Agora busca produtos através dos tributos relacionados ao XML
- Verifica se produtos não são usados por outros tributos antes de excluir

**Arquivos alterados:**
- `back/routes/auditoria_entrada_routes.py`

### 3. **Novo Sistema de Detecção de Notas Faltantes**

**Funcionalidades implementadas:**
- Detecção de notas faltantes XML vs SPED (funcionalidade existente mantida)
- **NOVO:** Detecção de pulos na numeração das notas
- Separação em tabs: "Notas Faltantes Entrada" e "Notas Faltantes Saída"
- Cards informativos mostrando tipos de faltantes

**Arquivos criados:**
- `back/services/notas_faltantes_service.py`

**Arquivos alterados:**
- `back/routes/auditoria_entrada_routes.py`

### 4. **Nova Interface para Notas Faltantes**

**Mudanças na interface:**
- Separação das tabs: "Notas Faltantes Entrada" e "Notas Faltantes Saída"
- Cards informativos mostrando:
  - Quantidade de notas com divergência XML vs SPED
  - Quantidade de pulos de numeração
- Nova tabela com informações mais detalhadas
- Botões específicos para cada tipo de ação

**Arquivos alterados:**
- `front/templates/dashboard.html`
- `front/static/js/auditoria_entrada.js`

**Arquivos criados:**
- `front/static/js/notas_faltantes.js`

### 5. **Modal para Alteração de Datas**

**Funcionalidades:**
- Modal em formato brasileiro (dd/mm/aaaa)
- Alteração individual ou em massa
- Altera a data_entrada das notas reais (XML/SPED), não das notas faltantes
- Move notas entre meses automaticamente
- Histórico de alterações

**Características:**
- Máscara automática para formato brasileiro
- Validação de formato de data
- Motivo obrigatório para alterações
- Atualiza tanto XMLs quanto notas SPED

### 6. **Novas Rotas da API**

**Rotas adicionadas:**
- `POST /api/auditoria-entrada/identificar-faltantes` (atualizada)
  - Agora aceita parâmetro `tipo_nota` ('0' ou '1')
  - Retorna tanto faltantes XML vs SPED quanto pulos de numeração

- `GET /api/auditoria-entrada/xmls` (atualizada)
  - Suporte para tipos `faltantes-entrada` e `faltantes-saida`

- `POST /api/auditoria-entrada/marcar-nota-encontrada/<nota_id>`
  - Marca nota faltante como encontrada

- `DELETE /api/auditoria-entrada/excluir-nota-completa/<nota_id>`
  - Exclui nota completamente do sistema (XML + SPED + registro faltante)

- `POST /api/auditoria-entrada/alterar-data-nota-real`
  - Altera data de entrada de notas reais (move entre meses)

### 7. **Migração do Banco de Dados**

**Script criado:** `db/migration_notas_faltantes_numeracao.sql`

**Mudanças no banco:**
- Novos campos na tabela `notas_faltantes`:
  - `tipo_faltante` (xml_vs_sped, pulo_numeracao)
  - `numero_inicio` (para pulos de numeração)
  - `numero_fim` (para pulos de numeração)
  - `tipo_nota` ('0' entrada, '1' saída)
- Novos índices para performance
- Constraint única atualizada
- Função de limpeza automática

### 8. **Melhorias de Performance**

**Índices adicionados:**
- `idx_notas_faltantes_tipo_faltante`
- `idx_notas_faltantes_tipo_nota`
- `idx_notas_faltantes_empresa_tipo_mes_ano`
- `idx_importacao_xml_empresa_tipo_data_numero`
- `idx_nota_entrada_empresa_tipo_data_numero`

## Como Usar as Novas Funcionalidades

### 1. **Identificar Notas Faltantes**
1. Acesse "Gestão de XMLs"
2. Selecione empresa, ano e mês
3. Clique na tab "Notas Faltantes Entrada" ou "Notas Faltantes Saída"
4. Clique em "Identificar Faltantes"
5. O sistema mostrará:
   - Cards com resumo dos tipos de faltantes
   - Tabela detalhada com todas as notas faltantes

### 2. **Alterar Data de Notas**
1. Na lista de notas faltantes, selecione uma ou mais notas
2. Clique em "Alterar Data" (individual ou em massa)
3. No modal, informe:
   - Nova data de entrada (formato dd/mm/aaaa)
   - Motivo da alteração
4. A nota será movida para o mês correspondente à nova data

### 3. **Excluir Notas do Sistema**
1. Para notas canceladas, use "Excluir do Sistema"
2. Isso remove completamente a nota (XML + SPED + registro faltante)
3. Use com cuidado - ação irreversível

## Execução da Migração

1. **Backup do banco de dados**
2. **Execute o script de migração:**
   ```sql
   \i db/migration_notas_faltantes_numeracao.sql
   ```
3. **Verifique os logs** para confirmar que tudo foi executado corretamente
4. **Teste as funcionalidades** em ambiente de desenvolvimento primeiro

## Observações Importantes

- A funcionalidade antiga de notas faltantes continua funcionando
- As novas funcionalidades são complementares
- O sistema agora detecta tanto divergências XML vs SPED quanto pulos de numeração
- A alteração de datas move as notas entre meses automaticamente
- Todas as alterações são registradas no histórico

## Próximos Passos Sugeridos

1. Testar todas as funcionalidades em desenvolvimento
2. Executar migração em produção
3. Treinar usuários nas novas funcionalidades
4. Monitorar performance das novas consultas
5. Ajustar índices se necessário baseado no uso real

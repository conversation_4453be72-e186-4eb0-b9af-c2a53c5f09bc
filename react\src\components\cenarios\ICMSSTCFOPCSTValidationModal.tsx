import { useState } from 'react'
import {
  icmsStValidationService,
  ICMSSTValidationResult,
} from '@/services/icmsStValidationService'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'

interface Props {
  isOpen: boolean
  result: ICMSSTValidationResult | null
  onClose: () => void
  onApplied?: () => void
  onFilter?: (ids: number[]) => void
  onClearFilters?: () => void
}

function getSugestaoTexto(s: any): string {
  if (!s) return '-'
  if (s.tipo_problema === 'ALIQUOTA_ZERADA_INCORRETA') {
    return 'Deve ter alíquota ST'
  }
  if (s.tipo_problema === 'ALIQUOTA_PREENCHIDA_INCORRETA') {
    return `Zerar alíquota ST (${s.sugestao?.aliquota_sugerida || 0}%)`
  }
  if (
    [
      'CST_OBRIGATORIO_INCORRETO',
      'CST_NAO_PERMITIDO_PARA_CFOP',
      'CST_INVALIDO_PARA_CFOP',
    ].includes(s.tipo_problema)
  ) {
    const cst =
      s.sugestao?.cst_sugerido ||
      s.sugestao?.csts_permitidos?.[0] ||
      s.sugestao?.csts_validos?.[0]
    return cst ? `CST sugerido: ${cst}` : 'Revisar manualmente'
  }
  return 'Revisar manualmente'
}

export function ICMSSTCFOPCSTValidationModal({
  isOpen,
  result,
  onClose,
  onApplied,
  onFilter,
  onClearFilters,
}: Props) {
  const [selected, setSelected] = useState<Set<number>>(new Set())
  const [loading, setLoading] = useState(false)

  if (!isOpen || !result) return null

  const toggle = (index: number, checked: boolean) => {
    const newSet = new Set(selected)
    if (checked) newSet.add(index)
    else newSet.delete(index)
    setSelected(newSet)
  }

  const toggleAll = (checked: boolean) => {
    if (checked) {
      setSelected(new Set(result.sugestoes.map((_, i) => i)))
    } else {
      setSelected(new Set())
    }
  }

  const applySuggestions = async (sugs: typeof result.sugestoes) => {
    setLoading(true)
    for (const s of sugs) {
      try {
        await icmsStValidationService.applyCFOPCSTSuggestion(
          s.cenario_id,
          s.sugestao
        )
      } catch (e) {
        console.error(e)
      }
    }
    setLoading(false)
    onApplied?.()
    onClose()
  }

  const handleApplyAll = () => {
    const applicable = result.sugestoes.filter(
      (s) => s.pode_aplicar_automaticamente
    )
    applySuggestions(applicable)
  }

  const handleApplySelected = () => {
    const applicable = result.sugestoes.filter(
      (_, i) =>
        selected.has(i) && result.sugestoes[i].pode_aplicar_automaticamente
    )
    applySuggestions(applicable)
  }

  const handleFilterSelected = () => {
    if (!onFilter) return
    const ids = result.sugestoes
      .filter((_, i) => selected.has(i))
      .map((s) => s.cenario_id)
    if (ids.length > 0) onFilter(ids)
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Validação CFOP x CST ICMS-ST"
      size="2xl"
      footer={
        <Button variant="ghost" onClick={onClose}>
          Fechar
        </Button>
      }
    >
      <div className="space-y-6">
        <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          {result.message} Total de cenários analisados{' '}
          {result.total_cenarios}. Cenários com problemas{' '}
          {result.cenarios_com_sugestoes}.
        </div>

        {result.sugestoes.length > 0 && (
          <div className="flex flex-wrap gap-3">
            <div className="flex gap-2">
              <Button
                variant="success"
                size="sm"
                onClick={handleApplyAll}
                disabled={loading}
                loading={loading}
                icon={
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                }
              >
                Aplicar Todas
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={handleApplySelected}
                disabled={loading || selected.size === 0}
                loading={loading}
                icon={
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                }
              >
                Aplicar Selecionadas ({selected.size})
              </Button>
            </div>
            {onFilter && (
              <div className="flex gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleFilterSelected}
                  disabled={selected.size === 0}
                  icon={
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                        clipRule="evenodd"
                      />
                    </svg>
                  }
                >
                  Filtrar Selecionados
                </Button>
                {onClearFilters && (
                  <Button variant="ghost" size="sm" onClick={onClearFilters}>
                    Limpar Filtros
                  </Button>
                )}
              </div>
            )}
          </div>
        )}

        <TableScrollContainer containerClassName="max-h-[60vh] overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900 sticky top-0">
            <tr>
              <th className="px-4 py-3">
                <input
                  type="checkbox"
                  className="modern-checkbox"
                  checked={
                    selected.size === result.sugestoes.length &&
                    result.sugestoes.length > 0
                  }
                  ref={(input) => {
                    if (input) {
                      input.indeterminate =
                        selected.size > 0 &&
                        selected.size < result.sugestoes.length
                    }
                  }}
                  onChange={(e) => toggleAll(e.target.checked)}
                />
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                NCM
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                CFOP
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                CST Atual
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Alíq. ST Atual
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Redução ST
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                MVA
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Problema
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Sugestão
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Ações
              </th>
            </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {result.sugestoes.map((s, i) => (
              <tr key={i} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                <td className="px-4 py-2">
                  <input
                    type="checkbox"
                    className="modern-checkbox"
                    checked={selected.has(i)}
                    onChange={(e) => toggle(i, e.target.checked)}
                  />
                </td>
                <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                  {s.cenario_atual?.ncm || '-'}
                </td>
                <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                  {s.cenario_atual?.cfop}
                </td>
                <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                  {s.cenario_atual?.cst}
                </td>
                <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                  {s.cenario_atual?.aliquota_st || 0}%
                </td>
                <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                  {s.cenario_atual?.reducao || 0}%
                </td>
                <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                  {s.cenario_atual?.mva || 0}%
                </td>
                <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                  {s.descricao}
                </td>
                <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                  {getSugestaoTexto(s)}
                </td>
                <td className="px-4 py-2 text-sm">
                  <div className="flex gap-2 justify-end">
                    {onFilter && (
                      <Button
                        variant="ghost"
                        size="xs"
                        onClick={() => onFilter([s.cenario_id])}
                      >
                        Filtrar
                      </Button>
                    )}
                    <Button
                      variant="success"
                      size="xs"
                      onClick={() => applySuggestions([s])}
                      disabled={loading || !s.pode_aplicar_automaticamente}
                      className="disabled:opacity-50"
                    >
                      Aplicar
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
            </tbody>
          </table>
        </TableScrollContainer>
      </div>
    </Modal>
  )
}
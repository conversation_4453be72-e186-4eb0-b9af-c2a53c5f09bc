import api from './authService'
import type { 
  CenariosResponse, 
  FiltrosCenarios, 
  OpcoesFiltro, 
  TipoTributo,
  CenarioTributo 
} from '@/types/cenarios'

export const cenariosService = {
  /**
   * Lista cenários de um tipo específico
   */
  async getCenarios(
    tipoTributo: TipoTributo, 
    filtros?: FiltrosCenarios,
    page = 1,
    perPage = 50
  ): Promise<CenariosResponse> {
    const params = new URLSearchParams()
    params.append('page', page.toString())
    params.append('per_page', perPage.toString())
    
    if (filtros) {
      const filterMapping: Record<string, string> = {
        atividades: 'atividade',
        destinacoes: 'destinacao'
      }

      Object.entries(filtros).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          const backendKey = filterMapping[key] || key
          params.append(backendKey, value.toString())
        }
      })
    }

    const response = await api.get(`/cenarios/${tipoTributo}?${params}`)
    return response.data
  },

  /**
   * Lista cenários em lotes para carregamento progressivo
   */
  async getCenariosBatch(
    tipoTributo: TipoTributo,
    offset = 0,
    limit = 100,
    filtros?: FiltrosCenarios
  ): Promise<CenariosResponse> {
    const filterMapping: Record<string, string> = {
      atividades: 'atividade',
      destinacoes: 'destinacao'
    }

    const mappedFilters: Record<string, any> = {}
    if (filtros) {
      Object.entries(filtros).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          const backendKey = filterMapping[key] || key
          mappedFilters[backendKey] = value
        }
      })
    }

    const params = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...mappedFilters
    })

    const response = await api.get(`/cenarios/${tipoTributo}/batch?${params}`)
    return response.data
  },

  /**
   * Obtém um cenário específico
   */
  async getCenario(
    tipoTributo: TipoTributo,
    id: number,
    empresaId?: number,
    direcao: 'entrada' | 'saida' = 'saida'
  ): Promise<CenarioTributo | null> {
    try {
      const params = new URLSearchParams()
      if (empresaId) params.append('empresa_id', empresaId.toString())
      if (direcao) params.append('direcao', direcao)
      const query = params.toString()
      const response = await api.get(
        `/cenarios/${tipoTributo}/${id}${query ? `?${query}` : ''}`
      )
      return response.data.cenario as CenarioTributo
    } catch {
      return null
    }
  },

  /**
   * Busca múltiplos cenários pelo ID
   */
  async getCenariosByIds(
    tipoTributo: TipoTributo,
    ids: number[],
    empresaId?: number,
    direcao: 'entrada' | 'saida' = 'saida'
  ): Promise<CenarioTributo[]> {
    const requests = ids.map((id) =>
      this.getCenario(tipoTributo, id, empresaId, direcao)
    )
    const results = await Promise.all(requests)
    return results.filter((c): c is CenarioTributo => c !== null)
  },

  /**
   * Obtém opções para filtros
   */
  async getOpcoesFiltro(
    tipoTributo: TipoTributo,
    empresaId?: number
  ): Promise<OpcoesFiltro> {
    const params = new URLSearchParams()
    if (empresaId) {
      params.append('empresa_id', empresaId.toString())
    }

    const response = await api.get(`/cenarios/${tipoTributo}/filter-options?${params}`)
    return response.data
  },

  /**
   * Obtém contadores de cenários por status
   */
  async getContadores(
    tipoTributo: TipoTributo,
    empresaId: number,
    direcao: 'entrada' | 'saida',
    year?: number,
    month?: number
  ): Promise<{
    success: boolean
    counts: {
      novo: number
      producao: number
      inconsistente: number
    }
  }> {
    const params = new URLSearchParams({
      empresa_id: empresaId.toString(),
      direcao
    })

    if (year) params.append('year', year.toString())
    if (month) params.append('month', month.toString())

    const url = `/cenarios/${tipoTributo}/count?${params}`

    const response = await api.get(url)
    return response.data
  },

  /**
   * Obtém estatísticas de todos os tributos (usando as rotas individuais como no sistema atual)
   */
  async getEstatisticas(empresaId?: number): Promise<{
    icms: { total: number; ativos: number; inconsistentes: number }
    icms_st: { total: number; ativos: number; inconsistentes: number }
    ipi: { total: number; ativos: number; inconsistentes: number }
    pis: { total: number; ativos: number; inconsistentes: number }
    cofins: { total: number; ativos: number; inconsistentes: number }
    difal: { total: number; ativos: number; inconsistentes: number }
  }> {
    if (!empresaId) {
      // Retornar dados vazios se não há empresa selecionada
      return {
        icms: { total: 0, ativos: 0, inconsistentes: 0 },
        icms_st: { total: 0, ativos: 0, inconsistentes: 0 },
        ipi: { total: 0, ativos: 0, inconsistentes: 0 },
        pis: { total: 0, ativos: 0, inconsistentes: 0 },
        cofins: { total: 0, ativos: 0, inconsistentes: 0 },
        difal: { total: 0, ativos: 0, inconsistentes: 0 }
      }
    }

    // Buscar contadores para cada tributo individualmente (como no sistema atual)
    const tributos: TipoTributo[] = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']
    const estatisticas: any = {}

    for (const tributo of tributos) {
      try {
        const result = await this.getContadores(tributo, empresaId, 'saida')
        
        if (result.success) {
          estatisticas[tributo] = {
            total: result.counts.novo + result.counts.producao + result.counts.inconsistente,
            ativos: result.counts.producao, // 'producao' é o equivalente a 'ativo'
            inconsistentes: result.counts.inconsistente
          }
        } else {
          estatisticas[tributo] = { total: 0, ativos: 0, inconsistentes: 0 }
        }
      } catch (error) {
        estatisticas[tributo] = { total: 0, ativos: 0, inconsistentes: 0 }
      }
    }

    return estatisticas
  }
}
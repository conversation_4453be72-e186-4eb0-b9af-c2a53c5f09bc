from models import db, AuditoriaEntrada, NotasFaltantes, HistoricoAuditoriaEntrada
from models import ImportacaoXML, NotaEntrada, ItemNotaEntrada, NotaFiscalItem
from models import Empresa, Cliente, Produto, Usuario
from models import CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS
from datetime import datetime, date
from sqlalchemy import and_, or_, func
from decimal import Decimal
import json
from services.transactional import transactional_session

class AuditoriaEntradaService:
    """
    Serviço para auditoria de notas de entrada
    Compara dados entre XML, SPED e Cenários
    """
    
    def __init__(self, empresa_id, escritorio_id, usuario_id=None):
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id
    
    def identificar_notas_faltantes(self, mes, ano):
        """
        Identifica notas que estão no XML mas não no SPED ou vice-versa
        """
        try:
            with transactional_session():
                # Buscar XMLs de entrada do período
                xmls_entrada = db.session.query(ImportacaoXML).filter(
                    ImportacaoXML.empresa_id == self.empresa_id,
                    ImportacaoXML.tipo_nota == '0',  # Entrada
                    func.extract('month', ImportacaoXML.data_entrada) == mes,
                    func.extract('year', ImportacaoXML.data_entrada) == ano
                ).all()

                print(f"DEBUG - XMLs encontrados: {len(xmls_entrada)} para empresa {self.empresa_id}, mês {mes}, ano {ano}")

                # Buscar notas SPED do período
                notas_sped = db.session.query(NotaEntrada).filter(
                    NotaEntrada.empresa_id == self.empresa_id,
                    func.extract('month', NotaEntrada.data_entrada_saida) == mes,
                    func.extract('year', NotaEntrada.data_entrada_saida) == ano
                ).all()

                print(f"DEBUG - Notas SPED encontradas: {len(notas_sped)} para empresa {self.empresa_id}, mês {mes}, ano {ano}")

                # Criar sets de chaves para comparação
                chaves_xml = {xml.chave_nf for xml in xmls_entrada if xml.chave_nf}
                chaves_sped = {nota.chave_nf for nota in notas_sped if nota.chave_nf}

                # Identificar faltantes
                faltantes_sped = chaves_xml - chaves_sped  # No XML mas não no SPED
                faltantes_xml = chaves_sped - chaves_xml   # No SPED mas não no XML

                # Registrar notas faltantes
                notas_faltantes_criadas = []
            
                # XMLs sem SPED
                for chave in faltantes_sped:
                    xml = next((x for x in xmls_entrada if x.chave_nf == chave), None)
                    if xml:
                        nota_faltante = NotasFaltantes(
                            empresa_id=self.empresa_id,
                            escritorio_id=self.escritorio_id,
                            chave_nf=chave,
                            numero_nf=xml.numero_nf,
                            data_emissao=xml.data_emissao,
                            data_entrada=xml.data_entrada,
                            origem='XML',
                            mes_referencia=mes,
                            ano_referencia=ano,
                            observacoes='Nota presente no XML mas ausente no SPED'
                        )
                    
                        # Verificar se já existe
                        existe = db.session.query(NotasFaltantes).filter(
                            NotasFaltantes.empresa_id == self.empresa_id,
                            NotasFaltantes.chave_nf == chave,
                            NotasFaltantes.origem == 'XML'
                        ).first()
                    
                        if not existe:
                            db.session.add(nota_faltante)
                            notas_faltantes_criadas.append(nota_faltante)
            
                # SPEDs sem XML
                for chave in faltantes_xml:
                    nota = next((n for n in notas_sped if n.chave_nf == chave), None)
                    if nota:
                        nota_faltante = NotasFaltantes(
                            empresa_id=self.empresa_id,
                            escritorio_id=self.escritorio_id,
                            chave_nf=chave,
                            numero_nf=nota.numero_nf,
                            data_emissao=nota.data_documento,
                            data_entrada=nota.data_entrada_saida,
                            origem='SPED',
                            mes_referencia=mes,
                            ano_referencia=ano,
                            observacoes='Nota presente no SPED mas ausente no XML'
                        )
                    
                        # Verificar se já existe
                        existe = db.session.query(NotasFaltantes).filter(
                            NotasFaltantes.empresa_id == self.empresa_id,
                            NotasFaltantes.chave_nf == chave,
                            NotasFaltantes.origem == 'SPED'
                        ).first()
                    
                        if not existe:
                            db.session.add(nota_faltante)
                            notas_faltantes_criadas.append(nota_faltante)
            
                notas_criadas = [nf.to_dict() for nf in notas_faltantes_criadas]

            return {
                'success': True,
                'total_faltantes': len(notas_criadas),
                'faltantes_xml': len(faltantes_sped),
                'faltantes_sped': len(faltantes_xml),
                'notas_criadas': notas_criadas
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao identificar notas faltantes: {str(e)}'
            }
    
    def gerar_auditoria_entrada(self, mes, ano, forcar_recalculo=False):
        """Gera registros de auditoria comparando XML, SPED e Cenários."""
        try:
            with transactional_session():
                query_xml = db.session.query(ImportacaoXML).filter(
                    ImportacaoXML.empresa_id == self.empresa_id,
                    ImportacaoXML.tipo_nota == '0',
                    func.extract('month', ImportacaoXML.data_entrada) == mes,
                    func.extract('year', ImportacaoXML.data_entrada) == ano,
                )

                query_sped = db.session.query(NotaEntrada).filter(
                    NotaEntrada.empresa_id == self.empresa_id,
                    func.extract('month', NotaEntrada.data_entrada_saida) == mes,
                    func.extract('year', NotaEntrada.data_entrada_saida) == ano,
                )

                xmls = query_xml.all()
                notas_sped = query_sped.all()

                sped_dict = {n.chave_nf: n for n in notas_sped if n.chave_nf}
                auditorias_criadas = []

                for xml in xmls:
                    if not xml.chave_nf or xml.chave_nf not in sped_dict:
                        continue

                    nota_sped = sped_dict[xml.chave_nf]

                    itens_sped = db.session.query(ItemNotaEntrada).filter(
                        ItemNotaEntrada.nota_entrada_id == nota_sped.id
                    ).all()

                    itens_xml = db.session.query(NotaFiscalItem).filter(
                        NotaFiscalItem.empresa_id == self.empresa_id,
                        NotaFiscalItem.chave_nf == xml.chave_nf,
                        NotaFiscalItem.tipo_nota == '0'
                    ).all()

                    for item_xml in itens_xml:
                        item_sped = None
                        for item_s in itens_sped:
                            if (
                                item_s.produto_entrada_id
                                and item_xml.produto_id
                                and self._produtos_correspondem(
                                    item_s.produto_entrada_id, item_xml.produto_id
                                )
                            ):
                                item_sped = item_s
                                break

                        if not item_sped:
                            continue

                        auditoria_existe = db.session.query(AuditoriaEntrada).filter(
                            AuditoriaEntrada.empresa_id == self.empresa_id,
                            AuditoriaEntrada.chave_nf == xml.chave_nf,
                            AuditoriaEntrada.produto_id == item_xml.produto_id,
                        ).first()

                        if auditoria_existe and not forcar_recalculo:
                            continue

                        cenarios = self._buscar_cenarios_produto(
                            item_xml.produto_id, item_xml.cfop, item_xml.ncm
                        )

                        if auditoria_existe:
                            auditoria = auditoria_existe
                        else:
                            auditoria = AuditoriaEntrada(
                                empresa_id=self.empresa_id,
                                escritorio_id=self.escritorio_id,
                                usuario_id=self.usuario_id,
                                chave_nf=xml.chave_nf,
                                numero_nf=xml.numero_nf,
                                cliente_id=item_xml.cliente_id,
                                produto_id=item_xml.produto_id,
                                mes_referencia=mes,
                                ano_referencia=ano,
                            )

                        auditoria.xml_data_emissao = xml.data_emissao
                        auditoria.xml_data_entrada = xml.data_entrada
                        auditoria.xml_cfop = item_xml.cfop
                        auditoria.xml_ncm = item_xml.ncm
                        auditoria.xml_valor_total = item_xml.valor_total
                        auditoria.xml_quantidade = item_xml.quantidade

                        auditoria.sped_data_documento = nota_sped.data_documento
                        auditoria.sped_data_entrada_saida = nota_sped.data_entrada_saida
                        auditoria.sped_cfop = item_sped.cfop
                        auditoria.sped_valor_item = item_sped.valor_item
                        auditoria.sped_quantidade = item_sped.quantidade

                        auditoria.sped_icms_aliquota = item_sped.aliquota_icms
                        auditoria.sped_icms_valor = item_sped.valor_icms
                        auditoria.sped_icms_st_aliquota = item_sped.aliquota_st
                        auditoria.sped_icms_st_valor = item_sped.valor_icms_st
                        auditoria.sped_ipi_aliquota = item_sped.aliquota_ipi
                        auditoria.sped_ipi_valor = item_sped.valor_ipi
                        auditoria.sped_pis_aliquota = item_sped.aliquota_pis
                        auditoria.sped_pis_valor = item_sped.valor_pis
                        auditoria.sped_cofins_aliquota = item_sped.aliquota_cofins
                        auditoria.sped_cofins_valor = item_sped.valor_cofins

                        if cenarios:
                            auditoria.cenario_icms_aliquota = cenarios.get('icms_aliquota')
                            auditoria.cenario_icms_st_aliquota = cenarios.get('icms_st_aliquota')
                            auditoria.cenario_ipi_aliquota = cenarios.get('ipi_aliquota')
                            auditoria.cenario_pis_aliquota = cenarios.get('pis_aliquota')
                            auditoria.cenario_cofins_aliquota = cenarios.get('cofins_aliquota')

                        self._calcular_status_conformidade(auditoria)

                        if not auditoria_existe:
                            db.session.add(auditoria)

                        auditorias_criadas.append(auditoria)

                auditorias_dicts = [a.to_dict() for a in auditorias_criadas]

            return {
                'success': True,
                'total_auditorias': len(auditorias_dicts),
                'auditorias_criadas': auditorias_dicts,
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao gerar auditoria de entrada: {str(e)}',
            }

    def gerar_auditoria_escrituracao(self, mes, ano, forcar_recalculo=False, tolerancia=0.01):
        """
        Gera auditoria de escrituração comparando apenas valores totais das notas XML vs SPED
        Esta é uma auditoria simplificada que compara valor_documento (SPED) vs valor total da nota (XML)
        """
        try:
            with transactional_session():
                query_xml = db.session.query(ImportacaoXML).filter(
                    ImportacaoXML.empresa_id == self.empresa_id,
                    ImportacaoXML.tipo_nota == '0',
                    func.extract('month', ImportacaoXML.data_entrada) == mes,
                    func.extract('year', ImportacaoXML.data_entrada) == ano,
                )

                query_sped = db.session.query(NotaEntrada).filter(
                    NotaEntrada.empresa_id == self.empresa_id,
                    func.extract('month', NotaEntrada.data_entrada_saida) == mes,
                    func.extract('year', NotaEntrada.data_entrada_saida) == ano,
                )

                xmls = query_xml.all()
                notas_sped = query_sped.all()

                print(f"DEBUG - XMLs encontrados: {len(xmls)}")
                print(f"DEBUG - Notas SPED encontradas: {len(notas_sped)}")

                sped_dict = {nota.chave_nf: nota for nota in notas_sped if nota.chave_nf}
                print(f"DEBUG - Notas SPED com chave: {len(sped_dict)}")

                auditorias_criadas = []

                for xml in xmls:
                    if not xml.chave_nf:
                        print(f"DEBUG - XML sem chave NF: {xml.arquivo_nome}")
                        continue

                    if xml.chave_nf not in sped_dict:
                        print(f"DEBUG - XML não encontrado no SPED: {xml.chave_nf}")
                        continue

                    nota_sped = sped_dict[xml.chave_nf]

                    auditoria_existe = db.session.query(AuditoriaEntrada).filter(
                        AuditoriaEntrada.empresa_id == self.empresa_id,
                        AuditoriaEntrada.chave_nf == xml.chave_nf,
                        AuditoriaEntrada.produto_id.is_(None),
                    ).first()

                    if auditoria_existe and not forcar_recalculo:
                        print(f"DEBUG - Auditoria já existe para: {xml.chave_nf}")
                        continue

                    valor_total_xml = xml.valor_total_nf
                    if valor_total_xml is None:
                        valor_total_xml = db.session.query(func.sum(NotaFiscalItem.valor_total)).filter(
                            NotaFiscalItem.empresa_id == self.empresa_id,
                            NotaFiscalItem.chave_nf == xml.chave_nf,
                            NotaFiscalItem.tipo_nota == '0',
                        ).scalar() or 0

                    valor_total_sped = nota_sped.valor_documento or 0

                    print(f"DEBUG - Nota {xml.numero_nf}: XML={valor_total_xml}, SPED={valor_total_sped}")

                    cliente_id = nota_sped.cliente_id

                    if auditoria_existe:
                        auditoria = auditoria_existe
                    else:
                        auditoria = AuditoriaEntrada(
                            empresa_id=self.empresa_id,
                            escritorio_id=self.escritorio_id,
                            usuario_id=self.usuario_id,
                            chave_nf=xml.chave_nf,
                            numero_nf=xml.numero_nf,
                            cliente_id=cliente_id,
                            mes_referencia=mes,
                            ano_referencia=ano,
                        )

                    auditoria.xml_data_emissao = xml.data_emissao
                    auditoria.xml_data_entrada = xml.data_entrada
                    auditoria.sped_data_documento = nota_sped.data_documento
                    auditoria.sped_data_entrada_saida = nota_sped.data_entrada_saida

                    auditoria.xml_valor_total_nota = valor_total_xml
                    auditoria.sped_valor_total_nota = valor_total_sped

                    divergencia = float(valor_total_xml) - float(valor_total_sped)
                    auditoria.divergencia_valor_total = divergencia

                    if valor_total_xml > 0:
                        auditoria.percentual_divergencia = (divergencia / float(valor_total_xml)) * 100
                    else:
                        auditoria.percentual_divergencia = 0

                    if abs(divergencia) <= tolerancia:
                        auditoria.status_escrituracao = 'conforme'
                    else:
                        auditoria.status_escrituracao = 'divergente'

                    if not auditoria_existe:
                        db.session.add(auditoria)

                    auditorias_criadas.append(auditoria)

                print(f"DEBUG - Auditorias criadas: {len(auditorias_criadas)}")

                auditorias_dicts = [a.to_dict() for a in auditorias_criadas]

            return {
                'success': True,
                'total_auditorias': len(auditorias_dicts),
                'message': f'Auditoria de escrituração gerada com sucesso! {len(auditorias_dicts)} registros processados.',
            }

        except Exception as e:
            print(f"DEBUG - Erro na auditoria de escrituração: {str(e)}")
            return {
                'success': False,
                'message': f'Erro ao gerar auditoria de escrituração: {str(e)}',
            }

    def _produtos_correspondem(self, produto_entrada_id, produto_id):
        """
        Verifica se um produto de entrada corresponde a um produto regular
        Compara por NCM, descrição ou código
        """
        try:
            from models import ProdutoEntrada, Produto

            produto_entrada = db.session.get(ProdutoEntrada, produto_entrada_id)
            produto = db.session.get(Produto, produto_id)

            if not produto_entrada or not produto:
                return False

            # Comparar por NCM (mais confiável)
            if produto_entrada.ncm and produto.ncm:
                if produto_entrada.ncm == produto.ncm:
                    return True

            # Comparar por descrição (similaridade)
            if produto_entrada.descricao and produto.descricao:
                desc_entrada = produto_entrada.descricao.lower().strip()
                desc_produto = produto.descricao.lower().strip()

                # Verificar se uma descrição contém a outra (pelo menos 80% de similaridade)
                if desc_entrada in desc_produto or desc_produto in desc_entrada:
                    return True

                # Verificar palavras-chave em comum
                palavras_entrada = set(desc_entrada.split())
                palavras_produto = set(desc_produto.split())
                palavras_comuns = palavras_entrada.intersection(palavras_produto)

                # Se mais de 60% das palavras são comuns, considerar correspondente
                if len(palavras_comuns) / max(len(palavras_entrada), len(palavras_produto)) > 0.6:
                    return True

            return False

        except Exception as e:
            print(f"Erro ao verificar correspondência de produtos: {str(e)}")
            return False
    
    def _buscar_cenarios_produto(self, produto_id, cfop, ncm):
        """
        Busca cenários configurados para um produto
        """
        try:
            cenarios = {}
            
            # Buscar cenário ICMS
            icms = db.session.query(CenarioICMS).filter(
                CenarioICMS.empresa_id == self.empresa_id,
                CenarioICMS.produto_id == produto_id,
                CenarioICMS.cfop == cfop
            ).first()
            if icms:
                cenarios['icms_aliquota'] = icms.aliquota
            
            # Buscar outros cenários...
            # (implementar para ICMS-ST, IPI, PIS, COFINS)
            
            return cenarios
            
        except Exception:
            return {}
    
    def _calcular_status_conformidade(self, auditoria):
        """
        Calcula o status de conformidade comparando SPED x Cenários
        """
        # ICMS
        if (auditoria.sped_icms_aliquota and auditoria.cenario_icms_aliquota):
            if abs(float(auditoria.sped_icms_aliquota) - float(auditoria.cenario_icms_aliquota)) < 0.01:
                auditoria.status_icms = 'conforme'
            else:
                auditoria.status_icms = 'divergente'
        
        # Implementar para outros tributos...
        
        # Status geral
        status_list = [auditoria.status_icms, auditoria.status_icms_st, 
                      auditoria.status_ipi, auditoria.status_pis, auditoria.status_cofins]
        
        if any(s == 'divergente' for s in status_list):
            auditoria.status_geral = 'divergente'
        elif all(s == 'conforme' for s in status_list if s != 'pendente'):
            auditoria.status_geral = 'conforme'
        else:
            auditoria.status_geral = 'pendente'

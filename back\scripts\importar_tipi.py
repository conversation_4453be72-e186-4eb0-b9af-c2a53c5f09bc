import os
import sys
import pandas as pd
import psycopg2
from dotenv import load_dotenv
from datetime import datetime

# Adiciona o diretório raiz ao path para importar módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Carregar variáveis de ambiente
load_dotenv()

# Configurações do banco de dados
DB_CONFIG = {
    'dbname': os.getenv('DB_NAME', 'fiscal_bruno'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', '123!asd'),
    'host': os.getenv('DB_HOST', '**************'),
    'port': os.getenv('DB_PORT', '5432')
}

def get_connection():
    """Estabelece conexão com o banco de dados"""
    return psycopg2.connect(**DB_CONFIG)

def criar_tabela_tipi():
    """Cria a tabela TIPI se não existir"""
    conn = None
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # Verifica se a tabela já existe
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'tipi'
            );
        """)
        
        if not cursor.fetchone()[0]:
            # Cria a tabela se não existir
            cursor.execute("""
                CREATE TABLE tipi (
                    id SERIAL PRIMARY KEY,
                    ncm VARCHAR(10) NOT NULL,
                    ex VARCHAR(2),
                    descricao TEXT,
                    aliquota VARCHAR(20),
                    data_criacao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    data_atualizacao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(ncm, ex)
                );
                
                COMMENT ON TABLE tipi IS 'Tabela de NCMs e suas respectivas alíquotas do IPI conforme TIPI';
                COMMENT ON COLUMN tipi.ncm IS 'Código NCM (Nomenclatura Comum do Mercosul)';
                COMMENT ON COLUMN tipi.ex IS 'Código EX da NCM (quando aplicável)';
                COMMENT ON COLUMN tipi.descricao IS 'Descrição do produto conforme TIPI';
                COMMENT ON COLUMN tipi.aliquota IS 'Alíquota do IPI (pode ser um número ou texto como "NT")';
            """)
            conn.commit()
            print("Tabela TIPI criada com sucesso!")
        else:
            print("A tabela TIPI já existe.")
            
    except Exception as e:
        print(f"Erro ao criar tabela TIPI: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            cursor.close()
            conn.close()

def ler_arquivo_excel(caminho_arquivo):
    """Lê o arquivo Excel e retorna um DataFrame com os dados da TIPI"""
    try:
        # Primeiro, lê apenas os cabeçalhos para verificar as colunas disponíveis
        df_headers = pd.read_excel(caminho_arquivo, nrows=0)
        print(f"Colunas encontradas no arquivo: {list(df_headers.columns)}")
        
        # Mapeia os nomes das colunas para minúsculas e remove espaços
        mapeamento_colunas = {
            'NCM': 'ncm',
            'EX': 'ex',
            'DESCRIÇÃO': 'descricao',
            'DESCRICAO': 'descricao',
            'ALÍQUOTA': 'aliquota',
            'ALIQUOTA': 'aliquota',
            'ALIQ': 'aliquota'
        }
        
        # Cria um dicionário para mapear as colunas originais para os nomes padronizados
        colunas_para_ler = []
        mapeamento = {}
        
        for col in df_headers.columns:
            col_limpa = str(col).strip().upper()
            if col_limpa in mapeamento_colunas:
                nome_padrao = mapeamento_colunas[col_limpa]
                mapeamento[col] = nome_padrao
                colunas_para_ler.append(col)
        
        print(f"Colunas a serem lidas: {colunas_para_ler}")
        
        # Verifica se encontrou pelo menos a coluna NCM
        if 'ncm' not in mapeamento.values():
            raise ValueError("Não foi possível encontrar a coluna 'NCM' no arquivo Excel")
        
        # Lê o arquivo Excel com as colunas mapeadas
        df = pd.read_excel(
            caminho_arquivo,
            dtype=str,  # Garante que todos os dados sejam lidos como texto
            usecols=colunas_para_ler  # Lê apenas as colunas mapeadas
        )
        
        # Renomeia as colunas para os nomes padronizados
        df = df.rename(columns=mapeamento)
        
        # Remove linhas completamente vazias
        df = df.dropna(how='all')
        
        # Preenche valores nulos com string vazia
        df = df.fillna('')
        
        # Remove espaços em branco dos valores
        for col in df.columns:
            df[col] = df[col].astype(str).str.strip()
        
        # Remove linhas sem NCM
        df = df[df['ncm'] != '']
        
        # Remove caracteres não numéricos do NCM
        df['ncm'] = df['ncm'].str.replace(r'[^0-9]', '', regex=True)
        
        # Filtra apenas NCMs com 8 dígitos
        df = df[df['ncm'].str.len() == 8]
        
        # Remove duplicatas mantendo a primeira ocorrência
        colunas_chave = ['ncm']
        if 'ex' in df.columns:
            colunas_chave.append('ex')
        
        df = df.drop_duplicates(subset=colunas_chave, keep='first')
        
        # Limpa e padroniza o campo EX se existir
        if 'ex' in df.columns:
            df['ex'] = df['ex'].str.replace(r'[^0-9]', '', regex=True)  # Mantém apenas dígitos
            df['ex'] = df['ex'].str[:2]  # Garante no máximo 2 dígitos
        else:
            df['ex'] = ''  # Adiciona coluna EX vazia se não existir
        
        # Garante que todas as colunas necessárias existam
        if 'descricao' not in df.columns:
            df['descricao'] = ''
        
        if 'aliquota' not in df.columns:
            df['aliquota'] = ''
        
        # Limpa a descrição e alíquota
        df['descricao'] = df['descricao'].str.strip()
        df['aliquota'] = df['aliquota'].str.strip().str.upper()
        
        print(f"Total de registros processados: {len(df)}")
        return df
    
    except Exception as e:
        print(f"Erro ao ler o arquivo Excel: {e}")
        raise

def importar_dados_tipi(caminho_arquivo):
    """Importa os dados do Excel para o banco de dados"""
    conn = None
    try:
        # Lê os dados do Excel
        print(f"Lendo arquivo: {caminho_arquivo}")
        df = ler_arquivo_excel(caminho_arquivo)
        
        # Verifica se o DataFrame está vazio
        if df.empty:
            print("Nenhum dado para importar.")
            return
        
        print(f"Encontrados {len(df)} registros no arquivo.")
        
        # Conecta ao banco de dados
        conn = get_connection()
        cursor = conn.cursor()
        
        # Contadores para estatísticas
        inseridos = 0
        atualizados = 0
        ignorados = 0
        
        # Itera sobre as linhas do DataFrame e insere/atualiza no banco
        for _, row in df.iterrows():
            try:
                # Prepara os dados
                ncm = row['ncm']
                ex = row['ex'] if 'ex' in row and pd.notna(row['ex']) else ''
                descricao = row['descricao'] if 'descricao' in row and pd.notna(row['descricao']) else ''
                aliquota = row['aliquota'] if 'aliquota' in row and pd.notna(row['aliquota']) else ''
                
                # Verifica se o registro já existe
                cursor.execute(
                    """
                    SELECT id FROM tipi 
                    WHERE ncm = %s AND (ex = %s OR (ex IS NULL AND %s = ''))
                    """,
                    (ncm, ex, ex)
                )
                
                existe = cursor.fetchone()
                
                if existe:
                    # Atualiza o registro existente
                    cursor.execute(
                        """
                        UPDATE tipi 
                        SET descricao = %s, 
                            aliquota = %s,
                            data_atualizacao = NOW()
                        WHERE id = %s
                        RETURNING id
                        """,
                        (descricao, aliquota, existe[0])
                    )
                    atualizados += 1
                else:
                    # Insere um novo registro
                    cursor.execute(
                        """
                        INSERT INTO tipi (ncm, ex, descricao, aliquota)
                        VALUES (%s, %s, %s, %s)
                        RETURNING id
                        """,
                        (ncm, ex if ex else None, descricao, aliquota)
                    )
                    inseridos += 1
                
                # Commit a cada 1000 registros para melhor desempenho
                if (inseridos + atualizados) % 1000 == 0:
                    conn.commit()
                    print(f"Processados {inseridos + atualizados} registros...")
            
            except Exception as e:
                print(f"Erro ao processar registro NCM: {row.get('ncm', '')}, EX: {row.get('ex', '')}")
                print(f"Erro: {e}")
                ignorados += 1
                continue
        
        # Faz o commit final
        conn.commit()
        
        print("\nImportação concluída com sucesso!")
        print(f"- Registros inseridos: {inseridos}")
        print(f"- Registros atualizados: {atualizados}")
        print(f"- Registros ignorados: {ignorados}")
        
    except Exception as e:
        print(f"Erro durante a importação: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            cursor.close()
            conn.close()

def obter_caminho_arquivo_tipi():
    """Obtém o caminho para o arquivo tipi.xlsx na raiz do projeto"""
    # Obtém o diretório raiz do projeto (um nível acima de 'back')
    diretorio_raiz = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    caminho_arquivo = os.path.join(diretorio_raiz, 'tipi.xlsx')
    
    if not os.path.isfile(caminho_arquivo):
        raise FileNotFoundError(
            f"Arquivo 'tipi.xlsx' não encontrado na raiz do projeto. "
            f"Caminho procurado: {caminho_arquivo}"
        )
    
    return caminho_arquivo

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Importar dados da TIPI do arquivo tipi.xlsx para o banco de dados.')
    parser.add_argument('--criar-tabela', action='store_true', help='Cria a tabela TIPI se não existir')
    
    try:
        # Obtém o caminho do arquivo tipi.xlsx na raiz do projeto
        caminho_arquivo = obter_caminho_arquivo_tipi()
        
        # Parseia os argumentos
        args = parser.parse_args()
        
        # Cria a tabela se solicitado
        if args.criar_tabela:
            print("Criando tabela TIPI...")
            criar_tabela_tipi()
        
        # Executa a importação
        print(f"Iniciando importação do arquivo: {caminho_arquivo}")
        importar_dados_tipi(caminho_arquivo)
        
    except FileNotFoundError as e:
        print(f"Erro: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Erro durante a execução: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

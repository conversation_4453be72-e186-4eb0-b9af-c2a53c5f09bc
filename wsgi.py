import os
import sys

# Adiciona os diretórios necessários ao path
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
BACK_DIR = os.path.join(BASE_DIR, 'back')

# Adiciona os diretórios ao path do Python
sys.path.insert(0, BASE_DIR)
sys.path.insert(0, BACK_DIR)

# Agora importa a aplicação
from back.app import create_app

# Criar a aplicação
flask_app, socketio = create_app()

# Expor apenas o app para o Gunicorn
app = flask_app

if __name__ == '__main__':
    debug_mode = os.getenv('FLASK_DEBUG', '0') == '1'
    
    socketio.run(
        flask_app,
        host='0.0.0.0',  # Aceita conexões de qualquer IP
        port=5000,
        debug=debug_mode,
        use_reloader=False,  # Sempre False para evitar problemas
        allow_unsafe_werkzeug=True  # Permite rodar em produção
    )
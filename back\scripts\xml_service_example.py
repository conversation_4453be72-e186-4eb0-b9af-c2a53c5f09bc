import os
import time
import shutil
import requests
import threading
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# --- Confi<PERSON>ra<PERSON>ões ---
PASTA_A_MONITORAR = "C:\\temp_xmls"
ESCRITORIO_ID = 1
API_KEY = "3f50c92ae6154040b27de74347605e94"
URL_ENDPOINT = "http://localhost:5000/fiscal/api/service-upload"
INTERVALO_REENVIO_SEGUNDOS = 30
# -------------------

# Nomes das subpastas de controle
PASTA_PENDENTES = os.path.join(PASTA_A_MONITORAR, "_pendentes")
PASTA_ENVIADOS = os.path.join(PASTA_A_MONITORAR, "_enviados")

def enviar_arquivo(path: str) -> bool:
    """
    Tenta enviar um único arquivo para o endpoint.
    Retorna True em caso de sucesso, False em caso de falha.
    """
    print(f"[INFO] Tentando enviar o arquivo: {os.path.basename(path)}")
    try:
        with open(path, 'rb') as f:
            files = {'arquivo': (os.path.basename(path), f)}
            data = {'escritorio_id': str(ESCRITORIO_ID)}
            headers = {'X-API-KEY': API_KEY}
            
            response = requests.post(URL_ENDPOINT, files=files, data=data, headers=headers, timeout=15)
            
            if response.status_code in [200, 201]:
                print(f"[SUCESSO] Arquivo {os.path.basename(path)} enviado com sucesso. Status: {response.status_code}")
                return True
            else:
                print(f"[ERRO] Falha ao enviar {os.path.basename(path)}. Servidor respondeu com status {response.status_code}: {response.text}")
                return False
    except requests.exceptions.RequestException as e:
        print(f"[ERRO DE CONEXÃO] Não foi possível enviar {os.path.basename(path)}: {e}")
        return False
    except Exception as e:
        print(f"[ERRO INESPERADO] Ocorreu um problema ao enviar {os.path.basename(path)}: {e}")
        return False

class NovoXMLHandler(FileSystemEventHandler):
    """
    Manipulador que reage a novos arquivos XML na pasta principal.
    """
    def on_created(self, event):
        if event.is_directory or not event.src_path.lower().endswith('.xml'):
            return

        # Aguarda um instante para garantir que o arquivo foi completamente escrito
        time.sleep(1) 
        
        print(f"[INFO] Novo arquivo detectado: {os.path.basename(event.src_path)}")
        
        try:
            # Move o arquivo para a pasta de pendentes para garantir o processamento
            shutil.move(event.src_path, os.path.join(PASTA_PENDENTES, os.path.basename(event.src_path)))
            print(f"[INFO] Arquivo movido para a pasta de pendentes.")
        except Exception as e:
            print(f"[ERRO] Não foi possível mover {os.path.basename(event.src_path)} para a pasta de pendentes: {e}")

def processar_pendentes():
    """
    Verifica a pasta de pendentes e tenta enviar cada arquivo.
    Roda em um loop infinito em uma thread separada.
    """
    while True:
        arquivos_pendentes = [os.path.join(PASTA_PENDENTES, f) for f in os.listdir(PASTA_PENDENTES) if f.lower().endswith('.xml')]
        
        if not arquivos_pendentes:
            time.sleep(INTERVALO_REENVIO_SEGUNDOS)
            continue

        print(f"\n[INFO] Verificando {len(arquivos_pendentes)} arquivo(s) na pasta de pendentes...")
        
        for path_arquivo in arquivos_pendentes:
            if enviar_arquivo(path_arquivo):
                try:
                    # Move o arquivo para a pasta de enviados após o sucesso
                    shutil.move(path_arquivo, os.path.join(PASTA_ENVIADOS, os.path.basename(path_arquivo)))
                    print(f"[INFO] Arquivo {os.path.basename(path_arquivo)} movido para a pasta de enviados.")
                except Exception as e:
                    print(f"[ERRO] Não foi possível mover o arquivo {os.path.basename(path_arquivo)} para a pasta de enviados: {e}")
        
        print(f"[INFO] Verificação de pendentes concluída. Próxima verificação em {INTERVALO_REENVIO_SEGUNDOS} segundos.")
        time.sleep(INTERVALO_REENVIO_SEGUNDOS)

def main():
    """
    Função principal que inicializa o serviço.
    """
    print("--- Serviço de Monitoramento de XML ---")
    
    # 1. Validação e criação das pastas de controle
    if not os.path.isdir(PASTA_A_MONITORAR):
        raise SystemExit(f"[CRÍTICO] A pasta principal '{PASTA_A_MONITORAR}' não foi encontrada. O serviço não pode iniciar.")
        
    for pasta in [PASTA_PENDENTES, PASTA_ENVIADOS]:
        if not os.path.exists(pasta):
            print(f"[INFO] Criando pasta de controle: {pasta}")
            os.makedirs(pasta)

    # 2. Iniciar a thread que processa arquivos pendentes
    thread_pendentes = threading.Thread(target=processar_pendentes, daemon=True)
    thread_pendentes.start()
    print("[INFO] Serviço de processamento de arquivos pendentes iniciado.")

    # 3. Iniciar o monitoramento da pasta principal para novos arquivos
    event_handler = NovoXMLHandler()
    observer = Observer()
    observer.schedule(event_handler, PASTA_A_MONITORAR, recursive=False)
    observer.start()
    print(f"[INFO] Monitorando a pasta '{PASTA_A_MONITORAR}' para novos arquivos XML.")
    print("-----------------------------------------")
    print("O serviço está em execução. Pressione Ctrl+C para sair.")

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n[INFO] Encerrando o serviço...")
        observer.stop()
    
    observer.join()
    print("[INFO] Serviço encerrado.")

if __name__ == "__main__":
    main()
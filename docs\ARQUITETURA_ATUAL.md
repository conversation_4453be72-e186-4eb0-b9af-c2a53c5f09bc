# Visão Geral da Arquitetura do Sistema

Este documento resume como o sistema está estruturado atualmente para que um profissional de infraestrutura compreenda os principais componentes.

## Importação de Dados
- Utiliza serviços dedicados (`XMLImportService`, `BatchXMLImportService` e `OptimizedXMLImportService`).
- Suporta processamento em lote e importações assíncronas.
- Progresso é enviado via WebSocket, permitindo que o usuário navegue enquanto os arquivos são processados.
- Informações sobre a importação são persistidas em `importacao_async` para recuperar o estado após reconexões.

Trecho do documento **ATUALIZACOES_SISTEMA_XML.md** indicando a importação assíncrona:
```
### 3. **Importação Assíncrona com WebSocket Persistente**
- ✅ **Problema:** Usuário não pode navegar durante importação em lotes
- ✅ **Solução:** Sistema assíncrono com estado persistente
- ✅ **Funcionalidades:**
  - Importação continua em background
  - WebSocket reconecta automaticamente
  - Estado salvo no banco de dados
  - Interface mostra progresso mesmo após navegação
```

## Sistema de Filas
- Gerenciado pelo módulo `queue_manager.py`.
- Possui filas de prioridade independentes para importação, auditoria e cálculo de tributos.
- Cada fila é processada por um `ThreadPoolExecutor` dedicado.
- As tarefas são submetidas à fila e executadas em background pelos workers.

Código relevante mostrando o uso de `PriorityQueue` e `ThreadPoolExecutor`:
```python
    # Filas por tipo de operação
    self.import_queue = queue.PriorityQueue(maxsize=max_queue_size)
    self.audit_queue = queue.PriorityQueue(maxsize=max_queue_size)
    self.calc_queue = queue.PriorityQueue(maxsize=max_queue_size)

    # Pool de threads para cada tipo de operação
    self.import_executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="import")
    self.audit_executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="audit")
    self.calc_executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="calc")
```

## Controle por Semáforo
- Para evitar sobrecarga de uma única empresa, o `QueueManager` mantém um semáforo por `empresa_id`.
- Cada empresa pode executar no máximo **duas** operações simultâneas (importação, auditoria ou cálculo).

Trecho de código demonstrando essa lógica:
```python
    def _get_company_semaphore(self, empresa_id: int) -> threading.Semaphore:
        """Obtém ou cria um semáforo para controlar operações por empresa"""
        with self.company_lock:
            if empresa_id not in self.company_locks:
                # Máximo 2 operações simultâneas por empresa
                self.company_locks[empresa_id] = threading.Semaphore(2)
            return self.company_locks[empresa_id]
```

## Suporte a Múltiplos Usuários
- O sistema utiliza Flask com JWT para autenticação e Flask-SocketIO para comunicação em tempo real.
- Como as importações são assíncronas e processadas em background, diversas requisições podem ser atendidas ao mesmo tempo.
- A fila central controla a ordem de execução e garante que as tarefas sejam processadas mesmo quando vários usuários realizam importações simultaneamente.

Em resumo, o sistema suporta múltiplos usuários conectados e realizando importações ao mesmo tempo, pois:
1. As requisições são colocadas em filas independentes.
2. Os workers processam essas filas em paralelo.
3. O semáforo por empresa impede que uma única empresa monopolize todos os recursos.
export interface Cliente {
  id: number
  cnpj: string
  razao_social: string
  inscricao_estadual?: string
  uf?: string
  municipio?: string
  atividade?: string
  destinacao?: string
  cnae?: string
  natureza_juridica?: string
  descricao?: string
  logradouro?: string
  numero?: string
  bairro?: string
  cep?: string
  simples_nacional?: boolean
}

export interface ClientesResponse {
  clientes: Cliente[]
  pagination: {
    total: number
    per_page: number
    current_page: number
    last_page: number
    from: number
    to: number
    has_prev: boolean
    has_next: boolean
    prev_page: number | null
    next_page: number | null
  }
}

export interface ClienteFilterOptions {
  ufs: string[]
  atividades: string[]
  destinacoes: string[]
  cnaes: string[]
  municipios?: string[]
}
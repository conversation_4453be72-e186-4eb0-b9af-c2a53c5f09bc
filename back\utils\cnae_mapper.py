"""
Utilitário para mapear códigos CNAE para atividades e destinações
"""

# Dicionário CNAE: divisão → (atividade, destinação sugerida)
cnae_map = {
    # Agricultura, Pecuária etc.
    "01": ("Produtor Rural", "Industrialização"),
    "02": ("Produtor Rural", "Industrialização"),
    "03": ("Produtor Rural", "Industrialização"),

    # Indústrias Extrativas
    "05": ("Indústria ou Equiparado", "Industrialização"),
    "06": ("Indústria ou Equiparado", "Industrialização"),
    "07": ("Indústria ou Equiparado", "Industrialização"),
    "08": ("Indústria ou Equiparado", "Industrialização"),
    "09": ("Indústria ou Equiparado", "Industrialização"),

    # Indústrias de Transformação (10-33)
    "10": ("Indústria ou Equiparado", "Industrialização"),
    "11": ("Indústria ou Equiparado", "Industrialização"),
    "12": ("Indústria ou Equiparado", "Industrialização"),
    "13": ("Indústria ou Equiparado", "Industrialização"),
    "14": ("Indústria ou Equiparado", "Industrialização"),
    "15": ("Indústria ou Equiparado", "Industrialização"),
    "16": ("Indústria ou Equiparado", "Industrialização"),
    "17": ("Indústria ou Equiparado", "Industrialização"),
    "18": ("Indústria ou Equiparado", "Industrialização"),
    "19": ("Indústria ou Equiparado", "Industrialização"),
    "20": ("Indústria ou Equiparado", "Industrialização"),
    "21": ("Indústria ou Equiparado", "Industrialização"),
    "22": ("Indústria ou Equiparado", "Industrialização"),
    "23": ("Indústria ou Equiparado", "Industrialização"),
    "24": ("Indústria ou Equiparado", "Industrialização"),
    "25": ("Indústria ou Equiparado", "Industrialização"),
    "26": ("Indústria ou Equiparado", "Industrialização"),
    "27": ("Indústria ou Equiparado", "Industrialização"),
    "28": ("Indústria ou Equiparado", "Industrialização"),
    "29": ("Indústria ou Equiparado", "Industrialização"),
    "30": ("Indústria ou Equiparado", "Industrialização"),
    "31": ("Indústria ou Equiparado", "Industrialização"),
    "32": ("Indústria ou Equiparado", "Industrialização"),
    "33": ("Indústria ou Equiparado", "Industrialização"),

    # Eletricidade e Gás
    "35": ("Serviço", "Uso e Consumo"),

    # Água, Esgoto etc.
    "36": ("Serviço", "Uso e Consumo"),
    "37": ("Serviço", "Uso e Consumo"),
    "38": ("Serviço", "Uso e Consumo"),
    "39": ("Serviço", "Uso e Consumo"),

    # Construção
    "41": ("Serviço", "Uso e Consumo"),
    "42": ("Serviço", "Uso e Consumo"),
    "43": ("Serviço", "Uso e Consumo"),

    # Comércio
    "45": ("Comércio", "Revenda"),
    "46": ("Comércio Atacadista", "Revenda"),
    "47": ("Comércio Varejista", "Revenda"),

    # Transporte, Armazenagem e Correio
    "49": ("Serviço", "Uso e Consumo"),
    "50": ("Serviço", "Uso e Consumo"),
    "51": ("Serviço", "Uso e Consumo"),
    "52": ("Serviço", "Uso e Consumo"),
    "53": ("Serviço", "Uso e Consumo"),

    # Alojamento e Alimentação
    "55": ("Serviço", "Uso e Consumo"),
    "56": ("Serviço", "Uso e Consumo"),

    # Informação e Comunicação
    "58": ("Serviço", "Uso e Consumo"),
    "59": ("Serviço", "Uso e Consumo"),
    "60": ("Serviço", "Uso e Consumo"),
    "61": ("Serviço", "Uso e Consumo"),
    "62": ("Serviço", "Uso e Consumo"),
    "63": ("Serviço", "Uso e Consumo"),

    # Atividades Financeiras etc.
    "64": ("Serviço", "Uso e Consumo"),
    "65": ("Serviço", "Uso e Consumo"),
    "66": ("Serviço", "Uso e Consumo"),

    # Atividades Imobiliárias
    "68": ("Serviço", "Uso e Consumo"),

    # Atividades Profissionais, Científicas e Técnicas
    "69": ("Serviço", "Uso e Consumo"),
    "70": ("Serviço", "Uso e Consumo"),
    "71": ("Serviço", "Uso e Consumo"),
    "72": ("Serviço", "Uso e Consumo"),
    "73": ("Serviço", "Uso e Consumo"),
    "74": ("Serviço", "Uso e Consumo"),
    "75": ("Serviço", "Uso e Consumo"),

    # Atividades Administrativas
    "77": ("Serviço", "Uso e Consumo"),
    "78": ("Serviço", "Uso e Consumo"),
    "79": ("Serviço", "Uso e Consumo"),
    "80": ("Serviço", "Uso e Consumo"),
    "81": ("Serviço", "Uso e Consumo"),
    "82": ("Serviço", "Uso e Consumo"),

    # Administração Pública
    "84": ("Serviço", "Uso e Consumo"),

    # Educação
    "85": ("Serviço", "Uso e Consumo"),

    # Saúde Humana e Serviços Sociais
    "86": ("Serviço", "Uso e Consumo"),
    "87": ("Serviço", "Uso e Consumo"),
    "88": ("Serviço", "Uso e Consumo"),

    # Artes, Cultura, Esporte e Recreação
    "90": ("Serviço", "Uso e Consumo"),
    "91": ("Serviço", "Uso e Consumo"),
    "92": ("Serviço", "Uso e Consumo"),
    "93": ("Serviço", "Uso e Consumo"),

    # Outras Atividades de Serviços
    "94": ("Serviço", "Uso e Consumo"),
    "95": ("Serviço", "Uso e Consumo"),
    "96": ("Serviço", "Uso e Consumo"),

    # Serviços Domésticos
    "97": ("Serviço", "Uso e Consumo"),

    # Organismos Internacionais
    "99": ("Serviço", "Uso e Consumo"),
}

def extract_cnae_division(cnae_code):
    """
    Extrai a divisão do código CNAE
    
    Args:
        cnae_code (str): Código CNAE completo (ex: '2063100')
        
    Returns:
        str: Divisão do CNAE (ex: '20') ou None se inválido
    """
    if not cnae_code:
        return None
    
    # Remover caracteres não numéricos
    cnae_code = ''.join(filter(str.isdigit, cnae_code))
    
    # Verificar se o código tem pelo menos 2 dígitos
    if len(cnae_code) < 2:
        return None
    
    # Extrair os dois primeiros dígitos (divisão)
    division = cnae_code[:2]
    
    return division

def get_activity_and_destination(cnae_code):
    """
    Obtém a atividade e destinação sugeridas com base no código CNAE
    
    Args:
        cnae_code (str): Código CNAE completo
        
    Returns:
        tuple: (atividade, destinação) ou (None, None) se não encontrado
    """
    division = extract_cnae_division(cnae_code)
    
    if not division:
        return None, None
    
    # Buscar no dicionário
    result = cnae_map.get(division, (None, None))
    
    return result

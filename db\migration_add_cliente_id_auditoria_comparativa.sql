-- Migration: Adicionar cliente_id na tabela auditoria_comparativa_impostos
-- Data: 2025-06-16
-- Descrição: Adiciona campo cliente_id para identificar o cliente da nota fiscal
--           Essencial para aprendizado do sistema e exibição correta dos dados

BEGIN;

-- Adicionar campo cliente_id
ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN IF NOT EXISTS cliente_id INTEGER REFERENCES cliente(id);

-- Adicionar índice para performance
CREATE INDEX IF NOT EXISTS idx_auditoria_comparativa_cliente ON auditoria_comparativa_impostos(cliente_id);

-- Comentário no campo
COMMENT ON COLUMN auditoria_comparativa_impostos.cliente_id IS 'ID do cliente da nota fiscal - essencial para aprendizado do sistema';

-- Atualizar registros existentes com cliente_id baseado no xml_item_id
UPDATE auditoria_comparativa_impostos 
SET cliente_id = nfi.cliente_id
FROM nota_fiscal_item nfi
WHERE auditoria_comparativa_impostos.xml_item_id = nfi.id
  AND auditoria_comparativa_impostos.cliente_id IS NULL;

COMMIT;

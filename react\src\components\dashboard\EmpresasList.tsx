import { useNavigate } from 'react-router-dom'
import { Empresa } from '@/services/dashboardService'
import { Button } from '@/components/ui/Button'
import { Table, type Column } from '@/components/ui/Table'
import { Tooltip } from '@/components/ui/Tooltip'

interface EmpresasListProps {
  empresas: Empresa[]
  tipo: 'saida' | 'entrada'
  viewMode: 'card' | 'list'
}

export function EmpresasList({ empresas, tipo, viewMode }: EmpresasListProps) {
  const navigate = useNavigate()

  const handleNavigate = (empresa: Empresa) => {
    localStorage.setItem('selectedCompany', empresa.id.toString())
    navigate(`/fiscal/dashboard/empresa/${empresa.id}?tipo=${tipo}`)
  }

  if (empresas.length === 0) {
    return (
      <div className="p-6 text-center">
        <div className="text-gray-400 dark:text-gray-500 mb-4">
          <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Nenhuma empresa encontrada
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Não há empresas disponíveis para o período selecionado.
        </p>
      </div>
    )
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completa':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Completa
          </span>
        )
      case 'parcial':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
            Parcial
          </span>
        )
      case 'pendente':
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            Pendente
          </span>
        )
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value)
  }

  const formatCurrencyShort = (value: number) => {
    if (value >= 1000000) {
      return `R$ ${(value / 1000000).toLocaleString('pt-BR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} mi`
    } else if (value >= 1000) {
      return `R$ ${(value / 1000).toLocaleString('pt-BR', { minimumFractionDigits: 3, maximumFractionDigits: 3 })} mil`
    }
    return formatCurrency(value)
  }

  const formatCNPJ = (cnpj: string) => {
    if (!cnpj) return ''
    return cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5')
  }

  const columns: Column<Empresa>[] = [
    {
      key: 'razao_social',
      title: 'Empresa',
      width: '25%',
      render: (_, empresa) => (
        <div>
          <div className="font-semibold text-gray-900 dark:text-white">
            {empresa.razao_social}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            CNPJ: {formatCNPJ(empresa.cnpj)}
          </div>
        </div>
      ),
    },
    {
      key: 'status_auditoria',
      title: 'Status',
      render: (value) => getStatusBadge(value),
      align: 'center',
    },
    {
      key: 'progresso',
      title: 'Progresso',
      render: (_, empresa) => {
        const progressPercent = Math.round(
          ((empresa.tributos_auditados?.length || 0) / 6) * 100
        )
        return (
          <div className="w-32">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${
                  empresa.status_auditoria === 'completa'
                    ? 'bg-green-500'
                    : empresa.status_auditoria === 'parcial'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`}
                style={{ width: `${progressPercent}%` }}
              />
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400 mt-1 text-right">
              {progressPercent}%
            </div>
          </div>
        )
      },
      align: 'center',
    },
    {
      key: 'auditados',
      title: 'Auditados',
      render: (_, empresa) => `${empresa.tributos_auditados?.length || 0}/6`,
      align: 'center',
    },
    {
      key: 'total_impostos',
      title: 'Total Impostos',
      render: (value) =>
        value > 0 ? (
          <Tooltip content={formatCurrency(value)}>
            <span>
              {formatCurrencyShort(value).replace('R$', '').trim()}
            </span>
          </Tooltip>
        ) : (
          '0'
        ),
      align: 'right',
    },
    {
      key: 'total_inconsistencias',
      title: 'Inconsistências',
      align: 'center',
    },
    {
      key: 'total_valor_inconsistente',
      title: 'Valor',
      render: (value) =>
        value > 0
          ? formatCurrency(value).replace('R$', '').trim()
          : '0',
      align: 'right',
    },
    {
      key: 'tributos_pendentes',
      title: 'Pendentes',
      render: (value) =>
        value && value.length > 0 ? value.join(', ').toUpperCase() : '-',
    },
    {
      key: 'actions',
      title: '',
      render: (_, empresa) => (
        <Button
          variant="blue"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            handleNavigate(empresa)
          }}
          icon={
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          }
          iconPosition="right"
          glow
        >
          Ver Detalhes
        </Button>
      ),
      align: 'right',
    },
  ]

  return (
    <div className="p-6">
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {empresas.map((empresa) => {
            const progressPercent = Math.round(((empresa.tributos_auditados?.length || 0) / 6) * 100)

            return (
              <div
                key={empresa.id}
                className="card hover:shadow-lg transition-all duration-200 hover:-translate-y-1"
            >
              <div className="p-4">
                {/* Header com nome e status */}
                <div className="flex justify-between items-start mb-3">
                  <h5 className="font-semibold text-gray-900 dark:text-white text-sm leading-tight">
                    {empresa.razao_social}
                  </h5>
                  {getStatusBadge(empresa.status_auditoria)}
                </div>

                {/* CNPJ */}
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                  CNPJ: {formatCNPJ(empresa.cnpj)}
                </p>

                {/* Barra de progresso */}
                <div className="mb-3">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-600 dark:text-gray-400">Progresso</span>
                    <span className="text-xs font-medium text-gray-900 dark:text-white">{progressPercent}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        empresa.status_auditoria === 'completa' 
                          ? 'bg-green-500' 
                          : empresa.status_auditoria === 'parcial'
                          ? 'bg-yellow-500'
                          : 'bg-red-500'
                      }`}
                      style={{ width: `${progressPercent}%` }}
                    />
                  </div>
                </div>

                {/* Métricas em grid */}
                <div className="grid grid-cols-3 gap-2 text-center mb-3">
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Auditados</div>
                    <div className="font-bold text-sm text-gray-900 dark:text-white">
                      {empresa.tributos_auditados?.length || 0}/6
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Inconsistências</div>
                    <div className="font-bold text-sm text-red-600 dark:text-red-400">
                      {empresa.total_inconsistencias || 0}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Valor</div>
                    <div className="font-bold text-sm text-gray-900 dark:text-white">
                      {empresa.total_valor_inconsistente > 0 
                        ? formatCurrency(empresa.total_valor_inconsistente).replace('R$', '').trim()
                        : '0'
                      }
                    </div>
                  </div>
                </div>

                {/* Tributos pendentes */}
                {empresa.tributos_pendentes && empresa.tributos_pendentes.length > 0 && (
                  <div className="mb-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded text-center">
                    <div className="text-xs text-yellow-700 dark:text-yellow-400">
                      Pendentes: {empresa.tributos_pendentes.join(', ').toUpperCase()}
                    </div>
                  </div>
                )}

                {/* Botão Ver Detalhes */}
                <Button
                  variant="blue"
                  size="md"
                  fullWidth
                  onClick={() => {
                    localStorage.setItem('selectedCompany', empresa.id.toString())
                    navigate(`/fiscal/dashboard/empresa/${empresa.id}?tipo=${tipo}`)
                  }}
                  icon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  }
                  iconPosition="right"
                  glow
                >
                  🔍 Ver Detalhes
                </Button>
              </div>
            </div>
          )
          })}
        </div>
      ) : (
        <Table<Empresa>
          data={empresas}
          columns={columns}
          onRowClick={handleNavigate}
        />
      )}
    </div>
  )
}
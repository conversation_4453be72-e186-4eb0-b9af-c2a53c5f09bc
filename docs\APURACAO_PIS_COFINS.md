# Apuração de PIS e COFINS

Este documento descreve a implementação dos cálculos e gráficos de **PIS** e **COFINS** no módulo de Apuração.

## Serviço `ApuracaoPisCofinsService`
- Local: `back/services/apuracao_pis_cofins_service.py`
- Calcula débitos e créditos a partir de `Tributo` e `NotaFiscalItem`.
- Filtra por `empresa_id`, ano e mês da emissão (`Tributo.data_emissao`).
- **Créditos**: soma de `pis_valor` ou `cofins_valor` das notas com CFOP iniciado em `1`, `2` ou `3`.
- **Débitos**: soma de `pis_valor` ou `cofins_valor` das notas com CFOP iniciado em `5` ou `6`.
- O valor "a recolher" é a diferença entre débitos e créditos.

```python
service = ApuracaoPisCofinsService(empresa_id)
resultado = service.calcular_pis(ano, mes)
# => { 'vl_tot_debitos': 0.0, 'vl_tot_creditos': 0.0, 'vl_pis_a_recolher': 0.0 }
```

## Endpoints de Histórico
Foram criadas novas rotas em `back/routes/apuracao_routes.py`:

- `/api/apuracao/<empresa_id>/historico/pis`
- `/api/apuracao/<empresa_id>/historico/cofins`

Cada endpoint retorna os últimos 12 meses a partir do período informado (ou mês atual) com rótulos e valores para os gráficos.

Exemplo de resposta:
```json
{
  "success": true,
  "empresa": { "id": 1, "razao_social": "Empresa", "cnpj": "..." },
  "periodo": { "inicio": "Jul 2024", "fim": "Jun 2025" },
  "dados": {
    "labels": ["Jul 24", ...],
    "vl_tot_debitos": [0.0, ...],
    "vl_tot_creditos": [0.0, ...],
    "vl_pis_a_recolher": [0.0, ...]
  }
}
```

## Novas Tabelas
Os modelos `ApuracaoPIS` e `ApuracaoCOFINS` foram adicionados em `back/models/apuracao.py`. Eles permitem registrar os valores calculados por empresa, ano e mês, mas atualmente os dados são gerados sob demanda pelo serviço.

## Atualizações no Front-end
`front/static/js/apuracao.js` e `front/static/js/apuracao_graficos.js` incluem:
- Abas "Gráficos PIS" e "Gráficos COFINS" na página de Apuração.
- Funções que consultam os novos endpoints e exibem três gráficos:
  1. Imposto a Recolher/Saldo Credor Transportar
  2. Valor Total de Débitos
  3. Valor Total de Créditos
- Os gráficos utilizam Chart.js e respeitam o tema claro/escuro.

## Resumo
Com essas alterações o sistema passa a calcular PIS e COFINS a partir das notas fiscais e disponibilizar o histórico em forma de gráficos, de modo semelhante aos módulos de ICMS e IPI.

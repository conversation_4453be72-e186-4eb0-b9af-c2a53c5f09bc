from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Tributo, Cliente, Produto, TributoHistorico, Empresa, NotaFiscalItem, CenarioICMS
from services.transactional import transactional_session
from datetime import datetime

tributo_bp = Blueprint('tributo_bp', __name__)

def get_tributo_values(tributo, tipo_tributo):
    """
    Obtém os valores de um tributo específico

    Args:
        tributo (Tributo): Objeto do tributo
        tipo_tributo (str): Tipo de tributo (icms, icms_st, ipi, pis, cofins, difal)

    Returns:
        dict: Dicionário com os valores do tributo
    """
    values = {}

    if tipo_tributo == 'icms':
        values = {
            'icms_origem': tributo.icms_origem,
            'icms_cst': tributo.icms_cst,
            'icms_mod_bc': tributo.icms_mod_bc,
            'icms_p_red_bc': float(tributo.icms_p_red_bc) if tributo.icms_p_red_bc is not None else None,
            'icms_vbc': float(tributo.icms_vbc) if tributo.icms_vbc is not None else None,
            'icms_aliquota': float(tributo.icms_aliquota) if tributo.icms_aliquota is not None else None,
            'icms_valor': float(tributo.icms_valor) if tributo.icms_valor is not None else None,
            'icms_v_op': float(tributo.icms_v_op) if tributo.icms_v_op is not None else None,
            'icms_p_dif': float(tributo.icms_p_dif) if tributo.icms_p_dif is not None else None,
            'icms_v_dif': float(tributo.icms_v_dif) if tributo.icms_v_dif is not None else None,
        }
    elif tipo_tributo == 'icms_st':
        values = {
            'icms_st_mod_bc': tributo.icms_st_mod_bc,
            'icms_st_p_mva': float(tributo.icms_st_p_mva) if tributo.icms_st_p_mva is not None else None,
            'icms_st_p_red_bc': float(tributo.icms_st_p_red_bc) if tributo.icms_st_p_red_bc is not None else None,
            'icms_st_vbc': float(tributo.icms_st_vbc) if tributo.icms_st_vbc is not None else None,
            'icms_st_aliquota': float(tributo.icms_st_aliquota) if tributo.icms_st_aliquota is not None else None,
            'icms_st_valor': float(tributo.icms_st_valor) if tributo.icms_st_valor is not None else None,
        }
    elif tipo_tributo == 'ipi':
        values = {
            'ipi_cst': tributo.ipi_cst,
            'ipi_vbc': float(tributo.ipi_vbc) if tributo.ipi_vbc is not None else None,
            'ipi_aliquota': float(tributo.ipi_aliquota) if tributo.ipi_aliquota is not None else None,
            'ipi_valor': float(tributo.ipi_valor) if tributo.ipi_valor is not None else None,
            'ipi_codigo_enquadramento': tributo.ipi_codigo_enquadramento,
        }
    elif tipo_tributo == 'pis':
        values = {
            'pis_cst': tributo.pis_cst,
            'pis_vbc': float(tributo.pis_vbc) if tributo.pis_vbc is not None else None,
            'pis_aliquota': float(tributo.pis_aliquota) if tributo.pis_aliquota is not None else None,
            'pis_valor': float(tributo.pis_valor) if tributo.pis_valor is not None else None,
        }
    elif tipo_tributo == 'cofins':
        values = {
            'cofins_cst': tributo.cofins_cst,
            'cofins_vbc': float(tributo.cofins_vbc) if tributo.cofins_vbc is not None else None,
            'cofins_aliquota': float(tributo.cofins_aliquota) if tributo.cofins_aliquota is not None else None,
            'cofins_valor': float(tributo.cofins_valor) if tributo.cofins_valor is not None else None,
        }
    elif tipo_tributo == 'difal':
        values = {
            'difal_vbc': float(tributo.difal_vbc) if tributo.difal_vbc is not None else None,
            'difal_p_fcp_uf_dest': float(tributo.difal_p_fcp_uf_dest) if tributo.difal_p_fcp_uf_dest is not None else None,
            'difal_p_icms_uf_dest': float(tributo.difal_p_icms_uf_dest) if tributo.difal_p_icms_uf_dest is not None else None,
            'difal_p_icms_inter': float(tributo.difal_p_icms_inter) if tributo.difal_p_icms_inter is not None else None,
            'difal_p_icms_inter_part': float(tributo.difal_p_icms_inter_part) if tributo.difal_p_icms_inter_part is not None else None,
            'difal_v_fcp_uf_dest': float(tributo.difal_v_fcp_uf_dest) if tributo.difal_v_fcp_uf_dest is not None else None,
            'difal_v_icms_uf_dest': float(tributo.difal_v_icms_uf_dest) if tributo.difal_v_icms_uf_dest is not None else None,
            'difal_v_icms_uf_remet': float(tributo.difal_v_icms_uf_remet) if tributo.difal_v_icms_uf_remet is not None else None,
        }

    return values

@tributo_bp.route('/api/tributos', methods=['GET'])
@jwt_required()
def listar_tributos():
    """
    Lista os tributos com base nas permissões do usuário
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        tipo_tributo = request.args.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal
        status = request.args.get('status')  # novo, producao, inconsistente, conforme
        direcao = request.args.get('direcao')  # entrada, saida
        cliente_id = request.args.get('cliente_id', type=int)
        produto_id = request.args.get('produto_id', type=int)
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)

        with transactional_session():
            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return jsonify({"message": "Usuário não encontrado"}), 404

            # Construir a query base
            query = Tributo.query

            # Filtrar por empresa se especificado
            if empresa_id:
                query = query.filter_by(empresa_id=empresa_id)

            # Filtrar por direção (entrada/saída) usando o campo tipo_operacao
            if direcao == 'entrada':
                # Para entrada, tipo_operacao deve ser '0'
                query = query.filter(Tributo.tipo_operacao == '0')
            elif direcao == 'saida':
                # Para saída, tipo_operacao deve ser '1'
                query = query.filter(Tributo.tipo_operacao == '1')

            # Filtrar por cliente se especificado
            if cliente_id:
                query = query.filter_by(cliente_id=cliente_id)

            # Filtrar por produto se especificado
            if produto_id:
                query = query.filter_by(produto_id=produto_id)

            # Filtrar por ano se especificado
            if year:
                from sqlalchemy import extract
                query = query.filter(extract('year', Tributo.data_emissao) == year)

            # Filtrar por mês se especificado
            if month:
                from sqlalchemy import extract
                query = query.filter(extract('month', Tributo.data_emissao) == month)

            # Filtrar por status específico do tipo de tributo
            if tipo_tributo and status:
                status_column = f"{tipo_tributo}_status"
                if hasattr(Tributo, status_column):
                    query = query.filter(getattr(Tributo, status_column) == status)

            # Aplicar filtros com base no tipo de usuário
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores veem todos os tributos
                pass
            elif usuario.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório veem tributos do seu escritório
                query = query.filter_by(escritorio_id=usuario.escritorio_id)
            else:
                # Usuários comuns veem apenas tributos das empresas permitidas
                empresas_permitidas = usuario.empresas_permitidas or []
                query = query.filter(Tributo.empresa_id.in_(empresas_permitidas))

            # Executar a query
            tributos = query.all()

            # Carregar informações relacionadas (cliente e produto)
            result = []
            for tributo in tributos:
                tributo_dict = tributo.to_dict()

                # Adicionar informações do cliente
                cliente = db.session.get(Cliente, tributo.cliente_id)
                if cliente:
                    tributo_dict['cliente'] = {
                        'id': cliente.id,
                        'razao_social': cliente.razao_social,
                        'cnpj': cliente.cnpj,
                        'uf': cliente.uf,
                        'municipio': cliente.municipio
                    }

                # Adicionar informações do produto
                produto = db.session.get(Produto, tributo.produto_id)
                if produto:
                    # Buscar o NCM e CFOP da nota fiscal relacionada a este tributo
                    nota_fiscal_item = NotaFiscalItem.query.filter_by(
                        id=tributo.nota_fiscal_item_id
                    ).first() if hasattr(tributo, 'nota_fiscal_item_id') and tributo.nota_fiscal_item_id else None

                    # Se não encontrar na nota fiscal, buscar o mais recente para este produto
                    if not nota_fiscal_item:
                        nota_fiscal_item = NotaFiscalItem.query.filter_by(
                            produto_id=produto.id
                        ).order_by(NotaFiscalItem.data_emissao.desc()).first()

                    ncm = '-'
                    cfop = '-'

                    if nota_fiscal_item:
                        ncm = nota_fiscal_item.ncm
                        cfop = nota_fiscal_item.cfop
                    else:
                        # Se não encontrar na tabela nota_fiscal_item, buscar na tabela cenario_icms
                        cenario = CenarioICMS.query.filter_by(
                            produto_id=produto.id
                        ).order_by(CenarioICMS.data_atualizacao.desc()).first()

                        if cenario:
                            ncm = cenario.ncm
                            cfop = cenario.cfop

                    tributo_dict['produto'] = {
                        'id': produto.id,
                        'codigo': produto.codigo,
                        'descricao': produto.descricao,
                        'ncm': ncm,
                        'cfop': cfop,
                        'unidade_comercial': produto.unidade_comercial,
                        'unidade_tributavel': produto.unidade_tributavel
                    }

                result.append(tributo_dict)

            return jsonify({
                "tributos": result
            }), 200

    except Exception as e:
        print(f"Erro ao listar tributos: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@tributo_bp.route('/api/tributos/<int:tributo_id>', methods=['GET'])
@jwt_required()
def obter_tributo(tributo_id):
    """
    Obtém os detalhes de um tributo específico
    """
    try:
        with transactional_session():
            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return jsonify({"message": "Usuário não encontrado"}), 404

            # Buscar o tributo
            tributo = db.session.get(Tributo, tributo_id)

            if not tributo:
                return jsonify({"message": "Tributo não encontrado"}), 404

            # Verificar permissões para visualizar o tributo
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem ver qualquer tributo
                pass
            elif usuario.tipo_usuario == 'escritorio' and tributo.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem ver tributos do seu próprio escritório
                pass
            elif usuario.empresas_permitidas and tributo.empresa_id in usuario.empresas_permitidas:
                # Usuários comuns só podem ver tributos das empresas permitidas
                pass
            else:
                return jsonify({"message": "Você não tem permissão para visualizar este tributo"}), 403

            # Converter para dicionário
            tributo_dict = tributo.to_dict()

            # Adicionar informações do cliente
            cliente = db.session.get(Cliente, tributo.cliente_id)
            if cliente:
                tributo_dict['cliente'] = cliente.to_dict()

            # Adicionar informações do produto
            produto = db.session.get(Produto, tributo.produto_id)
            if produto:
                produto_dict = produto.to_dict()

                # Buscar o NCM e CFOP da nota fiscal relacionada a este tributo
                nota_fiscal_item = NotaFiscalItem.query.filter_by(
                    id=tributo.nota_fiscal_item_id
                ).first() if hasattr(tributo, 'nota_fiscal_item_id') and tributo.nota_fiscal_item_id else None

                # Se não encontrar na nota fiscal, buscar o mais recente para este produto
                if not nota_fiscal_item:
                    nota_fiscal_item = NotaFiscalItem.query.filter_by(
                        produto_id=produto.id
                    ).order_by(NotaFiscalItem.data_emissao.desc()).first()

                if nota_fiscal_item:
                    produto_dict['ncm'] = nota_fiscal_item.ncm
                    produto_dict['cfop'] = nota_fiscal_item.cfop
                else:
                    # Se não encontrar na tabela nota_fiscal_item, buscar na tabela cenario_icms
                    cenario = CenarioICMS.query.filter_by(
                        produto_id=produto.id
                    ).order_by(CenarioICMS.data_atualizacao.desc()).first()

                    if cenario:
                        produto_dict['ncm'] = cenario.ncm
                        produto_dict['cfop'] = cenario.cfop
                    else:
                        produto_dict['ncm'] = '-'
                        produto_dict['cfop'] = '-'

                tributo_dict['produto'] = produto_dict

            return jsonify({
                "tributo": tributo_dict
            }), 200

    except Exception as e:
        print(f"Erro ao obter tributo: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@tributo_bp.route('/api/tributos/<int:tributo_id>/status', methods=['PUT'])
@jwt_required()
def atualizar_status_tributo(tributo_id):
    """
    Atualiza o status de um tributo específico
    """
    try:
        data = request.get_json()
        tipo_tributo = data.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal
        novo_status = data.get('status')  # novo, producao, inconsistente, conforme

        if not tipo_tributo or not novo_status:
            return jsonify({"message": "Tipo de tributo e status são obrigatórios"}), 400

        with transactional_session():
            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return jsonify({"message": "Usuário não encontrado"}), 404

            # Buscar o tributo
            tributo = db.session.get(Tributo, tributo_id)

            if not tributo:
                return jsonify({"message": "Tributo não encontrado"}), 404

            # Verificar permissões para atualizar o tributo
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem atualizar qualquer tributo
                pass
            elif usuario.tipo_usuario == 'escritorio' and tributo.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem atualizar tributos do seu próprio escritório
                pass
            elif usuario.empresas_permitidas and tributo.empresa_id in usuario.empresas_permitidas:
                # Usuários comuns só podem atualizar tributos das empresas permitidas
                pass
            else:
                return jsonify({"message": "Você não tem permissão para atualizar este tributo"}), 403

            # Atualizar o status específico do tipo de tributo
            status_column = f"{tipo_tributo}_status"
            if hasattr(Tributo, status_column):
                # Obter o status anterior
                status_anterior = getattr(tributo, status_column)

                # Se o status for o mesmo, não fazer nada
                if status_anterior == novo_status:
                    return jsonify({
                        "message": f"Status do tributo {tipo_tributo} já é {novo_status}",
                        "tributo": tributo.to_dict()
                    }), 200

                # Registrar o histórico de alteração
                historico = TributoHistorico(
                    tributo_id=tributo.id,
                    usuario_id=usuario_id,
                    tipo_tributo=tipo_tributo,
                    status_anterior=status_anterior,
                    status_novo=novo_status,
                    valores_anteriores=None,
                    valores_novos=None
                )
                db.session.add(historico)

                # Atualizar o status
                setattr(tributo, status_column, novo_status)

                return jsonify({
                    "message": f"Status do tributo {tipo_tributo} atualizado para {novo_status}",
                    "tributo": tributo.to_dict()
                }), 200
            else:
                return jsonify({"message": f"Tipo de tributo {tipo_tributo} inválido"}), 400

    except Exception as e:
        print(f"Erro ao atualizar status do tributo: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@tributo_bp.route('/api/tributos/reverificar-status', methods=['POST'])
@jwt_required()
def reverificar_status_tributos():
    """
    Reverifica o status de todos os tributos para uma empresa específica
    Compara tributos novos e inconsistentes com tributos em produção
    e atualiza seus status para 'conforme' ou 'inconsistente'

    Parâmetros:
    - empresa_id: ID da empresa (obrigatório)
    - tipo_tributo: Tipo de tributo a ser verificado (opcional)
    - produto_id: ID do produto para limitar a verificação (opcional)
    """
    try:
        data = request.get_json()
        empresa_id = data.get('empresa_id')
        tipo_tributo = data.get('tipo_tributo')  # opcional, se não fornecido, verifica todos os tipos
        produto_id = data.get('produto_id')  # opcional, se fornecido, verifica apenas para este produto

        if not empresa_id:
            return jsonify({"message": "ID da empresa é obrigatório"}), 400

        with transactional_session():
            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return jsonify({"message": "Usuário não encontrado"}), 404

            # Verificar permissões para a empresa
            if not usuario.is_admin and not usuario.tipo_usuario == 'admin':
                if usuario.tipo_usuario == 'escritorio':
                    # Verificar se a empresa pertence ao escritório do usuário
                    empresa = db.session.query(Empresa).filter_by(id=empresa_id).first()
                    if not empresa or empresa.escritorio_id != usuario.escritorio_id:
                        return jsonify({"message": "Você não tem permissão para esta empresa"}), 403
                elif usuario.empresas_permitidas and empresa_id not in usuario.empresas_permitidas:
                    return jsonify({"message": "Você não tem permissão para esta empresa"}), 403

            # Definir os tipos de tributo a serem verificados
            tipos_tributo = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']
            if tipo_tributo and tipo_tributo in tipos_tributo:
                tipos_tributo = [tipo_tributo]

            # Contador de atualizações
            atualizacoes = {
                'total': 0,
                'conforme': 0,
                'inconsistente': 0,
                'por_tipo': {}
            }

            for tipo in tipos_tributo:
                atualizacoes['por_tipo'][tipo] = {
                    'total': 0,
                    'conforme': 0,
                    'inconsistente': 0
                }

                # Buscar todos os tributos em produção para este tipo
                query = Tributo.query.filter_by(
                    empresa_id=empresa_id,
                    **{f"{tipo}_status": 'producao'}
                )

                # Se produto_id foi fornecido, filtrar apenas para este produto
                if produto_id:
                    query = query.filter_by(produto_id=produto_id)

                tributos_producao = query.all()

                # Para cada tributo em produção, buscar tributos novos ou inconsistentes
                # para o mesmo produto e cliente
                for tributo_producao in tributos_producao:
                    # Buscar tributos novos ou inconsistentes para o mesmo produto e cliente
                    tributos_para_verificar = Tributo.query.filter(
                        Tributo.empresa_id == empresa_id,
                        Tributo.produto_id == tributo_producao.produto_id,
                        Tributo.cliente_id == tributo_producao.cliente_id,
                        getattr(Tributo, f"{tipo}_status").in_(['novo', 'inconsistente'])
                    ).all()

                for tributo in tributos_para_verificar:
                    # Comparar valores com base no tipo de tributo
                    status_anterior = getattr(tributo, f"{tipo}_status")

                    if tipo == 'icms':
                        if (tributo.icms_cst == tributo_producao.icms_cst and
                            tributo.icms_aliquota == tributo_producao.icms_aliquota and
                            tributo.icms_p_red_bc == tributo_producao.icms_p_red_bc):
                            setattr(tributo, f"{tipo}_status", 'conforme')
                            atualizacoes['conforme'] += 1
                            atualizacoes['por_tipo'][tipo]['conforme'] += 1
                        else:
                            setattr(tributo, f"{tipo}_status", 'inconsistente')
                            atualizacoes['inconsistente'] += 1
                            atualizacoes['por_tipo'][tipo]['inconsistente'] += 1
                    elif tipo == 'icms_st':
                        if (tributo.icms_st_p_mva == tributo_producao.icms_st_p_mva and
                            tributo.icms_st_aliquota == tributo_producao.icms_st_aliquota and
                            tributo.icms_st_p_red_bc == tributo_producao.icms_st_p_red_bc):
                            setattr(tributo, f"{tipo}_status", 'conforme')
                            atualizacoes['conforme'] += 1
                            atualizacoes['por_tipo'][tipo]['conforme'] += 1
                        else:
                            setattr(tributo, f"{tipo}_status", 'inconsistente')
                            atualizacoes['inconsistente'] += 1
                            atualizacoes['por_tipo'][tipo]['inconsistente'] += 1
                    elif tipo == 'ipi':
                        if (tributo.ipi_cst == tributo_producao.ipi_cst and
                            tributo.ipi_aliquota == tributo_producao.ipi_aliquota):
                            setattr(tributo, f"{tipo}_status", 'conforme')
                            atualizacoes['conforme'] += 1
                            atualizacoes['por_tipo'][tipo]['conforme'] += 1
                        else:
                            setattr(tributo, f"{tipo}_status", 'inconsistente')
                            atualizacoes['inconsistente'] += 1
                            atualizacoes['por_tipo'][tipo]['inconsistente'] += 1
                    elif tipo == 'pis':
                        if (tributo.pis_cst == tributo_producao.pis_cst and
                            tributo.pis_aliquota == tributo_producao.pis_aliquota):
                            setattr(tributo, f"{tipo}_status", 'conforme')
                            atualizacoes['conforme'] += 1
                            atualizacoes['por_tipo'][tipo]['conforme'] += 1
                        else:
                            setattr(tributo, f"{tipo}_status", 'inconsistente')
                            atualizacoes['inconsistente'] += 1
                            atualizacoes['por_tipo'][tipo]['inconsistente'] += 1
                    elif tipo == 'cofins':
                        if (tributo.cofins_cst == tributo_producao.cofins_cst and
                            tributo.cofins_aliquota == tributo_producao.cofins_aliquota):
                            setattr(tributo, f"{tipo}_status", 'conforme')
                            atualizacoes['conforme'] += 1
                            atualizacoes['por_tipo'][tipo]['conforme'] += 1
                        else:
                            setattr(tributo, f"{tipo}_status", 'inconsistente')
                            atualizacoes['inconsistente'] += 1
                            atualizacoes['por_tipo'][tipo]['inconsistente'] += 1
                    elif tipo == 'difal':
                        if (tributo.difal_p_icms_uf_dest == tributo_producao.difal_p_icms_uf_dest and
                            tributo.difal_p_icms_inter == tributo_producao.difal_p_icms_inter):
                            setattr(tributo, f"{tipo}_status", 'conforme')
                            atualizacoes['conforme'] += 1
                            atualizacoes['por_tipo'][tipo]['conforme'] += 1
                        else:
                            setattr(tributo, f"{tipo}_status", 'inconsistente')
                            atualizacoes['inconsistente'] += 1
                            atualizacoes['por_tipo'][tipo]['inconsistente'] += 1

                    # Registrar histórico se o status mudou
                    if status_anterior != getattr(tributo, f"{tipo}_status"):
                        historico = TributoHistorico(
                            tributo_id=tributo.id,
                            usuario_id=usuario_id,
                            tipo_tributo=tipo,
                            status_anterior=status_anterior,
                            status_novo=getattr(tributo, f"{tipo}_status"),
                            valores_anteriores=None,
                            valores_novos=None
                        )
                        db.session.add(historico)

                    atualizacoes['total'] += 1
                    atualizacoes['por_tipo'][tipo]['total'] += 1

            # Personalizar a mensagem com base nos filtros aplicados
            mensagem = f"Status de {atualizacoes['total']} tributos reverificados com sucesso. {atualizacoes['conforme']} conformes, {atualizacoes['inconsistente']} inconsistentes."

            # Adicionar detalhes sobre os filtros aplicados
            if produto_id:
                produto = db.session.get(Produto, produto_id)
                if produto:
                    mensagem += f" Filtrado para o produto: {produto.codigo} - {produto.descricao}."

            if tipo_tributo:
                mensagem += f" Tipo de tributo: {tipo_tributo.upper()}."

            return jsonify({
                "message": mensagem,
                "atualizacoes": atualizacoes
            }), 200

    except Exception as e:
        print(f"Erro ao reverificar status dos tributos: {str(e)}")
        return jsonify({"message": f"Erro ao processar solicitação: {str(e)}"}), 500

@tributo_bp.route('/api/tributos/<int:tributo_id>/historico', methods=['GET'])
@jwt_required()
def obter_historico_tributo(tributo_id):
    """
    Obtém o histórico de alterações de um tributo específico
    """
    try:
        with transactional_session():
            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return jsonify({"message": "Usuário não encontrado"}), 404

            # Buscar o tributo
            tributo = db.session.get(Tributo, tributo_id)

            if not tributo:
                return jsonify({"message": "Tributo não encontrado"}), 404

            # Verificar permissões para visualizar o tributo
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem ver qualquer tributo
                pass
            elif usuario.tipo_usuario == 'escritorio' and tributo.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem ver tributos do seu próprio escritório
                pass
            elif usuario.empresas_permitidas and tributo.empresa_id in usuario.empresas_permitidas:
                # Usuários comuns só podem ver tributos das empresas permitidas
                pass
            else:
                return jsonify({"message": "Você não tem permissão para visualizar este tributo"}), 403

            # Buscar o histórico do tributo
            historico = TributoHistorico.query.filter_by(tributo_id=tributo_id).order_by(TributoHistorico.data_alteracao.desc()).all()

            # Converter para dicionário
            historico_list = [h.to_dict() for h in historico]

            return jsonify({
                "historico": historico_list
            }), 200

    except Exception as e:
        print(f"Erro ao obter histórico do tributo: {str(e)}")
        return jsonify({"message": f"Erro ao processar solicitação: {str(e)}"}), 500

@tributo_bp.route('/api/tributos/<int:tributo_id>', methods=['PUT'])
@jwt_required()
def atualizar_tributo(tributo_id):
    """
    Atualiza os valores de um tributo específico
    """
    try:
        data = request.get_json()
        tipo_tributo = data.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal

        if not tipo_tributo:
            return jsonify({"message": "Tipo de tributo é obrigatório"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o tributo
        tributo = db.session.get(Tributo, tributo_id)

        if not tributo:
            return jsonify({"message": "Tributo não encontrado"}), 404

        # Verificar permissões para atualizar o tributo
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem atualizar qualquer tributo
            pass
        elif usuario.tipo_usuario == 'escritorio' and tributo.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem atualizar tributos do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and tributo.empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem atualizar tributos das empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para atualizar este tributo"}), 403

        # Capturar os valores anteriores
        valores_anteriores = get_tributo_values(tributo, tipo_tributo)

        # Atualizar os valores do tributo com base no tipo
        if tipo_tributo == 'icms':
            # Atualizar valores de ICMS
            if 'icms_origem' in data:
                tributo.icms_origem = data.get('icms_origem')
            if 'icms_cst' in data:
                tributo.icms_cst = data.get('icms_cst')
            if 'icms_mod_bc' in data:
                tributo.icms_mod_bc = data.get('icms_mod_bc')
            if 'icms_p_red_bc' in data:
                tributo.icms_p_red_bc = data.get('icms_p_red_bc')
            if 'icms_vbc' in data:
                tributo.icms_vbc = data.get('icms_vbc')
            if 'icms_aliquota' in data:
                tributo.icms_aliquota = data.get('icms_aliquota')
            if 'icms_valor' in data:
                tributo.icms_valor = data.get('icms_valor')
            if 'icms_v_op' in data:
                tributo.icms_v_op = data.get('icms_v_op')
            if 'icms_p_dif' in data:
                tributo.icms_p_dif = data.get('icms_p_dif')
            if 'icms_v_dif' in data:
                tributo.icms_v_dif = data.get('icms_v_dif')

        elif tipo_tributo == 'icms_st':
            # Atualizar valores de ICMS-ST
            if 'icms_st_mod_bc' in data:
                tributo.icms_st_mod_bc = data.get('icms_st_mod_bc')
            if 'icms_st_p_mva' in data:
                tributo.icms_st_p_mva = data.get('icms_st_p_mva')
            if 'icms_st_p_red_bc' in data:
                tributo.icms_st_p_red_bc = data.get('icms_st_p_red_bc')
            if 'icms_st_vbc' in data:
                tributo.icms_st_vbc = data.get('icms_st_vbc')
            if 'icms_st_aliquota' in data:
                tributo.icms_st_aliquota = data.get('icms_st_aliquota')
            if 'icms_st_valor' in data:
                tributo.icms_st_valor = data.get('icms_st_valor')

        elif tipo_tributo == 'ipi':
            # Atualizar valores de IPI
            if 'ipi_cst' in data:
                tributo.ipi_cst = data.get('ipi_cst')
            if 'ipi_vbc' in data:
                tributo.ipi_vbc = data.get('ipi_vbc')
            if 'ipi_aliquota' in data:
                tributo.ipi_aliquota = data.get('ipi_aliquota')
            if 'ipi_valor' in data:
                tributo.ipi_valor = data.get('ipi_valor')
            if 'ipi_codigo_enquadramento' in data:
                tributo.ipi_codigo_enquadramento = data.get('ipi_codigo_enquadramento')

        elif tipo_tributo == 'pis':
            # Atualizar valores de PIS
            if 'pis_cst' in data:
                tributo.pis_cst = data.get('pis_cst')
            if 'pis_vbc' in data:
                tributo.pis_vbc = data.get('pis_vbc')
            if 'pis_aliquota' in data:
                tributo.pis_aliquota = data.get('pis_aliquota')
            if 'pis_valor' in data:
                tributo.pis_valor = data.get('pis_valor')

        elif tipo_tributo == 'cofins':
            # Atualizar valores de COFINS
            if 'cofins_cst' in data:
                tributo.cofins_cst = data.get('cofins_cst')
            if 'cofins_vbc' in data:
                tributo.cofins_vbc = data.get('cofins_vbc')
            if 'cofins_aliquota' in data:
                tributo.cofins_aliquota = data.get('cofins_aliquota')
            if 'cofins_valor' in data:
                tributo.cofins_valor = data.get('cofins_valor')

        elif tipo_tributo == 'difal':
            # Atualizar valores de DIFAL
            if 'difal_vbc' in data:
                tributo.difal_vbc = data.get('difal_vbc')
            if 'difal_p_fcp_uf_dest' in data:
                tributo.difal_p_fcp_uf_dest = data.get('difal_p_fcp_uf_dest')
            if 'difal_p_icms_uf_dest' in data:
                tributo.difal_p_icms_uf_dest = data.get('difal_p_icms_uf_dest')
            if 'difal_p_icms_inter' in data:
                tributo.difal_p_icms_inter = data.get('difal_p_icms_inter')
            if 'difal_p_icms_inter_part' in data:
                tributo.difal_p_icms_inter_part = data.get('difal_p_icms_inter_part')
            if 'difal_v_fcp_uf_dest' in data:
                tributo.difal_v_fcp_uf_dest = data.get('difal_v_fcp_uf_dest')
            if 'difal_v_icms_uf_dest' in data:
                tributo.difal_v_icms_uf_dest = data.get('difal_v_icms_uf_dest')
            if 'difal_v_icms_uf_remet' in data:
                tributo.difal_v_icms_uf_remet = data.get('difal_v_icms_uf_remet')
        else:
            return jsonify({"message": f"Tipo de tributo {tipo_tributo} inválido"}), 400

        # Verificar se o produto também deve ser atualizado
        if 'produto' in data and data['produto']:
            produto = db.session.get(Produto, tributo.produto_id)
            if produto:
                produto_data = data['produto']
                if 'codigo' in produto_data:
                    produto.codigo = produto_data['codigo']
                if 'descricao' in produto_data:
                    produto.descricao = produto_data['descricao']
                # Campos ncm e cfop removidos - agora estão apenas nas tabelas nota_fiscal_item e cenarios
                if 'unidade_comercial' in produto_data:
                    produto.unidade_comercial = produto_data['unidade_comercial']
                if 'unidade_tributavel' in produto_data:
                    produto.unidade_tributavel = produto_data['unidade_tributavel']
                if 'codigo_ean' in produto_data:
                    produto.codigo_ean = produto_data['codigo_ean']
                if 'codigo_ean_tributavel' in produto_data:
                    produto.codigo_ean_tributavel = produto_data['codigo_ean_tributavel']
                if 'unidade_tributaria' in produto_data:
                    produto.unidade_tributaria = produto_data['unidade_tributaria']

        # Verificar se o cliente também deve ser atualizado
        if 'cliente' in data and data['cliente']:
            cliente = db.session.get(Cliente, tributo.cliente_id)
            if cliente:
                cliente_data = data['cliente']
                if 'razao_social' in cliente_data:
                    cliente.razao_social = cliente_data['razao_social']
                if 'nome_fantasia' in cliente_data:
                    cliente.nome_fantasia = cliente_data['nome_fantasia']
                if 'cnpj' in cliente_data:
                    cliente.cnpj = cliente_data['cnpj']
                if 'cpf' in cliente_data:
                    cliente.cpf = cliente_data['cpf']
                if 'inscricao_estadual' in cliente_data:
                    cliente.inscricao_estadual = cliente_data['inscricao_estadual']
                if 'endereco' in cliente_data:
                    cliente.endereco = cliente_data['endereco']
                if 'municipio' in cliente_data:
                    cliente.municipio = cliente_data['municipio']
                if 'uf' in cliente_data:
                    cliente.uf = cliente_data['uf']
                if 'atividade' in cliente_data:
                    cliente.atividade = cliente_data['atividade']
                if 'destinacao' in cliente_data:
                    cliente.destinacao = cliente_data['destinacao']
                if 'cnae' in cliente_data:
                    cliente.cnae = cliente_data['cnae']
                if 'simples_nacional' in cliente_data:
                    cliente.simples_nacional = cliente_data['simples_nacional']

        # Capturar os novos valores
        valores_novos = get_tributo_values(tributo, tipo_tributo)

        # Registrar o histórico de alteração
        historico = TributoHistorico(
            tributo_id=tributo.id,
            usuario_id=usuario_id,
            tipo_tributo=tipo_tributo,
            status_anterior=valores_anteriores.get('status'),
            status_novo=valores_novos.get('status'),
            valores_anteriores=valores_anteriores,
            valores_novos=valores_novos
        )
        db.session.add(historico)

        # Salvar as alterações
        db.session.commit()

        # Verificar se o tributo em produção precisa ser comparado novamente
        if tipo_tributo and tributo[f"{tipo_tributo}_status"] == 'inconsistente':
            # Buscar tributo em produção para o mesmo produto e cliente
            tributo_producao = Tributo.query.filter_by(
                empresa_id=tributo.empresa_id,
                produto_id=tributo.produto_id,
                cliente_id=tributo.cliente_id,
                **{f"{tipo_tributo}_status": 'producao'}
            ).first()

        return jsonify({
            "message": f"Tributo {tipo_tributo} atualizado com sucesso",
            "tributo": tributo.to_dict()
        }), 200

    except Exception as e:
        print(f"Erro ao atualizar tributo: {str(e)}")
        db.session.rollback()
        return jsonify({"message": f"Erro ao processar solicitação: {str(e)}"}), 500

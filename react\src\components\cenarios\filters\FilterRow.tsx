
import { AdvancedFilterDropdown } from './AdvancedFilterDropdown'
import { SimpleTextFilter } from './SimpleTextFilter'
import { Button } from '@/components/ui/Button'
import type { FilterState, FilterOptions } from '@/hooks/useAdvancedFilters'
import type { TipoTributo } from '@/types/cenarios'

interface FilterRowProps {
  tipoTributo: TipoTributo
  filters: FilterState
  options: FilterOptions
  isLoading: boolean
  hasActiveFilters: boolean
  onUpdateFilter: (filterType: keyof FilterState, values: string[]) => void
  onUpdateTextFilter: (filterType: keyof FilterState, value: string) => void
  onClearAllFilters: () => void
  getFilteredOptions: (filterType: keyof FilterOptions) => any[]
}

// Mapeamento de colunas por tipo de tributo - ordem deve corresponder exatamente às colunas da tabela
const COLUMN_MAPPINGS: Record<TipoTributo, Array<{
  key: keyof FilterState | 'none'
  textKey?: keyof FilterState
  label: string
  placeholder: string
  show: boolean
  isText: boolean
}>> = {
  icms: [
    { key: 'produto_codigo', label: 'Produto', placeholder: 'Filtrar produto...', show: true, isText: true },
    { key: 'produto_descricao', label: 'Descrição', placeholder: 'Filtrar descrição...', show: true, isText: true },
    { key: 'cfops', label: 'CFOP', placeholder: 'Filtrar CFOP', show: true, isText: false },
    { key: 'ncms', label: 'NCM', placeholder: 'Filtrar NCM', show: true, isText: false },
    { key: 'origem', label: 'Origem', placeholder: 'Filtrar origem...', show: true, isText: true },
    { key: 'csts', label: 'CST', placeholder: 'Filtrar CST', show: true, isText: false },
    { key: 'cest', label: 'CEST', placeholder: 'Filtrar CEST...', show: true, isText: true },
    { key: 'estados', label: 'UF', placeholder: 'Filtrar UF', show: true, isText: false },
    { key: 'aliquotas', label: 'Alíquota', placeholder: 'Filtrar alíquota', show: true, isText: false },
    { key: 'reducoes', label: 'Redução', placeholder: 'Filtrar redução', show: true, isText: false },
    { key: 'none', label: '% ICMS Diferido', placeholder: 'Filtrar diferimento...', show: true, isText: true },
    { key: 'cliente_razao', label: 'Cliente', placeholder: 'Filtrar cliente...', show: true, isText: true },
    { key: 'none', label: 'Simples Nacional', placeholder: 'Sim/Não', show: true, isText: true },
    { key: 'atividades', label: 'Atividade', placeholder: 'Filtrar atividade', show: true, isText: false },
    { key: 'destinacoes', label: 'Destinação', placeholder: 'Filtrar destinação', show: true, isText: false },
  ],
  icms_st: [
    { key: 'produto_codigo', label: 'Produto', placeholder: 'Filtrar produto...', show: true, isText: true },
    { key: 'produto_descricao', label: 'Descrição', placeholder: 'Filtrar descrição...', show: true, isText: true },
    { key: 'cfops', label: 'CFOP', placeholder: 'Filtrar CFOP', show: true, isText: false },
    { key: 'ncms', label: 'NCM', placeholder: 'Filtrar NCM', show: true, isText: false },
    { key: 'cest', label: 'CEST', placeholder: 'Filtrar CEST...', show: true, isText: true },
    { key: 'origem', label: 'Origem', placeholder: 'Filtrar origem...', show: true, isText: true },
    { key: 'csts', label: 'CST', placeholder: 'Filtrar CST', show: true, isText: false },
    { key: 'estados', label: 'Estado', placeholder: 'Filtrar UF', show: true, isText: false },
    { key: 'aliquotas', label: '% ICMS', placeholder: 'Filtrar % ICMS', show: true, isText: false },
    { key: 'reducoes', label: '% Red. BC ICMS', placeholder: 'Filtrar % Red. BC ICMS', show: true, isText: false },
    { key: 'aliquotas_st', label: '% ICMS-ST', placeholder: 'Filtrar % ICMS ST', show: true, isText: false },
    { key: 'mvas', label: '% MVA', placeholder: 'Filtrar % MVA', show: true, isText: false },
    { key: 'reducoes_st', label: '% Red. BC ST', placeholder: 'Filtrar % Red. BC ST', show: true, isText: false },
    { key: 'cliente_razao', label: 'Cliente', placeholder: 'Filtrar cliente...', show: true, isText: true },
    { key: 'none', label: 'Simples Nacional', placeholder: 'Sim/Não', show: true, isText: true },
    { key: 'atividades', label: 'Atividade', placeholder: 'Filtrar Atividade', show: true, isText: false },
    { key: 'destinacoes', label: 'Destinação', placeholder: 'Filtrar Destinação', show: true, isText: false },
  ],
  ipi: [
    { key: 'produto_codigo', label: 'Produto', placeholder: 'Filtrar produto...', show: true, isText: true },
    { key: 'produto_descricao', label: 'Descrição', placeholder: 'Filtrar descrição...', show: true, isText: true },
    { key: 'cfops', label: 'CFOP', placeholder: 'Filtrar CFOP', show: true, isText: false },
    { key: 'ncms', label: 'NCM', placeholder: 'Filtrar NCM', show: true, isText: false },
    { key: 'csts', label: 'CST', placeholder: 'Filtrar CST', show: true, isText: false },
    { key: 'estados', label: 'Estado', placeholder: 'Filtrar UF', show: true, isText: false },
    { key: 'aliquotas', label: '% IPI', placeholder: 'Filtrar % IPI', show: true, isText: false },
    { key: 'ex', label: 'EX', placeholder: 'Filtrar EX...', show: true, isText: true },
    { key: 'cliente_razao', label: 'Cliente', placeholder: 'Filtrar cliente...', show: true, isText: true },
    { key: 'none', label: 'Simples Nacional', placeholder: 'Sim/Não', show: true, isText: true },
    { key: 'atividades', label: 'Atividade', placeholder: 'Filtrar Atividade', show: true, isText: false },
    { key: 'destinacoes', label: 'Destinação', placeholder: 'Filtrar Destinação', show: true, isText: false },
  ],
  pis: [
    { key: 'produto_codigo', label: 'Produto', placeholder: 'Filtrar produto...', show: true, isText: true },
    { key: 'produto_descricao', label: 'Descrição', placeholder: 'Filtrar descrição...', show: true, isText: true },
    { key: 'cfops', label: 'CFOP', placeholder: 'Filtrar CFOP', show: true, isText: false },
    { key: 'ncms', label: 'NCM', placeholder: 'Filtrar NCM', show: true, isText: false },
    { key: 'csts', label: 'CST', placeholder: 'Filtrar CST', show: true, isText: false },
    { key: 'estados', label: 'Estado', placeholder: 'Filtrar UF', show: true, isText: false },
    { key: 'aliquotas', label: '% PIS', placeholder: 'Filtrar % PIS', show: true, isText: false },
    { key: 'reducoes', label: '% Red. BC', placeholder: 'Filtrar % Red. BC', show: true, isText: false },
    { key: 'cliente_razao', label: 'Cliente', placeholder: 'Filtrar cliente...', show: true, isText: true },
    { key: 'none', label: 'Simples Nacional', placeholder: 'Sim/Não', show: true, isText: true },
    { key: 'atividades', label: 'Atividade', placeholder: 'Filtrar Atividade', show: true, isText: false },
    { key: 'destinacoes', label: 'Destinação', placeholder: 'Filtrar Destinação', show: true, isText: false },
  ],
  cofins: [
    { key: 'produto_codigo', label: 'Produto', placeholder: 'Filtrar produto...', show: true, isText: true },
    { key: 'produto_descricao', label: 'Descrição', placeholder: 'Filtrar descrição...', show: true, isText: true },
    { key: 'cfops', label: 'CFOP', placeholder: 'Filtrar CFOP', show: true, isText: false },
    { key: 'ncms', label: 'NCM', placeholder: 'Filtrar NCM', show: true, isText: false },
    { key: 'csts', label: 'CST', placeholder: 'Filtrar CST', show: true, isText: false },
    { key: 'estados', label: 'Estado', placeholder: 'Filtrar UF', show: true, isText: false },
    { key: 'aliquotas', label: '% COFINS', placeholder: 'Filtrar % COFINS', show: true, isText: false },
    { key: 'reducoes', label: '% Red. BC', placeholder: 'Filtrar % Red. BC', show: true, isText: false },
    { key: 'cliente_razao', label: 'Cliente', placeholder: 'Filtrar cliente...', show: true, isText: true },
    { key: 'none', label: 'Simples Nacional', placeholder: 'Sim/Não', show: true, isText: true },
    { key: 'atividades', label: 'Atividade', placeholder: 'Filtrar Atividade', show: true, isText: false },
    { key: 'destinacoes', label: 'Destinação', placeholder: 'Filtrar Destinação', show: true, isText: false },
  ],
  difal: [
    { key: 'produto_codigo', label: 'Produto', placeholder: 'Filtrar produto...', show: true, isText: true },
    { key: 'produto_descricao', label: 'Descrição', placeholder: 'Filtrar descrição...', show: true, isText: true },
    { key: 'cfops', label: 'CFOP', placeholder: 'Filtrar CFOP', show: true, isText: false },
    { key: 'ncms', label: 'NCM', placeholder: 'Filtrar NCM', show: true, isText: false },
    { key: 'csts', label: 'CST', placeholder: 'Filtrar CST', show: true, isText: false },
    { key: 'estados', label: 'UF', placeholder: 'Filtrar UF', show: true, isText: false },
    { key: 'aliquotas', label: '% DIFAL ICMS', placeholder: 'Filtrar % DIFAL ICMS', show: true, isText: false },
    { key: 'cliente_razao', label: 'Cliente', placeholder: 'Filtrar cliente...', show: true, isText: true },
    { key: 'none', label: 'Simples Nacional', placeholder: 'Sim/Não', show: true, isText: true },
    { key: 'atividades', label: 'Atividade', placeholder: 'Filtrar Atividade', show: true, isText: false },
    { key: 'destinacoes', label: 'Destinação', placeholder: 'Filtrar Destinação', show: true, isText: false },
  ],
}

export function FilterRow({
  tipoTributo,
  filters,
  options,
  isLoading,
  hasActiveFilters,
  onUpdateFilter,
  onUpdateTextFilter,
  onClearAllFilters,
  getFilteredOptions
}: FilterRowProps) {
  const columnMapping = COLUMN_MAPPINGS[tipoTributo] || COLUMN_MAPPINGS.icms

  return (
    <tr className="bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700">
      {/* Coluna de checkbox (se aplicável) */}
      <th className="px-3 py-2 w-12">
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="xs"
            onClick={onClearAllFilters}
            className="text-error-600 hover:text-error-700 hover:bg-error-50 dark:text-error-400 dark:hover:bg-error-900/20"
            title="Limpar todos os filtros"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </Button>
        )}
      </th>

      {/* Filtros das colunas */}
      {columnMapping.map((column, index) => (
        <th key={`${column.key}-${index}`} className="px-3 py-2">
          {column.show && column.isText ? (
            <SimpleTextFilter
              placeholder={column.placeholder}
              onFilter={(value) => {
                if (column.key !== 'none') {
                  onUpdateTextFilter(column.key as keyof FilterState, value)
                }
              }}
              className="min-w-[120px]"
            />
          ) : column.show && column.key !== 'none' && !column.isText ? (
            <AdvancedFilterDropdown
              options={getFilteredOptions(column.key as keyof FilterOptions)}
              selectedValues={filters[column.key as keyof FilterState] as string[] || []}
              placeholder={column.placeholder}
              onApply={(values) => onUpdateFilter(column.key as keyof FilterState, values)}
              onClear={() => onUpdateFilter(column.key as keyof FilterState, [])}
              isLoading={isLoading}
              className="min-w-[120px]"
            />
          ) : column.show && column.key === 'none' && column.isText ? (
            <SimpleTextFilter
              placeholder={column.placeholder}
              onFilter={() => {
                // Para campos 'none' que são apenas visuais, não aplicar filtro
                console.log(`Filtro não implementado para: ${column.label}`)
              }}
              className="min-w-[120px]"
            />
          ) : (
            <div className="h-8"></div> // Espaço vazio para colunas sem filtro
          )}
        </th>
      ))}

      {/* Coluna de ações (vazia) */}
      <th className="px-3 py-2 w-32"></th>
    </tr>
  )
}

import { TotalGeral } from '@/services/dashboardService'

interface ResumoGeralProps {
  totalGeral: TotalGeral
}

export function ResumoGeral({ totalGeral }: ResumoGeralProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value)
  }

  const conformePercent = totalGeral.total_produtos > 0 
    ? Math.round((totalGeral.total_conforme / totalGeral.total_produtos) * 100)
    : 0

  const inconsistentePercent = totalGeral.total_produtos > 0
    ? Math.round((totalGeral.total_inconsistente / totalGeral.total_produtos) * 100)
    : 0

  return (
    <div className="mb-6">
      <div className="flex items-center mb-4">
        <svg className="w-5 h-5 text-primary-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
          Resumo Geral
        </h4>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        {/* Total de Notas */}
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
            {totalGeral.total_notas.toLocaleString('pt-BR')}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Total de Notas
          </div>
        </div>

        {/* Total de Operações */}
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {totalGeral.total_produtos.toLocaleString('pt-BR')}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Operações
          </div>
        </div>

        {/* Conformes */}
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {totalGeral.total_conforme.toLocaleString('pt-BR')}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Conformes
          </div>
          <div className="text-xs text-green-600 dark:text-green-400 font-medium">
            {conformePercent}%
          </div>
        </div>

        {/* Inconsistentes */}
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {totalGeral.total_inconsistente.toLocaleString('pt-BR')}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Inconsistentes
          </div>
          <div className="text-xs text-red-600 dark:text-red-400 font-medium">
            {inconsistentePercent}%
          </div>
        </div>

        {/* Valor Inconsistente */}
        <div className="card p-4 text-center">
          <div className="text-lg font-bold text-red-600 dark:text-red-400">
            {formatCurrency(totalGeral.valor_inconsistente_total)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Valor Inconsistente
          </div>
        </div>
      </div>

      {/* Barra de progresso visual */}
      <div className="mt-4">
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
          <span>Conformidade Geral</span>
          <span>{conformePercent}% conforme</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div className="flex h-3 rounded-full overflow-hidden">
            <div 
              className="bg-green-500 transition-all duration-500"
              style={{ width: `${conformePercent}%` }}
            />
            <div 
              className="bg-red-500 transition-all duration-500"
              style={{ width: `${inconsistentePercent}%` }}
            />
          </div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>✓ {totalGeral.total_conforme} conformes</span>
          <span>✗ {totalGeral.total_inconsistente} inconsistentes</span>
        </div>
      </div>
    </div>
  )
}
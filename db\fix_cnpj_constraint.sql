-- Remove a constraint UNIQUE do campo CNPJ da tabela empresa
-- para permitir que diferentes escritórios cadastrem a mesma empresa

-- Primeiro, vamos verificar o nome da constraint
SELECT conname 
FROM pg_constraint 
WHERE conrelid = 'empresa'::regclass 
AND contype = 'u' 
AND array_to_string(conkey, ',') = (
    SELECT array_to_string(array_agg(attnum), ',')
    FROM pg_attribute 
    WHERE attrelid = 'empresa'::regclass 
    AND attname = 'cnpj'
);

-- Remove a constraint UNIQUE do CNPJ (o nome pode variar)
-- Tente executar uma dessas opções:
ALTER TABLE empresa DROP CONSTRAINT IF EXISTS empresa_cnpj_key;
ALTER TABLE empresa DROP CONSTRAINT IF EXISTS empresa_cnpj_unique;
ALTER TABLE empresa DROP CONSTRAINT IF EXISTS unique_empresa_cnpj;

-- Adiciona uma constraint composta para garantir unicidade por escritório
ALTER TABLE empresa ADD CONSTRAINT unique_empresa_cnpj_escritorio 
UNIQUE (cnpj, escritorio_id);

-- Verificar se a alteração foi aplicada
\d empresa;
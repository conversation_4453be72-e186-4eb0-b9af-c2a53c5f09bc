import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card } from '@/components/ui/Card'
import type { TipoTributo } from '@/types/cenarios'
import { cn } from '@/utils/cn'

interface CenarioCardProps {
  tipo: TipoTributo
  nome: string
  descricao: string
  icon: React.ReactElement
  color: string
  bgColor: string
  stats?: {
    total: number
    ativos: number
    inconsistentes: number
  }
}

export function CenarioCard({ tipo, nome, descricao, icon, color, bgColor, stats }: CenarioCardProps) {
  const total = stats?.total || 0
  const ativos = stats?.ativos || 0
  const inconsistentes = stats?.inconsistentes || 0

  const getStatusColor = (inconsistentes: number, total: number) => {
    if (total === 0) return 'text-gray-400'
    if (inconsistentes === 0) return 'text-green-600'
    if (inconsistentes / total > 0.1) return 'text-red-600'
    return 'text-yellow-600'
  }

  const getStatusText = (inconsistentes: number, total: number) => {
    if (total === 0) return 'Sem dados'
    if (inconsistentes === 0) return 'Todos OK'
    return `${inconsistentes} inconsistentes`
  }

  const getProgressColor = (inconsistentes: number, total: number) => {
    if (inconsistentes === 0) return 'bg-green-500'
    if (inconsistentes / total > 0.1) return 'bg-red-500'
    return 'bg-yellow-500'
  }

  return (
    <Link to={`/fiscal/cenarios/saida/${tipo}`} className="block group">
      <Card 
        hover 
        className="h-full relative overflow-hidden group-hover:shadow-xl group-hover:scale-[1.02] transition-all duration-300"
        gradient
      >
        {/* Background Pattern */}
        <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-100/20 to-transparent rounded-full -mr-10 -mt-10"></div>
        
        <div className="relative z-10">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-4">
            <div
                className={cn(
                  'w-12 h-12 rounded-xl flex items-center justify-center text-xl shadow-lg',
                  bgColor
                )}
              >
                {React.cloneElement(icon, {
                  className: cn(icon.props.className, color)
                })}
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                  {nome}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {descricao}
                </p>
              </div>
            </div>

            {/* Arrow Icon */}
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {total.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Total
              </div>
            </div>
            
            <div className="text-center p-3 bg-success-50 dark:bg-success-900/20 rounded-lg">
              <div className="text-lg font-bold text-success-600 dark:text-success-400">
                {ativos.toLocaleString()}
              </div>
              <div className="text-xs text-success-600/70 dark:text-success-400/70">
                Ativos
              </div>
            </div>
            
            <div className="text-center p-3 bg-error-50 dark:bg-error-900/20 rounded-lg">
              <div className="text-lg font-bold text-error-600 dark:text-error-400">
                {inconsistentes.toLocaleString()}
              </div>
              <div className="text-xs text-error-600/70 dark:text-error-400/70">
                Erros
              </div>
            </div>
          </div>

          {/* Status Badge */}
          <div className="flex items-center justify-between">
            <div className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium ${
              inconsistentes === 0 
                ? 'bg-success-100 text-success-700 dark:bg-success-900/30 dark:text-success-400'
                : inconsistentes / total > 0.1
                  ? 'bg-error-100 text-error-700 dark:bg-error-900/30 dark:text-error-400'
                  : 'bg-warning-100 text-warning-700 dark:bg-warning-900/30 dark:text-warning-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                inconsistentes === 0 
                  ? 'bg-success-500'
                  : inconsistentes / total > 0.1
                    ? 'bg-error-500'
                    : 'bg-warning-500'
              }`}></div>
              {getStatusText(inconsistentes, total)}
            </div>

            {/* Progress Indicator */}
            {total > 0 && (
              <div className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                {Math.round((ativos / total) * 100)}% completo
              </div>
            )}
          </div>

          {/* Progress Bar */}
          {total > 0 && (
            <div className="mt-3">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
                <div 
                  className={`h-full rounded-full transition-all duration-500 ${getProgressColor(inconsistentes, total)} relative`}
                  style={{ width: `${Math.round((ativos / total) * 100)}%` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
    </Link>
  )
}
import{r as s,R as ye}from"./vendor-BKU87Gzz.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function T(){return T=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},T.apply(this,arguments)}var b;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(b||(b={}));const Y="popstate";function xe(e){e===void 0&&(e={});function t(r,a){let{pathname:l,search:i,hash:u}=r.location;return D("",{pathname:l,search:i,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:j(a)}return Pe(t,n,null,e)}function x(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function le(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Ce(){return Math.random().toString(36).substr(2,8)}function Z(e,t){return{usr:e.state,key:e.key,idx:t}}function D(e,t,n,r){return n===void 0&&(n=null),T({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?O(t):t,{state:n,key:t&&t.key||r||Ce()})}function j(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function O(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Pe(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,u=b.Pop,o=null,h=f();h==null&&(h=0,i.replaceState(T({},i.state,{idx:h}),""));function f(){return(i.state||{idx:null}).idx}function c(){u=b.Pop;let d=f(),C=d==null?null:d-h;h=d,o&&o({action:u,location:m.location,delta:C})}function p(d,C){u=b.Push;let v=D(m.location,d,C);h=f()+1;let y=Z(v,h),P=m.createHref(v);try{i.pushState(y,"",P)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;a.location.assign(P)}l&&o&&o({action:u,location:m.location,delta:1})}function E(d,C){u=b.Replace;let v=D(m.location,d,C);h=f();let y=Z(v,h),P=m.createHref(v);i.replaceState(y,"",P),l&&o&&o({action:u,location:m.location,delta:0})}function g(d){let C=a.location.origin!=="null"?a.location.origin:a.location.href,v=typeof d=="string"?d:j(d);return v=v.replace(/ $/,"%20"),x(C,"No window.location.(origin|href) available to create URL for href: "+v),new URL(v,C)}let m={get action(){return u},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(Y,c),o=d,()=>{a.removeEventListener(Y,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let C=g(d);return{pathname:C.pathname,search:C.search,hash:C.hash}},push:p,replace:E,go(d){return i.go(d)}};return m}var ee;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(ee||(ee={}));function Ee(e,t,n){return n===void 0&&(n="/"),we(e,t,n)}function we(e,t,n,r){let a=typeof t=="string"?O(t):t,l=N(a.pathname||"/",n);if(l==null)return null;let i=ie(e);Re(i);let u=null;for(let o=0;u==null&&o<i.length;++o){let h=We(l);u=Ie(i[o],h)}return u}function ie(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(l,i,u)=>{let o={relativePath:u===void 0?l.path||"":u,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(x(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let h=L([r,o.relativePath]),f=n.concat(o);l.children&&l.children.length>0&&(x(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+h+'".')),ie(l.children,t,f,h)),!(l.path==null&&!l.index)&&t.push({path:h,score:Be(h,l.index),routesMeta:f})};return e.forEach((l,i)=>{var u;if(l.path===""||!((u=l.path)!=null&&u.includes("?")))a(l,i);else for(let o of oe(l.path))a(l,i,o)}),t}function oe(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let i=oe(r.join("/")),u=[];return u.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&u.push(...i),u.map(o=>e.startsWith("/")&&o===""?"/":o)}function Re(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Te(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Se=/^:[\w-]+$/,be=3,Le=2,Ue=1,Ne=10,Oe=-2,te=e=>e==="*";function Be(e,t){let n=e.split("/"),r=n.length;return n.some(te)&&(r+=Oe),t&&(r+=Le),n.filter(a=>!te(a)).reduce((a,l)=>a+(Se.test(l)?be:l===""?Ue:Ne),r)}function Te(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function Ie(e,t,n){let{routesMeta:r}=e,a={},l="/",i=[];for(let u=0;u<r.length;++u){let o=r[u],h=u===r.length-1,f=l==="/"?t:t.slice(l.length)||"/",c=J({path:o.relativePath,caseSensitive:o.caseSensitive,end:h},f),p=o.route;if(!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:L([l,c.pathname]),pathnameBase:Fe(L([l,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(l=L([l,c.pathnameBase]))}return i}function J(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=je(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:r.reduce((h,f,c)=>{let{paramName:p,isOptional:E}=f;if(p==="*"){let m=u[c]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const g=u[c];return E&&!g?h[p]=void 0:h[p]=(g||"").replace(/%2F/g,"/"),h},{}),pathname:l,pathnameBase:i,pattern:e}}function je(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),le(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,u,o)=>(r.push({paramName:u,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function We(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return le(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function N(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function ke(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?O(e):e;return{pathname:n?n.startsWith("/")?n:$e(n,t):t,search:Me(r),hash:Ae(a)}}function $e(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function V(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function _e(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function G(e,t){let n=_e(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function X(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=O(e):(a=T({},e),x(!a.pathname||!a.pathname.includes("?"),V("?","pathname","search",a)),x(!a.pathname||!a.pathname.includes("#"),V("#","pathname","hash",a)),x(!a.search||!a.search.includes("#"),V("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,u;if(i==null)u=n;else{let c=t.length-1;if(!r&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}u=c>=0?t[c]:"/"}let o=ke(a,u),h=i&&i!=="/"&&i.endsWith("/"),f=(l||i===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(h||f)&&(o.pathname+="/"),o}const L=e=>e.join("/").replace(/\/\/+/g,"/"),Fe=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Me=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ae=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ve(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const se=["post","put","patch","delete"];new Set(se);const De=["get",...se];new Set(De);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)}const k=s.createContext(null),ue=s.createContext(null),w=s.createContext(null),$=s.createContext(null),R=s.createContext({outlet:null,matches:[],isDataRoute:!1}),ce=s.createContext(null);function Je(e,t){let{relative:n}=t===void 0?{}:t;B()||x(!1);let{basename:r,navigator:a}=s.useContext(w),{hash:l,pathname:i,search:u}=_(e,{relative:n}),o=i;return r!=="/"&&(o=i==="/"?r:L([r,i])),a.createHref({pathname:o,search:u,hash:l})}function B(){return s.useContext($)!=null}function U(){return B()||x(!1),s.useContext($).location}function he(e){s.useContext(w).static||s.useLayoutEffect(e)}function H(){let{isDataRoute:e}=s.useContext(R);return e?rt():ze()}function ze(){B()||x(!1);let e=s.useContext(k),{basename:t,future:n,navigator:r}=s.useContext(w),{matches:a}=s.useContext(R),{pathname:l}=U(),i=JSON.stringify(G(a,n.v7_relativeSplatPath)),u=s.useRef(!1);return he(()=>{u.current=!0}),s.useCallback(function(h,f){if(f===void 0&&(f={}),!u.current)return;if(typeof h=="number"){r.go(h);return}let c=X(h,JSON.parse(i),l,f.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:L([t,c.pathname])),(f.replace?r.replace:r.push)(c,f.state,f)},[t,r,i,l,e])}function wt(){let{matches:e}=s.useContext(R),t=e[e.length-1];return t?t.params:{}}function _(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=s.useContext(w),{matches:a}=s.useContext(R),{pathname:l}=U(),i=JSON.stringify(G(a,r.v7_relativeSplatPath));return s.useMemo(()=>X(e,JSON.parse(i),l,n==="path"),[e,i,l,n])}function Ke(e,t){return qe(e,t)}function qe(e,t,n,r){B()||x(!1);let{navigator:a}=s.useContext(w),{matches:l}=s.useContext(R),i=l[l.length-1],u=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let h=U(),f;if(t){var c;let d=typeof t=="string"?O(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||x(!1),f=d}else f=h;let p=f.pathname||"/",E=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");E="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=Ee(e,{pathname:E}),m=Ye(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},u,d.params),pathname:L([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:L([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,n,r);return t&&m?s.createElement($.Provider,{value:{location:I({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:b.Pop}},m):m}function Ge(){let e=nt(),t=Ve(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),n?s.createElement("pre",{style:a},n):null,null)}const Xe=s.createElement(Ge,null);class He extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?s.createElement(R.Provider,{value:this.props.routeContext},s.createElement(ce.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Qe(e){let{routeContext:t,match:n,children:r}=e,a=s.useContext(k);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),s.createElement(R.Provider,{value:t},r)}function Ye(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if(!n)return null;if(n.errors)e=n.matches;else if((l=r)!=null&&l.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,u=(a=n)==null?void 0:a.errors;if(u!=null){let f=i.findIndex(c=>c.route.id&&(u==null?void 0:u[c.route.id])!==void 0);f>=0||x(!1),i=i.slice(0,Math.min(i.length,f+1))}let o=!1,h=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<i.length;f++){let c=i[f];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(h=f),c.route.id){let{loaderData:p,errors:E}=n,g=c.route.loader&&p[c.route.id]===void 0&&(!E||E[c.route.id]===void 0);if(c.route.lazy||g){o=!0,h>=0?i=i.slice(0,h+1):i=[i[0]];break}}}return i.reduceRight((f,c,p)=>{let E,g=!1,m=null,d=null;n&&(E=u&&c.route.id?u[c.route.id]:void 0,m=c.route.errorElement||Xe,o&&(h<0&&p===0?(at("route-fallback"),g=!0,d=null):h===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let C=t.concat(i.slice(0,p+1)),v=()=>{let y;return E?y=m:g?y=d:c.route.Component?y=s.createElement(c.route.Component,null):c.route.element?y=c.route.element:y=f,s.createElement(Qe,{match:c,routeContext:{outlet:f,matches:C,isDataRoute:n!=null},children:y})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?s.createElement(He,{location:n.location,revalidation:n.revalidation,component:m,error:E,children:v(),routeContext:{outlet:null,matches:C,isDataRoute:!0}}):v()},null)}var fe=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(fe||{}),de=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(de||{});function Ze(e){let t=s.useContext(k);return t||x(!1),t}function et(e){let t=s.useContext(ue);return t||x(!1),t}function tt(e){let t=s.useContext(R);return t||x(!1),t}function pe(e){let t=tt(),n=t.matches[t.matches.length-1];return n.route.id||x(!1),n.route.id}function nt(){var e;let t=s.useContext(ce),n=et(),r=pe();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function rt(){let{router:e}=Ze(fe.UseNavigateStable),t=pe(de.UseNavigateStable),n=s.useRef(!1);return he(()=>{n.current=!0}),s.useCallback(function(a,l){l===void 0&&(l={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,I({fromRouteId:t},l)))},[e,t])}const ne={};function at(e,t,n){ne[e]||(ne[e]=!0)}function lt(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function Rt(e){let{to:t,replace:n,state:r,relative:a}=e;B()||x(!1);let{future:l,static:i}=s.useContext(w),{matches:u}=s.useContext(R),{pathname:o}=U(),h=H(),f=X(t,G(u,l.v7_relativeSplatPath),o,a==="path"),c=JSON.stringify(f);return s.useEffect(()=>h(JSON.parse(c),{replace:n,state:r,relative:a}),[h,c,a,n,r]),null}function it(e){x(!1)}function ot(e){let{basename:t="/",children:n=null,location:r,navigationType:a=b.Pop,navigator:l,static:i=!1,future:u}=e;B()&&x(!1);let o=t.replace(/^\/*/,"/"),h=s.useMemo(()=>({basename:o,navigator:l,static:i,future:I({v7_relativeSplatPath:!1},u)}),[o,u,l,i]);typeof r=="string"&&(r=O(r));let{pathname:f="/",search:c="",hash:p="",state:E=null,key:g="default"}=r,m=s.useMemo(()=>{let d=N(f,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:E,key:g},navigationType:a}},[o,f,c,p,E,g,a]);return m==null?null:s.createElement(w.Provider,{value:h},s.createElement($.Provider,{children:n,value:m}))}function St(e){let{children:t,location:n}=e;return Ke(z(t),n)}new Promise(()=>{});function z(e,t){t===void 0&&(t=[]);let n=[];return s.Children.forEach(e,(r,a)=>{if(!s.isValidElement(r))return;let l=[...t,a];if(r.type===s.Fragment){n.push.apply(n,z(r.props.children,l));return}r.type!==it&&x(!1),!r.props.index||!r.props.children||x(!1);let i={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=z(r.props.children,l)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W.apply(this,arguments)}function me(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,l;for(l=0;l<r.length;l++)a=r[l],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function st(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ut(e,t){return e.button===0&&(!t||t==="_self")&&!st(e)}function K(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map(a=>[n,a]):[[n,r]])},[]))}function ct(e,t){let n=K(e);return t&&t.forEach((r,a)=>{n.has(a)||t.getAll(a).forEach(l=>{n.append(a,l)})}),n}const ht=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ft=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],dt="6";try{window.__reactRouterVersion=dt}catch{}const pt=s.createContext({isTransitioning:!1}),mt="startTransition",re=ye[mt];function bt(e){let{basename:t,children:n,future:r,window:a}=e,l=s.useRef();l.current==null&&(l.current=xe({window:a,v5Compat:!0}));let i=l.current,[u,o]=s.useState({action:i.action,location:i.location}),{v7_startTransition:h}=r||{},f=s.useCallback(c=>{h&&re?re(()=>o(c)):o(c)},[o,h]);return s.useLayoutEffect(()=>i.listen(f),[i,f]),s.useEffect(()=>lt(r),[r]),s.createElement(ot,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:i,future:r})}const vt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",gt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,yt=s.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:l,replace:i,state:u,target:o,to:h,preventScrollReset:f,viewTransition:c}=t,p=me(t,ht),{basename:E}=s.useContext(w),g,m=!1;if(typeof h=="string"&&gt.test(h)&&(g=h,vt))try{let y=new URL(window.location.href),P=h.startsWith("//")?new URL(y.protocol+h):new URL(h),S=N(P.pathname,E);P.origin===y.origin&&S!=null?h=S+P.search+P.hash:m=!0}catch{}let d=Je(h,{relative:a}),C=Ct(h,{replace:i,state:u,target:o,preventScrollReset:f,relative:a,viewTransition:c});function v(y){r&&r(y),y.defaultPrevented||C(y)}return s.createElement("a",W({},p,{href:g||d,onClick:m||l?r:v,ref:n,target:o}))}),Lt=s.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:a=!1,className:l="",end:i=!1,style:u,to:o,viewTransition:h,children:f}=t,c=me(t,ft),p=_(o,{relative:c.relative}),E=U(),g=s.useContext(ue),{navigator:m,basename:d}=s.useContext(w),C=g!=null&&Pt(p)&&h===!0,v=m.encodeLocation?m.encodeLocation(p).pathname:p.pathname,y=E.pathname,P=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;a||(y=y.toLowerCase(),P=P?P.toLowerCase():null,v=v.toLowerCase()),P&&d&&(P=N(P,d)||P);const S=v!=="/"&&v.endsWith("/")?v.length-1:v.length;let F=y===v||!i&&y.startsWith(v)&&y.charAt(S)==="/",Q=P!=null&&(P===v||!i&&P.startsWith(v)&&P.charAt(v.length)==="/"),M={isActive:F,isPending:Q,isTransitioning:C},ve=F?r:void 0,A;typeof l=="function"?A=l(M):A=[l,F?"active":null,Q?"pending":null,C?"transitioning":null].filter(Boolean).join(" ");let ge=typeof u=="function"?u(M):u;return s.createElement(yt,W({},c,{"aria-current":ve,className:A,ref:n,style:ge,to:o,viewTransition:h}),typeof f=="function"?f(M):f)});var q;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(q||(q={}));var ae;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ae||(ae={}));function xt(e){let t=s.useContext(k);return t||x(!1),t}function Ct(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,viewTransition:u}=t===void 0?{}:t,o=H(),h=U(),f=_(e,{relative:i});return s.useCallback(c=>{if(ut(c,n)){c.preventDefault();let p=r!==void 0?r:j(h)===j(f);o(e,{replace:p,state:a,preventScrollReset:l,relative:i,viewTransition:u})}},[h,o,f,r,a,n,e,l,i,u])}function Ut(e){let t=s.useRef(K(e)),n=s.useRef(!1),r=U(),a=s.useMemo(()=>ct(r.search,n.current?null:t.current),[r.search]),l=H(),i=s.useCallback((u,o)=>{const h=K(typeof u=="function"?u(a):u);n.current=!0,l("?"+h,o)},[l,a]);return[a,i]}function Pt(e,t){t===void 0&&(t={});let n=s.useContext(pt);n==null&&x(!1);let{basename:r}=xt(q.useViewTransitionState),a=_(e,{relative:t.relative});if(!n.isTransitioning)return!1;let l=N(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=N(n.nextLocation.pathname,r)||n.nextLocation.pathname;return J(a.pathname,i)!=null||J(a.pathname,l)!=null}export{bt as B,yt as L,Lt as N,St as R,H as a,wt as b,Ut as c,Rt as d,it as e,U as u};
//# sourceMappingURL=router-1egnX6Ak.js.map

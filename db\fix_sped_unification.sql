-- Script para corrigir a unificação das tabelas SPED
-- <PERSON><PERSON> script resolve o problema de mapeamento do SQLAlchemy

BEGIN;

-- 1. PRIMEIRO: Migrar dados de cliente_entrada para cliente (se existirem)
-- Verificar se a tabela cliente_entrada existe e tem dados
DO $$
DECLARE
    cliente_entrada_exists BOOLEAN;
    record_count INTEGER;
BEGIN
    -- Verificar se a tabela existe
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'cliente_entrada'
    ) INTO cliente_entrada_exists;
    
    IF cliente_entrada_exists THEN
        -- Contar registros
        EXECUTE 'SELECT COUNT(*) FROM cliente_entrada' INTO record_count;
        
        IF record_count > 0 THEN
            RAISE NOTICE 'Migrando % registros de cliente_entrada para cliente', record_count;
            
            -- Migrar dados de cliente_entrada para cliente
            INSERT INTO cliente (
                empresa_id, escritorio_id, cnpj, razao_social, inscricao_estadual,
                logradouro, numero, bairro, municipio, uf, cep, pais, codigo_pais,
                cnae, atividade, destinacao, natureza_juridica, simples_nacional,
                ind_ie_dest, ind_final, cod_part, cpf, codigo_municipio, suframa, 
                complemento, data_cadastro, status
            )
            SELECT 
                ce.empresa_id, ce.escritorio_id, ce.cnpj, ce.razao_social, ce.inscricao_estadual,
                ce.endereco, ce.numero, ce.bairro, NULL, NULL, NULL, NULL, ce.codigo_pais,
                ce.cnae, ce.atividade, ce.destinacao, ce.natureza_juridica, ce.simples_nacional,
                NULL, NULL, ce.cod_part, ce.cpf, ce.codigo_municipio, ce.suframa,
                ce.complemento, ce.data_cadastro, 'ativo'
            FROM cliente_entrada ce
            WHERE NOT EXISTS (
                SELECT 1 FROM cliente c 
                WHERE c.empresa_id = ce.empresa_id 
                AND (
                    (c.cnpj = ce.cnpj AND ce.cnpj IS NOT NULL) OR
                    (c.cpf = ce.cpf AND ce.cpf IS NOT NULL) OR
                    (c.cod_part = ce.cod_part AND ce.cod_part IS NOT NULL)
                )
            );
            
            RAISE NOTICE 'Migração de dados concluída';
        END IF;
    END IF;
END $$;

-- 2. Atualizar referências em nota_entrada
-- Se a coluna cliente_entrada_id ainda existir, migrar para cliente_id
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    -- Verificar se a coluna cliente_entrada_id existe
    SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'nota_entrada' 
        AND column_name = 'cliente_entrada_id'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE 'Atualizando referências em nota_entrada';
        
        -- Atualizar cliente_id baseado no mapeamento
        UPDATE nota_entrada ne
        SET cliente_id = (
            SELECT c.id 
            FROM cliente c 
            WHERE c.cod_part = ne.cod_part 
            AND c.empresa_id = ne.empresa_id
            LIMIT 1
        )
        WHERE cliente_id IS NULL;
        
        -- Remover a coluna antiga
        ALTER TABLE nota_entrada DROP COLUMN IF EXISTS cliente_entrada_id;
        
        RAISE NOTICE 'Referências atualizadas';
    END IF;
END $$;

-- 3. Adicionar campos SPED na tabela cliente (se não existirem)
ALTER TABLE cliente 
ADD COLUMN IF NOT EXISTS cod_part VARCHAR(50),
ADD COLUMN IF NOT EXISTS cpf VARCHAR(14),
ADD COLUMN IF NOT EXISTS codigo_municipio VARCHAR(10),
ADD COLUMN IF NOT EXISTS suframa VARCHAR(20),
ADD COLUMN IF NOT EXISTS complemento VARCHAR(255);

-- 4. Garantir que cliente_id existe em nota_entrada
ALTER TABLE nota_entrada 
ADD COLUMN IF NOT EXISTS cliente_id INTEGER;

-- 5. Adicionar constraint de foreign key se não existir
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'nota_entrada_cliente_id_fkey'
        AND table_name = 'nota_entrada'
    ) THEN
        ALTER TABLE nota_entrada 
        ADD CONSTRAINT nota_entrada_cliente_id_fkey 
        FOREIGN KEY (cliente_id) REFERENCES cliente(id);
    END IF;
END $$;

-- 6. Adicionar campos de percentual de redução em item_nota_entrada
ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS p_red_icms DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS p_red_icms_st DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS p_mva_icms_st DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS p_red_ipi DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS p_red_cofins DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS valor_icms_cenario DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS valor_icms_st_cenario DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS valor_ipi_cenario DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS valor_pis_cenario DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS valor_cofins_cenario DECIMAL(15,2);

-- 7. REMOVER tabela cliente_entrada e seus índices
DROP INDEX IF EXISTS idx_cliente_entrada_empresa_cod_part;
DROP INDEX IF EXISTS idx_cliente_entrada_cnpj;
DROP TABLE IF EXISTS cliente_entrada CASCADE;

-- 8. Criar índices para os novos campos em cliente
CREATE INDEX IF NOT EXISTS idx_cliente_cod_part ON cliente(cod_part) WHERE cod_part IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_cliente_cpf ON cliente(cpf) WHERE cpf IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_cliente_empresa_cod_part ON cliente(empresa_id, cod_part) WHERE cod_part IS NOT NULL;

-- 9. Comentários nos novos campos
COMMENT ON COLUMN cliente.cod_part IS 'Código do participante no SPED';
COMMENT ON COLUMN cliente.cpf IS 'CPF para pessoas físicas';
COMMENT ON COLUMN cliente.codigo_municipio IS 'Código do município';
COMMENT ON COLUMN cliente.suframa IS 'Código SUFRAMA';
COMMENT ON COLUMN cliente.complemento IS 'Complemento do endereço';

COMMENT ON COLUMN item_nota_entrada.p_red_icms IS 'Percentual de redução de ICMS';
COMMENT ON COLUMN item_nota_entrada.p_red_icms_st IS 'Percentual de redução de ICMS-ST';
COMMENT ON COLUMN item_nota_entrada.p_mva_icms_st IS 'Percentual de MVA de ICMS-ST';
COMMENT ON COLUMN item_nota_entrada.p_red_ipi IS 'Percentual de redução de IPI';
COMMENT ON COLUMN item_nota_entrada.p_red_cofins IS 'Percentual de redução de COFINS';
COMMENT ON COLUMN item_nota_entrada.valor_icms_cenario IS 'Valor ICMS calculado pelo cenário';
COMMENT ON COLUMN item_nota_entrada.valor_icms_st_cenario IS 'Valor ICMS-ST calculado pelo cenário';
COMMENT ON COLUMN item_nota_entrada.valor_ipi_cenario IS 'Valor IPI calculado pelo cenário';
COMMENT ON COLUMN item_nota_entrada.valor_pis_cenario IS 'Valor PIS calculado pelo cenário';
COMMENT ON COLUMN item_nota_entrada.valor_cofins_cenario IS 'Valor COFINS calculado pelo cenário';

COMMIT;

-- Verificar o resultado
SELECT 'Migração concluída com sucesso!' as status;

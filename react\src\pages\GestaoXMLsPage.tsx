import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useFilters } from '@/hooks/useFilters'
import { xmlsService, GestaoTab } from '@/services/xmlsService'
import type { XMLItem, NotaFaltante, XMLDetalhesResponse } from '@/types/xmls'
import { XMLDetailsModal } from '@/components/xml/XMLDetailsModal'
import { AlterarDataModal } from '@/components/xml/AlterarDataModal'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'
import { Pagination } from '@/components/ui/Table'
import { HelpButton, HelpModal } from '@/components/ui'

import { 
  Search, 
  AlertTriangle, 
  ArrowRight, 
  ArrowLeft, 
  Workflow, 
  UsersRound, 
  Package, 
  Upload, 
  Eye, 
  Trash2,
  ChevronDown
} from 'lucide-react'

export function GestaoXMLsPage() {
  const { selectedCompany, selectedYear, selectedMonth } = useFilters()
  const [activeTab, setActiveTab] = useState<GestaoTab>('entrada')
  const [xmlDetalhes, setXmlDetalhes] = useState<XMLDetalhesResponse | null>(null)
  const [selectedChaves, setSelectedChaves] = useState<string[]>([])
  const [alterarDataOpen, setAlterarDataOpen] = useState(false)
  const [alterarChaves, setAlterarChaves] = useState<string[]>([])
  const [page, setPage] = useState(1)
  const pageSize = 10
  const [helpOpen, setHelpOpen] = useState(false)

  useEffect(() => {
    setPage(1)
  }, [activeTab, selectedCompany, selectedYear, selectedMonth])

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['gestao-xmls', activeTab, selectedCompany, selectedYear, selectedMonth],
    queryFn: () =>
      xmlsService.getDados(
        selectedCompany!,
        activeTab,
        selectedYear,
        selectedMonth
      ),
    enabled: !!selectedCompany,
  })

  const identifyMissing = async () => {
    if (!selectedCompany) return
    const faltantesTab =
      activeTab === 'entrada' || activeTab === 'faltantes-entrada'
        ? 'faltantes-entrada'
        : 'faltantes-saida'
    await xmlsService.identificarNotasFaltantes(
      selectedCompany,
      faltantesTab,
      selectedYear,
      selectedMonth
    )
    setActiveTab(faltantesTab)
  }

  const handleCancel = async (id: number) => {
    if (!confirm('Marcar este XML como cancelado?')) return
    await xmlsService.cancelarXML(id)
    refetch()
  }

  const handleDelete = async (id: number) => {
    if (!confirm('Excluir este XML?')) return
    await xmlsService.excluirXML(id)
    refetch()
  }

  const openDetails = async (id: number) => {
    const res = await xmlsService.getDetalhes(id)
    if (res.success) setXmlDetalhes(res)
  }

  const closeDetails = () => setXmlDetalhes(null)

  const openAlterar = (chaves: string[]) => {
    setAlterarChaves(chaves)
    setAlterarDataOpen(true)
  }

  const handleAlterarSave = async (dataEntrada: string, motivo?: string) => {
    await xmlsService.alterarDataNotas(alterarChaves, dataEntrada, motivo)
    setSelectedChaves((prev) => prev.filter((c) => !alterarChaves.includes(c)))
    setAlterarDataOpen(false)
    setAlterarChaves([])
    refetch()
  }

  const handleExcluirSistema = async (chaves: string[]) => {
    if (!confirm('Excluir nota(s) do sistema?')) return
    await xmlsService.excluirNotasSistema(chaves)
    setSelectedChaves((prev) => prev.filter((c) => !chaves.includes(c)))
    refetch()
  }

  const marcarEncontrada = () => {
    alert('Funcionalidade em desenvolvimento')
  }

  const renderXMLs = (xmls: XMLItem[]) => {
    const paginated = xmls.slice((page - 1) * pageSize, page * pageSize)

    return (
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h5 className="text-lg font-medium">
            {activeTab === 'entrada' ? 'XMLs de Entrada' : 'XMLs de Saída'}
          </h5>
          <Button
            variant="primary"
            size="sm"
            onClick={identifyMissing}
            icon={
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            }
            glow
          >
            Identificar Faltantes
          </Button>
        </div>

        {activeTab === 'saida' && data?.primeira_nota && data?.ultima_nota && (
          <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
            <div>
              Primeira Nota: {data.primeira_nota.numero_nf || '-'}
              {data.primeira_nota.data
                ? `, ${new Date(data.primeira_nota.data).toLocaleDateString('pt-BR')}`
                : ''}
            </div>
            <div>
              Última Nota: {data.ultima_nota.numero_nf || '-'}
              {data.ultima_nota.data
                ? `, ${new Date(data.ultima_nota.data).toLocaleDateString('pt-BR')}`
                : ''}
            </div>
          </div>
        )}

        <TableScrollContainer>
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Número NF
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Data Emissão
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Data Entrada
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Participante
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Status
                </th>
                <th className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300">Ações</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {paginated.map((xml) => (
                <tr key={xml.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {xml.numero_nf || '-'}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {xml.data_emissao
                      ? new Date(xml.data_emissao).toLocaleDateString('pt-BR')
                      : '-'}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {xml.data_entrada
                      ? new Date(xml.data_entrada).toLocaleDateString('pt-BR')
                      : '-'}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    <div>
                      <strong>{xml.participante_razao_social || '-'}</strong>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {xml.participante_cnpj || '-'}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-2 text-sm">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        xml.status_validacao === 'validado'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : xml.status_validacao === 'cancelado'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                      }`}
                    >
                      {xml.status_validacao || 'Pendente'}
                    </span>
                  </td>
                  <td className="px-4 py-2 text-sm space-x-2">
                    <button
                      onClick={() => openDetails(xml.id)}
                      className="text-blue-600 hover:text-blue-800 p-1"
                      title="Detalhes"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    {activeTab === 'saida' && xml.status_validacao !== 'cancelado' && (
                      <button
                        onClick={() => handleCancel(xml.id)}
                        className="text-yellow-600 hover:text-yellow-800 p-1"
                        title="Cancelar"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path
                            fillRule="evenodd"
                            d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    )}
                    <button
                      onClick={() => handleDelete(xml.id)}
                      className="text-red-600 hover:text-red-800 p-1"
                      title="Excluir"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
              {xmls.length === 0 && !isLoading && (
                <tr>
                  <td
                    colSpan={6}
                    className="px-4 py-8 text-center text-gray-500 dark:text-gray-400"
                  >
                    Nenhum XML encontrado.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </TableScrollContainer>
        <Pagination
          current={page}
          total={xmls.length}
          pageSize={pageSize}
          onChange={setPage}
          showTotal={(total, range) =>
            `Mostrando ${range[0]} a ${range[1]} de ${total} registros`}
        />
      </div>
    )
  }

  const renderNotas = (notas: NotaFaltante[]) => {
    const xmlVsSped = notas.filter(
      (n) => n.tipo === 'xml_sem_sped' || n.tipo === 'sped_sem_xml'
    )
    const pulosNumeracao = notas.filter((n) => n.tipo === 'pulo_numeracao')
    const paginated = notas.slice((page - 1) * pageSize, page * pageSize)
    const allSelectable = notas.filter((n) => n.chave_nf)
    const allSelected =
      selectedChaves.length > 0 &&
      allSelectable.every((n) => selectedChaves.includes(n.chave_nf!))

    const toggleSelectAll = (checked: boolean) => {
      if (checked) {
        setSelectedChaves(allSelectable.map((n) => n.chave_nf!))
      } else {
        setSelectedChaves([])
      }
    }

    const toggleSelect = (nota: NotaFaltante, checked: boolean) => {
      if (!nota.chave_nf) return
      setSelectedChaves((prev) =>
        checked ? [...prev, nota.chave_nf!] : prev.filter((c) => c !== nota.chave_nf)
      )
    }

    return (
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h5 className="text-lg font-medium">
            Notas Faltantes - {activeTab === 'faltantes-entrada' ? 'Entrada' : 'Saída'}
          </h5>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="primary"
              size="sm"
              onClick={identifyMissing}
              icon={
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              }
              glow
            >
              Identificar Faltantes
            </Button>
            <Button
              variant="warning"
              size="sm"
              onClick={() => openAlterar(selectedChaves)}
              disabled={selectedChaves.length === 0}
              icon={
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
              }
            >
              Alterar Data ({selectedChaves.length})
            </Button>
            <Button
              variant="danger"
              size="sm"
              onClick={() => handleExcluirSistema(selectedChaves)}
              disabled={selectedChaves.length === 0}
              icon={
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd" />
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              }
            >
              Excluir ({selectedChaves.length})
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-gradient-to-r from-warning-500 to-warning-600 rounded-xl shadow-lg overflow-hidden">
            <div className="p-5 flex items-center gap-4">
              <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
                <Search className="w-6 h-6 text-white" />
              </div>
              <div>
                <h6 className="font-semibold text-lg text-white">XML vs SPED</h6>
                <p className="text-warning-100 text-sm mt-1">
                  Notas com divergência entre XML e SPED
                </p>
              </div>
            </div>
            <div className="px-5 pb-5 text-center">
              <div className="text-4xl font-bold text-white mb-1">
                {xmlVsSped.length}
              </div>
            </div>
          </div>        
          
          <div className="bg-gradient-to-r from-error-500 to-error-600 rounded-xl shadow-lg overflow-hidden">
            <div className="p-5 flex items-center gap-4">
              <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
                <AlertTriangle className="w-6 h-6 text-white" />
              </div>
              <div>
                <h6 className="font-semibold text-lg text-white">Pulos de Numeração</h6>
                <p className="text-danger-100 text-sm mt-1">
                  Notas faltantes na sequência numérica
                </p>
              </div>
            </div>
            <div className="px-5 pb-5 text-center">
              <div className="text-4xl font-bold text-white mb-1">
                {pulosNumeracao.length}
              </div>
            </div>
        </div>
      </div>          
        <TableScrollContainer>
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-2">
                  <input
                    type="checkbox"
                    checked={allSelected}
                    onChange={(e) => toggleSelectAll(e.target.checked)}
                  />
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Tipo
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Número/Chave NF
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Data Emissão
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Data Entrada
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Origem
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                  Observação
                </th>
                <th className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300">Ações</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {paginated.map((nota) => {
                const checked = nota.chave_nf
                  ? selectedChaves.includes(nota.chave_nf)
                  : false
                return (
                  <tr
                    key={nota.chave_nf || nota.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-4 py-2">
                      <input
                        type="checkbox"
                        disabled={!nota.chave_nf}
                        checked={checked}
                        onChange={(e) => toggleSelect(nota, e.target.checked)}
                      />
                    </td>
                    <td className="px-4 py-2 text-sm whitespace-nowrap">
                      {nota.tipo === 'pulo_numeracao' ? (
                        <span className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                          Pulo Numeração
                        </span>
                      ) : (
                        <span className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                          XML vs SPED
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      {nota.numero_nf ||
                        (nota.numeros_faltantes ? nota.numeros_faltantes.join(', ') : '-')}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                      {nota.data_emissao
                        ? new Date(nota.data_emissao).toLocaleDateString('pt-BR')
                        : '-'}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                      {nota.data_entrada
                        ? new Date(nota.data_entrada).toLocaleDateString('pt-BR')
                        : '-'}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                      {nota.origem || '-'}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 max-w-[200px] truncate">
                      {nota.observacao || '-'}
                    </td>
                    <td className="px-4 py-2 text-sm space-x-2 whitespace-nowrap">
                      {nota.chave_nf ? (
                        <>
                          <button
                            onClick={() => openAlterar([nota.chave_nf!])}
                            className="text-yellow-600 hover:text-yellow-800 p-1"
                            title="Alterar Data"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                            </svg>
                          </button>
                          <button
                            onClick={marcarEncontrada}
                            className="text-green-600 hover:text-green-800 p-1"
                            title="Marcar como Encontrada"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleExcluirSistema([nota.chave_nf!])}
                            className="text-red-600 hover:text-red-800 p-1"
                            title="Excluir"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </>
                      ) : (
                        <span className="text-gray-400">Pulo de numeração</span>
                      )}
                    </td>
                  </tr>
                )
              })}
              {notas.length === 0 && !isLoading && (
                <tr>
                  <td
                    colSpan={8}
                    className="px-4 py-8 text-center text-gray-500 dark:text-gray-400"
                  >
                    Nenhuma nota faltante encontrada.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </TableScrollContainer>
        <Pagination
          current={page}
          total={notas.length}
          pageSize={pageSize}
          onChange={setPage}
          showTotal={(total, range) =>
            `Mostrando ${range[0]} a ${range[1]} de ${total} registros`}
        />
      </div>
    )
  }

  const helpTabs = [
    {
      label: 'Entrada',
      content: (
        <div className="space-y-2">
          <p>Exibe os XMLs de entrada importados.</p>
          <ul className="list-disc pl-4 space-y-1">
            <li>
              <strong>Identificar Faltantes</strong>: procura notas de entrada que
              não foram importadas.
            </li>
            <li>
              <strong>Detalhes</strong>: mostra as informações completas do XML.
            </li>
            <li>
              <strong>Excluir</strong>: remove o XML do sistema.
            </li>
          </ul>
        </div>
      ),
    },
    {
      label: 'Saída',
      content: (
        <div className="space-y-2">
          <p>Exibe os XMLs de saída cadastrados.</p>
          <ul className="list-disc pl-4 space-y-1">
            <li>
              <strong>Identificar Faltantes</strong>: busca notas de saída
              ausentes.
            </li>
            <li>
              <strong>Marcar como Cancelado</strong>: registra o cancelamento da
              nota fiscal.
            </li>
            <li>
              <strong>Detalhes</strong>: visualiza os dados completos do XML.
            </li>
            <li>
              <strong>Excluir</strong>: remove a nota do sistema.
            </li>
          </ul>
        </div>
      ),
    },
    {
      label: 'Faltantes Entrada',
      content: (
        <div className="space-y-2">
          <p>
            Lista notas de entrada faltantes ou divergentes entre XML e SPED.
          </p>
          <ul className="list-disc pl-4 space-y-1">
            <li>
              <strong>Identificar Faltantes</strong>: reexecuta a busca de notas
              ausentes.
            </li>
            <li>
              <strong>Alterar Data</strong>: ajusta a data das notas selecionadas.
            </li>
            <li>
              <strong>Excluir</strong>: remove as notas selecionadas do
              sistema.
            </li>
            <li>
              <strong>Marcar como Encontrada</strong>: indica que a nota já foi
              localizada.
            </li>
          </ul>
        </div>
      ),
    },
    {
      label: 'Faltantes Saída',
      content: (
        <div className="space-y-2">
          <p>
            Lista notas de saída faltantes ou divergentes entre XML e SPED.
          </p>
          <ul className="list-disc pl-4 space-y-1">
            <li>
              <strong>Identificar Faltantes</strong>: reexecuta a checagem de
              notas ausentes.
            </li>
            <li>
              <strong>Alterar Data</strong>: altera a data das notas
              selecionadas.
            </li>
            <li>
              <strong>Excluir</strong>: remove as notas selecionadas do
              sistema.
            </li>
            <li>
              <strong>Marcar como Encontrada</strong>: marca a nota como
              localizada.
            </li>
          </ul>
        </div>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gestão de XMLs
          </h1>
          <HelpButton onClick={() => setHelpOpen(true)} />
        </div>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Gerencie XMLs importados, identifique notas faltantes e valide dados
        </p>
      </div>

      {!selectedCompany && (
        <div className="p-4 bg-yellow-50 text-yellow-800 rounded-lg">
          Selecione uma empresa para visualizar os dados.
        </div>
      )}

      {selectedCompany && (
        <>
          {/* Modern Tabs */}
          <Card className="p-2 bg-gray-50 dark:bg-gray-800/50">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
              <Button
                variant={activeTab === 'entrada' ? 'primary' : 'ghost'}
                size="md"
                onClick={() => setActiveTab('entrada')}
                className="justify-center"
                icon={
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                }
                glow={activeTab === 'entrada'}
              >
                Entrada
              </Button>
              
              <Button
                variant={activeTab === 'saida' ? 'primary' : 'ghost'}
                size="md"
                onClick={() => setActiveTab('saida')}
                className="justify-center"
                icon={
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 13.293a1 1 0 010 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 14.586V7a1 1 0 10-2 0v7.586L7.707 13.293a1 1 0 00-1.414 0z" clipRule="evenodd" />
                  </svg>
                }
                glow={activeTab === 'saida'}
              >
                Saída
              </Button>
              
              <Button
                variant={activeTab === 'faltantes-entrada' ? 'warning' : 'ghost'}
                size="md"
                onClick={() => setActiveTab('faltantes-entrada')}
                className="justify-center"
                icon={
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                }
                glow={activeTab === 'faltantes-entrada'}
              >
                Faltantes Entrada
              </Button>
              
              <Button
                variant={activeTab === 'faltantes-saida' ? 'warning' : 'ghost'}
                size="md"
                onClick={() => setActiveTab('faltantes-saida')}
                className="justify-center"
                icon={
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                }
                glow={activeTab === 'faltantes-saida'}
              >
                Faltantes Saída
              </Button>
            </div>
          </Card>

          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 mt-6">
            {isLoading && (
              <div className="p-4 text-center text-gray-500">Carregando...</div>
            )}
            {!isLoading && activeTab && data && (
              <>
                {(activeTab === 'entrada' || activeTab === 'saida') &&
                  renderXMLs(data.xmls || [])}
                {(activeTab === 'faltantes-entrada' || activeTab === 'faltantes-saida') &&
                  renderNotas(data.notas || [])}
              </>
            )}
          </div>
        </>
      )}
      {xmlDetalhes && (
        <div className="fixed inset-0 z-50">
          <XMLDetailsModal details={xmlDetalhes} onClose={closeDetails} />
        </div>
      )}
      <AlterarDataModal
        isOpen={alterarDataOpen}
        chaves={alterarChaves}
        onClose={() => {
          setAlterarDataOpen(false)
          setAlterarChaves([])
        }}
        onSave={handleAlterarSave}
      />
      <HelpModal
        isOpen={helpOpen}
        onClose={() => setHelpOpen(false)}
        title="Ajuda - Gestão de XMLs"
        tabs={helpTabs}
      />
    </div>
  )
}
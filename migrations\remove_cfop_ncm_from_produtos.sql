-- Migration to remove CFOP and NCM columns from the produto table
-- These fields are now only stored in the nota_fiscal_item table and cenario tables

-- First, create a backup of the current data
CREATE TABLE IF NOT EXISTS produto_backup AS SELECT * FROM produto;

-- Remove the columns from the produto table
ALTER TABLE produto DROP COLUMN IF EXISTS cfop;
ALTER TABLE produto DROP COLUMN IF EXISTS ncm;

-- Add a comment to document the change
COMMENT ON TABLE produto IS 'Tabela de produtos. CFOP e NCM foram removidos e agora são armazenados apenas nas tabelas nota_fiscal_item e cenarios.';

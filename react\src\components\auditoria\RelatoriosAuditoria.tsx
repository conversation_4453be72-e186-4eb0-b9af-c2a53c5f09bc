import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Modal } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import { auditoriaService } from '@/services/auditoriaService'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { useFilterStore } from '@/store/filterStore'
import { useMutation } from '@tanstack/react-query'

interface RelatoriosAuditoriaProps {
  tipoTributo: string
}

interface RelatorioConfig {
  tipo: 'completo' | 'inconsistencias' | 'resumo' | 'comparativo'
  formato: 'pdf' | 'excel'
  incluirDetalhes: boolean
  incluirGraficos: boolean
  filtros: {
    status?: string
    analista_visualizou?: string
    cfops?: string[]
    ncms?: string[]
    csts?: string[]
    aliquotas?: string[]
  }
}

const tiposRelatorio = [
  {
    id: 'completo',
    nome: 'Relatório Completo',
    descricao: 'Relatório completo com todas as informações da auditoria',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    )
  },
  {
    id: 'inconsistencias',
    nome: 'Relatório de Inconsistências',
    descricao: 'Apenas registros com inconsistências encontradas',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    )
  },
  {
    id: 'resumo',
    nome: 'Relatório Resumo',
    descricao: 'Resumo executivo com métricas principais',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    )
  },
  {
    id: 'comparativo',
    nome: 'Relatório Comparativo',
    descricao: 'Comparação entre valores da nota e cenários',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    )
  }
]

export function RelatoriosAuditoria({ tipoTributo }: RelatoriosAuditoriaProps) {
  const [showModal, setShowModal] = useState(false)
  const [config, setConfig] = useState<RelatorioConfig>({
    tipo: 'completo',
    formato: 'pdf',
    incluirDetalhes: true,
    incluirGraficos: true,
    filtros: {}
  })

  const empresaId = useSelectedCompany()
  const { selectedYear, selectedMonth } = useFilterStore()

  const gerarRelatorioMutation = useMutation({
    mutationFn: async () => {
      if (!empresaId) throw new Error('Empresa não selecionada')
      
      return auditoriaService.gerarRelatorioCompleto({
        empresaId,
        tipoTributo,
        year: selectedYear,
        month: selectedMonth,
        ...config
      })
    },
    onSuccess: (blob) => {
      // Download do arquivo
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `relatorio-auditoria-${tipoTributo}-${selectedYear}-${selectedMonth}.${config.formato}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      setShowModal(false)
    },
    onError: (error) => {
      console.error('Erro ao gerar relatório:', error)
    }
  })

  const getTributoDisplayName = (tipo: string) => {
    const names: Record<string, string> = {
      'icms': 'ICMS',
      'icms-st': 'ICMS-ST',
      'icms_st': 'ICMS-ST',
      'ipi': 'IPI',
      'pis': 'PIS',
      'cofins': 'COFINS',
      'difal': 'DIFAL',
    }
    return names[tipo] || tipo.toUpperCase()
  }

  return (
    <>
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Relatórios de Auditoria
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Gere relatórios personalizados da auditoria de {getTributoDisplayName(tipoTributo)}
            </p>
          </div>
          <Button
            variant="primary"
            onClick={() => setShowModal(true)}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            }
          >
            Gerar Relatório
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {tiposRelatorio.map((tipo) => (
            <div
              key={tipo.id}
              className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 transition-colors cursor-pointer"
              onClick={() => {
                setConfig(prev => ({ ...prev, tipo: tipo.id as any }))
                setShowModal(true)
              }}
            >
              <div className="flex items-center gap-3 mb-2">
                <div className="text-primary-600 dark:text-primary-400">
                  {tipo.icon}
                </div>
                <h4 className="font-medium text-gray-900 dark:text-white">
                  {tipo.nome}
                </h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {tipo.descricao}
              </p>
            </div>
          ))}
        </div>
      </Card>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Configurar Relatório"
        size="lg"
      >
        <div className="space-y-6">
          {/* Tipo de Relatório */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Tipo de Relatório
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {tiposRelatorio.map((tipo) => (
                <div
                  key={tipo.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    config.tipo === tipo.id
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                  onClick={() => setConfig(prev => ({ ...prev, tipo: tipo.id as any }))}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <div className={`${config.tipo === tipo.id ? 'text-primary-600 dark:text-primary-400' : 'text-gray-400'}`}>
                      {tipo.icon}
                    </div>
                    <span className="font-medium text-sm">{tipo.nome}</span>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {tipo.descricao}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Formato */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Formato
            </label>
            <div className="flex gap-4">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="formato"
                  value="pdf"
                  checked={config.formato === 'pdf'}
                  onChange={(e) => setConfig(prev => ({ ...prev, formato: e.target.value as any }))}
                  className="text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm">PDF</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="formato"
                  value="excel"
                  checked={config.formato === 'excel'}
                  onChange={(e) => setConfig(prev => ({ ...prev, formato: e.target.value as any }))}
                  className="text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm">Excel</span>
              </label>
            </div>
          </div>

          {/* Opções */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Opções
            </label>
            <div className="space-y-3">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={config.incluirDetalhes}
                  onChange={(e) => setConfig(prev => ({ ...prev, incluirDetalhes: e.target.checked }))}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm">Incluir detalhes das operações</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={config.incluirGraficos}
                  onChange={(e) => setConfig(prev => ({ ...prev, incluirGraficos: e.target.checked }))}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm">Incluir gráficos e visualizações</span>
              </label>
            </div>
          </div>

          {/* Filtros */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Filtros (opcional)
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                  Status
                </label>
                <select
                  value={config.filtros.status || ''}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    filtros: { ...prev.filtros, status: e.target.value || undefined }
                  }))}
                  className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="">Todos</option>
                  <option value="conforme">Conforme</option>
                  <option value="inconsistente">Inconsistente</option>
                </select>
              </div>
              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                  Análise
                </label>
                <select
                  value={config.filtros.analista_visualizou || ''}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    filtros: { ...prev.filtros, analista_visualizou: e.target.value || undefined }
                  }))}
                  className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="">Todas</option>
                  <option value="true">Analisadas</option>
                  <option value="false">Não analisadas</option>
                </select>
              </div>
            </div>
          </div>

          {/* Informações do Período */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Período do Relatório
            </h4>
            <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <p><strong>Tributo:</strong> {getTributoDisplayName(tipoTributo)}</p>
              <p><strong>Período:</strong> {new Date(selectedYear, selectedMonth - 1).toLocaleDateString('pt-BR', {
                month: 'long',
                year: 'numeric'
              })}</p>
              <p><strong>Empresa:</strong> {empresaId ? `ID ${empresaId}` : 'Não selecionada'}</p>
            </div>
          </div>

          {/* Botões */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="ghost"
              onClick={() => setShowModal(false)}
              disabled={gerarRelatorioMutation.isPending}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={() => gerarRelatorioMutation.mutate()}
              loading={gerarRelatorioMutation.isPending}
              disabled={!empresaId}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              }
            >
              {gerarRelatorioMutation.isPending ? 'Gerando...' : 'Gerar Relatório'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}
import api from './authService'

export interface DashboardStats {
  total_empresas: number
  empresas_auditadas: number
  empresas_pendentes: number
  total_impostos_auditados: number
}

export interface Empresa {
  id: number
  razao_social: string
  cnpj: string
  status_auditoria: 'completa' | 'parcial' | 'pendente'
  tributos_auditados: string[]
  tributos_pendentes: string[]
  total_inconsistencias: number
  total_valor_inconsistente: number
  total_impostos: number
  ultima_auditoria?: string
}

export interface CardTributo {
  tipo_tributo: string
  nome_tributo: string
  auditado: boolean
  status_tipo: 'aplicavel' | 'nao_aplicavel'
  total_notas: number
  total_produtos: number
  total_conforme: number
  total_inconsistente: number
  valor_inconsistente_maior: number
  valor_inconsistente_menor: number
  valor_tributo?: number
  status_manual?: {
    motivo: string
  }
}

export interface TotalGeral {
  total_notas: number
  total_produtos: number
  total_conforme: number
  total_inconsistente: number
  valor_inconsistente_total: number
}

export interface EmpresaDetails {
  success: boolean
  empresa: {
    id: number
    razao_social: string
    cnpj: string
  }
  cards_tributos: CardTributo[]
  total_geral: TotalGeral
  message?: string
}

export interface DashboardResponse {
  success: boolean
  estatisticas?: DashboardStats
  empresas?: Empresa[]
  message?: string
}

export interface EmpresaGraficos {
  success: boolean
  empresa: {
    id: number
    razao_social: string
    cnpj: string
  }
  periodo: {
    inicio: string
    fim: string
  }
  dados: {
    labels: string[]
    geral: {
      valores_inconsistentes: number[]
      total_inconsistencias: number[]
    }
    tributos: Record<
      string,
      {
        valores_inconsistentes: number[]
        total_inconsistencias: number[]
      }
    >
  }
  message?: string
}

export const dashboardService = {
  /**
   * Obtém estatísticas do dashboard de saída
   */
  async getStats(year?: number, month?: number): Promise<DashboardStats> {
    const params = new URLSearchParams()
    if (year) params.append('year', year.toString())
    if (month) params.append('month', month.toString())
    
    const response = await api.get(`/dashboard/estatisticas?${params}`)
    return response.data.estatisticas
  },

  /**
   * Lista empresas do dashboard de saída
   */
  async getEmpresas(year?: number, month?: number): Promise<Empresa[]> {
    const params = new URLSearchParams()
    if (year) params.append('year', year.toString())
    if (month) params.append('month', month.toString())
    
    const response = await api.get(`/dashboard/empresas?${params}`)
    return response.data.empresas
  },

  /**
   * Obtém estatísticas do dashboard de entrada
   */
  async getStatsEntrada(year?: number, month?: number): Promise<DashboardStats> {
    const params = new URLSearchParams()
    if (year) params.append('year', year.toString())
    if (month) params.append('month', month.toString())
    
    const response = await api.get(`/dashboard-entrada/estatisticas?${params}`)
    return response.data.estatisticas
  },

  /**
   * Lista empresas do dashboard de entrada
   */
  async getEmpresasEntrada(year?: number, month?: number): Promise<Empresa[]> {
    const params = new URLSearchParams()
    if (year) params.append('year', year.toString())
    if (month) params.append('month', month.toString())
    
    const response = await api.get(`/dashboard-entrada/empresas?${params}`)
    return response.data.empresas
  },

  /**
   * Obtém detalhes de uma empresa específica (saída)
   */
  async getEmpresaDetails(empresaId: number, year?: number, month?: number) {
    const params = new URLSearchParams()
    if (year) params.append('year', year.toString())
    if (month) params.append('month', month.toString())
    
    const response = await api.get(`/dashboard/empresa/${empresaId}?${params}`)
    return response.data
  },

  /**
   * Obtém detalhes de uma empresa específica (entrada)
   */
  async getEmpresaDetailsEntrada(empresaId: number, year?: number, month?: number) {
    const params = new URLSearchParams()
    if (year) params.append('year', year.toString())
    if (month) params.append('month', month.toString())
    
    const response = await api.get(`/dashboard-entrada/empresa/${empresaId}?${params}`)
    return response.data
  },

  /**
   * Obtém gráficos da empresa (apenas saída)
   */
  async getEmpresaGraficos(
    empresaId: number,
    year?: number,
    month?: number,
  ): Promise<EmpresaGraficos> {
    const params = new URLSearchParams()
    if (year) params.append('year', year.toString())
    if (month) params.append('month', month.toString())
    
    const response = await api.get(`/dashboard/empresa/${empresaId}/graficos?${params}`)
    return response.data
  },
}
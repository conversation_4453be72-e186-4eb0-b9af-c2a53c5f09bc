import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { usuariosService } from '@/services/usuariosService'
import { UsuarioModal } from '@/components/usuarios/UsuarioModal'
import type { Usuario } from '@/types/usuarios'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Table } from '@/components/ui/Table'
import { Edit } from 'lucide-react'
import { HelpButton } from '@/components/ui/HelpButton'
import { HelpModal } from '@/components/ui/HelpModal'

export function UsuariosPage() {
  const { data, isLoading, refetch } = useQuery({
    queryKey: ['usuarios'],
    queryFn: () => usuariosService.getUsuarios(),
  })

  const usuarios: Usuario[] = data?.usuarios || []

  const [modalOpen, setModalOpen] = useState(false)
  const [editingUsuario, setEditingUsuario] = useState<Usuario | null>(null)
  const [helpOpen, setHelpOpen] = useState(false)

  const openEdit = async (id: number) => {
    const user = await usuariosService.getUsuario(id)
    setEditingUsuario(user)
    setModalOpen(true)
  }

  const closeModal = () => {
    setModalOpen(false)
    setEditingUsuario(null)
  }

  const handleSaved = () => {
    refetch()
  }

  const columns = [
    { key: 'nome', title: 'Nome' },
    {
      key: 'tipo_usuario',
      title: 'Tipo',
      render: (value: string) => {
        if (value === 'admin') return 'Administrador'
        if (value === 'escritorio') return 'Escritório'
        return 'Usuário'
      },
    },
    {
      key: 'empresas_permitidas',
      title: 'Empresas Permitidas',
      render: (value: number[]) => `${value?.length || 0} empresa(s)`,
    },
    {
      key: 'actions',
      title: 'Ações',
      render: (_: any, record: Usuario) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => openEdit(record.id)}
          icon={<Edit className="w-4 h-4" />}
          className="text-primary-600 hover:text-primary-800"
        >
          Editar
        </Button>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Usuários
            </h1>
            <HelpButton onClick={() => setHelpOpen(true)} />
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Gerencie os usuários do sistema
          </p>
        </div>
      </div>

      <Card>
        <Table<Usuario> data={usuarios} columns={columns} loading={isLoading} />
      </Card>

      <UsuarioModal
        usuario={editingUsuario}
        isOpen={modalOpen}
        onClose={closeModal}
        onSaved={handleSaved}
      />

      <HelpModal
        isOpen={helpOpen}
        onClose={() => setHelpOpen(false)}
        title="Ajuda"
        tabs={[
          {
            label: 'Tipos de Usuário',
            content: (
              <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                <p>
                  <strong>Administrador:</strong> acesso completo ao sistema e
                  às configurações.
                </p>
                <p>
                  <strong>Escritório:</strong> gerencia as empresas do
                  escritório e seus usuários.
                </p>
                <p>
                  <strong>Usuário:</strong> acesso restrito às empresas
                  permitidas.
                </p>
              </div>
            ),
          },
          {
            label: 'Empresas Permitidas',
            content: (
              <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                <p>
                  Define quais empresas o usuário pode visualizar e operar no
                  sistema.
                </p>
                <p>
                  Marque as empresas desejadas durante a criação ou edição do
                  usuário.
                </p>
              </div>
            ),
          },
          {
            label: 'Criação e Edição',
            content: (
              <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                <p>
                  Utilize o botão "Novo Usuário" para cadastrar um novo
                  usuário, preenchendo dados e selecionando as empresas
                  permitidas.
                </p>
                <p>
                  Na tabela, clique em "Editar" para ajustar as empresas
                  permitidas de um usuário existente.
                </p>
              </div>
            ),
          },
        ]}
      />
    </div>
  )
}
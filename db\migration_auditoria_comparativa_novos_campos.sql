-- Migração para adicionar novos campos na tabela auditoria_comparativa_impostos
-- Campos para dados tributários XML e NCM do SPED

-- Adicionar campos de dados tributários XML
ALTER TABLE auditoria_comparativa_impostos 
ADD COLUMN xml_icms_origem VARCHAR(2),
ADD COLUMN xml_icms_cst VARCHAR(3),
ADD COLUMN xml_icms_csosn VARCHAR(3),
ADD COLUMN xml_icms_bc DECIMAL(15,2),
ADD COLUMN xml_icms_aliquota DECIMAL(5,2),
ADD COLUMN xml_ipi_valor DECIMAL(15,2),
ADD COLUMN xml_ipi_bc DECIMAL(15,2),
ADD COLUMN xml_ipi_aliquota DECIMAL(5,2),
ADD COLUMN xml_ipi_cst VARCHAR(2),
ADD COLUMN xml_pis_valor DECIMAL(15,2),
ADD COLUMN xml_pis_bc DECIMAL(15,2),
ADD COLUMN xml_pis_aliquota DECIMAL(5,4),
ADD COLUMN xml_pis_cst VARCHAR(2),
ADD COLUMN xml_cofins_valor DECIMAL(15,2),
ADD COLUMN xml_cofins_bc DECIMAL(15,2),
ADD COLUMN xml_cofins_aliquota DECIMAL(5,4),
ADD COLUMN xml_cofins_cst VARCHAR(2);

-- Adicionar campo NCM do SPED
ALTER TABLE auditoria_comparativa_impostos
ADD COLUMN sped_ncm VARCHAR(20);

-- Adicionar campos para ICMS-ST que estavam faltando
ALTER TABLE auditoria_comparativa_impostos
ADD COLUMN xml_icms_st_valor DECIMAL(15,2),
ADD COLUMN xml_icms_st_bc DECIMAL(15,2),
ADD COLUMN xml_icms_st_aliquota DECIMAL(5,2),
ADD COLUMN xml_icms_st_cst VARCHAR(3);

-- Comentários para documentação
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_icms_origem IS 'Origem do ICMS do XML (0-8)';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_icms_cst IS 'CST ICMS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_icms_csosn IS 'CSOSN ICMS do XML (Simples Nacional)';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_icms_bc IS 'Base de cálculo ICMS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_icms_aliquota IS 'Alíquota ICMS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_ipi_valor IS 'Valor IPI do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_ipi_bc IS 'Base de cálculo IPI do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_ipi_aliquota IS 'Alíquota IPI do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_ipi_cst IS 'CST IPI do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_pis_valor IS 'Valor PIS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_pis_bc IS 'Base de cálculo PIS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_pis_aliquota IS 'Alíquota PIS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_pis_cst IS 'CST PIS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_cofins_valor IS 'Valor COFINS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_cofins_bc IS 'Base de cálculo COFINS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_cofins_aliquota IS 'Alíquota COFINS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.xml_cofins_cst IS 'CST COFINS do XML';
COMMENT ON COLUMN auditoria_comparativa_impostos.sped_ncm IS 'NCM do produto no SPED';

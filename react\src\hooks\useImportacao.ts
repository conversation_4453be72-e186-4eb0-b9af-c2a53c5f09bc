import { useState, useCallback, useRef, useEffect } from 'react'
import { useWebSocket } from './useWebSocket'
import { importacaoService } from '@/services/importacaoService'

export interface ImportProgress {
  importId: string
  processed: number
  total: number
  percentage: number
  currentFile: string
  successCount: number
  errorCount: number
  elapsedTime: number
  status: 'idle' | 'processing' | 'completed' | 'error'
  errors: string[]
  stage?: string
  message?: string
  totalNotas?: number
  totalItens?: number
  totalClientes?: number
  totalProdutos?: number
}

export function useImportacao() {
  const [progress, setProgress] = useState<ImportProgress>({
    importId: '',
    processed: 0,
    total: 0,
    percentage: 0,
    currentFile: '',
    successCount: 0,
    errorCount: 0,
    elapsedTime: 0,
    status: 'idle',
    errors: [],
    stage: undefined,
    message: undefined,
    totalNotas: undefined,
    totalItens: undefined,
    totalClientes: undefined,
    totalProdutos: undefined,
  })

  const [activeImports, setActiveImports] = useState<Set<string>>(new Set())
  const activeImportsRef = useRef<Set<string>>(new Set())
  const timerRef = useRef<number | null>(null)
  const startTimeRef = useRef<number>(0)
  const listenersConfigured = useRef<boolean>(false)

  const {
    socket,
    state: socketState,
    joinRoom,
    leaveRoom,
    on,
    off,
    connect,
  } = useWebSocket({}, () => {
    // Ao conectar, verificar importações ativas
    console.log('🔌 WebSocket conectado!')
    checkActiveImports()
  })

  useEffect(() => {
    activeImportsRef.current = activeImports
  }, [activeImports])

  const generateImportId = useCallback(() => {
    return (
      crypto.randomUUID?.() ||
      'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0
        const v = c === 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    )
  }, [])

  const startTimer = useCallback(() => {
    startTimeRef.current = Date.now()
    timerRef.current = setInterval(() => {
      const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000)
      setProgress((prev) => ({ ...prev, elapsedTime: elapsed }))
    }, 1000)
  }, [])

  const stopTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
  }, [])

  const resetProgress = useCallback(() => {
    setProgress({
      importId: '',
      processed: 0,
      total: 0,
      percentage: 0,
      currentFile: '',
      successCount: 0,
      errorCount: 0,
      elapsedTime: 0,
      status: 'idle',
      errors: [],
      stage: undefined,
      message: undefined,
      totalNotas: undefined,
      totalItens: undefined,
      totalClientes: undefined,
      totalProdutos: undefined,
    })
    stopTimer()
  }, [stopTimer])

  const updateProgress = useCallback((data: Partial<ImportProgress>) => {
    setProgress((prev) => {
      const newProgress = { ...prev, ...data }
      if (newProgress.total > 0) {
        newProgress.percentage = Math.round(
          (newProgress.processed / newProgress.total) * 100
        )
      }
      return newProgress
    })
  }, [])

  const checkActiveImports = useCallback(async () => {
    try {
      console.log('🔍 Verificando importações ativas...')
      const imports = await importacaoService.getImportacoesAtivas()
      console.log('📋 Importações ativas encontradas:', imports)

      const activeIds = new Set(imports.map((imp) => imp.id))
      setActiveImports(activeIds)

      // Reconectar às importações ativas
      imports.forEach((imp) => {
        console.log('🔄 Reconectando à importação:', imp.id)
        joinRoom(imp.id)

        setProgress({
          importId: imp.id,
          processed: imp.arquivos_processados,
          total: imp.total_arquivos,
          percentage:
            imp.total_arquivos > 0
              ? Math.round(
                  (imp.arquivos_processados / imp.total_arquivos) * 100
                )
              : 0,
          currentFile: 'Reconectando...',
          successCount: 0,
          errorCount: 0,
          elapsedTime: 0,
          status: 'processing',
          errors: [],
        })

        startTimer()
      })
    } catch (error) {
      console.error('❌ Erro ao verificar importações ativas:', error)
    }
  }, [joinRoom, startTimer])

  const startImport = useCallback(
    (importId: string, totalFiles: number) => {
      const id = importId || generateImportId()

      console.log('🚀 Iniciando importação:', { id, totalFiles })

      setProgress({
        importId: id,
        processed: 0,
        total: totalFiles,
        percentage: 0,
        currentFile: 'Iniciando importação...',
        successCount: 0,
        errorCount: 0,
        elapsedTime: 0,
        status: 'processing',
        errors: [],
        stage: undefined,
        message: undefined,
        totalNotas: undefined,
        totalItens: undefined,
        totalClientes: undefined,
        totalProdutos: undefined,
      })

      setActiveImports((prev) => new Set([...prev, id]))

      // Conectar WebSocket se não estiver conectado
      if (!socket?.connected) {
        console.log('🔌 Conectando WebSocket para importação...')
        const handleConnect = () => {
          console.log('🔗 Conectado, entrando na sala WebSocket:', id)
          joinRoom(id)
          socket?.off('connect', handleConnect)
        }
        socket?.on('connect', handleConnect)
        connect()
      } else {
        console.log('🔗 Conectando à sala WebSocket:', id)
        joinRoom(id)
      }

      startTimer()

      return id
    },
    [generateImportId, joinRoom, startTimer, socket, connect]
  )

  const finishImport = useCallback(
    (importId: string, success: boolean = true) => {
      setProgress((prev) => ({
        ...prev,
        status: success ? 'completed' : 'error',
      }))

      setActiveImports((prev) => {
        const newSet = new Set(prev)
        newSet.delete(importId)
        return newSet
      })

      leaveRoom(importId)
      stopTimer()
    },
    [leaveRoom, stopTimer]
  )

  // Configurar listeners do WebSocket
  useEffect(() => {
    if (!socket) return

    // Se já configuramos listeners para este socket, não configurar novamente
    if (listenersConfigured.current) return

    console.log('🎧 Configurando listeners WebSocket...')
    listenersConfigured.current = true

    // Listener genérico para debug
    const handleAnyEvent = (eventName: string, ...args: any[]) => {
      console.log(`🔔 Evento WebSocket recebido: ${eventName}`, args)
    }

    // Configurar listener para todos os eventos (se disponível)
    if (socket.onAny) {
      socket.onAny(handleAnyEvent)
    } else {
      console.warn(
        '⚠️ socket.onAny não disponível - usando listeners específicos'
      )
    }

    const handleImportStart = (data: any) => {
      console.log('🚀 Import started:', data)
      setProgress((prev) => ({
        ...prev,
        total: data.total_files || data.total || prev.total,
        currentFile: 'Iniciando processamento...',
        status: 'processing',
      }))

      // Iniciar timer quando a importação começar
      if (!timerRef.current) {
        startTimer()
      }
    }

    const handleImportProgress = (data: any) => {
      console.log('📊 Import progress:', data)
      setProgress((prev) => {
        const total = data.total ?? data.total_files ?? prev.total
        const processed = data.processed ?? prev.processed
        return {
          ...prev,
          total,
          processed,
          currentFile: data.current_file || data.arquivo || 'Processando...',
          successCount: data.success_count ?? prev.successCount,
          errorCount: data.error_count ?? prev.errorCount,
          percentage: total > 0 ? Math.round((processed / total) * 100) : 0,
        }
      })
    }

    const handleImportComplete = (data: any) => {
      console.log('✅ Import completed:', data)
      setProgress((prev) => {
        const importId = data.import_id || prev.importId
        finishImport(importId, true)

        const results = data.results || {}
        const errors = Array.isArray(results.errors)
          ? results.errors.map((err: any) =>
              err.filename
                ? `${err.filename}: ${err.error}`
                : err.error || String(err)
            )
          : prev.errors
        const successCount = results.successful_imports ?? prev.successCount
        const errorCount = results.failed_imports ?? errors.length

        const totalProcessed =
          results.successful_imports !== undefined &&
          results.failed_imports !== undefined
            ? results.successful_imports + results.failed_imports
            : data.total || prev.total

        return {
          ...prev,
          processed: totalProcessed,
          currentFile: 'Importação concluída!',
          status: 'completed',
          percentage: 100,
          errors,
          successCount,
          errorCount,
          totalNotas: results.totais?.notas ?? prev.totalNotas,
          totalItens: results.totais?.itens ?? prev.totalItens,
          totalClientes: results.totais?.clientes ?? prev.totalClientes,
          totalProdutos: results.totais?.produtos ?? prev.totalProdutos,
        }
      })
    }

    const handleImportError = (data: any) => {
      console.log('❌ Import error:', data)
      setProgress((prev) => {
        const importId = data.import_id || prev.importId
        if (data.fatal) {
          finishImport(importId, false)
        }
        return {
          ...prev,
          status: data.fatal ? 'error' : prev.status,
          errors: [...prev.errors, data.error || 'Erro desconhecido'],
          errorCount: prev.errorCount + 1,
        }
      })
    }

    const handleFileProcessed = (data: any) => {
      console.log('📄 File processed:', data)
      setProgress((prev) => {
        const newProcessed = data.processed || prev.processed + 1
        const newSuccessCount = data.success
          ? prev.successCount + 1
          : prev.successCount
        const newErrorCount = data.error ? prev.errorCount + 1 : prev.errorCount

        return {
          ...prev,
          processed: newProcessed,
          currentFile:
            data.current_file || data.arquivo || `Arquivo ${newProcessed}`,
          successCount: newSuccessCount,
          errorCount: newErrorCount,
          percentage:
            prev.total > 0 ? Math.round((newProcessed / prev.total) * 100) : 0,
        }
      })
    }

    const handleSpedProgress = (data: any) => {
      console.log('📈 SPED import progress:', data)
      setProgress((prev) => ({
        ...prev,
        processed: data.progresso ?? prev.processed,
        total: data.total ?? prev.total,
        percentage:
          data.porcentagem ??
          (prev.total > 0
            ? Math.round(
                ((data.progresso ?? prev.processed) /
                  (data.total ?? prev.total)) *
                  100
              )
            : 0),
        currentFile: data.mensagem || prev.currentFile,
        stage: data.etapa || prev.stage,
        message: data.mensagem || prev.message,
        status: data.status || prev.status,
      }))
    }

    const handleSpedComplete = (data: any) => {
      console.log('✅ SPED import completed:', data)
      setProgress((prev) => {
        finishImport(prev.importId, true)
        return {
          ...prev,
          currentFile: 'Importação SPED concluída!',
          message: data.message || prev.message,
          status: 'completed',
          percentage: 100,
          totalNotas: data.total_notas,
          totalItens: data.total_itens,
          totalClientes: data.total_clientes,
          totalProdutos: data.total_produtos,
        }
      })
    }

    const handleSpedError = (data: any) => {
      console.log('❌ SPED import error:', data)
      setProgress((prev) => {
        finishImport(prev.importId, false)
        return {
          ...prev,
          status: 'error',
          errors: [
            ...prev.errors,
            data.error || data.message || 'Erro desconhecido',
          ],
        }
      })
    }

    // Registrar listeners
    on('import_start', handleImportStart)
    on('import_progress', handleImportProgress)
    on('import_complete', handleImportComplete)
    on('import_error', handleImportError)
    on('file_processed', handleFileProcessed)
    on('sped_import_progress', handleSpedProgress)
    on('sped_import_complete', handleSpedComplete)
    on('sped_import_error', handleSpedError)

    return () => {
      // Limpar listeners
      if (socket.offAny) {
        socket.offAny(handleAnyEvent)
      }
      off('import_start', handleImportStart)
      off('import_progress', handleImportProgress)
      off('import_complete', handleImportComplete)
      off('import_error', handleImportError)
      off('file_processed', handleFileProcessed)
      off('sped_import_progress', handleSpedProgress)
      off('sped_import_complete', handleSpedComplete)
      off('sped_import_error', handleSpedError)

      // Resetar flag para permitir reconfiguração
      listenersConfigured.current = false
    }
  }, [socket, on, off, finishImport, startTimer])

  // Cleanup ao desmontar
  useEffect(() => {
    return () => {
      stopTimer()
      activeImportsRef.current.forEach((importId) => {
        leaveRoom(importId)
      })
    }
  }, [stopTimer, leaveRoom])

  return {
    progress,
    socketState,
    activeImports,
    startImport,
    finishImport,
    resetProgress,
    updateProgress,
    checkActiveImports,
    generateImportId,
  }
}

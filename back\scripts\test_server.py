#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servidor de teste local para o serviço cliente XML
Simula o endpoint de produção para testes
"""

from flask import Flask, request, jsonify
import os
import zipfile
import tempfile
from datetime import datetime

app = Flask(__name__)

# Configurações do servidor de teste
UPLOAD_FOLDER = "test_uploads"
VALID_API_KEYS = [
    "test_key_123",
    "demo_api_key",
    "3f50c92ae6154040b27de74347605e94"  # Chave do exemplo original
]

# Criar pasta de uploads se não existir
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route('/fiscal/api/service-upload', methods=['POST'])
def upload_service():
    """Endpoint que simula o servidor de produção"""
    
    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] Nova requisição recebida")
    
    # Verificar API Key
    api_key = request.headers.get('X-API-KEY')
    if not api_key:
        print("❌ API Key não fornecida")
        return jsonify({"error": "API Key obrigatória"}), 401
    
    if api_key not in VALID_API_KEYS:
        print(f"❌ API Key inválida: {api_key}")
        return jsonify({"error": "API Key inválida"}), 403
    
    print(f"✅ API Key válida: {api_key}")
    
    # Verificar escritório ID
    escritorio_id = request.form.get('escritorio_id')
    if not escritorio_id:
        print("❌ Escritório ID não fornecido")
        return jsonify({"error": "escritorio_id obrigatório"}), 400
    
    print(f"📋 Escritório ID: {escritorio_id}")
    
    # Verificar arquivo
    if 'arquivo' not in request.files:
        print("❌ Nenhum arquivo enviado")
        return jsonify({"error": "Arquivo obrigatório"}), 400
    
    file = request.files['arquivo']
    if file.filename == '':
        print("❌ Nome do arquivo vazio")
        return jsonify({"error": "Nome do arquivo inválido"}), 400
    
    print(f"📁 Arquivo recebido: {file.filename}")
    
    # Salvar arquivo
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    safe_filename = f"{timestamp}_{file.filename}"
    file_path = os.path.join(UPLOAD_FOLDER, safe_filename)
    
    try:
        file.save(file_path)
        print(f"💾 Arquivo salvo: {file_path}")
        
        # Se for ZIP, listar conteúdo
        if file.filename.lower().endswith('.zip'):
            try:
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    files_in_zip = zip_ref.namelist()
                    print(f"📦 Arquivos no ZIP: {len(files_in_zip)}")
                    for f in files_in_zip:
                        print(f"   - {f}")
            except Exception as e:
                print(f"⚠️  Erro ao ler ZIP: {e}")
        
        # Resposta de sucesso
        response = {
            "status": "success",
            "message": "Arquivo recebido com sucesso",
            "arquivo": file.filename,
            "escritorio_id": escritorio_id,
            "timestamp": datetime.now().isoformat(),
            "saved_as": safe_filename
        }
        
        print("✅ Upload processado com sucesso")
        return jsonify(response), 200
        
    except Exception as e:
        print(f"❌ Erro ao salvar arquivo: {e}")
        return jsonify({"error": f"Erro interno: {str(e)}"}), 500

@app.route('/fiscal/api/test-connection', methods=['GET'])
def api_test_connection():
    """Endpoint para testar conectividade"""
    return jsonify({
        "status": "ok",
        "message": "Servidor de teste funcionando",
        "timestamp": datetime.now().isoformat()
    }), 200

@app.route('/fiscal/api/simulate-error/<int:status_code>', methods=['POST'])
def simulate_error(status_code):
    """Endpoint para simular diferentes tipos de erro"""
    
    error_messages = {
        400: "Requisição inválida",
        401: "Não autorizado",
        403: "API Key inválida",
        404: "Endpoint não encontrado",
        500: "Erro interno do servidor",
        503: "Serviço indisponível"
    }
    
    message = error_messages.get(status_code, "Erro simulado")
    print(f"🔴 Simulando erro {status_code}: {message}")
    
    return jsonify({"error": message}), status_code

@app.route('/fiscal/api/uploads', methods=['GET'])
def list_uploads():
    """Lista arquivos recebidos"""
    try:
        files = []
        for filename in os.listdir(UPLOAD_FOLDER):
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            if os.path.isfile(file_path):
                stat = os.stat(file_path)
                files.append({
                    "filename": filename,
                    "size": stat.st_size,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
                })
        
        return jsonify({
            "uploads": files,
            "total": len(files)
        }), 200
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/', methods=['GET'])
def home():
    """Página inicial do servidor de teste"""
    return """
    <h1>🧪 Servidor de Teste - Serviço XML</h1>
    <h2>Endpoints Disponíveis:</h2>
    <ul>
        <li><strong>POST /fiscal/api/service-upload</strong> - Upload de arquivos (endpoint principal)</li>
        <li><strong>GET /fiscal/api/test-connection</strong> - Teste de conectividade</li>
        <li><strong>GET /fiscal/api/uploads</strong> - Listar arquivos recebidos</li>
        <li><strong>POST /fiscal/api/simulate-error/&lt;code&gt;</strong> - Simular erros (400, 403, 500, etc.)</li>
    </ul>
    
    <h2>API Keys Válidas para Teste:</h2>
    <ul>
        <li>test_key_123</li>
        <li>demo_api_key</li>
        <li>3f50c92ae6154040b27de74347605e94</li>
    </ul>
    
    <h2>Como Testar:</h2>
    <ol>
        <li>Configure o cliente para usar: <code>http://localhost:5001</code></li>
        <li>Use uma das API Keys válidas</li>
        <li>Coloque arquivos XML na pasta monitorada</li>
        <li>Verifique os logs do servidor e do cliente</li>
    </ol>
    """

def main():
    """Inicia o servidor de teste"""
    print("=" * 50)
    print("🧪 SERVIDOR DE TESTE - SERVIÇO XML")
    print("=" * 50)
    print(f"📁 Pasta de uploads: {os.path.abspath(UPLOAD_FOLDER)}")
    print(f"🔑 API Keys válidas: {', '.join(VALID_API_KEYS)}")
    print("🌐 Servidor iniciando em http://localhost:5001")
    print("=" * 50)
    print("Para testar:")
    print("1. Configure o cliente para usar: http://localhost:5001/fiscal/api/service-upload")
    print("2. Use uma das API Keys válidas")
    print("3. Monitore os logs aqui")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5001, debug=True)

if __name__ == "__main__":
    main()
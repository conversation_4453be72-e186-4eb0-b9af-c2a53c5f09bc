from models import db, HistoricoAuditoriaEntrada, AuditoriaEntrada
from models import <PERSON>p<PERSON><PERSON>, C<PERSON><PERSON>, Produto
from datetime import datetime, date
from sqlalchemy import and_, or_, func, desc
from decimal import Decimal
import json

class MachineLearningService:
    """
    Serviço de Machine Learning para auditoria fiscal
    Aprende padrões de auditoria e sugere configurações automáticas
    """
    
    def __init__(self, empresa_id):
        self.empresa_id = empresa_id
    
    def registrar_padrao_aprovado(self, auditoria_id, usuario_id):
        """
        Registra um padrão aprovado pelo usuário para aprendizado
        """
        try:
            auditoria = db.session.get(AuditoriaEntrada, auditoria_id)
            if not auditoria:
                return {'success': False, 'message': 'Auditoria não encontrada'}
            
            # <PERSON>car ou criar histórico
            historico = db.session.query(HistoricoAuditoriaEntrada).filter(
                HistoricoAuditoriaEntrada.empresa_id == auditoria.empresa_id,
                HistoricoAuditoriaEntrada.cliente_id == auditoria.cliente_id,
                HistoricoAuditoriaEntrada.produto_id == auditoria.produto_id,
                HistoricoAuditoriaEntrada.cfop == auditoria.sped_cfop,
                HistoricoAuditoriaEntrada.ncm == auditoria.sped_ncm
            ).first()
            
            if historico:
                # Atualizar histórico existente
                historico.frequencia_uso += 1
                historico.ultima_utilizacao = datetime.now()
                historico.aprovado_usuario = True
                
                # Atualizar alíquotas com base nos dados aprovados
                if auditoria.sped_icms_aliquota:
                    historico.icms_aliquota_padrao = auditoria.sped_icms_aliquota
                if auditoria.sped_icms_st_aliquota:
                    historico.icms_st_aliquota_padrao = auditoria.sped_icms_st_aliquota
                if auditoria.sped_ipi_aliquota:
                    historico.ipi_aliquota_padrao = auditoria.sped_ipi_aliquota
                if auditoria.sped_pis_aliquota:
                    historico.pis_aliquota_padrao = auditoria.sped_pis_aliquota
                if auditoria.sped_cofins_aliquota:
                    historico.cofins_aliquota_padrao = auditoria.sped_cofins_aliquota
                
                # Calcular nova confiabilidade
                historico.confiabilidade = min(1.0, historico.confiabilidade + 0.1)
                
            else:
                # Criar novo histórico
                historico = HistoricoAuditoriaEntrada(
                    empresa_id=auditoria.empresa_id,
                    cliente_id=auditoria.cliente_id,
                    produto_id=auditoria.produto_id,
                    cfop=auditoria.sped_cfop,
                    ncm=auditoria.sped_ncm,
                    icms_aliquota_padrao=auditoria.sped_icms_aliquota,
                    icms_st_aliquota_padrao=auditoria.sped_icms_st_aliquota,
                    ipi_aliquota_padrao=auditoria.sped_ipi_aliquota,
                    pis_aliquota_padrao=auditoria.sped_pis_aliquota,
                    cofins_aliquota_padrao=auditoria.sped_cofins_aliquota,
                    frequencia_uso=1,
                    confiabilidade=0.7,  # Confiabilidade inicial
                    aprovado_usuario=True
                )
                db.session.add(historico)
            
            # Marcar auditoria como aprovada
            auditoria.status_geral = 'auditado'
            auditoria.data_auditoria = datetime.now()
            auditoria.usuario_auditoria = usuario_id
            
            db.session.commit()
            
            return {
                'success': True,
                'message': 'Padrão registrado com sucesso',
                'confiabilidade': float(historico.confiabilidade),
                'frequencia': historico.frequencia_uso
            }
            
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'Erro ao registrar padrão: {str(e)}'
            }
    
    def sugerir_configuracao(self, cliente_id, produto_id, cfop, ncm):
        """
        Sugere configuração baseada no histórico de aprendizado
        """
        try:
            # Buscar padrões similares
            padroes = db.session.query(HistoricoAuditoriaEntrada).filter(
                HistoricoAuditoriaEntrada.empresa_id == self.empresa_id,
                HistoricoAuditoriaEntrada.aprovado_usuario == True
            ).order_by(desc(HistoricoAuditoriaEntrada.confiabilidade)).all()
            
            sugestoes = []
            
            # 1. Busca exata (cliente + produto + cfop + ncm)
            padrao_exato = next((p for p in padroes if 
                               p.cliente_id == cliente_id and 
                               p.produto_id == produto_id and 
                               p.cfop == cfop and 
                               p.ncm == ncm), None)
            
            if padrao_exato:
                sugestoes.append({
                    'tipo': 'exato',
                    'confiabilidade': float(padrao_exato.confiabilidade),
                    'frequencia': padrao_exato.frequencia_uso,
                    'icms_aliquota': float(padrao_exato.icms_aliquota_padrao) if padrao_exato.icms_aliquota_padrao else None,
                    'icms_st_aliquota': float(padrao_exato.icms_st_aliquota_padrao) if padrao_exato.icms_st_aliquota_padrao else None,
                    'ipi_aliquota': float(padrao_exato.ipi_aliquota_padrao) if padrao_exato.ipi_aliquota_padrao else None,
                    'pis_aliquota': float(padrao_exato.pis_aliquota_padrao) if padrao_exato.pis_aliquota_padrao else None,
                    'cofins_aliquota': float(padrao_exato.cofins_aliquota_padrao) if padrao_exato.cofins_aliquota_padrao else None,
                    'descricao': 'Configuração exata encontrada no histórico'
                })
            
            # 2. Busca por produto + cfop (mesmo produto, mesmo cfop)
            padroes_produto_cfop = [p for p in padroes if 
                                   p.produto_id == produto_id and 
                                   p.cfop == cfop and
                                   p.confiabilidade >= 0.6]
            
            if padroes_produto_cfop:
                # Calcular média ponderada das alíquotas
                total_peso = sum(p.frequencia_uso * p.confiabilidade for p in padroes_produto_cfop)
                
                if total_peso > 0:
                    icms_media = sum(p.icms_aliquota_padrao * p.frequencia_uso * p.confiabilidade 
                                   for p in padroes_produto_cfop if p.icms_aliquota_padrao) / total_peso
                    
                    sugestoes.append({
                        'tipo': 'produto_cfop',
                        'confiabilidade': min(0.8, sum(p.confiabilidade for p in padroes_produto_cfop) / len(padroes_produto_cfop)),
                        'frequencia': sum(p.frequencia_uso for p in padroes_produto_cfop),
                        'icms_aliquota': round(icms_media, 2) if icms_media else None,
                        'descricao': f'Baseado em {len(padroes_produto_cfop)} padrões similares do mesmo produto'
                    })
            
            # 3. Busca por NCM + CFOP (produtos similares)
            padroes_ncm_cfop = [p for p in padroes if 
                               p.ncm == ncm and 
                               p.cfop == cfop and
                               p.confiabilidade >= 0.5]
            
            if padroes_ncm_cfop and not padrao_exato:
                confiabilidade_media = sum(p.confiabilidade for p in padroes_ncm_cfop) / len(padroes_ncm_cfop)
                
                sugestoes.append({
                    'tipo': 'ncm_cfop',
                    'confiabilidade': min(0.7, confiabilidade_media),
                    'frequencia': sum(p.frequencia_uso for p in padroes_ncm_cfop),
                    'descricao': f'Baseado em {len(padroes_ncm_cfop)} produtos com mesmo NCM e CFOP'
                })
            
            # 4. Busca por cliente (padrões do mesmo cliente)
            padroes_cliente = [p for p in padroes if 
                              p.cliente_id == cliente_id and
                              p.confiabilidade >= 0.4]
            
            if padroes_cliente and len(sugestoes) < 2:
                sugestoes.append({
                    'tipo': 'cliente',
                    'confiabilidade': min(0.6, sum(p.confiabilidade for p in padroes_cliente) / len(padroes_cliente)),
                    'frequencia': sum(p.frequencia_uso for p in padroes_cliente),
                    'descricao': f'Baseado em {len(padroes_cliente)} padrões do mesmo cliente'
                })
            
            return {
                'success': True,
                'sugestoes': sugestoes[:3],  # Máximo 3 sugestões
                'total_padroes': len(padroes)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao gerar sugestões: {str(e)}'
            }
    
    def analisar_anomalias(self, mes, ano):
        """
        Analisa anomalias baseado no histórico de padrões
        """
        try:
            # Buscar auditorias do período
            auditorias = db.session.query(AuditoriaEntrada).filter(
                AuditoriaEntrada.empresa_id == self.empresa_id,
                AuditoriaEntrada.mes_referencia == mes,
                AuditoriaEntrada.ano_referencia == ano
            ).all()
            
            anomalias = []
            
            for auditoria in auditorias:
                # Buscar padrão histórico
                padrao = db.session.query(HistoricoAuditoriaEntrada).filter(
                    HistoricoAuditoriaEntrada.empresa_id == auditoria.empresa_id,
                    HistoricoAuditoriaEntrada.cliente_id == auditoria.cliente_id,
                    HistoricoAuditoriaEntrada.produto_id == auditoria.produto_id,
                    HistoricoAuditoriaEntrada.cfop == auditoria.sped_cfop,
                    HistoricoAuditoriaEntrada.aprovado_usuario == True
                ).first()
                
                if padrao and padrao.confiabilidade >= 0.7:
                    # Verificar desvios significativos
                    desvios = []
                    
                    if (auditoria.sped_icms_aliquota and padrao.icms_aliquota_padrao and
                        abs(float(auditoria.sped_icms_aliquota) - float(padrao.icms_aliquota_padrao)) > 1.0):
                        desvios.append({
                            'tributo': 'ICMS',
                            'valor_atual': float(auditoria.sped_icms_aliquota),
                            'valor_esperado': float(padrao.icms_aliquota_padrao),
                            'desvio': abs(float(auditoria.sped_icms_aliquota) - float(padrao.icms_aliquota_padrao))
                        })
                    
                    if desvios:
                        anomalias.append({
                            'auditoria_id': auditoria.id,
                            'numero_nf': auditoria.numero_nf,
                            'cliente_id': auditoria.cliente_id,
                            'produto_id': auditoria.produto_id,
                            'confiabilidade_padrao': float(padrao.confiabilidade),
                            'desvios': desvios
                        })
            
            return {
                'success': True,
                'anomalias': anomalias,
                'total_analisadas': len(auditorias)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao analisar anomalias: {str(e)}'
            }
    
    def obter_estatisticas_ml(self):
        """
        Obtém estatísticas do sistema de ML
        """
        try:
            total_padroes = db.session.query(HistoricoAuditoriaEntrada).filter(
                HistoricoAuditoriaEntrada.empresa_id == self.empresa_id
            ).count()
            
            padroes_aprovados = db.session.query(HistoricoAuditoriaEntrada).filter(
                HistoricoAuditoriaEntrada.empresa_id == self.empresa_id,
                HistoricoAuditoriaEntrada.aprovado_usuario == True
            ).count()
            
            confiabilidade_media = db.session.query(
                func.avg(HistoricoAuditoriaEntrada.confiabilidade)
            ).filter(
                HistoricoAuditoriaEntrada.empresa_id == self.empresa_id,
                HistoricoAuditoriaEntrada.aprovado_usuario == True
            ).scalar() or 0
            
            return {
                'success': True,
                'total_padroes': total_padroes,
                'padroes_aprovados': padroes_aprovados,
                'confiabilidade_media': round(float(confiabilidade_media), 2),
                'taxa_aprovacao': round((padroes_aprovados / max(total_padroes, 1)) * 100, 1)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erro ao obter estatísticas: {str(e)}'
            }

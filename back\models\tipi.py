from .escritorio import db
from sqlalchemy.sql import func
from datetime import datetime

class TIPI(db.Model):
    """Modelo para a tabela TIPI - Tabela de Incidência do Imposto sobre Produtos Industrializados"""
    __tablename__ = 'tipi'

    id = db.Column(db.Integer, primary_key=True)
    ncm = db.Column(db.String(10), nullable=False, index=True)
    ex = db.Column(db.String(2), nullable=True)
    descricao = db.Column(db.Text, nullable=True)
    aliquota = db.Column(db.String(20), nullable=True)  # Pode ser número ou 'NT'
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    data_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())

    # Índice único para NCM + EX
    __table_args__ = (
        db.UniqueConstraint('ncm', 'ex', name='uq_tipi_ncm_ex'),
        db.Index('idx_tipi_ncm', 'ncm'),
    )

    def __repr__(self):
        return f"<TIPI NCM:{self.ncm} EX:{self.ex} Alíquota:{self.aliquota}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        return {
            'id': self.id,
            'ncm': self.ncm,
            'ex': self.ex,
            'descricao': self.descricao,
            'aliquota': self.aliquota,
            'aliquota_numerica': self.get_aliquota_numerica(),
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None
        }

    def get_aliquota_numerica(self):
        """
        Retorna a alíquota como número decimal
        Converte 'NT' para 0.0
        """
        if not self.aliquota or self.aliquota.upper() == 'NT':
            return 0.0

        try:
            # Remove % se presente e converte para float
            aliquota_str = str(self.aliquota).replace('%', '').replace(',', '.')
            return float(aliquota_str)
        except (ValueError, TypeError):
            return 0.0

    @classmethod
    def buscar_por_ncm(cls, ncm, ex=None):
        """
        Busca registro TIPI por NCM e opcionalmente por EX

        Args:
            ncm (str): Código NCM
            ex (str, optional): Código EX

        Returns:
            TIPI: Registro encontrado ou None
        """
        query = cls.query.filter(cls.ncm == ncm)

        if ex:
            query = query.filter(cls.ex == ex)
        else:
            # Se não especificar EX, buscar primeiro com EX null, depois qualquer um
            result = query.filter(cls.ex.is_(None)).first()
            if not result:
                result = query.first()
            return result

        return query.first()

    @classmethod
    def from_dict(cls, data):
        """Cria instância a partir de dicionário"""
        return cls(
            ncm=data.get('ncm', ''),
            ex=data.get('ex', ''),
            descricao=data.get('descricao', ''),
            aliquota=data.get('aliquota', '')
        )

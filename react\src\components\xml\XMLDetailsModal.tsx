import type { XMLDetalhesResponse } from '@/types/xmls'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { useEffect } from 'react'

interface Props {
  details: XMLDetalhesResponse
  onClose: () => void
}

export function XMLDetailsModal({ details, onClose }: Props) {
  const { xml, produtos, totais, estatisticas } = details

  // Função para formatar valores monetários
  const formatCurrency = (value: string | number | undefined) => {
    if (!value) return '-'
    const num = typeof value === 'string' ? parseFloat(value) : value
    return isNaN(num) 
      ? '-' 
      : num.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })
  }

  // Função para formatar datas
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString('pt-BR')
    } catch {
      return 'Data inválida'
    }
  }

  // Efeito para lidar com o fechamento ao pressionar ESC e prevenir scroll
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    const handleScroll = () => {
      document.body.style.overflow = 'hidden'
    }

    const handleBodyScroll = () => {
      document.documentElement.style.overflow = 'hidden'
    }

    // Prevenir scroll do body quando o modal está aberto
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'
    handleScroll()
    handleBodyScroll()

    document.addEventListener('keydown', handleEscape)
    
    return () => {
      document.body.style.overflow = 'unset'
      document.documentElement.style.overflow = 'unset'
      document.removeEventListener('keydown', handleEscape)
    }
  }, [onClose])

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
      onClick={(e) => {
        // Fecha o modal ao clicar no backdrop (fora do conteúdo do modal)
        if (e.target === e.currentTarget) {
          onClose()
        }
      }}
    >
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-4">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Detalhes do XML
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Nota Fiscal {xml.numero_nf}
            </p>
          </div>
          <button 
            onClick={onClose} 
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-0">
            {/* Informações da Nota e Participante */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="px-5 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 rounded-t-xl">
                  <h3 className="font-semibold text-gray-900 dark:text-white">Informações da Nota</h3>
                </div>
                <div className="p-5 space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Número:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{xml.numero_nf || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Data Emissão:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{formatDate(xml.data_emissao)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Data Entrada:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{formatDate(xml.data_entrada)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Tipo:</span>
                    <Badge variant={xml.tipo_nota === '0' ? 'success' : 'primary'} size="sm">
                      {xml.tipo_nota === '0' ? 'Entrada' : 'Saída'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Status:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {xml.status_validacao || 'Pendente'}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="px-5 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 rounded-t-xl">
                  <h3 className="font-semibold text-gray-900 dark:text-white">Participante</h3>
                </div>
                <div className="p-5 space-y-3">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 block text-sm">Razão Social:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {xml.participante_razao_social || 'N/A'}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 block text-sm">CNPJ:</span>
                    <span className="font-mono text-gray-900 dark:text-white">
                      {xml.participante_cnpj || 'N/A'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Totais da Nota */}
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm mb-6">
              <div className="px-5 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 rounded-t-xl">
                <h3 className="font-semibold text-gray-900 dark:text-white">Totais da Nota</h3>
              </div>
              <div className="p-5">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4">
                  <div className="text-center p-3 bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 rounded-lg">
                    <div className="text-lg font-bold text-primary-600 dark:text-primary-400">
                      {formatCurrency(totais.valor_total_nota)}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">Valor Total</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {formatCurrency(totais.total_icms)}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">ICMS</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {formatCurrency(totais.total_icms_st)}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">ICMS-ST</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {formatCurrency(totais.total_ipi)}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">IPI</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {formatCurrency(totais.total_pis)}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">PIS</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {formatCurrency(totais.total_cofins)}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">COFINS</div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Produtos */}
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="px-5 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 rounded-t-xl flex justify-between items-center">
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  Produtos
                </h3>
                <Badge variant="secondary">
                  {estatisticas.total_produtos} {estatisticas.total_produtos === 1 ? 'item' : 'itens'}
                </Badge>
              </div>
              <div className="p-2">
                <TableScrollContainer>
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-sm">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">Produto</th>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">CFOP</th>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">NCM</th>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">Qtd</th>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">Valor Unit.</th>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">Valor Total</th>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">ICMS</th>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">IPI</th>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">PIS</th>
                        <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-white whitespace-nowrap">COFINS</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                      {produtos.map((p, idx) => (
                        <tr 
                          key={idx} 
                          className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                        >
                          <td className="px-4 py-3 max-w-xs">
                            <div className="font-medium text-gray-900 dark:text-white truncate">
                              {p.produto_nome}
                            </div>
                            {p.cliente_nome && (
                              <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                                {p.cliente_nome}
                              </div>
                            )}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-gray-900 dark:text-white">
                            {p.item.cfop || '-'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-gray-900 dark:text-white">
                            {p.item.ncm || '-'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-gray-900 dark:text-white">
                            {p.item.quantidade?.toLocaleString('pt-BR') || '-'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-gray-900 dark:text-white">
                            {formatCurrency(p.item.valor_unitario)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-gray-900 dark:text-white">
                            {formatCurrency(p.item.valor_total)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-gray-900 dark:text-white">
                            {formatCurrency(p.tributo?.icms_valor)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-gray-900 dark:text-white">
                            {formatCurrency(p.tributo?.ipi_valor)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-gray-900 dark:text-white">
                            {formatCurrency(p.tributo?.pis_valor)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-gray-900 dark:text-white">
                            {formatCurrency(p.tributo?.cofins_valor)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </TableScrollContainer>
              </div>
            </div>
          </div>
        </div>
        
        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 flex justify-end">
          <Button variant="primary" onClick={onClose} size="md">
            Fechar
          </Button>
        </div>
      </div>
    </div>
  )
}
# src/model.py
import torch
import torch.nn as nn
import torch.nn.functional as F

class CenarioModel(nn.Module):
    def __init__(self, cardinalities, emb_dims, n_num):
        super().__init__()
        self.emb_layers = nn.ModuleList([
            nn.Embedding(cat_size, emb_dim)
            for cat_size, emb_dim in zip(cardinalities, emb_dims)
        ])
        n_emb = sum(emb_dims)
        self.fc1 = nn.Linear(n_emb + n_num, 128)
        self.fc2 = nn.Linear(128, 64)
        self.out = nn.Linear(64, 1)

    def forward(self, x_cat, x_num):
        embs = [emb(x_cat[:, i]) for i, emb in enumerate(self.emb_layers)]
        x = torch.cat(embs + [x_num], dim=1)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        # sigmoid para garantir saída em [0,1]
        return torch.sigmoid(self.out(x)).squeeze(1)

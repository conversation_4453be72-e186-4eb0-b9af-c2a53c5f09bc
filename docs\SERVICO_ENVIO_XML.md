# Serviço de Envio Automático de XML

Este documento descreve como ativar o envio automático de notas fiscais XML para o sistema.

## Plano de Ação (Evolução do Serviço)

1.  **Refatorar o Endpoint (Passo Atual):**
    *   **Endpoint:** Criar uma nova rota `POST /api/importacoes/service/upload`.
    *   **Funcionalidade:** A rota aceitará um único arquivo XML ou um arquivo ZIP com múltiplos XMLs.
    *   **Segurança (v1):** A autenticação será feita via `X-API-KEY` e `escritorio_id`. A lógica de negócio garantirá que os XMLs processados pertençam ao escritório informado.
    *   **Serviço:** Utilizará o `OptimizedXMLImportService` para maior performance e consistência.
    *   **Usuário:** As importações serão registradas em nome de um usuário de sistema dedicado.

2.  **Autenticação via JWT (Próximo Passo):**
    *   Substituir a autenticação por chave de API pela verificação de um token JWT, que será fornecido pelo portal central da empresa.
    *   O `escritorio_id` será extraído diretamente do token decodificado, eliminando a necessidade de enviá-lo no corpo da requisição e aumentando a segurança.

## Endpoint Atual (Em implementação)

`POST /api/importacoes/service/upload`

-   **Cabeçalho `X-API-KEY`**: Chave de API secreta para autenticação do serviço.
-   **Form Data**:
    -   `escritorio_id` (inteiro): ID do escritório que está enviando os arquivos.
    -   `arquivo`: Um único arquivo `.xml` ou um arquivo `.zip` contendo múltiplos XMLs.

## Configurando a chave de API

1.  Gere uma chave aleatória (exemplo em Python):

    ```python
    import uuid; print(uuid.uuid4().hex)
    ```

2.  Adicione a chave ao arquivo `.env` do backend:

    ```env
    SERVICE_UPLOAD_KEY=sua_chave_aqui
    ```

3.  Reinicie o servidor para aplicar a configuração.

## Serviço de monitoramento de pasta

O script `back/scripts/xml_service_example.py` pode ser usado para monitorar uma pasta e enviar automaticamente novos arquivos XML para o endpoint. **Atenção:** O script precisará ser atualizado para apontar para a nova rota `/api/importacoes/service/upload`.

### Instalação de dependências

```bash
pip install watchdog requests
```

### Execução (Exemplo)

```bash
python -m back.scripts.xml_service_example /caminho/para/pasta --escritorio-id 1 --api-key sua_chave
```


from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import (
    db,
    CenarioICMS,
    CenarioICMSST,
    CenarioIPI,
    CenarioPIS,
    CenarioCOFINS,
    CenarioDIFAL,
    Usuario,
    Cliente,
    Produto,
)
from decimal import Decimal, InvalidOperation
from services.cenario_service import CenarioService
from services.tributo_calculation_service import TributoCalculationService
from services.optimized_data_loader_service import OptimizedDataLoaderService
from services.memory_monitor_service import MemoryMonitorService
from services.transactional import transactional_session
from services.ipi_validation_service import IPIValidationService
from services.cfop_cst_validation_service import CFOPCSTValidationService
from services.icms_st_cfop_cst_validation_service import ICMSSTCFOPCSTValidationService
from services.icms_st_validation_service import ICMSSTValidationService
from datetime import datetime
from sqlalchemy import extract, distinct
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

# Criar blueprint
cenario_bp = Blueprint('cenario', __name__)

# Mapeamento de tipos de tributo para modelos
TIPO_TRIBUTO_MODELS = {
    'icms': CenarioICMS,
    'icms_st': CenarioICMSST,
    'ipi': CenarioIPI,
    'pis': CenarioPIS,
    'cofins': CenarioCOFINS,
    'difal': CenarioDIFAL
}

@cenario_bp.route('/api/cenarios/<tipo_tributo>', methods=['GET'])
def listar_cenarios(tipo_tributo):
    """
    Lista cenários de um tipo específico com suporte a paginação e carregamento completo otimizado

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter parâmetros da query
        filters = {
            'empresa_id': request.args.get('empresa_id', type=int),
            'cliente_id': request.args.get('cliente_id', type=int),
            'produto_id': request.args.get('produto_id', type=int),
            'status': request.args.get('status'),
            'cfop': request.args.get('cfop'),
            'ncm': request.args.get('ncm'),
            'cst': request.args.get('cst'),
            'uf': request.args.get('uf'),
            'atividade': request.args.get('atividade'),
            'destinacao': request.args.get('destinacao'),
            'aliquota': request.args.get('aliquota'),
            'aliquota_st': request.args.get('aliquota_st'),
            'reducao': request.args.get('reducao'),
            'reducao_st': request.args.get('reducao_st'),
            'mva': request.args.get('mva'),
            'ativo': request.args.get('ativo', type=bool),
            'direcao': request.args.get('direcao'),
            'year': request.args.get('year', type=int),
            'month': request.args.get('month', type=int),
            # Text-based filters
            'produto_codigo': request.args.get('produto_codigo'),
            'produto_descricao': request.args.get('produto_descricao'),
            'cliente_razao': request.args.get('cliente_razao'),
            'cliente_cnpj': request.args.get('cliente_cnpj'),
            'origem': request.args.get('origem'),
            'cest': request.args.get('cest'),
            'ex': request.args.get('ex'),
            'page': request.args.get('page', 1, type=int),
            'per_page': request.args.get('per_page', 100, type=int)
        }

        # Parâmetro de carregamento completo
        load_all = request.args.get('load_all', 'false').lower() == 'true'

        # Limitar per_page para paginação normal
        if not load_all and filters['per_page'] > 700:
            filters['per_page'] = 700

        # Usar o serviço otimizado para carregar dados
        data_loader = OptimizedDataLoaderService()
        result = data_loader.load_cenarios_optimized(tipo_tributo, filters, load_all)

        # Verificar se houve erro
        if not result['success']:
            if result.get('error') == 'memory_constraint':
                # Retornar erro de restrição de memória com informações detalhadas
                return jsonify({
                    'success': False,
                    'error': 'memory_constraint',
                    'message': result['message'],
                    'suggested_action': result['suggested_action'],
                    'total_count': result['total_count'],
                    'memory_stats': result.get('memory_stats', {})
                }), 413  # Payload Too Large
            else:
                # Erro genérico
                return jsonify({
                    'success': False,
                    'message': result['message']
                }), 500

        # Retornar dados com informações de paginação
        response_data = {
            'success': True,
            'cenarios': result['cenarios'],
            'pagination': result['pagination']
        }

        # Adicionar informações de memória se disponível
        if 'memory_stats' in result:
            response_data['memory_stats'] = result['memory_stats']

        # Adicionar informações de carregamento
        if load_all:
            response_data['load_info'] = {
                'total_count': result['total_count'],
                'loaded_count': result['loaded_count'],
                'load_all': True
            }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error in listar_cenarios: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao listar cenários: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/batch', methods=['GET'])
def listar_cenarios_batch(tipo_tributo):
    """
    Lista cenários em lotes para carregamento progressivo

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter parâmetros da query
        filters = {
            'empresa_id': request.args.get('empresa_id', type=int),
            'cliente_id': request.args.get('cliente_id', type=int),
            'produto_id': request.args.get('produto_id', type=int),
            'status': request.args.get('status'),
            'cfop': request.args.get('cfop'),
            'ncm': request.args.get('ncm'),
            'cst': request.args.get('cst'),
            'uf': request.args.get('uf'),
            'atividade': request.args.get('atividade'),
            'destinacao': request.args.get('destinacao'),
            'aliquota': request.args.get('aliquota'),
            'aliquota_st': request.args.get('aliquota_st'),
            'reducao': request.args.get('reducao'),
            'reducao_st': request.args.get('reducao_st'),
            'mva': request.args.get('mva'),
            'ativo': request.args.get('ativo', type=bool),
            'direcao': request.args.get('direcao'),
            'year': request.args.get('year', type=int),
            'month': request.args.get('month', type=int),
            # Text-based filters
            'produto_codigo': request.args.get('produto_codigo'),
            'produto_descricao': request.args.get('produto_descricao'),
            'cliente_razao': request.args.get('cliente_razao'),
            'cliente_cnpj': request.args.get('cliente_cnpj'),
            'origem': request.args.get('origem'),
            'cest': request.args.get('cest'),
            'ex': request.args.get('ex')
        }

        # Parâmetros de lote
        offset = request.args.get('offset', 0, type=int)
        batch_size = request.args.get('batch_size', 5000, type=int)

        # Limitar batch_size para evitar sobrecarga
        if batch_size > 10000:
            batch_size = 10000

        # Usar o serviço otimizado para carregar lote
        data_loader = OptimizedDataLoaderService()
        result = data_loader.get_progressive_batch(tipo_tributo, filters, offset, batch_size)

        # Verificar se houve erro
        if not result['success']:
            return jsonify({
                'success': False,
                'message': result['message']
            }), 500

        # Retornar dados do lote
        return jsonify({
            'success': True,
            'cenarios': result['cenarios'],
            'batch_info': {
                'offset': result['offset'],
                'batch_size': result['batch_size'],
                'loaded_count': result['loaded_count'],
                'has_more': result['has_more']
            }
        })

    except Exception as e:
        logger.error(f"Error in listar_cenarios_batch: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao listar cenários em lote: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/filter-options', methods=['GET'])
@jwt_required()
def obter_opcoes_filtro_cenarios(tipo_tributo):
    """
    Obtém as opções para os filtros de cenários com relacionamentos

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        direcao = request.args.get('direcao', 'saida')
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        status = request.args.get('status', 'novo')

        # Filtros de texto
        produto_codigo = request.args.get('produto_codigo')
        produto_descricao = request.args.get('produto_descricao')
        cliente_razao = request.args.get('cliente_razao')
        cliente_cnpj = request.args.get('cliente_cnpj')
        origem = request.args.get('origem')
        cest = request.args.get('cest')
        ex = request.args.get('ex')

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        # Obter o modelo correspondente
        CenarioModel = TIPO_TRIBUTO_MODELS[tipo_tributo]

        # Construir a query base
        query = CenarioModel.query.filter_by(empresa_id=empresa_id)

        # Adicionar filtro de direção
        query = query.filter_by(direcao=direcao)

        # Adicionar filtro de tipo_operacao
        tipo_operacao = '0' if direcao == 'entrada' else '1'
        query = query.filter_by(tipo_operacao=tipo_operacao)

        # Adicionar filtro de status se especificado
        if status:
            # Tratamento especial para status 'inconsistente' para incluir também os prefixados com 'incons_'
            if status == 'inconsistente':
                query = query.filter(
                    db.or_(
                        CenarioModel.status == 'inconsistente',
                        CenarioModel.status.like('incons_%')
                    )
                )
            else:
                query = query.filter_by(status=status)

        # Para filtros de ano e mês, precisamos fazer join com a tabela tributo
        # pois os cenários não têm data_emissao diretamente
        if year or month:
            from models import Tributo
            query = query.join(Tributo,
                (Tributo.empresa_id == CenarioModel.empresa_id) &
                (Tributo.cliente_id == CenarioModel.cliente_id) &
                (Tributo.produto_id == CenarioModel.produto_id)
            )

            if year:
                query = query.filter(extract('year', Tributo.data_emissao) == year)

            if month:
                query = query.filter(extract('month', Tributo.data_emissao) == month)

        # Aplicar filtros com base no tipo de usuário
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todos os cenários
            pass
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem cenários do seu escritório
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas cenários das empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            query = query.filter(CenarioModel.empresa_id.in_(empresas_permitidas))

        # Aplicar filtros de texto
        if produto_codigo or produto_descricao or cest:
            query = query.join(Produto, CenarioModel.produto_id == Produto.id)
            if produto_codigo:
                query = query.filter(Produto.codigo.ilike(f"%{produto_codigo}%"))
            if produto_descricao:
                query = query.filter(Produto.descricao.ilike(f"%{produto_descricao}%"))
            if cest and hasattr(Produto, 'cest'):
                query = query.filter(Produto.cest.ilike(f"%{cest}%"))

        if cliente_razao or cliente_cnpj:
            query = query.join(Cliente, CenarioModel.cliente_id == Cliente.id)
            if cliente_razao:
                query = query.filter(Cliente.razao_social.ilike(f"%{cliente_razao}%"))
            if cliente_cnpj:
                cnpj_search = cliente_cnpj.replace('.', '').replace('/', '').replace('-', '')
                query = query.filter(Cliente.cnpj.ilike(f"%{cnpj_search}%"))

        if origem and hasattr(CenarioModel, 'origem'):
            query = query.filter(CenarioModel.origem.ilike(f"%{origem}%"))

        if ex and tipo_tributo == 'ipi' and hasattr(CenarioModel, 'ex'):
            query = query.filter(CenarioModel.ex.ilike(f"%{ex}%"))

        # Carregar os relacionamentos necessários
        query = query.options(
            db.joinedload(CenarioModel.cliente)
        )

        # Obter valores únicos para cada campo de filtro
        cfops = defaultdict(lambda: {'ncms': set(), 'csts': set(), 'aliquotas': set(), 'aliquotas_st': set(), 'estados': set(), 'reducoes': set(), 'reducoes_st': set(), 'mvas': set()})
        ncms = defaultdict(lambda: {'cfops': set(), 'csts': set(), 'aliquotas': set(), 'aliquotas_st': set(), 'estados': set(), 'reducoes': set(), 'reducoes_st': set(), 'mvas': set()})
        csts = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'aliquotas': set(), 'aliquotas_st': set(), 'estados': set(), 'reducoes': set(), 'reducoes_st': set(), 'mvas': set()})
        aliquotas = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'estados': set(), 'reducoes': set(), 'reducoes_st': set(), 'mvas': set()})
        aliquotas_st = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'estados': set(), 'reducoes': set(), 'reducoes_st': set(), 'mvas': set()})
        estados = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'aliquotas': set(), 'aliquotas_st': set(), 'reducoes': set(), 'reducoes_st': set(), 'mvas': set()})
        reducoes = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'aliquotas': set(), 'aliquotas_st': set(), 'estados': set(), 'mvas': set()})
        reducoes_st = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'aliquotas': set(), 'aliquotas_st': set(), 'estados': set(), 'mvas': set()})
        atividades = defaultdict(lambda: {
            'cfops': set(),
            'ncms': set(),
            'csts': set(),
            'aliquotas': set(),
            'aliquotas_st': set(),
            'estados': set(),
            'reducoes': set(),
            'reducoes_st': set(),
            'destinacoes': set(),
            'mvas': set()
        })
        destinacoes = defaultdict(lambda: {
            'cfops': set(),
            'ncms': set(),
            'csts': set(),
            'aliquotas': set(),
            'aliquotas_st': set(),
            'estados': set(),
            'reducoes': set(),
            'reducoes_st': set(),
            'atividades': set(),
            'mvas': set()
        })
        mvas = defaultdict(lambda: {
            'cfops': set(),
            'ncms': set(),
            'csts': set(),
            'aliquotas': set(),
            'aliquotas_st': set(),
            'estados': set(),
            'reducoes': set(),
            'reducoes_st': set(),
            'atividades': set(),
            'destinacoes': set()
        })

        # Executar query e processar resultados
        cenarios = query.all()

        def format_percentage_value(val):
            """Formatar valor percentual para remover zeros desnecessários"""
            if val is None or val == '':
                return '0'
            try:
                # Converter para número, remover zeros à direita e ponto final se necessário
                num = float(val)
                if num == 0:
                    return '0'
                # Usar toFixed para garantir no máximo 4 casas decimais, depois remover zeros à direita
                str_val = f"{num:.4f}"
                str_val = str_val.rstrip('0').rstrip('.')
                return str_val
            except (ValueError, TypeError):
                return '0'

        for cenario in cenarios:
            cfop = cenario.cfop or ''
            ncm = cenario.ncm or ''
            cst = cenario.cst or ''
            aliquota = format_percentage_value(cenario.aliquota or '0')
            aliquota_st = format_percentage_value(getattr(cenario, 'icms_st_aliquota', '0') or '0')

            # Obter estado do cliente
            estado = cenario.cliente.uf if cenario.cliente and cenario.cliente.uf else ''
            atividade = cenario.cliente.atividade if cenario.cliente and cenario.cliente.atividade else ''
            destinacao_cli = cenario.cliente.destinacao if cenario.cliente and cenario.cliente.destinacao else ''


            # Obter redução e MVA com base no tipo de tributo
            reducao = '0'
            reducao_st = '0'
            mva_val = '0'
            if tipo_tributo == 'icms_st':
                reducao_st = format_percentage_value(getattr(cenario, 'icms_st_p_red_bc', '0') or '0')
                reducao = format_percentage_value(getattr(cenario, 'p_red_bc', '0') or '0')
                mva_val = format_percentage_value(getattr(cenario, 'icms_st_p_mva', '0') or '0')
            elif tipo_tributo in ['icms', 'pis', 'cofins', 'difal']:
                reducao = format_percentage_value(getattr(cenario, 'p_red_bc', '0') or '0')

            if cfop:
                cfops[cfop]['ncms'].add(ncm)
                cfops[cfop]['csts'].add(cst)
                cfops[cfop]['aliquotas'].add(aliquota)
                cfops[cfop]['aliquotas_st'].add(aliquota_st)
                cfops[cfop]['estados'].add(estado)
                cfops[cfop]['reducoes'].add(reducao)
                cfops[cfop]['reducoes_st'].add(reducao_st)
                cfops[cfop].setdefault('mvas', set()).add(mva_val)
                if atividade:
                    cfops[cfop].setdefault('atividades', set()).add(atividade)
                if destinacao_cli:
                    cfops[cfop].setdefault('destinacoes', set()).add(destinacao_cli)

            if ncm:
                ncms[ncm]['cfops'].add(cfop)
                ncms[ncm]['csts'].add(cst)
                ncms[ncm]['aliquotas'].add(aliquota)
                ncms[ncm]['aliquotas_st'].add(aliquota_st)
                ncms[ncm]['estados'].add(estado)
                ncms[ncm]['reducoes'].add(reducao)
                ncms[ncm]['reducoes_st'].add(reducao_st)
                ncms[ncm].setdefault('mvas', set()).add(mva_val)
                if atividade:
                    ncms[ncm].setdefault('atividades', set()).add(atividade)
                if destinacao_cli:
                    ncms[ncm].setdefault('destinacoes', set()).add(destinacao_cli)

            if cst:
                csts[cst]['cfops'].add(cfop)
                csts[cst]['ncms'].add(ncm)
                csts[cst]['aliquotas'].add(aliquota)
                csts[cst]['aliquotas_st'].add(aliquota_st)
                csts[cst]['estados'].add(estado)
                csts[cst]['reducoes'].add(reducao)
                csts[cst]['reducoes_st'].add(reducao_st)
                csts[cst].setdefault('mvas', set()).add(mva_val)
                if atividade:
                    csts[cst].setdefault('atividades', set()).add(atividade)
                if destinacao_cli:
                    csts[cst].setdefault('destinacoes', set()).add(destinacao_cli)

            if aliquota:
                aliquotas[aliquota]['cfops'].add(cfop)
                aliquotas[aliquota]['ncms'].add(ncm)
                aliquotas[aliquota]['csts'].add(cst)
                aliquotas[aliquota]['estados'].add(estado)
                aliquotas[aliquota]['reducoes'].add(reducao)
                aliquotas[aliquota]['reducoes_st'].add(reducao_st)
                aliquotas[aliquota].setdefault('mvas', set()).add(mva_val)
                if atividade:
                    aliquotas[aliquota].setdefault('atividades', set()).add(atividade)
                if destinacao_cli:
                    aliquotas[aliquota].setdefault('destinacoes', set()).add(destinacao_cli)

            if aliquota_st:
                aliquotas_st[aliquota_st]['cfops'].add(cfop)
                aliquotas_st[aliquota_st]['ncms'].add(ncm)
                aliquotas_st[aliquota_st]['csts'].add(cst)
                aliquotas_st[aliquota_st]['estados'].add(estado)
                aliquotas_st[aliquota_st]['reducoes'].add(reducao)
                aliquotas_st[aliquota_st]['reducoes_st'].add(reducao_st)
                aliquotas_st[aliquota_st].setdefault('mvas', set()).add(mva_val)
                if atividade:
                    aliquotas_st[aliquota_st].setdefault('atividades', set()).add(atividade)
                if destinacao_cli:
                    aliquotas_st[aliquota_st].setdefault('destinacoes', set()).add(destinacao_cli)

            if reducao_st and reducao_st != '0':
                reducoes_st[reducao_st]['cfops'].add(cfop)
                reducoes_st[reducao_st]['ncms'].add(ncm)
                reducoes_st[reducao_st]['csts'].add(cst)
                reducoes_st[reducao_st]['aliquotas'].add(aliquota)
                reducoes_st[reducao_st]['aliquotas_st'].add(aliquota_st)
                reducoes_st[reducao_st]['estados'].add(estado)
                reducoes_st[reducao_st].setdefault('mvas', set()).add(mva_val)
                if atividade:
                    reducoes_st[reducao_st].setdefault('atividades', set()).add(atividade)
                if destinacao_cli:
                    reducoes_st[reducao_st].setdefault('destinacoes', set()).add(destinacao_cli)

            if atividade:
                atividades[atividade]['cfops'].add(cfop)
                atividades[atividade]['ncms'].add(ncm)
                atividades[atividade]['csts'].add(cst)
                atividades[atividade]['aliquotas'].add(aliquota)
                atividades[atividade]['aliquotas_st'].add(aliquota_st)
                atividades[atividade]['estados'].add(estado)
                atividades[atividade]['reducoes'].add(reducao)
                atividades[atividade].setdefault('reducoes_st', set()).add(reducao_st)
                atividades[atividade].setdefault('mvas', set()).add(mva_val)
                if destinacao_cli:
                    atividades[atividade]['destinacoes'].add(destinacao_cli)

            if destinacao_cli:
                destinacoes[destinacao_cli]['cfops'].add(cfop)
                destinacoes[destinacao_cli]['ncms'].add(ncm)
                destinacoes[destinacao_cli]['csts'].add(cst)
                destinacoes[destinacao_cli]['aliquotas'].add(aliquota)
                destinacoes[destinacao_cli]['aliquotas_st'].add(aliquota_st)
                destinacoes[destinacao_cli]['estados'].add(estado)
                destinacoes[destinacao_cli]['reducoes'].add(reducao)
                destinacoes[destinacao_cli].setdefault('reducoes_st', set()).add(reducao_st)
                destinacoes[destinacao_cli].setdefault('mvas', set()).add(mva_val)
                if atividade:
                    destinacoes[destinacao_cli]['atividades'].add(atividade)

            if mva_val and mva_val != '0':
                mvas[mva_val]['cfops'].add(cfop)
                mvas[mva_val]['ncms'].add(ncm)
                mvas[mva_val]['csts'].add(cst)
                mvas[mva_val]['aliquotas'].add(aliquota)
                mvas[mva_val]['aliquotas_st'].add(aliquota_st)
                mvas[mva_val]['estados'].add(estado)
                mvas[mva_val]['reducoes'].add(reducao)
                mvas[mva_val]['reducoes_st'].add(reducao_st)
                if atividade:
                    mvas[mva_val]['atividades'].add(atividade)
                if destinacao_cli:
                    mvas[mva_val]['destinacoes'].add(destinacao_cli)

        # Inicializar dicionários para os novos filtros
        estados = {}
        reducoes = {}
        # Recalcular atividades e destinações para garantir que as opções sejam populadas
        atividades = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'aliquotas': set(), 'estados': set(), 'reducoes': set(), 'destinacoes': set(), 'mvas': set()})
        destinacoes = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'aliquotas': set(), 'estados': set(), 'reducoes': set(), 'atividades': set(), 'mvas': set()})

        # Processar estados e reduções dos cenários
        for cenario in cenarios:
            # Obter estado do cliente
            estado = cenario.cliente.uf if cenario.cliente and cenario.cliente.uf else ''
            atividade = cenario.cliente.atividade if cenario.cliente and cenario.cliente.atividade else ''
            destinacao_cli = cenario.cliente.destinacao if cenario.cliente and cenario.cliente.destinacao else ''

            # Obter redução com base no tipo de tributo
            reducao = '0'
            if tipo_tributo == 'icms_st':
                reducao = format_percentage_value(getattr(cenario, 'icms_st_p_red_bc', '0') or '0')
            elif tipo_tributo in ['icms', 'pis', 'cofins', 'difal']:
                reducao = format_percentage_value(getattr(cenario, 'p_red_bc', '0') or '0')

            # Processar estado
            if estado:
                if estado not in estados:
                    estados[estado] = {
                        'cfops': set(),
                        'ncms': set(),
                        'csts': set(),
                        'aliquotas': set(),
                        'reducoes': set(),
                        'mvas': set()
                    }

                if cenario.cfop:
                    estados[estado]['cfops'].add(cenario.cfop)
                if cenario.ncm:
                    estados[estado]['ncms'].add(cenario.ncm)
                if cenario.cst:
                    estados[estado]['csts'].add(cenario.cst)
                if cenario.aliquota is not None:
                    estados[estado]['aliquotas'].add(format_percentage_value(cenario.aliquota))
                if reducao and reducao != '0':
                    estados[estado]['reducoes'].add(reducao)
                if mva_val and mva_val != '0':
                    estados[estado]['mvas'].add(mva_val)
                if atividade:
                    estados[estado].setdefault('atividades', set()).add(atividade)
                if destinacao_cli:
                    estados[estado].setdefault('destinacoes', set()).add(destinacao_cli)

            # Processar redução
            if reducao and reducao != '0':
                if reducao not in reducoes:
                    reducoes[reducao] = {
                        'cfops': set(),
                        'ncms': set(),
                        'csts': set(),
                        'aliquotas': set(),
                        'estados': set(),
                        'mvas': set()
                    }

                if cenario.cfop:
                    reducoes[reducao]['cfops'].add(cenario.cfop)
                if cenario.ncm:
                    reducoes[reducao]['ncms'].add(cenario.ncm)
                if cenario.cst:
                    reducoes[reducao]['csts'].add(cenario.cst)
                if cenario.aliquota is not None:
                    reducoes[reducao]['aliquotas'].add(format_percentage_value(cenario.aliquota))
                if estado:
                    reducoes[reducao]['estados'].add(estado)
                if mva_val and mva_val != '0':
                    reducoes[reducao]['mvas'].add(mva_val)
                if atividade:
                    reducoes[reducao].setdefault('atividades', set()).add(atividade)
                if destinacao_cli:
                    reducoes[reducao].setdefault('destinacoes', set()).add(destinacao_cli)
                    
            # Atualizar mapas de atividades e destinações
            if atividade:
                atividades[atividade]['cfops'].add(cfop)
                atividades[atividade]['ncms'].add(ncm)
                atividades[atividade]['csts'].add(cst)
                atividades[atividade]['aliquotas'].add(aliquota)
                atividades[atividade]['estados'].add(estado)
                atividades[atividade]['reducoes'].add(reducao)
                if mva_val and mva_val != '0':
                    atividades[atividade]['mvas'].add(mva_val)
                if destinacao_cli:
                    atividades[atividade]['destinacoes'].add(destinacao_cli)

            if destinacao_cli:
                destinacoes[destinacao_cli]['cfops'].add(cfop)
                destinacoes[destinacao_cli]['ncms'].add(ncm)
                destinacoes[destinacao_cli]['csts'].add(cst)
                destinacoes[destinacao_cli]['aliquotas'].add(aliquota)
                destinacoes[destinacao_cli]['estados'].add(estado)
                destinacoes[destinacao_cli]['reducoes'].add(reducao)
                if mva_val and mva_val != '0':
                    destinacoes[destinacao_cli]['mvas'].add(mva_val)
                if atividade:
                    destinacoes[destinacao_cli]['atividades'].add(atividade)


        # Converter sets para listas e formatar resposta
        opcoes = {
            'cfops': [{'value': cfop, 'related': {
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0),
                'atividades': sorted(list(data.get('atividades', []))),
                'destinacoes': sorted(list(data.get('destinacoes', []))),
                'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0)
            }} for cfop, data in sorted(cfops.items()) if cfop],

            'ncms': [{'value': ncm, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0),
                'atividades': sorted(list(data.get('atividades', []))),
                'destinacoes': sorted(list(data.get('destinacoes', []))),
                'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0)
            }} for ncm, data in sorted(ncms.items()) if ncm],

            'csts': [{'value': cst, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0),
                'atividades': sorted(list(data.get('atividades', []))),
                'destinacoes': sorted(list(data.get('destinacoes', []))),
                'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0)
            }} for cst, data in sorted(csts.items()) if cst],

            'aliquotas': [{'value': aliquota, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0),
                'atividades': sorted(list(data.get('atividades', []))),
                'destinacoes': sorted(list(data.get('destinacoes', []))),
                'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0)
            }} for aliquota, data in sorted(aliquotas.items(), key=lambda x: float(x[0]) if x[0] else 0) if aliquota],

            'estados': [{'value': estado, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0),
                'atividades': sorted(list(data.get('atividades', []))),
                'destinacoes': sorted(list(data.get('destinacoes', []))),
                'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0)
            }} for estado, data in sorted(estados.items()) if estado],

            'reducoes': [{'value': reducao, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'atividades': sorted(list(data.get('atividades', []))),
                'destinacoes': sorted(list(data.get('destinacoes', []))),
                'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0)
            }} for reducao, data in sorted(reducoes.items(), key=lambda x: float(x[0]) if x[0] else 0) if reducao and reducao != '0'],

            'atividades': [{'value': atv, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0),
                'destinacoes': sorted(list(data['destinacoes'])),
                'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0)
            }} for atv, data in sorted(atividades.items()) if atv],

            'destinacoes': [{'value': dest, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0),
                'atividades': sorted(list(data['atividades'])),
                'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0)
            }} for dest, data in sorted(destinacoes.items()) if dest],

            'mvas': [{'value': mva, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0),
                'atividades': sorted(list(data['atividades'])),
                'destinacoes': sorted(list(data['destinacoes']))
            }} for mva, data in sorted(mvas.items(), key=lambda x: float(x[0]) if x[0] else 0) if mva and mva != '0']
        }

        if aliquotas_st:
            opcoes['aliquotas_st'] = [
                {
                    'value': alq,
                    'related': {
                        'cfops': sorted(list(data['cfops'])),
                        'ncms': sorted(list(data['ncms'])),
                        'csts': sorted(list(data['csts'])),
                        'estados': sorted(list(data['estados'])),
                        'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0),
                        'reducoes_st': sorted(list(data['reducoes_st']), key=lambda x: float(x) if x else 0),
                        'atividades': sorted(list(data.get('atividades', []))),
                        'destinacoes': sorted(list(data.get('destinacoes', []))),
                        'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0),
                    },
                }
                for alq, data in sorted(aliquotas_st.items(), key=lambda x: float(x[0]) if x[0] else 0)
                if alq
            ]

        if reducoes_st:
            opcoes['reducoes_st'] = [
                {
                    'value': red,
                    'related': {
                        'cfops': sorted(list(data['cfops'])),
                        'ncms': sorted(list(data['ncms'])),
                        'csts': sorted(list(data['csts'])),
                        'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                        'aliquotas_st': sorted(list(data['aliquotas_st']), key=lambda x: float(x) if x else 0),
                        'estados': sorted(list(data['estados'])),
                        'atividades': sorted(list(data.get('atividades', []))),
                        'destinacoes': sorted(list(data.get('destinacoes', []))),
                        'mvas': sorted(list(data['mvas']), key=lambda x: float(x) if x else 0),
                    },
                }
                for red, data in sorted(reducoes_st.items(), key=lambda x: float(x[0]) if x[0] else 0)
                if red and red != '0'
            ]


        return jsonify({
            "success": True,
            "opcoes": opcoes
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao obter opções de filtro de cenários: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>', methods=['GET'])
def obter_cenario(tipo_tributo, cenario_id):
    """
    Obtém um cenário específico

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Buscar cenário
        cenario = TIPO_TRIBUTO_MODELS[tipo_tributo].query.get(cenario_id)

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        # Converter para dicionário
        cenario_dict = cenario.to_dict()

        # Adicionar informações do cliente
        if cenario.cliente:
            cenario_dict['cliente'] = cenario.cliente.to_dict()

        # Adicionar informações do produto
        if cenario.produto:
            cenario_dict['produto'] = cenario.produto.to_dict()

        return jsonify({
            'success': True,
            'cenario': cenario_dict
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao obter cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>/status', methods=['PUT'])
def atualizar_status_cenario(tipo_tributo, cenario_id):
    """
    Atualiza o status de um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        novo_status = data.get('status')
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')

        # Validar dados
        if not novo_status:
            return jsonify({
                'success': False,
                'message': 'Status não informado'
            }), 400

        if novo_status not in ['novo', 'producao', 'inconsistente']:
            return jsonify({
                'success': False,
                'message': f'Status inválido: {novo_status}'
            }), 400

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Verificar se o cenário existe
        CenarioModel = cenario_service._get_cenario_model(tipo_tributo)
        cenario_atual = CenarioModel.query.get(cenario_id)

        if not cenario_atual:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        # Se estivermos promovendo para produção, verificar se há sobreposição de datas
        data_inicio_vigencia = None
        if novo_status == 'producao' and cenario_atual.status != 'producao':
            # Se o cenário atual já tem uma data de início de vigência, usá-la
            if cenario_atual.data_inicio_vigencia:
                data_inicio_vigencia = cenario_atual.data_inicio_vigencia
            else:
                # Se não tiver data de início de vigência, buscar a data de emissão da nota fiscal mais recente
                from models import NotaFiscalItem, Tributo

                # Buscar a nota fiscal mais recente para este produto e cliente
                nota_fiscal = NotaFiscalItem.query.filter_by(
                    empresa_id=cenario_atual.empresa_id,
                    cliente_id=cenario_atual.cliente_id,
                    produto_id=cenario_atual.produto_id
                ).order_by(NotaFiscalItem.data_emissao.desc()).first()

                if nota_fiscal:
                    data_inicio_vigencia = nota_fiscal.data_emissao
                else:
                    # Se não encontrar a nota fiscal, buscar no tributo
                    tributo = Tributo.query.filter_by(
                        empresa_id=cenario_atual.empresa_id,
                        cliente_id=cenario_atual.cliente_id,
                        produto_id=cenario_atual.produto_id
                    ).order_by(Tributo.data_emissao.desc()).first()

                    if tributo:
                        data_inicio_vigencia = tributo.data_emissao
                    else:
                        # Se não encontrar de jeito nenhum, usar a data atual
                        from datetime import datetime
                        data_inicio_vigencia = datetime.now().date()

                        # Definir a data de início de vigência no cenário atual para referência futura
                        cenario_atual.data_inicio_vigencia = data_inicio_vigencia
                        db.session.commit()

            # Garantir que a data de início de vigência foi definida
            if not data_inicio_vigencia:
                raise ValueError("Não foi possível determinar a data de início de vigência para o cenário.")


        try:
            # Atualizar status do cenário
            cenario = cenario_service.atualizar_status_cenario(cenario_id, tipo_tributo, novo_status, data_inicio_vigencia)

            if not cenario:
                return jsonify({
                    'success': False,
                    'message': f'Cenário não encontrado: {cenario_id}'
                }), 404

        except ValueError as e:
            # Capturar erros de validação e retornar mensagem amigável
            return jsonify({
                'success': False,
                'message': str(e)
            }), 400

        except Exception as e:
            # Capturar outros erros inesperados
            return jsonify({
                'success': False,
                'message': 'Ocorreu um erro inesperado ao atualizar o status do cenário. Por favor, tente novamente.'
            }), 500

        # Se o status foi atualizado para 'producao', recalcular tributos
        if novo_status == 'producao':
            # Inicializar serviço de cálculo de tributos
            tributo_calculation_service = TributoCalculationService(empresa_id)

            # Recalcular tributos para o produto
            resultado = tributo_calculation_service.calculate_tributos(produto_ids=[cenario.produto_id])

            # Verificar cenários compatíveis
            cenarios_compativeis = cenario_service.verificar_cenarios_compativeis(cenario.cliente_id, cenario.produto_id, tipo_tributo)

            return jsonify({
                'success': True,
                'cenario': cenario.to_dict(),
                'calculo_tributos': resultado,
                'cenarios_compativeis': cenarios_compativeis
            })

        return jsonify({
            'success': True,
            'cenario': cenario.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente.'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>/ativar', methods=['PUT'])
def ativar_cenario(tipo_tributo, cenario_id):
    """
    Ativa um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')
        ncm = data.get('ncm')
        cfop = data.get('cfop')

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Ativar o cenário
        try:
            cenario = cenario_service.ativar_cenario(cenario_id, tipo_tributo)
        except ValueError as e:
            return jsonify({
                'success': False,
                'message': str(e)
            }), 400

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        return jsonify({
            'success': True,
            'cenario': cenario.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao ativar cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>/desativar', methods=['PUT'])
def desativar_cenario(tipo_tributo, cenario_id):
    """
    Desativa um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Desativar o cenário
        cenario = cenario_service.desativar_cenario(cenario_id, tipo_tributo)

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        return jsonify({
            'success': True,
            'cenario': cenario.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao desativar cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>/vigencia', methods=['PUT'])
def atualizar_vigencia_cenario(tipo_tributo, cenario_id):
    """
    Atualiza a vigência de um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')
        data_inicio_vigencia_str = data.get('data_inicio_vigencia')
        data_fim_vigencia_str = data.get('data_fim_vigencia')

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        if not data_inicio_vigencia_str:
            return jsonify({
                'success': False,
                'message': 'Data de início de vigência não informada'
            }), 400

        # Converter datas
        try:
            data_inicio_vigencia = datetime.fromisoformat(data_inicio_vigencia_str)
        except ValueError:
            return jsonify({
                'success': False,
                'message': f'Data de início de vigência inválida: {data_inicio_vigencia_str}'
            }), 400

        data_fim_vigencia = None
        if data_fim_vigencia_str:
            try:
                data_fim_vigencia = datetime.fromisoformat(data_fim_vigencia_str)
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': f'Data de fim de vigência inválida: {data_fim_vigencia_str}'
                }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Atualizar vigência do cenário
        try:
            cenario = cenario_service.atualizar_vigencia_cenario(
                cenario_id,
                tipo_tributo,
                data_inicio_vigencia,
                data_fim_vigencia
            )
        except ValueError as e:
            return jsonify({
                'success': False,
                'message': str(e)
            }), 400

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        return jsonify({
            'success': True,
            'cenario': cenario.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao atualizar vigência do cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/bulk-status', methods=['PUT'])
def atualizar_status_cenarios_em_massa(tipo_tributo):
    """
    Atualiza o status de múltiplos cenários de uma vez

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        cenario_ids = data.get('cenario_ids', [])
        novo_status = data.get('status', 'producao')  # Default para 'producao'
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')
        data_inicio_vigencia_str = data.get('data_inicio_vigencia')
        filters = data.get('filters', {})
        apply_to_all = data.get('apply_to_all', False)

        # Validar dados
        if not apply_to_all:
            if not cenario_ids or not isinstance(cenario_ids, list):
                return jsonify({
                    'success': False,
                    'message': 'Lista de IDs de cenários não informada ou inválida'
                }), 400

        if novo_status not in ['novo', 'producao', 'inconsistente']:
            return jsonify({
                'success': False,
                'message': f'Status inválido: {novo_status}'
            }), 400

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Obter data de início de vigência para status 'producao'
        data_inicio_vigencia = None
        if novo_status == 'producao':
            # Se a data de início de vigência foi fornecida, usá-la
            if data_inicio_vigencia_str:
                try:
                    data_inicio_vigencia = datetime.fromisoformat(data_inicio_vigencia_str)
                except ValueError:
                    return jsonify({
                        'success': False,
                        'message': f'Data de início de vigência inválida: {data_inicio_vigencia_str}'
                    }), 400
            # Se não foi fornecida, a data será buscada no serviço para cada cenário

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Se aplicar a todos os filtrados, buscar IDs dos cenários
        if apply_to_all:
            filters_query = {**filters, 'empresa_id': empresa_id}
            if 'direcao' not in filters_query:
                filters_query['direcao'] = data.get('direcao', 'saida')
            data_loader = OptimizedDataLoaderService()
            result = data_loader.load_cenarios_optimized(tipo_tributo, filters_query, load_all=True)
            if not result.get('success'):
                return jsonify({
                    'success': False,
                    'message': result.get('message', 'Erro ao buscar cenários')
                }), 400
            cenario_ids = [c['id'] for c in result.get('cenarios', [])]

        if not cenario_ids:
            return jsonify({
                'success': False,
                'message': 'Nenhum cenário encontrado para atualização'
            }), 404

        # Atualizar status dos cenários
        updated_count = 0
        errors = []

        for cenario_id in cenario_ids:
            try:
                cenario = cenario_service.atualizar_status_cenario(
                    cenario_id,
                    tipo_tributo,
                    novo_status,
                    data_inicio_vigencia
                )
                if cenario:
                    updated_count += 1
            except Exception as e:
                errors.append(f"Erro ao atualizar cenário {cenario_id}: {str(e)}")

        # Se o status foi atualizado para 'producao', agendar recálculo de tributos
        task_id = None
        if novo_status == 'producao' and updated_count > 0:
            # Obter produtos únicos dos cenários atualizados
            produto_ids = []
            for cenario_id in cenario_ids:
                cenario = TIPO_TRIBUTO_MODELS[tipo_tributo].query.get(cenario_id)
                if cenario and cenario.produto_id not in produto_ids:
                    produto_ids.append(cenario.produto_id)

            if produto_ids:
                from services.queue_manager import get_queue_manager, Task
                import uuid

                queue_manager = get_queue_manager()
                task_id = str(uuid.uuid4())

                def process_calc(data):
                    service = TributoCalculationService(data['empresa_id'])
                    return service.calculate_tributos(produto_ids=data['produto_ids'])

                task = Task(
                    id=task_id,
                    type='calc_tributos',
                    user_id=0,
                    empresa_id=empresa_id,
                    data={'empresa_id': empresa_id, 'produto_ids': produto_ids},
                    priority=1,
                    callback=process_calc,
                )

                if not queue_manager.submit_calc_task(task):
                    errors.append('Fila cheia, recálculo não agendado')

        response = {
            'success': True,
            'message': f'{updated_count} cenários atualizados com sucesso',
            'updated_count': updated_count,
            'errors': errors,
        }
        if task_id:
            response['task_id'] = task_id
        status_code = 202 if task_id else 200
        return jsonify(response), status_code
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao atualizar status dos cenários em massa: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/bulk-edit', methods=['PUT'])
def editar_cenarios_em_massa(tipo_tributo):
    """Edita campos de múltiplos cenários de uma vez"""
    try:
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        data = request.json
        cenario_ids = data.get('cenario_ids', [])
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')
        ncm = data.get('ncm')
        cfop = data.get('cfop')
        cst = data.get('cst')
        aliquota = data.get('aliquota')
        filters = data.get('filters', {})
        apply_to_all = data.get('apply_to_all', False)

        if not apply_to_all:
            if not cenario_ids or not isinstance(cenario_ids, list):
                return jsonify({
                    'success': False,
                    'message': 'Lista de IDs de cenários não informada ou inválida'
                }), 400
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400
        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        if apply_to_all:
            filters_query = {**filters, 'empresa_id': empresa_id}
            if 'direcao' not in filters_query:
                filters_query['direcao'] = data.get('direcao', 'saida')
            data_loader = OptimizedDataLoaderService()
            result = data_loader.load_cenarios_optimized(tipo_tributo, filters_query, load_all=True)
            if not result.get('success'):
                return jsonify({
                    'success': False,
                    'message': result.get('message', 'Erro ao buscar cenários')
                }), 400
            cenario_ids = [c['id'] for c in result.get('cenarios', [])]
            if not cenario_ids:
                return jsonify({
                    'success': False,
                    'message': 'Nenhum cenário encontrado para edição'
                }), 404

        CenarioModel = TIPO_TRIBUTO_MODELS[tipo_tributo]
        updated_count = 0

        from models import Tributo
        for cenario_id in cenario_ids:
            cenario = CenarioModel.query.get(cenario_id)
            if not cenario:
                continue

            campos_criticos = False
            if ncm is not None and cenario.ncm != ncm:
                cenario.ncm = ncm
                campos_criticos = True
            if cfop is not None and cenario.cfop != cfop:
                cenario.cfop = cfop
                campos_criticos = True
            if hasattr(cenario, 'cst') and cst is not None and cenario.cst != cst:
                cenario.cst = cst
                campos_criticos = True
            if hasattr(cenario, 'aliquota') and aliquota is not None and cenario.aliquota != aliquota:
                cenario.aliquota = aliquota
                campos_criticos = True

            cenario.data_atualizacao = datetime.now()
            db.session.commit()

            if campos_criticos and cenario.empresa_id and cenario.produto_id:
                try:
                    calculation_service = TributoCalculationService(cenario.empresa_id)
                    tributos = Tributo.query.filter_by(
                        empresa_id=cenario.empresa_id,
                        produto_id=cenario.produto_id,
                        cliente_id=cenario.cliente_id
                    ).all()
                    with transactional_session():
                        for tributo in tributos:
                            calculation_service._calculate_tributo_values(tributo, tipo_tributo)
                except Exception:
                    db.session.rollback()

            updated_count += 1

        return jsonify({
            'success': True,
            'updated_count': updated_count
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Erro ao editar cenários em massa: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/count', methods=['GET'])
@jwt_required()
def contar_cenarios(tipo_tributo):
    """
    Conta os cenários por status

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter parâmetros da requisição
        empresa_id = request.args.get('empresa_id')
        direcao = request.args.get('direcao')
        year = request.args.get('year')
        month = request.args.get('month')

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not direcao:
            return jsonify({
                'success': False,
                'message': 'Direção não informada'
            }), 400

        # Obter o modelo correspondente
        CenarioModel = TIPO_TRIBUTO_MODELS[tipo_tributo]

        # Construir a query base
        query = CenarioModel.query.filter_by(empresa_id=empresa_id)

        # Adicionar filtro de direção
        query = query.filter_by(direcao=direcao)

        # Adicionar filtro de tipo_operacao
        tipo_operacao = '0' if direcao == 'entrada' else '1'
        query = query.filter_by(tipo_operacao=tipo_operacao)

        # Adicionar filtros de ano e mês, se informados (usando data_criacao)
        if year:
            query = query.filter(extract('year', CenarioModel.data_criacao) == year)

        if month:
            query = query.filter(extract('month', CenarioModel.data_criacao) == month)

        # Contar cenários por status
        counts = {}
        for status in ['novo', 'producao', 'inconsistente']:
            if status == 'inconsistente':
                # Contar tanto 'inconsistente' quanto os que começam com 'incons_'
                count = query.filter(
                    db.or_(
                        CenarioModel.status == 'inconsistente',
                        CenarioModel.status.like('incons_%')
                    )
                ).count()
            else:
                count = query.filter_by(status=status).count()
            counts[status] = count

        return jsonify({
            'success': True,
            'counts': counts
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao contar cenários: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>', methods=['DELETE'])
def excluir_cenario(tipo_tributo, cenario_id):
    """
    Exclui um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.args
        empresa_id = data.get('empresa_id', type=int)
        escritorio_id = data.get('escritorio_id', type=int)

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Excluir o cenário
        resultado = cenario_service.excluir_cenario(cenario_id, tipo_tributo)

        if not resultado:
            return jsonify({
                'success': False,
                'message': 'Não foi possível excluir o cenário. Verifique se ele existe e não está em produção ativo.'
            }), 400

        return jsonify({
            'success': True,
            'message': 'Cenário excluído com sucesso'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao excluir cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/reverificar-status', methods=['POST'])
def reverificar_status_cenarios(tipo_tributo):
    """
    Reverifica o status de cenários

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')
        produto_id = data.get('produto_id')  # Opcional

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Reverificar status dos cenários
        try:
            # Se produto_id for informado, reverificar apenas para este produto
            if produto_id:
                cenarios_atualizados = cenario_service.reverificar_status_cenarios_produto(
                    tipo_tributo,
                    produto_id
                )
                mensagem = f'Status de {cenarios_atualizados} cenários reverificados para o produto {produto_id}'
            else:
                cenarios_atualizados = cenario_service.reverificar_status_cenarios(tipo_tributo)
                mensagem = f'Status de {cenarios_atualizados} cenários reverificados'
        except ValueError as e:
            return jsonify({
                'success': False,
                'message': str(e)
            }), 400

        return jsonify({
            'success': True,
            'message': mensagem,
            'cenarios_atualizados': cenarios_atualizados
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao reverificar status dos cenários: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>', methods=['PUT'])
def atualizar_cenario(tipo_tributo, cenario_id):
    """
    Atualiza os dados de um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Buscar cenário
        cenario = TIPO_TRIBUTO_MODELS[tipo_tributo].query.get(cenario_id)

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404
            
        # Atualizar NCM e CFOP se fornecidos
        if 'ncm' in data:
            cenario.ncm = data['ncm']
        if 'cfop' in data:
            cenario.cfop = data['cfop']
        # Atualizar campos do cenário com base no tipo de tributo
        if tipo_tributo == 'icms':
            # Atualizar campos específicos do ICMS
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'origem' in data:
                cenario.origem = data.get('origem')
            if 'mod_bc' in data:
                cenario.mod_bc = data.get('mod_bc')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'p_dif' in data:
                cenario.p_dif = data.get('p_dif')
        elif tipo_tributo == 'icms_st':
            # Atualizar campos específicos do ICMS-ST
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'origem' in data:
                cenario.origem = data.get('origem')
            if 'mod_bc' in data:
                cenario.mod_bc = data.get('mod_bc')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'icms_st_mod_bc' in data:
                cenario.icms_st_mod_bc = data.get('icms_st_mod_bc')
            if 'icms_st_p_mva' in data:
                cenario.icms_st_p_mva = data.get('icms_st_p_mva')
            if 'icms_st_aliquota' in data:
                cenario.icms_st_aliquota = data.get('icms_st_aliquota')
            if 'icms_st_p_red_bc' in data:
                cenario.icms_st_p_red_bc = data.get('icms_st_p_red_bc')
        elif tipo_tributo == 'ipi':
            # Atualizar campos específicos do IPI
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'codigo_enquadramento' in data:
                cenario.codigo_enquadramento = data.get('codigo_enquadramento')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'ex' in data:
                cenario.ex = data.get('ex')
        elif tipo_tributo == 'pis':
            # Atualizar campos específicos do PIS
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
        elif tipo_tributo == 'cofins':
            # Atualizar campos específicos do COFINS
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
        elif tipo_tributo == 'difal':
            # Atualizar campos específicos do DIFAL
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'origem' in data:
                cenario.origem = data.get('origem')
            if 'mod_bc' in data:
                cenario.mod_bc = data.get('mod_bc')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'p_fcp_uf_dest' in data:
                cenario.p_fcp_uf_dest = data.get('p_fcp_uf_dest')
            if 'p_icms_uf_dest' in data:
                cenario.p_icms_uf_dest = data.get('p_icms_uf_dest')
            if 'p_icms_inter' in data:
                cenario.p_icms_inter = data.get('p_icms_inter')
            if 'p_icms_inter_part' in data:
                cenario.p_icms_inter_part = data.get('p_icms_inter_part')

        # Atualizar data de atualização
        cenario.data_atualizacao = datetime.now()

        # Verificar se campos críticos foram alterados
        campos_criticos = False
        if 'ncm' in data or 'cfop' in data:
            campos_criticos = True
        if tipo_tributo == 'icms' and any(field in data for field in ['cst', 'aliquota', 'p_red_bc', 'mod_bc']):
            campos_criticos = True
        elif tipo_tributo == 'icms_st' and any(field in data for field in ['cst', 'aliquota', 'p_red_bc', 'mod_bc', 'icms_st_aliquota', 'icms_st_p_mva']):
            campos_criticos = True
        elif tipo_tributo in ['ipi', 'pis', 'cofins'] and any(field in data for field in ['cst', 'aliquota']):
            campos_criticos = True
        elif tipo_tributo == 'difal' and any(field in data for field in ['cst', 'aliquota', 'p_fcp_uf_dest', 'p_icms_uf_dest']):
            campos_criticos = True

        # Salvar alterações
        db.session.commit()

        # Se campos críticos foram alterados, recalcular tributos afetados
        if campos_criticos and cenario.empresa_id and cenario.produto_id:
            try:
                from services.tributo_calculation_service import TributoCalculationService
                calculation_service = TributoCalculationService(cenario.empresa_id)

                # Buscar tributos que usam este produto e cliente
                from models import Tributo
                tributos = Tributo.query.filter_by(
                    empresa_id=cenario.empresa_id,
                    produto_id=cenario.produto_id,
                    cliente_id=cenario.cliente_id
                ).all()

                # Recalcular apenas o tipo de tributo específico que foi alterado
                with transactional_session():
                    for tributo in tributos:
                        calculation_service._calculate_tributo_values(tributo, tipo_tributo)

            except Exception as e:
                # Fazer rollback em caso de erro
                db.session.rollback()

        # Converter para dicionário
        cenario_dict = cenario.to_dict()

        # Adicionar informações do cliente
        if cenario.cliente:
            cenario_dict['cliente'] = cenario.cliente.to_dict()

        # Adicionar informações do produto
        if cenario.produto:
            cenario_dict['produto'] = cenario.produto.to_dict()

        # Preparar resposta
        response_data = {
            'success': True,
            'message': 'Cenário atualizado com sucesso',
            'campos_criticos_alterados': campos_criticos,
            'cenario': cenario_dict
        }

        # Adicionar informações sobre recálculo se foi realizado
        if campos_criticos and cenario.empresa_id and cenario.produto_id:
            response_data['recalculo_realizado'] = True
            response_data['tipo_tributo_recalculado'] = tipo_tributo

        # Auto-execução da validação IPI se for cenário IPI de saída
        if tipo_tributo == 'ipi' and cenario.direcao == 'saida':
            try:
                validation_service = IPIValidationService()
                # Executar validação apenas para este cenário
                filtros = {'cenario_id': cenario.id}
                validation_result = validation_service.validar_cenarios_empresa(
                    cenario.empresa_id,
                    filtros
                )
                response_data['ipi_validation_executed'] = True
                response_data['ipi_validation_result'] = validation_result
            except Exception as e:
                logger.warning(f"Erro na auto-validação IPI: {str(e)}")
                response_data['ipi_validation_executed'] = False
                response_data['ipi_validation_error'] = str(e)

        return jsonify(response_data)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao atualizar cenário: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/ipi/validate', methods=['POST'])
@jwt_required()
def validar_cenarios_ipi():
    """
    Valida cenários IPI de saída baseado na tabela TIPI

    Body:
        empresa_id (int): ID da empresa
        filtros (dict, optional): Filtros adicionais para os cenários

    Returns:
        JSON: Resultado da validação com sugestões de correção
    """
    try:
        data = request.get_json()

        if not data or 'empresa_id' not in data:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400

        empresa_id = data['empresa_id']
        filtros = data.get('filtros', {})

        # Validar se o usuário tem acesso à empresa
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Verificar permissão de acesso à empresa (simplificado)
        # Em produção, implementar verificação completa de permissões

        # Executar validação
        validation_service = IPIValidationService()
        resultado = validation_service.validar_cenarios_empresa(empresa_id, filtros)

        return jsonify(resultado)

    except Exception as e:
        logger.error(f"Erro na validação IPI: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/ipi/apply-suggestion', methods=['POST'])
@jwt_required()
def aplicar_sugestao_ipi():
    """
    Aplica uma sugestão de correção a um cenário IPI

    Body:
        cenario_id (int): ID do cenário
        sugestao (dict): Dados da sugestão a ser aplicada

    Returns:
        JSON: Resultado da aplicação
    """
    try:
        data = request.get_json()

        if not data or 'cenario_id' not in data or 'sugestao' not in data:
            return jsonify({
                'success': False,
                'message': 'cenario_id e sugestao são obrigatórios'
            }), 400

        cenario_id = data['cenario_id']
        sugestao = data['sugestao']

        # Validar se o usuário tem acesso ao cenário
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Aplicar sugestão
        validation_service = IPIValidationService()
        resultado = validation_service.aplicar_sugestao(cenario_id, sugestao)

        return jsonify(resultado)

    except Exception as e:
        logger.error(f"Erro ao aplicar sugestão IPI: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/ipi/validate-cfop-cst', methods=['POST'])
@jwt_required()
def validar_cfop_cst_ipi():
    """
    Valida combinações CFOP x CST x Alíquota para cenários IPI

    Body:
        empresa_id (int): ID da empresa
        filtros (dict, optional): Filtros adicionais para os cenários

    Returns:
        JSON: Resultado da validação com sugestões de correção
    """
    try:
        data = request.get_json()

        if not data or 'empresa_id' not in data:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400

        empresa_id = data['empresa_id']
        filtros = data.get('filtros', {})

        # Validar se o usuário tem acesso à empresa
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Verificar permissão de acesso à empresa (simplificado)
        # Em produção, implementar verificação completa de permissões

        # Executar validação CFOP x CST
        validation_service = CFOPCSTValidationService()
        resultado = validation_service.validar_cenarios_empresa(empresa_id, filtros)

        return jsonify(resultado)

    except Exception as e:
        logger.error(f"Erro na validação CFOP x CST: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/ipi/apply-cfop-cst-suggestion', methods=['POST'])
@jwt_required()
def aplicar_sugestao_cfop_cst():
    """
    Aplica uma sugestão de correção CFOP x CST a um cenário IPI

    Body:
        cenario_id (int): ID do cenário
        sugestao (dict): Dados da sugestão a ser aplicada

    Returns:
        JSON: Resultado da aplicação
    """
    try:
        data = request.get_json()

        if not data or 'cenario_id' not in data or 'sugestao' not in data:
            return jsonify({
                'success': False,
                'message': 'cenario_id e sugestao são obrigatórios'
            }), 400

        cenario_id = data['cenario_id']
        sugestao = data['sugestao']

        # Validar se o usuário tem acesso ao cenário
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Aplicar sugestão CFOP x CST
        validation_service = CFOPCSTValidationService()
        resultado = validation_service.aplicar_sugestao(cenario_id, sugestao, usuario.nome)

        return jsonify(resultado)

    except Exception as e:
        logger.error(f"Erro ao aplicar sugestão CFOP x CST: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/ipi/cfop-cst-rules', methods=['GET'])
@jwt_required()
def obter_regras_cfop_cst():
    """
    Obtém as regras CFOP x CST configuradas

    Returns:
        JSON: Regras CFOP x CST configuradas
    """
    try:
        validation_service = CFOPCSTValidationService()
        regras = validation_service.obter_regras_configuradas()

        return jsonify({
            'success': True,
            'regras': regras
        })

    except Exception as e:
        logger.error(f"Erro ao obter regras CFOP x CST: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/ipi/validation-history/<int:empresa_id>', methods=['GET'])
@jwt_required()
def historico_validacoes_ipi(empresa_id):
    """
    Retorna o histórico de validações IPI de uma empresa

    Args:
        empresa_id (int): ID da empresa

    Query Parameters:
        status (str, optional): Filtrar por status (pendente, aplicada, rejeitada)
        limit (int, optional): Limite de registros (padrão: 100)

    Returns:
        JSON: Lista de validações e estatísticas
    """
    try:
        # Validar acesso do usuário à empresa
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Obter parâmetros da query
        status = request.args.get('status')
        limit = request.args.get('limit', 100, type=int)

        # Buscar validações
        from models.ipi_validation_result import IPIValidationResult

        validacoes = IPIValidationResult.buscar_por_empresa(empresa_id, status, limit)
        estatisticas = IPIValidationResult.estatisticas_empresa(empresa_id)

        return jsonify({
            'success': True,
            'validacoes': [v.to_dict() for v in validacoes],
            'estatisticas': estatisticas,
            'total_retornado': len(validacoes)
        })

    except Exception as e:
        logger.error(f"Erro ao buscar histórico de validações IPI: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500
 
#  ==================== ENDPOINTS PIS/COFINS VALIDATION ====================

@cenario_bp.route('/api/cenarios/pis-cofins/validate', methods=['POST'])
@jwt_required()
def validar_cenarios_pis_cofins():
    """
    Valida cenários PIS e COFINS baseado na API externa
    """
    try:
        from services.pis_cofins_validation_service import PisCofinValidationService
        
        data = request.get_json()
        empresa_id = data.get('empresa_id')
        cfop_filtro = data.get('cfop')
        tributo_tipo = data.get('tributo_tipo')
        status_filtro = data.get('status_filtro')
        
        if not empresa_id:
            return jsonify({'erro': 'empresa_id é obrigatório'}), 400
        
        service = PisCofinValidationService()
        resultado = service.validar_cenarios_pis_cofins(
            empresa_id,
            cfop_filtro,
            tributo_tipo,
            status_filtro,
        )
        
        if 'erro' in resultado:
            return jsonify(resultado), 400
        
        return jsonify(resultado)
        
    except Exception as e:
        logger.error(f"Erro na validação PIS/COFINS: {str(e)}")
        return jsonify({'erro': f'Erro interno: {str(e)}'}), 500

@cenario_bp.route('/api/cenarios/pis-cofins/apply-suggestion', methods=['POST'])
@jwt_required()
def aplicar_sugestao_pis_cofins():
    """
    Aplica sugestão de correção em cenário PIS ou COFINS
    """
    try:
        from services.pis_cofins_validation_service import PisCofinValidationService
        
        data = request.get_json()
        cenario_id = data.get('cenario_id')
        tributo_tipo = data.get('tributo_tipo')  # 'PIS' ou 'COFINS'
        sugestoes = data.get('sugestoes', [])
        
        if not all([cenario_id, tributo_tipo, sugestoes]):
            return jsonify({'erro': 'cenario_id, tributo_tipo e sugestoes são obrigatórios'}), 400
        
        if tributo_tipo not in ['PIS', 'COFINS']:
            return jsonify({'erro': 'tributo_tipo deve ser PIS ou COFINS'}), 400
        
        # Obter usuário atual
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({'erro': 'Usuário não encontrado'}), 401

        usuario_nome = usuario.nome
        
        service = PisCofinValidationService()
        resultado = service.aplicar_sugestao(cenario_id, tributo_tipo, sugestoes, usuario_nome)
        
        if 'erro' in resultado:
            return jsonify(resultado), 400
        
        return jsonify(resultado)
        
    except Exception as e:
        logger.error(f"Erro ao aplicar sugestão PIS/COFINS: {str(e)}")
        return jsonify({'erro': f'Erro interno: {str(e)}'}), 500

@cenario_bp.route('/api/cenarios/pis-cofins/validation-history/<int:empresa_id>', methods=['GET'])
@jwt_required()
def obter_historico_validacao_pis_cofins(empresa_id):
    """
    Obtém histórico de validações PIS/COFINS de uma empresa
    """
    try:
        from services.pis_cofins_validation_service import PisCofinValidationService
        
        service = PisCofinValidationService()
        historico = service.obter_historico_validacoes(empresa_id)
        
        return jsonify({
            'empresa_id': empresa_id,
            'historico': historico
        })
        
    except Exception as e:
        logger.error(f"Erro ao obter histórico PIS/COFINS: {str(e)}")
        return jsonify({'erro': f'Erro interno: {str(e)}'}), 500


@cenario_bp.route('/api/cenarios/pis-cofins/cache-stats', methods=['GET'])
@jwt_required()
def obter_estatisticas_cache_pis_cofins():
    """
    Obtém estatísticas do cache PIS/COFINS
    """
    try:
        from models.pis_cofins_cache import PisCofinsCacheModel
        from datetime import datetime, timedelta
        
        # Estatísticas gerais
        total_registros = PisCofinsCacheModel.query.count()
        
        # Registros dos últimos 30 dias
        data_limite = datetime.now() - timedelta(days=30)
        registros_recentes = PisCofinsCacheModel.query.filter(
            PisCofinsCacheModel.data_consulta > data_limite
        ).count()
        
        # Registros por regime tributário
        from sqlalchemy import func
        por_regime = db.session.query(
            PisCofinsCacheModel.regime_tributario_origem,
            func.count(PisCofinsCacheModel.id)
        ).group_by(PisCofinsCacheModel.regime_tributario_origem).all()
        
        # Registros por atividade
        por_atividade = db.session.query(
            PisCofinsCacheModel.atividade_origem,
            func.count(PisCofinsCacheModel.id)
        ).group_by(PisCofinsCacheModel.atividade_origem).all()
        
        return jsonify({
            'total_registros': total_registros,
            'registros_recentes': registros_recentes,
            'por_regime': [{'regime': r[0], 'count': r[1]} for r in por_regime],
            'por_atividade': [{'atividade': a[0], 'count': a[1]} for a in por_atividade]
        })
        
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas do cache: {str(e)}")
        return jsonify({'erro': f'Erro interno: {str(e)}'}), 500

@cenario_bp.route('/api/cenarios/pis-cofins/refresh-cache', methods=['POST'])
@jwt_required()
def atualizar_cache_pis_cofins():
    """
    Força atualização do cache para NCMs específicos
    """
    try:
        from services.pis_cofins_validation_service import PisCofinValidationService
        
        data = request.get_json()
        ncms = data.get('ncms', [])
        regime_tributario = data.get('regime_tributario')
        atividade = data.get('atividade')
        
        if not all([ncms, regime_tributario, atividade]):
            return jsonify({'erro': 'ncms, regime_tributario e atividade são obrigatórios'}), 400
        
        service = PisCofinValidationService()
        resultados = []
        
        for ncm in ncms:
            try:
                dados = service.obter_dados_pis_cofins(ncm, regime_tributario, atividade, forcar_api=True)
                resultados.append({
                    'ncm': ncm,
                    'sucesso': True,
                    'registros': len(dados)
                })
            except Exception as e:
                resultados.append({
                    'ncm': ncm,
                    'sucesso': False,
                    'erro': str(e)
                })
        
        return jsonify({
            'resultados': resultados,
            'total_processados': len(ncms)
        })
        
    except Exception as e:
        logger.error(f"Erro ao atualizar cache: {str(e)}")
        return jsonify({'erro': f'Erro interno: {str(e)}'}), 500
        
# =================== ENDPOINTS ICMS-ST CFOP x CST VALIDATION ====================

@cenario_bp.route('/api/cenarios/icms-st/validate-cfop-cst', methods=['POST'])
@jwt_required()
def validar_cfop_cst_icms_st():
    """
    Valida combinações CFOP x CST x Alíquota para cenários ICMS-ST

    Body:
        empresa_id (int): ID da empresa
        filtros (dict, optional): Filtros adicionais para os cenários

    Returns:
        JSON: Resultado da validação com sugestões de correção
    """
    try:
        data = request.get_json()

        if not data or 'empresa_id' not in data:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400

        empresa_id = data['empresa_id']
        filtros = data.get('filtros', {})

        # Validar se o usuário tem acesso à empresa
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Verificar permissão de acesso à empresa (simplificado)
        # Em produção, implementar verificação completa de permissões

        # Executar validação CFOP x CST para ICMS-ST
        validation_service = ICMSSTCFOPCSTValidationService()
        resultado = validation_service.validar_cenarios_empresa(empresa_id, filtros)

        return jsonify(resultado)

    except Exception as e:
        logger.error(f"Erro na validação CFOP x CST ICMS-ST: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/icms-st/apply-cfop-cst-suggestion', methods=['POST'])
@jwt_required()
def aplicar_sugestao_cfop_cst_icms_st():
    """
    Aplica uma sugestão de correção CFOP x CST a um cenário ICMS-ST

    Body:
        cenario_id (int): ID do cenário
        sugestao (dict): Dados da sugestão a ser aplicada

    Returns:
        JSON: Resultado da aplicação
    """
    try:
        data = request.get_json()

        if not data or 'cenario_id' not in data or 'sugestao' not in data:
            return jsonify({
                'success': False,
                'message': 'cenario_id e sugestao são obrigatórios'
            }), 400

        cenario_id = data['cenario_id']
        sugestao = data['sugestao']

        # Validar se o usuário tem acesso ao cenário
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Aplicar sugestão CFOP x CST para ICMS-ST
        validation_service = ICMSSTCFOPCSTValidationService()
        resultado = validation_service.aplicar_sugestao(cenario_id, sugestao, usuario.nome)

        return jsonify(resultado)

    except Exception as e:
        logger.error(f"Erro ao aplicar sugestão CFOP x CST ICMS-ST: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/icms-st/cfop-cst-rules', methods=['GET'])
@jwt_required()
def obter_regras_cfop_cst_icms_st():
    """
    Obtém as regras CFOP x CST configuradas para ICMS-ST

    Returns:
        JSON: Regras CFOP x CST configuradas
    """
    try:
        validation_service = ICMSSTCFOPCSTValidationService()
        regras = validation_service.obter_regras_configuradas()

        return jsonify({
            'success': True,
            'regras': regras
        })

    except Exception as e:
        logger.error(f"Erro ao obter regras CFOP x CST ICMS-ST: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


# =================== ENDPOINTS ICMS-ST VALIDATION COMPLETA ====================

@cenario_bp.route('/api/cenarios/icms-st/validate', methods=['POST'])
@jwt_required()
def validar_icms_st():
    """
    Valida cenários ICMS-ST completos usando API externa

    Body:
        empresa_id (int): ID da empresa
        cfop_filtro (str, optional): CFOP específico para filtrar

    Returns:
        JSON: Resultado da validação com sugestões de correção
    """
    try:
        data = request.get_json()

        if not data or 'empresa_id' not in data:
            return jsonify({
                'success': False,
                'message': 'empresa_id é obrigatório'
            }), 400

        empresa_id = data['empresa_id']
        cfop_filtro = data.get('cfop_filtro')
        status_filtro = data.get('status_filtro', 'novo')  # Padrão: 'novo'

        # Validar se o usuário tem acesso à empresa
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Executar validação ICMS-ST completa
        validation_service = ICMSSTValidationService()
        resultado = validation_service.validar_cenarios_icms_st(empresa_id, cfop_filtro, status_filtro)

        if 'erro' in resultado:
            return jsonify({
                'success': False,
                'message': resultado['erro']
            }), 400

        return jsonify({
            'success': True,
            'empresa_id': resultado['empresa_id'],
            'total_sugestoes': resultado['total_sugestoes'],
            'sugestoes': resultado['sugestoes'],
            'message': f'Validação ICMS-ST concluída. {resultado["total_sugestoes"]} problemas encontrados.'
        })

    except Exception as e:
        logger.error(f"Erro na validação ICMS-ST: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/icms-st/apply-suggestion', methods=['POST'])
@jwt_required()
def aplicar_sugestao_icms_st():
    """
    Aplica uma sugestão de correção a um cenário ICMS-ST

    Body:
        cenario_id (int): ID do cenário
        sugestoes (list): Lista de sugestões a serem aplicadas

    Returns:
        JSON: Resultado da aplicação
    """
    try:
        data = request.get_json()

        if not data or 'cenario_id' not in data or 'sugestoes' not in data:
            return jsonify({
                'success': False,
                'message': 'cenario_id e sugestoes são obrigatórios'
            }), 400

        cenario_id = data['cenario_id']
        sugestoes = data['sugestoes']

        # Validar se o usuário tem acesso ao cenário
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Aplicar sugestão ICMS-ST
        validation_service = ICMSSTValidationService()
        resultado = validation_service.aplicar_sugestao(cenario_id, sugestoes, usuario.nome)

        if 'erro' in resultado:
            return jsonify({
                'success': False,
                'message': resultado['erro']
            }), 400

        return jsonify({
            'success': True,
            'message': 'Sugestão ICMS-ST aplicada com sucesso',
            'cenario_id': resultado['cenario_id']
        })

    except Exception as e:
        logger.error(f"Erro ao aplicar sugestão ICMS-ST: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


@cenario_bp.route('/api/cenarios/icms-st/historico/<int:empresa_id>', methods=['GET'])
@jwt_required()
def obter_historico_icms_st(empresa_id):
    """
    Obtém histórico de validações ICMS-ST de uma empresa

    Args:
        empresa_id (int): ID da empresa

    Returns:
        JSON: Histórico de validações
    """
    try:
        # Validar se o usuário tem acesso à empresa
        user_id = get_jwt_identity()
        usuario = Usuario.query.get(user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 401

        # Obter histórico
        validation_service = ICMSSTValidationService()
        historico = validation_service.obter_historico_validacoes(empresa_id)

        return jsonify({
            'success': True,
            'empresa_id': empresa_id,
            'historico': historico
        })

    except Exception as e:
        logger.error(f"Erro ao obter histórico ICMS-ST: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500
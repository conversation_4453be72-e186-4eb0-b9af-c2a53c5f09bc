import axios from 'axios'
import { useAuthStore } from "@/store/authStore"

// Configuração base do axios
const api = axios.create({
  baseURL: '/fiscal/api',
  withCredentials: true, // Importante para enviar cookies
})

// Interceptor para adicionar token nas requisições
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bear<PERSON> ${token}`
  }
  return config
})

// Interceptor para tratar erros de autenticação e tentar renovar o token
let isRefreshing = false
let refreshPromise: Promise<string> | null = null

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config || {}

    // Se for 401 e não for uma tentativa de login, tenta renovar o token usando o cookie
    if (error.response?.status === 401 && !originalRequest._retry && !originalRequest.url?.includes('/portal-login')) {
      originalRequest._retry = true

      try {
        if (!isRefreshing) {
          isRefreshing = true
          refreshPromise = axios
            .get('/fiscal/api/portal-login', { withCredentials: true })
            .then((res) => {
              const newToken = res.data.access_token
              const user = {
                id: res.data.usuario_id,
                nome: res.data.nome,
                email: '',
                tipo_usuario: res.data.tipo_usuario,
                escritorio_id: res.data.escritorio_id,
                is_admin: res.data.is_admin,
              }

              localStorage.setItem('token', newToken)
              localStorage.setItem('currentUser', JSON.stringify(user))

              // Atualiza estado global de autenticação
              useAuthStore.getState().login(newToken, user)

              return newToken
            })
            .finally(() => {
              isRefreshing = false
            })
        }

        const newToken = await refreshPromise!
        originalRequest.headers = originalRequest.headers || {}
        originalRequest.headers['Authorization'] = `Bearer ${newToken}`
        return api(originalRequest)
      } catch (refreshError) {
        // Falha ao renovar - limpa sessão e redireciona para login
        useAuthStore.getState().logout()
        if (
          !window.location.pathname.includes('/fiscal/login') &&
          !window.location.pathname.includes('/fiscal/test-auth')
        ) {
          window.location.href = '/fiscal/login'
        }
        return Promise.reject(refreshError)      }
    }
    return Promise.reject(error)
  }
)

export interface User {
  id: number
  nome: string
  email?: string
  tipo_usuario: string
  escritorio_id?: number
  is_admin?: boolean
}

export interface LoginResponse {
  access_token: string
  usuario_id: number
  nome: string
  escritorio_id?: number
  tipo_usuario: string
  is_admin: boolean
}

export const authService = {
  /**
   * Valida token do portal via cookie
   */
  async validatePortalToken(): Promise<LoginResponse> {
    const response = await api.get('/portal-login')
    return response.data
  },

  /**
   * Verifica se o usuário atual está autenticado
   */
  async getCurrentUser(): Promise<User> {
    const response = await api.get('/me')
    return response.data
  },

  /**
   * Debug: decodifica token do portal
   */
  async debugPortalToken(token: string) {
    const response = await api.post('/debug-portal-token', { token })
    return response.data
  },

  /**
   * Logout (limpa sessão)
   */
  logout() {
    localStorage.removeItem('token')
    localStorage.removeItem('currentUser')
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'
  },
}

export default api
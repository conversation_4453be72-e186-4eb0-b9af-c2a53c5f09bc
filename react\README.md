# Auditoria Fiscal - Frontend React

Sistema de auditoria fiscal inteligente desenvolvido em React com TypeScript.

## 🚀 Tecnologias

- **React 18** - Framework principal
- **TypeScript** - Type safety
- **Vite** - Build tool rápido
- **Tailwind CSS** - Styling
- **Zustand** - Gerenciamento de estado
- **React Query** - Cache e sincronização de dados
- **React Router** - Roteamento
- **Vitest** - Testes

## 📦 Instalação

```bash
# Instalar dependências
npm install

# Executar em desenvolvimento
npm run dev

# Build para produção
npm run build

# Executar testes
npm run test

# Executar linting
npm run lint
```

## 🏗️ Estrutura do Projeto

```
src/
├── components/          # Componentes reutilizáveis
│   ├── layout/         # Layout components (Header, Sidebar)
│   └── ui/             # UI components básicos
├── pages/              # Páginas principais
├── hooks/              # Custom hooks
├── services/           # API calls
├── store/              # Estado global (Zustand)
├── types/              # TypeScript types
├── utils/              # Utilitários
└── styles/             # Estilos globais
```

## 🔧 Desenvolvimento

### Comandos Disponíveis

- `npm run dev` - Servidor de desenvolvimento
- `npm run build` - Build para produção
- `npm run preview` - Preview do build
- `npm run test` - Executar testes
- `npm run test:ui` - Interface de testes
- `npm run test:coverage` - Cobertura de testes
- `npm run lint` - Linting do código

### Configuração do Proxy

O Vite está configurado para fazer proxy das requisições da API para o Flask:

- `/fiscal/api/*` → `http://localhost:5000`
- `/fiscal/socket.io/*` → `http://localhost:5000` (WebSocket)

### Rotas da Aplicação

Todas as rotas da aplicação estão prefixadas com `/fiscal`:

- `/fiscal/dashboard` - Dashboard principal
- `/fiscal/login` - Página de login
- `/fiscal/produtos` - Gestão de produtos
- `/fiscal/clientes` - Gestão de clientes
- `/fiscal/empresas` - Gestão de empresas
- etc.

### Padrões de Código

- **ESLint + Prettier** para formatação
- **TypeScript** para type safety
- **Tailwind CSS** para styling
- **Conventional Commits** para mensagens de commit

## 📱 Responsividade

O sistema é totalmente responsivo e funciona em:

- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🎨 Design System

### Cores

- **Primary**: Azul moderno (#0ea5e9)
- **Gray**: Escala de cinzas para textos e backgrounds
- **Success**: Verde para estados positivos
- **Warning**: Amarelo para alertas
- **Danger**: Vermelho para erros

### Componentes

- **Buttons**: Variações primary, secondary com tamanhos sm, md, lg
- **Cards**: Container padrão com shadow e border
- **Inputs**: Campos de formulário com validação visual
- **Loading**: Spinners e estados de carregamento

## 🔐 Autenticação

O sistema utiliza JWT tokens para autenticação:

1. Usuário é redirecionado do portal
2. Token é validado com o backend Flask
3. Estado de autenticação é mantido no Zustand
4. Rotas protegidas verificam autenticação

## 📊 Estado Global

Utilizamos Zustand para gerenciamento de estado:

- **authStore**: Estado de autenticação
- **themeStore**: Tema dark/light (futuro)
- **filterStore**: Filtros globais (futuro)

## 🧪 Testes

### Estrutura de Testes

- **Unit Tests**: Componentes individuais
- **Integration Tests**: Fluxos completos
- **E2E Tests**: Testes end-to-end (futuro)

### Executar Testes

```bash
# Testes em watch mode
npm run test

# Interface visual dos testes
npm run test:ui

# Cobertura de código
npm run test:coverage
```

## 🚀 Deploy

### Build de Produção

```bash
npm run build
```

O build gera arquivos otimizados na pasta `dist/`.

### Configuração do Servidor

O React app deve ser servido pelo Flask em produção:

1. Build do React vai para `dist/`
2. Flask serve arquivos estáticos do `dist/`
3. Rotas da API continuam funcionando normalmente

## 📈 Performance

### Otimizações Implementadas

- **Code Splitting**: Divisão automática por rotas
- **Lazy Loading**: Componentes carregados sob demanda
- **Bundle Optimization**: Chunks separados para vendor, UI, etc.
- **Tree Shaking**: Remoção de código não utilizado

### Métricas Alvo

- First Contentful Paint < 1s
- Largest Contentful Paint < 2s
- Bundle size < 500kb (gzipped)

## 🔄 Migração do Sistema Atual

Este projeto faz parte da migração incremental do sistema atual (Jinja2 + jQuery) para React.

### Fases da Migração

1. ✅ **Setup e Infraestrutura** - Ambiente React configurado
2. 🔄 **Autenticação e Layout** - Em desenvolvimento
3. ⏳ **Dashboard Principal** - Próximo
4. ⏳ **Módulos Específicos** - Futuro

### Coexistência

Durante a migração, ambos os sistemas funcionarão em paralelo:

- Sistema atual: `/fiscal/` (Jinja2)
- Sistema React: `/fiscal/app/` (React)

## 🤝 Contribuição

### Padrões de Desenvolvimento

1. **Branches**: `feature/nome-da-feature`
2. **Commits**: Conventional Commits
3. **PRs**: Template com checklist
4. **Code Review**: Obrigatório para main

### Checklist para PRs

- [ ] Testes passando
- [ ] Linting sem erros
- [ ] Componentes documentados
- [ ] Responsividade testada
- [ ] Acessibilidade verificada

## 📚 Documentação

- [Cronograma de Migração](../docs/CRONOGRAMA_MIGRACAO_REACT.md)
- [Guia de Componentes](./docs/components.md) (futuro)
- [API Reference](./docs/api.md) (futuro)

## 🐛 Troubleshooting

### Problemas Comuns

1. **Proxy não funciona**: Verificar se Flask está rodando na porta 5000
2. **Autenticação falha**: Verificar token no localStorage
3. **Build falha**: Verificar dependências e TypeScript errors

### Logs

```bash
# Logs do desenvolvimento
npm run dev -- --debug

# Logs dos testes
npm run test -- --reporter=verbose
```

## 📞 Suporte

Para dúvidas e suporte:

1. Verificar documentação
2. Abrir issue no repositório
3. Contatar equipe de desenvolvimento
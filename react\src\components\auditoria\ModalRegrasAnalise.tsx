import React from 'react'
import { Modal } from '@/components/ui/Modal'
import { Badge } from '@/components/ui/Badge'

interface ModalRegrasAnaliseProps {
  isOpen: boolean
  onClose: () => void
  analysisType: string
  tributo: string
}

export function ModalRegrasAnalise({
  isOpen,
  onClose,
  analysisType,
  tributo,
}: ModalRegrasAnaliseProps) {
  const getRegrasAnalise = (type: string, tributoAtual: string) => {
    const regras: Record<string, any> = {
      cfop: {
        title: 'Regras de Validação CFOP',
        icon: 'fas fa-exchange-alt',
        color: 'blue',
        description: 'Validações aplicadas aos códigos CFOP entre SPED e NFe',
        rules: [
          {
            title: 'Correspondência CFOP',
            description:
              'CFOP SPED deve corresponder ao CFOP NFe conforme mapeamento de entrada/saída',
            severity: 'error',
          },
          {
            title: 'CFOPs Inválidos',
            description:
              'Detecta CFOPs que não seguem as regras fiscais estabelecidas',
            severity: 'warning',
          },
          {
            title: 'Combinações Proibidas',
            description:
              'Identifica combinações de CFOP que são fiscalmente inválidas',
            severity: 'error',
          },
        ],
      },
      cfop_cst: {
        title: 'Regras CFOP x CST',
        icon: 'fas fa-link',
        color: 'purple',
        description: 'Validações de combinações entre CFOP e CST',
        rules: [
          {
            title: 'Combinações Inválidas',
            description:
              'CFOP 1101/1102 não pode ser usado com CFOP 5.40* no XML',
            severity: 'error',
          },
          {
            title: 'CST Incompatível',
            description: 'CST deve ser compatível com o CFOP utilizado',
            severity: 'warning',
          },
          {
            title: 'Validação Cruzada',
            description:
              'Verifica se a combinação CFOP x CST está correta fiscalmente',
            severity: 'info',
          },
        ],
      },
      origin: {
        title: 'Regras de Origem do Produto',
        icon: 'fas fa-map-marker-alt',
        color: 'green',
        description: 'Validações específicas para origem do produto (ICMS)',
        rules: [
          {
            title: 'Origem Nacional',
            description:
              'Produtos de origem nacional devem ter origem 0, 3, 4 ou 5',
            severity: 'warning',
          },
          {
            title: 'Origem Estrangeira',
            description: 'Produtos importados devem ter origem 1, 2, 6, 7 ou 8',
            severity: 'warning',
          },
          {
            title: 'Correspondência SPED x NFe',
            description: 'Origem no SPED deve corresponder à origem na NFe',
            severity: 'error',
          },
        ],
      },
      aliquota: {
        title: 'Regras de Alíquotas',
        icon: 'fas fa-percentage',
        color: 'orange',
        description: `Validações de alíquotas para ${tributoAtual.toUpperCase()}`,
        rules: [
          {
            title: 'Divergência de Alíquotas',
            description: 'Alíquota SPED deve ser igual à alíquota NFe',
            severity: 'error',
          },
          {
            title: 'Alíquotas Válidas',
            description:
              'Verifica se as alíquotas estão dentro dos valores permitidos',
            severity: 'warning',
          },
          {
            title: 'Tolerância',
            description:
              'Diferenças menores que 0,01% são consideradas aceitáveis',
            severity: 'info',
          },
        ],
      },
      ipi: {
        title: 'Regras Específicas IPI',
        icon: 'fas fa-industry',
        color: 'indigo',
        description: 'Validações específicas para o imposto IPI',
        rules: [
          {
            title: 'Produtos Industrializados',
            description: 'IPI aplica-se apenas a produtos industrializados',
            severity: 'info',
          },
          {
            title: 'Alíquotas IPI',
            description:
              'Verifica se as alíquotas IPI estão corretas conforme TIPI',
            severity: 'warning',
          },
          {
            title: 'Matching Automático',
            description:
              'Registros não matched podem indicar problemas de classificação',
            severity: 'error',
          },
        ],
      },
      pis_cofins: {
        title: 'Regras PIS/COFINS',
        icon: 'fas fa-coins',
        color: 'yellow',
        description: 'Validações específicas para PIS e COFINS',
        rules: [
          {
            title: 'Regime de Apuração',
            description:
              'Verifica se o regime (cumulativo/não-cumulativo) está correto',
            severity: 'warning',
          },
          {
            title: 'CST PIS/COFINS',
            description: 'CST deve ser compatível com a operação realizada',
            severity: 'error',
          },
          {
            title: 'Base de Cálculo',
            description:
              'Base de cálculo deve ser consistente entre PIS e COFINS',
            severity: 'warning',
          },
        ],
      },
    }

    return (
      regras[type] || {
        title: 'Regras Gerais',
        icon: 'fas fa-list',
        color: 'gray',
        description: 'Validações gerais aplicadas',
        rules: [
          {
            title: 'Matching Geral',
            description: 'Verifica correspondência entre registros SPED e NFe',
            severity: 'info',
          },
        ],
      }
    )
  }

  const regras = getRegrasAnalise(analysisType, tributo)

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'error'
      case 'warning':
        return 'warning'
      case 'info':
        return 'info'
      default:
        return 'secondary'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'fas fa-times-circle'
      case 'warning':
        return 'fas fa-exclamation-triangle'
      case 'info':
        return 'fas fa-info-circle'
      default:
        return 'fas fa-circle'
    }
  }

  const getSeverityLabel = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'Erro'
      case 'warning':
        return 'Aviso'
      case 'info':
        return 'Info'
      default:
        return severity
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={regras.title} size="lg">
      <div className="space-y-6">
        {/* Header */}
        <div
          className={`bg-${regras.color}-50 dark:bg-${regras.color}-900/20 rounded-lg p-4`}
        >
          <div className="flex items-center gap-3">
            <div
              className={`w-10 h-10 bg-${regras.color}-100 dark:bg-${regras.color}-900/50 rounded-full flex items-center justify-center`}
            >
              <i
                className={`${regras.icon} text-${regras.color}-600 dark:text-${regras.color}-400`}
              ></i>
            </div>
            <div>
              <h3
                className={`font-semibold text-${regras.color}-900 dark:text-${regras.color}-100`}
              >
                {regras.title}
              </h3>
              <p
                className={`text-sm text-${regras.color}-700 dark:text-${regras.color}-300`}
              >
                {regras.description}
              </p>
            </div>
          </div>
        </div>

        {/* Lista de Regras */}
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-900 dark:text-white">
            Regras Aplicadas
          </h4>

          <div className="space-y-3">
            {regras.rules.map((rule: any, index: number) => (
              <div
                key={index}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-1">
                    <i
                      className={`${getSeverityIcon(rule.severity)} text-${getSeverityColor(rule.severity)}-500`}
                    ></i>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        {rule.title}
                      </h5>
                      <Badge
                        variant={getSeverityColor(rule.severity) as any}
                        size="sm"
                      >
                        {getSeverityLabel(rule.severity)}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {rule.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Informações Adicionais */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
            Informações Importantes
          </h4>
          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <li>
              • As regras são aplicadas automaticamente durante a auditoria
            </li>
            <li>• Registros aprovados não são incluídos nas validações</li>
            <li>• Você pode aprovar registros individualmente ou em massa</li>
            <li>• Filtros podem ser aplicados clicando nos cards de análise</li>
          </ul>
        </div>
      </div>
    </Modal>
  )
}
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from .escritorio import db

class ServiceLogs(db.Model):
    __tablename__ = 'service_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    service_auth_id = db.Column(db.Integer, db.<PERSON>ey('service_authentication.id', ondelete='SET NULL'))
    portal_office_id = db.Column(db.String(50), nullable=False)
    operation_type = db.Column(db.String(100), nullable=False)
    operation_details = db.Column(db.JSON)
    status = db.Column(db.String(50), nullable=False, default='pending')
    error_message = db.Column(db.Text)
    request_data = db.Column(db.JSON)
    response_data = db.Column(db.JSON)
    execution_time_ms = db.Column(db.Integer)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    retry_count = db.Column(db.Integer, default=0)
    max_retries = db.Column(db.Integer, default=3)

    # Relationship to ServiceAuthentication
    service_auth = db.relationship('ServiceAuthentication', backref='logs')

    def __repr__(self):
        return f"<ServiceLogs {self.operation_type} - {self.status}>"

    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'service_auth_id': self.service_auth_id,
            'portal_office_id': self.portal_office_id,
            'operation_type': self.operation_type,
            'operation_details': self.operation_details,
            'status': self.status,
            'error_message': self.error_message,
            'request_data': self.request_data,
            'response_data': self.response_data,
            'execution_time_ms': self.execution_time_ms,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
        }
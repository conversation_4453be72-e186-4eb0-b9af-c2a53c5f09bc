import { useState, useEffect } from 'react'

// Função para encontrar o seletor de empresa (pode ter IDs diferentes)
function findCompanySelector(): HTMLSelectElement | null {
  const possibleIds = ['company-select', 'empresa-select', 'empresa-filter']
  
  for (const id of possibleIds) {
    const element = document.getElementById(id) as HTMLSelectElement
    if (element) {
      return element
    }
  }
  
  // Tentar por classe também
  const byClass = document.querySelector('.company-selector select') as HTMLSelectElement
  if (byClass) {
    return byClass
  }
  
  return null
}

// Função para salvar empresa no localStorage e variável global
function saveSelectedCompany(companyId: string | number) {
  const id = companyId.toString()
  
  // Salvar no localStorage
  localStorage.setItem('selectedCompany', id)
  
  // Salvar na variável global
  ;(window as any).selectedCompany = id
  
  // Disparar evento para outros componentes
  const event = new CustomEvent('company-changed', {
    detail: { companyId: id }
  })
  window.dispatchEvent(event)
}

export function useSelectedCompany() {
  const [selectedCompany, setSelectedCompany] = useState<number | undefined>(() => {
    if (typeof window !== 'undefined') {
      const globalCompany = (window as any).selectedCompany
      const storedCompany = localStorage.getItem('selectedCompany')
      
      // NOVO: Verificar o filter-storage do React
      let filterStorageCompany = null
      try {
        const filterStorage = localStorage.getItem('filter-storage')
        if (filterStorage) {
          const parsed = JSON.parse(filterStorage)
          filterStorageCompany = parsed.state?.selectedCompany
        }
      } catch (error) {
      }
      
      // Prioridade: filter-storage (React atual) > localStorage > window.selectedCompany
      const company = filterStorageCompany || globalCompany || storedCompany
            
      return company ? parseInt(company) : undefined
    }
    return undefined
  })

  useEffect(() => {
    // Função para atualizar quando a empresa mudar (como no sistema atual)
    const handleCompanyChange = (event: any) => {
      if (event.detail && event.detail.companyId) {
        setSelectedCompany(parseInt(event.detail.companyId))
      }
    }

    // Escutar evento de mudança de empresa (como no sistema atual)
    window.addEventListener('company-changed', handleCompanyChange)

    // Função para configurar listener no seletor
    const setupCompanySelector = () => {
      const companySelect = findCompanySelector()
      
      if (companySelect) {
        // Se o seletor já tem um valor, usar ele
        if (companySelect.value && companySelect.value !== selectedCompany?.toString()) {
          saveSelectedCompany(companySelect.value)
          setSelectedCompany(parseInt(companySelect.value))
        }

        // Listener para mudanças
        const handleSelectChange = (event: Event) => {
          const target = event.target as HTMLSelectElement
          if (target.value) {
            saveSelectedCompany(target.value)
            setSelectedCompany(parseInt(target.value))
          }
        }

        companySelect.addEventListener('change', handleSelectChange)
        
        return () => {
          companySelect.removeEventListener('change', handleSelectChange)
        }
      }
      
      return null
    }

    // Tentar configurar o seletor imediatamente
    const cleanup = setupCompanySelector()

    // Verificação periódica para pegar a empresa quando ela for carregada
    const interval = setInterval(() => {
      const currentGlobal = (window as any).selectedCompany
      const currentStorage = localStorage.getItem('selectedCompany')
      
      // Verificar filter-storage também
      let currentFilterStorage = null
      try {
        const filterStorage = localStorage.getItem('filter-storage')
        if (filterStorage) {
          const parsed = JSON.parse(filterStorage)
          currentFilterStorage = parsed.state?.selectedCompany
        }
      } catch (error) {
        // Ignorar erro silenciosamente
      }
      
      const companySelect = findCompanySelector()
      const currentSelectValue = companySelect?.value
      
      // Mesma prioridade: filter-storage > window.selectedCompany > localStorage > seletor
      const currentCompany = currentFilterStorage || currentGlobal || currentStorage || currentSelectValue
      
      if (currentCompany && parseInt(currentCompany) !== selectedCompany) {
        setSelectedCompany(parseInt(currentCompany))
      }

      // Se encontrou o seletor mas ainda não configurou, configurar agora
      if (companySelect && !cleanup) {
        setupCompanySelector()
      }
    }, 1000)

    return () => {
      window.removeEventListener('company-changed', handleCompanyChange)
      cleanup?.()
      clearInterval(interval)
    }
  }, [selectedCompany])

  return selectedCompany
}
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useFilterStore } from '@/store/filterStore'
import apuracaoService, { ApuracaoHistoricoResponse } from '@/services/apuracaoService'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Card, StatsCard } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { HelpButton, HelpModal } from '@/components/ui'
import {
  ResponsiveContainer,
  LineChart,
  Line,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  BarChart,
  Bar,
  Area,
  AreaChart,
} from 'recharts'
import { 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  BarChart3, 
  CheckCircle, 
  FileText, 
  TrendingUp, 
  TrendingDown,
  ArrowUp, 
  ArrowDown 
} from 'lucide-react'

export function ApuracaoPage() {
  const { selectedCompany, selectedYear, selectedMonth } = useFilterStore()
  const [activeTab, setActiveTab] = useState<'icms' | 'icms_st' | 'ipi' | 'pis' | 'cofins'>('icms')
  const [isHelpOpen, setIsHelpOpen] = useState(false)

  const helpTabs = [
    {
      label: 'ICMS',
      content: (
        <div className="space-y-4 text-sm text-gray-700 dark:text-gray-300">
          <p>
            <strong>Propósito:</strong> Demonstrar a apuração mensal do ICMS devido
            nas operações da empresa.
          </p>
          <p>
            <strong>Resultado exibido:</strong> O gráfico principal compara os
            valores a recolher com o saldo credor acumulado. Os gráficos
            complementares exibem o total de débitos e créditos do período.
          </p>
          <p>
            <strong>Como interpretar:</strong> Áreas vermelhas representam imposto
            a recolher e áreas verdes indicam saldo credor. Acompanhe a tendência
            mensal para identificar variações.
          </p>
        </div>
      )
    },
    {
      label: 'ICMS-ST',
      content: (
        <div className="space-y-4 text-sm text-gray-700 dark:text-gray-300">
          <p>
            <strong>Propósito:</strong> Controlar a apuração da Substituição
            Tributária do ICMS.
          </p>
          <p>
            <strong>Resultado exibido:</strong> Apresenta os valores a recolher e
            o saldo credor de ICMS-ST, além dos débitos e créditos gerados.
          </p>
          <p>
            <strong>Como interpretar:</strong> A leitura segue a mesma lógica do
            ICMS: vermelho indica imposto devido e verde representa saldo credor,
            enquanto os gráficos inferiores detalham débitos e créditos.
          </p>
        </div>
      )
    },
    {
      label: 'IPI',
      content: (
        <div className="space-y-4 text-sm text-gray-700 dark:text-gray-300">
          <p>
            <strong>Propósito:</strong> Exibir a apuração do Imposto sobre
            Produtos Industrializados.
          </p>
          <p>
            <strong>Resultado exibido:</strong> Mostra o saldo a recolher ou
            credor de IPI, acompanhado dos valores de débitos e créditos
            registrados.
          </p>
          <p>
            <strong>Como interpretar:</strong> Compare as áreas vermelha e verde
            para entender se há imposto a pagar ou crédito a compensar. Utilize os
            gráficos de débitos e créditos para identificar as origens dessas
            variações.
          </p>
        </div>
      )
    },
    {
      label: 'PIS',
      content: (
        <div className="space-y-4 text-sm text-gray-700 dark:text-gray-300">
          <p>
            <strong>Propósito:</strong> Acompanhar a contribuição do PIS gerada
            nas operações.
          </p>
          <p>
            <strong>Resultado exibido:</strong> O gráfico evidencia o valor a
            recolher ou o saldo credor e os gráficos auxiliares mostram débitos e
            créditos do período.
          </p>
          <p>
            <strong>Como interpretar:</strong> Observe a evolução mensal do valor
            devido (vermelho) e do crédito (verde). Os gráficos adicionais
            ajudam a compreender quais períodos geraram maiores débitos ou
            créditos.
          </p>
        </div>
      )
    },
    {
      label: 'COFINS',
      content: (
        <div className="space-y-4 text-sm text-gray-700 dark:text-gray-300">
          <p>
            <strong>Propósito:</strong> Demonstrar a contribuição da COFINS
            apurada mensalmente.
          </p>
          <p>
            <strong>Resultado exibido:</strong> Apresenta o valor a recolher ou
            saldo credor, além dos débitos e créditos contabilizados.
          </p>
          <p>
            <strong>Como interpretar:</strong> A área vermelha sinaliza imposto a
            recolher e a verde mostra créditos disponíveis. Utilize os gráficos
            inferiores para analisar a origem dessas movimentações.
          </p>
        </div>
      )
    }
  ]

  const queryMap: Record<string, (id: number, ano: number, mes: number) => Promise<ApuracaoHistoricoResponse>> = {
    icms: apuracaoService.getHistoricoICMS,
    icms_st: apuracaoService.getHistoricoICMSST,
    ipi: apuracaoService.getHistoricoIPI,
    pis: apuracaoService.getHistoricoPIS,
    cofins: apuracaoService.getHistoricoCOFINS,
  }

  const { data, isLoading, error } = useQuery({
    queryKey: ['apuracao-historico', activeTab, selectedCompany, selectedYear, selectedMonth],
    queryFn: () => queryMap[activeTab](selectedCompany!, selectedYear, selectedMonth),
    enabled: !!selectedCompany,
  }) as { data?: ApuracaoHistoricoResponse; isLoading: boolean; error: any }

  const buildData = () => {
    if (!data?.success) return null
    const labels: string[] = data.dados.labels || []
    let recolherKey = ''
    let credorKey = ''
    let debitosKey = ''
    let creditosKey = ''

    switch (activeTab) {
      case 'icms':
      case 'icms_st':
        recolherKey = 'vl_icms_recolher'
        credorKey = 'vl_sld_credor_transportar'
        debitosKey = 'vl_tot_debitos'
        creditosKey = 'vl_tot_creditos'
        break
      case 'ipi':
        recolherKey = 'vl_sd_ipi'
        credorKey = 'vl_sc_ipi'
        debitosKey = 'vl_deb_ipi'
        creditosKey = 'vl_cred_ipi'
        break
      case 'pis':
        recolherKey = 'vl_pis_a_recolher'
        credorKey = 'vl_tot_creditos'
        debitosKey = 'vl_tot_debitos'
        creditosKey = 'vl_tot_creditos'
        break
      case 'cofins':
        recolherKey = 'vl_cofins_a_recolher'
        credorKey = 'vl_tot_creditos'
        debitosKey = 'vl_tot_debitos'
        creditosKey = 'vl_tot_creditos'
        break
    }

    const recolher = (data.dados[recolherKey] || []) as number[]
    const credor = (data.dados[credorKey] || []) as number[]
    const debitos = (data.dados[debitosKey] || []) as number[]
    const creditos = (data.dados[creditosKey] || []) as number[]

    const combined = labels.map((label, idx) => ({
      label,
      recolher: Number(recolher[idx]) || 0,
      credor: -Math.abs(Number(credor[idx]) || 0),
    }))

    const debitosData = labels.map((label, idx) => ({
      label,
      valor: Number(debitos[idx]) || 0,
    }))

    const creditosData = labels.map((label, idx) => ({
      label,
      valor: Number(creditos[idx]) || 0,
    }))

    return { combined, debitosData, creditosData }
  }

  const chartData = buildData()

  return (
    <>
      <HelpModal
        isOpen={isHelpOpen}
        onClose={() => setIsHelpOpen(false)}
        title="Ajuda - Apuração Tributária"
        tabs={helpTabs}
        size="lg"
      />
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            Apuração Tributária
            <HelpButton onClick={() => setIsHelpOpen(true)} />
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Análise e acompanhamento dos tributos apurados
          </p>
        </div>

      {!selectedCompany && (
        <Card className="border-warning-200 dark:border-warning-800 bg-gradient-to-r from-warning-50 to-warning-100 dark:from-warning-900/20 dark:to-warning-800/20">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-warning-100 dark:bg-warning-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
              <AlertTriangle className="w-6 h-6 text-warning-600 dark:text-warning-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-warning-800 dark:text-warning-200 mb-1">
                ⚠️ Empresa não selecionada
              </h3>
              <p className="text-warning-700 dark:text-warning-300">
                Selecione uma empresa no cabeçalho para visualizar os dados de apuração tributária.
              </p>
            </div>
          </div>
        </Card>
      )}

      {selectedCompany && (
        <>
          {/* Modern Tabs */}
          <Card className="p-2 bg-gray-50 dark:bg-gray-800/50">
            <div className="flex gap-2 flex-wrap">
              {[
                { key: 'icms', label: 'ICMS', icon: '', color: 'primary' },
                { key: 'icms_st', label: 'ICMS-ST', icon: '', color: 'secondary' },
                { key: 'ipi', label: 'IPI', icon: '', color: 'success' },
                { key: 'pis', label: 'PIS', icon: '', color: 'warning' },
                { key: 'cofins', label: 'COFINS', icon: '', color: 'error' },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={activeTab === tab.key ? tab.color as any : 'ghost'}
                  size="md"
                  onClick={() => setActiveTab(tab.key as any)}
                  className="flex-1 justify-center min-w-[120px]"
                  icon={<span className="text-lg">{tab.icon}</span>}
                  glow={activeTab === tab.key}
                >
                  {tab.label}
                </Button>
              ))}
            </div>
          </Card>

          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <LoadingSpinner size="lg" className="mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  Carregando dados de apuração...
                </p>
              </div>
            </div>
          )}

          {error && (
            <Card className="border-error-200 dark:border-error-800 bg-gradient-to-r from-error-50 to-error-100 dark:from-error-900/20 dark:to-error-800/20">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-error-100 dark:bg-error-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
                  <XCircle className="w-6 h-6 text-error-600 dark:text-error-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-error-800 dark:text-error-200 mb-1">
                    ⚠️ Erro ao carregar dados de apuração
                  </h3>
                  <p className="text-error-700 dark:text-error-300 mb-4">
                    Ocorreu um erro ao carregar os dados de apuração. Verifique sua conexão e tente novamente.
                  </p>
                  <Button
                    variant="error"
                    size="sm"
                    onClick={() => window.location.reload()}
                    icon={
                      <RefreshCw className="w-4 h-4" />
                    }
                  >
                    Tentar Novamente
                  </Button>
                </div>
              </div>
            </Card>
          )}

          {chartData && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <StatsCard
                  title="Total a Recolher"
                  value={`R$ ${chartData.combined.reduce((acc, item) => acc + Math.max(0, item.recolher), 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
                  change={{
                    value: 5,
                    type: 'decrease',
                    period: 'vs mês anterior'
                  }}
                  icon={
                    <TrendingDown className="w-6 h-6 text-error-200 dark:text-error-400" />
                  }
                  color="error"
                />
                
                <StatsCard
                  title="Saldo Credor"
                  value={`R$ ${Math.abs(chartData.combined.reduce((acc, item) => acc + Math.min(0, item.credor), 0)).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
                  change={{
                    value: 12,
                    type: 'increase',
                    period: 'vs mês anterior'
                  }}
                  icon={
                    <CheckCircle className="w-6 h-6 text-success-200 dark:text-success-400" />
                  }
                  color="success"
                />
                
                <StatsCard
                  title="Situação"
                  value={chartData.combined.reduce((acc, item) => acc + item.recolher, 0) > 0 ? 'A Recolher' : 'Credor'}
                  icon={
                    <FileText className="w-6 h-6 text-primary-200 dark:text-primary-400" />
                  }
                  color="primary"
                />
              </div>

              {/* Main Chart */}
              <Card className="overflow-hidden" gradient>
                <div className="bg-gradient-to-r ">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-primary-900/30 rounded-xl flex items-center justify-center">
                      <BarChart3 className="w-6 h-6 text-blue-600 dark:text-primary-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                        {activeTab.toUpperCase()} - Apuração Mensal
                      </h2>
                      <p className="text-gray-600 dark:text-gray-400 mt-1">
                        Valores a recolher vs saldo credor por período
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={chartData.combined}>
                        <defs>
                          <linearGradient id="colorRecolher" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#ef4444" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#ef4444" stopOpacity={0.1}/>
                          </linearGradient>
                          <linearGradient id="colorCredor" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#22c55e" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#22c55e" stopOpacity={0.1}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                        <XAxis 
                          dataKey="label" 
                          stroke="#6b7280"
                          fontSize={12}
                        />
                        <YAxis 
                          stroke="#6b7280"
                          fontSize={12}
                          tickFormatter={(value) => `R$ ${value.toLocaleString('pt-BR')}`}
                        />
                        <Tooltip 
                          contentStyle={{
                            backgroundColor: '#ffffff',
                            border: '1px solid #e5e7eb',
                            borderRadius: '8px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                          formatter={(value: number, name: string) => [
                            `R$ ${Math.abs(value).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
                            name === 'recolher' ? 'A Recolher' : 'Saldo Credor'
                          ]}
                        />
                        <Legend />
                        <Area 
                          type="monotone" 
                          dataKey="recolher" 
                          stroke="#ef4444" 
                          fillOpacity={1} 
                          fill="url(#colorRecolher)"
                          name="A Recolher"
                          strokeWidth={2}
                        />
                        <Area 
                          type="monotone" 
                          dataKey="credor" 
                          stroke="#22c55e" 
                          fillOpacity={1} 
                          fill="url(#colorCredor)"
                          name="Saldo Credor"
                          strokeWidth={2}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </Card>

              {/* Secondary Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Total de Débitos - Gráfico de Área Azul */}
                <Card className="overflow-hidden" gradient>
                  <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 border-b border-blue-200 dark:border-blue-700">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
                        <ArrowUp className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                          💸 Total de Débitos
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Valores debitados por período
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={chartData.debitosData}>
                          <defs>
                            <linearGradient id="colorDebitos" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                              <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                            </linearGradient>
                          </defs>
                          <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                          <XAxis 
                            dataKey="label" 
                            stroke="#6b7280"
                            fontSize={12}
                          />
                          <YAxis 
                            stroke="#6b7280"
                            fontSize={12}
                            tickFormatter={(value) => `R$ ${value.toLocaleString('pt-BR')}`}
                          />
                          <Tooltip 
                            contentStyle={{
                              backgroundColor: '#ffffff',
                              border: '1px solid #e5e7eb',
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                            formatter={(value: number) => [
                              `R$ ${value.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
                              'Débitos'
                            ]}
                          />
                          <Area 
                            type="monotone" 
                            dataKey="valor" 
                            stroke="#3b82f6" 
                            fillOpacity={1} 
                            fill="url(#colorDebitos)"
                            name="Débitos"
                            strokeWidth={2}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </Card>

                {/* Total de Créditos - Gráfico de Área Laranja */}
                <Card className="overflow-hidden" gradient>
                  <div className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-4 border-b border-orange-200 dark:border-orange-700">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-xl flex items-center justify-center">
                        <ArrowDown className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                          💰 Total de Créditos
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Valores creditados por período
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={chartData.creditosData}>
                          <defs>
                            <linearGradient id="colorCreditos" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor="#f97316" stopOpacity={0.8}/>
                              <stop offset="95%" stopColor="#f97316" stopOpacity={0.1}/>
                            </linearGradient>
                          </defs>
                          <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                          <XAxis 
                            dataKey="label" 
                            stroke="#6b7280"
                            fontSize={12}
                          />
                          <YAxis 
                            stroke="#6b7280"
                            fontSize={12}
                            tickFormatter={(value) => `R$ ${value.toLocaleString('pt-BR')}`}
                          />
                          <Tooltip 
                            contentStyle={{
                              backgroundColor: '#ffffff',
                              border: '1px solid #e5e7eb',
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                            formatter={(value: number) => [
                              `R$ ${value.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
                              'Créditos'
                            ]}
                          />
                          <Area 
                            type="monotone" 
                            dataKey="valor" 
                            stroke="#f97316" 
                            fillOpacity={1} 
                            fill="url(#colorCreditos)"
                            name="Créditos"
                            strokeWidth={2}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}
        </>
      )}
    </div>
    </>
  )
}

export default ApuracaoPage
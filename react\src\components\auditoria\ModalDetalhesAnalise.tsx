import React from 'react'
import { Modal } from '@/components/ui/Modal'
import { Badge } from '@/components/ui/Badge'
import type { AuditoriaComparativaItem } from '@/services/auditoriaComparativaService'
import {
  ANALYSIS_CARDS_CONFIG,
  isCfopInconsistent,
  isCfopCstInconsistent,
  isProductTypeInconsistent,
  isOriginInconsistent,
  isAliquotaInconsistent,
  getIpiRuleViolation,
  getIpiRuleViolationByAnalysis,
  hasPisCofinsViolation,
  getPisCofinsViolations,
  getRuleViolationDescription,
} from '@/utils/fiscalAnalysisRules'

interface ModalDetalhesAnaliseProps {
  isOpen: boolean
  onClose: () => void
  analysisType: string
  tributo: string
  auditorias: AuditoriaComparativaItem[]
}

interface ViolationsSummary {
  total: number
  byRule: Record<string, number>
}

function analisarViolacoes(
  analysisType: string,
  auditorias: AuditoriaComparativaItem[],
  tributo: string
): ViolationsSummary {
  const summary: ViolationsSummary = { total: 0, byRule: {} }

  auditorias.forEach((auditoria) => {
    const mensagens: string[] = []

    switch (analysisType) {
      case 'cfop':
        if (
          isCfopInconsistent(
            auditoria.sped_cfop,
            auditoria.xml_cfop,
            auditoria.tipo_produto
          )
        ) {
          mensagens.push(getRuleViolationDescription('cfop', auditoria))
        }
        break
      case 'cfop_cst':
        if (isCfopCstInconsistent(auditoria)) {
          mensagens.push(getRuleViolationDescription('cfop_cst', auditoria))
        }
        break
      case 'product_type':
        if (
          isProductTypeInconsistent(
            auditoria.sped_cfop,
            auditoria.tributos?.icms?.cst,
            auditoria.tipo_produto
          )
        ) {
          mensagens.push(getRuleViolationDescription('product_type', auditoria))
        }
        break
      case 'origin':
        if (
          isOriginInconsistent(
            auditoria.sped_origem,
            auditoria.xml_origem || auditoria.xml_data?.origem
          )
        ) {
          mensagens.push(getRuleViolationDescription('origin', auditoria))
        }
        break
      case 'aliquota': {
        let spedAliq: number | undefined
        let xmlAliq: number | undefined
        switch (tributo) {
          case 'icms':
            spedAliq = auditoria.tributos?.icms?.aliquota
            xmlAliq =
              auditoria.xml_data?.icms_aliquota ||
              (auditoria as any).xml_icms_aliquota
            break
          case 'icms_st':
            spedAliq = auditoria.tributos?.icms_st?.aliquota
            xmlAliq =
              auditoria.xml_data?.icms_st_aliquota ||
              (auditoria as any).xml_icms_st_aliquota
            break
          case 'ipi':
            spedAliq = auditoria.tributos?.ipi?.aliquota
            xmlAliq =
              auditoria.xml_data?.ipi_aliquota ||
              (auditoria as any).xml_ipi_aliquota
            break
          case 'pis':
          case 'cofins':
          case 'pis_cofins':
            spedAliq = auditoria.tributos?.pis?.aliquota
            xmlAliq =
              auditoria.xml_data?.pis_aliquota ||
              (auditoria as any).xml_pis_aliquota
            break
        }

        if (
          isAliquotaInconsistent(
            spedAliq,
            xmlAliq,
            auditoria.tipo_produto,
            auditoria.sped_cfop
          )
        ) {
          mensagens.push(getRuleViolationDescription('aliquota', auditoria))
        } else if (tributo === 'ipi') {
          const ipiAliq = getIpiRuleViolationByAnalysis('aliquota', auditoria)
          if (ipiAliq) mensagens.push(ipiAliq)
        }
        break
      }
      case 'ipi': {
        const reason = getIpiRuleViolation(auditoria)
        if (reason) mensagens.push(reason)
        break
      }
      case 'pis_cofins':
        if (hasPisCofinsViolation(auditoria)) {
          const vios = getPisCofinsViolations(auditoria)
          vios.forEach((v) => mensagens.push(v))
        }
        break
    }

    mensagens.forEach((msg) => {
      summary.total++
      summary.byRule[msg] = (summary.byRule[msg] || 0) + 1
    })
  })

  return summary
}

export function ModalDetalhesAnalise({
  isOpen,
  onClose,
  analysisType,
  tributo,
  auditorias,
}: ModalDetalhesAnaliseProps) {
  const config = ANALYSIS_CARDS_CONFIG[analysisType] || { title: analysisType }

  const violacoes = React.useMemo(
    () => analisarViolacoes(analysisType, auditorias, tributo),
    [analysisType, auditorias, tributo]
  )

  const totalTipos = Object.keys(violacoes.byRule).length

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Detalhes da ${config.title}`}
      size="lg"
    >
      <div className="space-y-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <h6 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">
            Resumo da Análise
          </h6>
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <strong>{violacoes.total}</strong>{' '}
            {violacoes.total === 1
              ? 'violação encontrada'
              : 'violações encontradas'}{' '}
            em <strong>{totalTipos}</strong>{' '}
            {totalTipos === 1 ? 'tipo de regra' : 'tipos de regras'}
          </p>
        </div>

        <div className="space-y-4">
          {Object.entries(violacoes.byRule).map(([rule, count]) => (
            <div
              key={rule}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
            >
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h5 className="font-medium text-gray-900 dark:text-white">
                    {rule}
                  </h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {count} {count === 1 ? 'ocorrência' : 'ocorrências'}
                  </p>
                </div>
                <Badge variant="error" size="sm">
                  {((count / violacoes.total) * 100).toFixed(0)}%
                </Badge>
              </div>
              <div className="w-full bg-red-100 dark:bg-red-900/20 h-2 rounded">
                <div
                  className="bg-red-500 h-2 rounded"
                  style={{ width: `${(count / violacoes.total) * 100}%` }}
                />
              </div>
            </div>
          ))}

          {violacoes.total === 0 && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Nenhuma violação encontrada.
            </p>
          )}
        </div>
      </div>
    </Modal>
  )
}
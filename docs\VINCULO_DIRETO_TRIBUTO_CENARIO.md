# Plano de Ação: Vínculo Direto Tributo-Cenário

Este documento detalha os passos necessários para refatorar o sistema de auditoria, substituindo a correspondência dinâmica de cenários por um vínculo direto e explícito entre um tributo e o cenário que o audita.

## Motivação

A abordagem atual, que busca um cenário correspondente em tempo de auditoria com base em CFOP/NCM, é frágil. A edição de um cenário (por exemplo, corrigindo um CFOP) quebra a ligação com os tributos que o originaram, resultando em tributos "não auditados" incorretamente. A nova abordagem criará um vínculo permanente, garantindo rastreabilidade, precisão e flexibilidade.

## Passos de Implementação

### Passo 1: Alteração no Banco de Dados

**Objetivo:** Adicionar a coluna que armazenará o vínculo do tributo com o cenário.

1.  **Identificar a Tabela Correta:** A lógica de tributos está na tabela `tributo`. É aqui que a nova coluna será adicionada.
2.  **Criar Script de Migração:** Desenvolver um script SQL para adicionar a coluna `cenario_aplicado_id` à tabela `tributo`.
    *   **Nome da Coluna:** `cenario_aplicado_id`
    *   **Tipo:** `INTEGER` ou `BIGINT`, correspondendo ao tipo da coluna `id` na tabela `cenarios`.
    *   **Constraint:** `FOREIGN KEY` referenciando `cenarios(id)`.
    *   **Propriedade:** `NULLABLE`, para permitir que tributos existam antes de um cenário ser aplicado.
3.  **Nome do Arquivo:** `db/migration_add_cenario_aplicado_id_to_tributo.sql`

### Passo 2: Backfill - Preenchimento dos Dados Existentes

**Objetivo:** Vincular os tributos já existentes no banco de dados aos seus cenários correspondentes para garantir a continuidade.

1.  **Criar Script de Backfill:** Desenvolver um script Python (`back/scripts/backfill_cenario_id.py`) que irá:
    a.  Iterar sobre todos os tributos na tabela `tributo` que ainda não possuem um `cenario_aplicado_id`.
    b.  Para cada tributo, buscar na tabela `cenarios` por um cenário que corresponda exatamente aos seus `cliente_id`, `tipo_imposto`, `cfop` e `ncm`. A busca deve priorizar cenários com status `aprovado`.
    c.  Se um cenário correspondente for encontrado, atualizar o campo `tributo.cenario_aplicado_id` com o `id` do cenário.
    d.  Este script será uma operação única para migrar os dados legados.

### Passo 3: Refatorar Lógica de Importação

**Objetivo:** Modificar o processo de importação de notas (XML e SPED) para criar o vínculo no momento da criação do tributo.

1.  **Localizar os Arquivos:** A lógica de importação principal reside em `back/services/importacao_service.py` (ou similar) e é chamada pelas rotas em `back/routes/importacao_routes.py`.
2.  **Alterar o Fluxo:**
    a.  Após a extração dos dados de um tributo da nota fiscal, o sistema **deve** buscar por um cenário existente e **aprovado** que corresponda à combinação (cliente, imposto, cfop, ncm) e data de vigência.
    b.  **Se um cenário aprovado for encontrado:** O `cenario_aplicado_id` do novo tributo é preenchido com o ID deste cenário.
    c.  **Se nenhum cenário aprovado for encontrado:**
        i.  O sistema busca por um cenário **pendente** com a mesma combinação.
        ii. Se encontrar um pendente, vincula o tributo a ele.
        iii. Se não encontrar nenhum, o sistema cria um **novo cenário** com status `pendente` e vincula o tributo a este novo cenário.
3.  **Garantir a Persistência:** A alteração deve garantir que o `cenario_aplicado_id` seja salvo no banco de dados junto com o novo tributo.

### Passo 4: Refatorar Lógica da Auditoria

**Objetivo:** Simplificar e robustecer o cálculo da auditoria para usar o novo vínculo direto.

1.  **Localizar o Arquivo:** A lógica principal da auditoria de saída está em `back/services/auditoria_service.py` (ou similar).
2.  **Alterar a Consulta:**
    a.  Remover a lógica atual que faz um `JOIN` complexo baseado em CFOP, NCM, etc.
    b.  A nova consulta para obter os dados para comparação será um `JOIN` direto:
        ```sql
        SELECT t.*, c.*
        FROM tributo t
        JOIN cenarios c ON t.cenario_aplicado_id = c.id
        WHERE t.cliente_id = ? AND t.tipo_imposto = ?;
        ```
    c.  Um tributo será considerado "não auditado" se `cenario_aplicado_id` for `NULL` ou se o cenário vinculado tiver o status `pendente`.

### Passo 5: Planejamento Futuro - Ferramenta de Revinculação

**Objetivo:** Criar uma interface ou API para permitir a correção e revinculação de tributos a cenários.

*   **Interface:** Adicionar uma funcionalidade na tela de "Cenários" ou em uma nova tela de "Manutenção de Tributos".
*   **Funcionalidade:**
    *   Permitir que o usuário filtre tributos (por data, status, cenário atual, etc.).
    *   Permitir a seleção de um ou mais tributos.
    *   Apresentar uma lista de cenários compatíveis (mesmo tipo de imposto, mesma empresa).
    *   Permitir que o usuário selecione um novo cenário e clique em "Revincular", o que atualizará o campo `cenario_aplicado_id` para todos os tributos selecionados.
*   **API:** Criar um endpoint `POST /api/tributos/revincular` que aceite uma lista de `tributo_ids` e um `novo_cenario_id`.

---

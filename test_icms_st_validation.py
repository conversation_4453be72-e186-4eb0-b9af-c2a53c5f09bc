#!/usr/bin/env python3
"""
Script de teste para validação ICMS-ST
Testa a integração com a API externa e funcionalidades do cache
"""

import sys
import os

# Adicionar o diretório back ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'back'))

def test_imports():
    """Testa se todos os imports necessários funcionam"""
    print("=== Teste de Imports ===")
    
    try:
        from services.icms_st_validation_service import ICMSSTValidationService
        print("✅ ICMSSTValidationService importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar ICMSSTValidationService: {e}")
        return False
    
    try:
        from models.icms_st_cache import ICMSSTCacheModel
        print("✅ ICMSSTCacheModel importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar ICMSSTCacheModel: {e}")
        return False
    
    try:
        from models.icms_st_validation_result import ICMSSTValidationResultModel
        print("✅ ICMSSTValidationResultModel importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar ICMSSTValidationResultModel: {e}")
        return False
    
    return True

def test_api_connection():
    """Testa conexão com as APIs externas"""
    print("\n=== Teste de Conexão com APIs ===")
    
    try:
        from services.icms_st_validation_service import ICMSSTValidationService
        service = ICMSSTValidationService()
        
        # Teste ST Interna
        print("\n1. Testando API ST Interna...")
        try:
            resultado = service._consultar_api_st_interna("87089200", "SP")
            if resultado and resultado.get('registros', 0) > 0:
                print(f"✅ API ST Interna funcionando - {resultado.get('registros', 0)} registros encontrados")
                if resultado.get('resposta'):
                    primeiro = resultado['resposta'][0]
                    print(f"   Exemplo: CEST {primeiro.get('cest')} - {primeiro.get('descricao', '')[:50]}...")
            else:
                print("❌ API ST Interna não retornou dados válidos")
        except Exception as e:
            print(f"❌ Erro na API ST Interna: {str(e)}")
        
        # Teste ST Interestadual
        print("\n2. Testando API ST Interestadual...")
        try:
            resultado = service._consultar_api_st_interestadual("8708", "SP", "MG", "Revenda")
            if resultado and resultado.get('registros', 0) > 0:
                print(f"✅ API ST Interestadual funcionando - {resultado.get('registros', 0)} registros encontrados")
                if resultado.get('resposta'):
                    primeiro = resultado['resposta'][0]
                    print(f"   Exemplo: CEST {primeiro.get('cest')} - {primeiro.get('descricao', '')[:50]}...")
            else:
                print("❌ API ST Interestadual não retornou dados válidos")
        except Exception as e:
            print(f"❌ Erro na API ST Interestadual: {str(e)}")
            
    except Exception as e:
        print(f"❌ Erro geral no teste de API: {str(e)}")

def test_mapping_logic():
    """Testa lógica de mapeamento"""
    print("\n=== Teste de Mapeamento ===")
    
    try:
        from services.icms_st_validation_service import ICMSSTValidationService
        service = ICMSSTValidationService()
        
        # Testar mapeamento de destinação
        print("\n1. Testando mapeamento de destinação...")
        for destinacao, codigo in service.DESTINACAO_MAPPING.items():
            print(f"   {destinacao} -> {codigo}")
        
        print("✅ Mapeamento de destinação OK")
        
        # Testar determinação de tipo ST
        print("\n2. Testando determinação de tipo ST...")
        casos = [
            ("SP", "SP", "interna"),
            ("SP", "MG", "interestadual"),
            ("RJ", "RJ", "interna"),
            ("RS", "SC", "interestadual")
        ]
        
        for estado_emp, estado_cli, esperado in casos:
            tipo = 'interna' if estado_emp == estado_cli else 'interestadual'
            status = "✅" if tipo == esperado else "❌"
            print(f"   {estado_emp} -> {estado_cli}: {tipo} {status}")
            
        # Testar CFOPs válidos
        print("\n3. Testando CFOPs válidos...")
        print(f"   CFOPs configurados: {', '.join(service.CFOPS_VALIDOS)}")
        print("✅ CFOPs válidos OK")
        
    except Exception as e:
        print(f"❌ Erro no teste de mapeamento: {str(e)}")

def test_cache_functionality():
    """Testa funcionalidade do cache (simulado)"""
    print("\n=== Teste de Cache (Simulado) ===")
    
    try:
        from services.icms_st_validation_service import ICMSSTValidationService
        service = ICMSSTValidationService()
        
        print("\n1. Testando geração de hash...")
        
        # Teste hash ST interna
        hash1 = service._gerar_hash_consulta('interna', '87089200', estado='SP')
        hash2 = service._gerar_hash_consulta('interna', '87089200', estado='SP')
        hash3 = service._gerar_hash_consulta('interna', '87089200', estado='RJ')
        
        if hash1 == hash2:
            print("✅ Hash consistente para mesmos parâmetros")
        else:
            print("❌ Hash inconsistente para mesmos parâmetros")
            
        if hash1 != hash3:
            print("✅ Hash diferente para parâmetros diferentes")
        else:
            print("❌ Hash igual para parâmetros diferentes")
        
        # Teste hash ST interestadual
        hash4 = service._gerar_hash_consulta(
            'interestadual', '8708', 
            estado_origem='SP', estado_destino='MG', destinacao_mercadoria='Revenda'
        )
        hash5 = service._gerar_hash_consulta(
            'interestadual', '8708', 
            estado_origem='SP', estado_destino='RJ', destinacao_mercadoria='Revenda'
        )
        
        if hash4 != hash5:
            print("✅ Hash interestadual diferente para estados diferentes")
        else:
            print("❌ Hash interestadual igual para estados diferentes")
            
        print(f"   Exemplo hash ST interna: {hash1[:16]}...")
        print(f"   Exemplo hash ST interestadual: {hash4[:16]}...")
        
    except Exception as e:
        print(f"❌ Erro no teste de cache: {str(e)}")

def test_validation_logic():
    """Testa lógica de validação (sem banco de dados)"""
    print("\n=== Teste de Lógica de Validação (Simulado) ===")
    
    try:
        from services.icms_st_validation_service import ICMSSTValidationService
        service = ICMSSTValidationService()
        
        print("\n1. Testando estrutura do serviço...")
        
        # Verificar se métodos existem
        metodos_esperados = [
            '_gerar_hash_consulta',
            '_consultar_api_st_interna', 
            '_consultar_api_st_interestadual',
            'obter_dados_icms_st',
            'validar_cenarios_icms_st',
            '_validar_cenario_individual',
            'aplicar_sugestao',
            'obter_historico_validacoes'
        ]
        
        for metodo in metodos_esperados:
            if hasattr(service, metodo):
                print(f"   ✅ Método {metodo} existe")
            else:
                print(f"   ❌ Método {metodo} não encontrado")
        
        print("\n2. Testando configurações...")
        print(f"   ✅ API Token configurado: {service.API_TOKEN[:10]}...")
        print(f"   ✅ Cliente ID: {service.CLIENTE_ID}")
        print(f"   ✅ URL ST Interna: {service.API_BASE_URL_INTERNA}")
        print(f"   ✅ URL ST Interestadual: {service.API_BASE_URL_INTERESTADUAL}")
        
    except Exception as e:
        print(f"❌ Erro no teste de validação: {str(e)}")

def test_database_models():
    """Testa modelos do banco de dados (sem conexão real)"""
    print("\n=== Teste de Modelos do Banco (Estrutura) ===")
    
    try:
        from models.icms_st_cache import ICMSSTCacheModel
        from models.icms_st_validation_result import ICMSSTValidationResultModel
        
        print("\n1. Testando modelo ICMSSTCacheModel...")
        
        # Verificar atributos do modelo de cache
        atributos_cache = [
            'tipo_st', 'ncm', 'estado', 'estado_origem', 'estado_destino',
            'cest', 'descricao', 'aliquota_interna', 'aliquota_interestadual',
            'mva', 'mva_ajustada', 'hash_consulta', 'data_consulta'
        ]
        
        for attr in atributos_cache:
            if hasattr(ICMSSTCacheModel, attr):
                print(f"   ✅ Atributo {attr} existe no modelo de cache")
            else:
                print(f"   ❌ Atributo {attr} não encontrado no modelo de cache")
        
        # Verificar métodos do modelo de cache
        metodos_cache = ['buscar_st_interna', 'buscar_st_interestadual', 'to_dict']
        for metodo in metodos_cache:
            if hasattr(ICMSSTCacheModel, metodo):
                print(f"   ✅ Método {metodo} existe no modelo de cache")
            else:
                print(f"   ❌ Método {metodo} não encontrado no modelo de cache")
        
        print("\n2. Testando modelo ICMSSTValidationResultModel...")
        
        # Verificar atributos do modelo de resultado
        atributos_resultado = [
            'empresa_id', 'cenario_id', 'dados_originais', 'sugestoes',
            'tipo_validacao', 'status', 'data_validacao'
        ]
        
        for attr in atributos_resultado:
            if hasattr(ICMSSTValidationResultModel, attr):
                print(f"   ✅ Atributo {attr} existe no modelo de resultado")
            else:
                print(f"   ❌ Atributo {attr} não encontrado no modelo de resultado")
        
    except Exception as e:
        print(f"❌ Erro no teste de modelos: {str(e)}")

def test_integration_flow():
    """Testa fluxo de integração completo (simulado)"""
    print("\n=== Teste de Fluxo de Integração ===")
    
    try:
        from services.icms_st_validation_service import ICMSSTValidationService
        service = ICMSSTValidationService()
        
        print("\n1. Simulando fluxo ST Interna...")
        
        # Simular dados de entrada
        ncm = "87089200"
        estado_empresa = "SP"
        estado_cliente = "SP"
        destinacao = "Revenda"
        cest = "17.096.00"
        
        print(f"   Entrada: NCM={ncm}, Empresa={estado_empresa}, Cliente={estado_cliente}")
        print(f"   Destinação: {destinacao}, CEST: {cest}")
        
        # Determinar tipo
        tipo_st = 'interna' if estado_empresa == estado_cliente else 'interestadual'
        print(f"   ✅ Tipo ST determinado: {tipo_st}")
        
        # Gerar hash
        if tipo_st == 'interna':
            hash_consulta = service._gerar_hash_consulta(tipo_st, ncm, estado=estado_empresa)
        else:
            hash_consulta = service._gerar_hash_consulta(
                tipo_st, ncm, 
                estado_origem=estado_empresa, 
                estado_destino=estado_cliente, 
                destinacao_mercadoria=destinacao
            )
        
        print(f"   ✅ Hash gerado: {hash_consulta[:16]}...")
        
        print("\n2. Simulando fluxo ST Interestadual...")
        
        estado_cliente_inter = "MG"
        tipo_st_inter = 'interna' if estado_empresa == estado_cliente_inter else 'interestadual'
        
        print(f"   Entrada: NCM={ncm}, Empresa={estado_empresa}, Cliente={estado_cliente_inter}")
        print(f"   ✅ Tipo ST determinado: {tipo_st_inter}")
        
        # Verificar mapeamento de destinação
        codigo_destinacao = service.DESTINACAO_MAPPING.get(destinacao)
        print(f"   ✅ Destinação mapeada: {destinacao} -> {codigo_destinacao}")
        
    except Exception as e:
        print(f"❌ Erro no teste de integração: {str(e)}")

def main():
    """Executa todos os testes"""
    print("🚀 Iniciando testes de validação ICMS-ST")
    print("=" * 60)
    
    # Executar testes em ordem
    if not test_imports():
        print("\n❌ Falha nos imports básicos. Verifique a instalação.")
        return
    
    test_mapping_logic()
    test_cache_functionality()
    test_validation_logic()
    test_database_models()
    test_integration_flow()
    test_api_connection()
    
    print("\n" + "=" * 60)
    print("🏁 Testes concluídos!")
    print("\n📋 Próximos passos:")
    print("1. Executar migração do banco:")
    print("   psql -d sua_base_dados -f db/migration_icms_st_validation.sql")
    print("2. Testar interface web:")
    print("   Acessar: /fiscal/cenarios/saida/icms_st")
    print("3. Verificar logs da aplicação para possíveis erros")
    print("4. Testar com dados reais de uma empresa")
    
    print("\n🔧 Filtros implementados:")
    print("- ST Interna: NCM + Estado")
    print("- ST Interestadual: NCM + Estado Origem + Estado Destino + Destinação")
    print("- CEST: Usado como filtro pós-consulta para dados específicos")

if __name__ == "__main__":
    main()
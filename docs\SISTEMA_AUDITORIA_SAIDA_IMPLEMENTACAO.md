# Sistema de Auditoria de Saída - Implementação Completa

## 📋 Resumo das Funcionalidades

Este documento descreve como o sistema realiza a auditoria de notas fiscais de **saída**, calculando os tributos conforme cenários cadastrados e registrando os resultados para consulta posterior.

## 🗄️ Estrutura de Banco de Dados

### Tabelas Envolvidas

1. **`tributo`**
   - Armazena informações dos itens das notas e dos tributos calculados.
   - Campo `tipo_operacao` indica **0 = entrada** e **1 = saída**.
   - Campos de auditoria por tributo (`auditoria_icms_status`, `auditoria_ipi_status`, etc.).

2. **`auditoria_resultado`**
   - Guarda o comparativo nota x cenário para cada tributo.
   - Principais colunas: `tributo_id`, `tipo_tributo`, `valor_nota`, `valor_calculado`, `status`.

3. **`auditoria_sumario`**
   - Resumo mensal por empresa, ano, mês e tipo de tributo.
   - Contadores de notas conforme e divergentes.

4. **Tabelas de Cenário** (`cenario_icms`, `cenario_icms_st`, `cenario_ipi`, `cenario_pis`, `cenario_cofins`, `cenario_difal`)
   - Possuem campos `direcao` e `tipo_operacao` para diferenciar entrada e saída.

## 🔧 Backend - Serviços

### `AuditoriaService` (`back/services/auditoria_service.py`)

- Orquestra a auditoria de todos os tributos.
- Recebe os filtros de **empresa**, **tipo_tributo** e **tipo_operacao** (`'1'` para saída).
- Executa as rotinas de cálculo na ordem IPI → ICMS → ICMS-ST → PIS → COFINS → DIFAL.

### `TributoCalculationService`

- Implementa as fórmulas para cada tributo.
- Busca o cenário vigente conforme `empresa_id`, `cliente_id`, `produto_id` e `tipo_operacao`.
- Exemplos de cálculo:

```python
# ICMS
base_calculo, valor_icms = TributoCalculationService.calcular_icms(
    valor_total,
    cenario_icms,
    valor_ipi,
    cliente_uso_consumo_ativo,
    valor_frete,
    valor_desconto
)
```

```python
# PIS
base_calculo, valor_pis = TributoCalculationService.calcular_pis(
    valor_total,
    cenario_pis,
    valor_icms,
    valor_frete,
    valor_desconto
)
```

## 🌐 Rotas API Principais

- `POST /api/auditoria/executar` – inicia auditoria para um período. Parâmetros:
  `empresa_id`, `tipo_tributo`, `tipo_operacao`, `year`, `month`.
- `GET /api/auditoria/tributos` – lista tributos auditados filtrando por direção
  (`direcao=saida`).
- `GET /api/auditoria/resultados` – consulta resultados gravados.

## 🔄 Fluxo de Trabalho

1. **Importação de XMLs**
   - O serviço de importação determina `tipo_operacao='1'` quando a empresa é
     emitente e `tpNF = 1`.
   - Os tributos dos itens são gravados na tabela `tributo`.

2. **Execução da Auditoria**
   - O usuário solicita a auditoria de saída via endpoint.
   - `AuditoriaService` filtra tributos com `tipo_operacao='1'` e aplica os cálculos.
   - Cada tributo gera registros em `auditoria_resultado` e atualiza `auditoria_sumario`.

3. **Análise e Aprovação**
   - Os resultados podem ser consultados na interface, permitindo verificar as divergências por tributo.
   - Status possíveis: `conforme`, `inconsistente` ou `pendente`.

## 📊 Regras de Cálculo (Resumo)

- **IPI**: aplicado quando o cenário possui CST `50`. Base = valor total da mercadoria.
- **ICMS**: considera redução da base (`p_red_bc`), inclusão de frete/desconto e, quando apropriado, o valor do IPI.
- **ICMS-ST**: aplica MVA (`icms_st_p_mva`) e subtrai o ICMS próprio da operação.
- **PIS/COFINS**: base reduz o ICMS e segue as alíquotas do cenário.
- **DIFAL**: calcula diferença de alíquotas interestaduais com partilha, quando configurado.

As fórmulas completas encontram-se em `back/services/tributo_calculation_service.py`.

## 🔒 Segurança

- Endpoints protegidos por JWT (`flask_jwt_extended`).
- Usuário só acessa empresas/autorizadas conforme permissões.
- Todas as execuções registram usuário e data na tabela `auditoria_resultado`.

## 📝 Execução Inicial

1. Execute as migrações existentes para criar as tabelas de auditoria e cenários.
2. Importe os XMLs de saída para popular a tabela `tributo`.
3. Utilize o endpoint `/api/auditoria/executar` especificando `tipo_operacao='1'`.

---

Com essa estrutura, o sistema consegue auditar notas de **saída**, comparar os tributos calculados com os valores das notas e registrar divergências para posterior análise.
import React, { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Modal } from '@/components/ui/Modal'
import { auditoriaService } from '@/services/auditoriaService'
import { useMutation } from '@tanstack/react-query'
import { useAuditoriaWebSocket } from '@/hooks/useAuditoriaWebSocket'

interface ExecutarAuditoriaButtonProps {
  empresaId: number
  tipoTributo: string
  year: number
  month: number
  onSuccess?: () => void
  onAuditStart?: (auditId: string) => void
}

export function ExecutarAuditoriaButton({ 
  empresaId, 
  tipoTributo, 
  year, 
  month, 
  onSuccess,
  onAuditStart
}: ExecutarAuditoriaButtonProps) {
  const [showModal, setShowModal] = useState(false)
  const [forcarRecalculo, setForcarRecalculo] = useState(true)

  const { subscribeToAudit } = useAuditoriaWebSocket({
    onComplete: () => {
      onSuccess?.()
    }
  })

  const executarMutation = useMutation({
    mutationFn: () => auditoriaService.executar({
      empresaId,
      tipoTributo,
      tipoOperacao: 'saida',
      year,
      month,
      forcarRecalculo
    }),
    onSuccess: (data) => {
      setShowModal(false)
      
      if (data.audit_id) {
        console.log('Auditoria iniciada com ID:', data.audit_id)
        
        // Inscrever-se no WebSocket para acompanhar o progresso
        subscribeToAudit(data.audit_id)
        
        // Notificar o componente pai que a auditoria começou
        onAuditStart?.(data.audit_id)
      }
    },
    onError: (error) => {
      console.error('Erro ao executar auditoria:', error)
    }
  })

  const getTributoDisplayName = (tipo: string) => {
    const names: Record<string, string> = {
      'icms': 'ICMS',
      'icms-st': 'ICMS-ST',
      'icms_st': 'ICMS-ST',
      'ipi': 'IPI',
      'pis': 'PIS',
      'cofins': 'COFINS',
      'difal': 'DIFAL',
    }
    return names[tipo] || tipo.toUpperCase()
  }

  return (
    <>
      <Button
        variant="primary"
        onClick={() => setShowModal(true)}
        icon={
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        }
        glow
      >
        Executar Auditoria
      </Button>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Executar Auditoria"
      >
        <div className="space-y-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
              Confirmar Execução da Auditoria
            </h4>
            <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <p><strong>Tributo:</strong> {getTributoDisplayName(tipoTributo)}</p>
              <p><strong>Período:</strong> {new Date(year, month - 1).toLocaleDateString('pt-BR', {
                month: 'long',
                year: 'numeric'
              })}</p>
              <p><strong>Tipo:</strong> Saída</p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="forcar-recalculo"
                checked={forcarRecalculo}
                onChange={(e) => setForcarRecalculo(e.target.checked)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="forcar-recalculo" className="text-sm text-gray-700 dark:text-gray-300">
                Forçar recálculo (recomendado)
              </label>
            </div>
            
            <p className="text-xs text-gray-600 dark:text-gray-400">
              O recálculo forçado garante que todos os cenários sejam recalculados com as regras mais atuais.
            </p>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div className="text-sm text-yellow-800 dark:text-yellow-200">
                <p className="font-medium mb-1">Importante:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>A auditoria pode levar alguns minutos para ser concluída</li>
                  <li>Você pode acompanhar o progresso em tempo real</li>
                  <li>Os dados do dashboard serão atualizados automaticamente</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="ghost"
              onClick={() => setShowModal(false)}
              disabled={executarMutation.isPending}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={() => executarMutation.mutate()}
              loading={executarMutation.isPending}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              }
            >
              {executarMutation.isPending ? 'Executando...' : 'Executar Auditoria'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}
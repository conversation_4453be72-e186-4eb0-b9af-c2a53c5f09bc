import { useState } from 'react'
import { Link } from 'react-router-dom'
import { Card } from '@/components/ui/Card'
import { HelpButton, HelpModal } from '@/components/ui'
import {
  ClipboardCheck,
  Scale,
  FileText,
  Percent,
  Factory,
  Coins,
  Gauge,
} from 'lucide-react'

export function AuditoriaEntradaPage() {
  const [showHelp, setShowHelp] = useState(false)

  return (
    <div className="space-y-8 p-6">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <ClipboardCheck className="w-8 h-8" />
            Auditoria de Entrada
          </h2>
          <HelpButton onClick={() => setShowHelp(true)} />
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          Execute auditorias comparativas entre XML, SPED e Cenários
        </p>
      </div>

      <HelpModal
        isOpen={showHelp}
        onClose={() => setShowHelp(false)}
        title="Auditoria de Entrada"
      >
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-400">
            A auditoria comparativa confronta dados de XML, SPED e cenários
            configurados para identificar divergências.
          </p>
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 dark:text-white">
              Tributos disponíveis
            </h4>
            <ul className="list-disc list-inside space-y-1 text-gray-600 dark:text-gray-400">
              <li>
                <Link
                  to="/fiscal/auditoria/entrada/auditoria/escrituracao"
                  className="text-primary-600 hover:underline"
                >
                  Escrituração
                </Link>
              </li>
              <li>
                <Link
                  to="/fiscal/auditoria/entrada/auditoria/icms"
                  className="text-primary-600 hover:underline"
                >
                  ICMS
                </Link>
              </li>
              <li>
                <Link
                  to="/fiscal/auditoria/entrada/auditoria/icms_st"
                  className="text-primary-600 hover:underline"
                >
                  ICMS-ST
                </Link>
              </li>
              <li>
                <Link
                  to="/fiscal/auditoria/entrada/auditoria/ipi"
                  className="text-primary-600 hover:underline"
                >
                  IPI
                </Link>
              </li>
              <li>
                <Link
                  to="/fiscal/auditoria/entrada/auditoria/pis"
                  className="text-primary-600 hover:underline"
                >
                  PIS
                </Link>
              </li>
              <li>
                <Link
                  to="/fiscal/auditoria/entrada/auditoria/cofins"
                  className="text-primary-600 hover:underline"
                >
                  COFINS
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 dark:text-white">
              Fluxo de análise
            </h4>
            <ol className="list-decimal list-inside space-y-1 text-gray-600 dark:text-gray-400">
              <li>Escolha o tributo ou a auditoria comparativa.</li>
              <li>Avalie as divergências apresentadas.</li>
              <li>Finalize gerando relatórios ou ajustando cenários.</li>
            </ol>
          </div>
        </div>
      </HelpModal>

      <Link
        to="/fiscal/auditoria/entrada/auditoria/escrituracao"
        className="block"
      >
        <Card
          hover
          padding="lg"
          className="flex flex-col items-center text-center gap-4 min-h-[160px]"
        >
          <div className="p-4 rounded-2xl bg-primary-600 text-white">
            <Scale className="w-8 h-8" />
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Auditoria Comparativa</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Compare dados entre XML, SPED e Cenários configurados para
              identificar divergências
            </p>
          </div>
        </Card>
      </Link>

      <div className="space-y-6">
        <div className="space-y-1">
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <Gauge className="w-6 h-6" />
            Auditoria por Tributo
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Acesso direto à auditoria de tributos específicos
          </p>
        </div>

        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <Link
            to="/fiscal/auditoria/entrada/auditoria/escrituracao"
            className="block"
          >
            <Card
              hover
              padding="lg"
              className="flex flex-col items-center text-center gap-2 min-h-[140px]"
            >
              <div className="p-4 rounded-2xl bg-blue-600 text-white">
                <FileText className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-medium">Escrituração</h3>
            </Card>
          </Link>

          <Link to="/fiscal/auditoria/entrada/auditoria/icms" className="block">
            <Card
              hover
              padding="lg"
              className="flex flex-col items-center text-center gap-2 min-h-[140px]"
            >
              <div className="p-4 rounded-2xl bg-primary-600 text-white">
                <Percent className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-medium">ICMS</h3>
            </Card>
          </Link>

          <Link to="/fiscal/auditoria/entrada/auditoria/icms_st" className="block">
            <Card
              hover
              padding="lg"
              className="flex flex-col items-center text-center gap-2 min-h-[140px]"
            >
              <div className="p-4 rounded-2xl bg-success-600 text-white">
                <Percent className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-medium">ICMS-ST</h3>
            </Card>
          </Link>

          <Link to="/fiscal/auditoria/entrada/auditoria/ipi" className="block">
            <Card
              hover
              padding="lg"
              className="flex flex-col items-center text-center gap-2 min-h-[140px]"
            >
              <div className="p-4 rounded-2xl bg-warning-600 text-white">
                <Factory className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-medium">IPI</h3>
            </Card>
          </Link>

          <Link to="/fiscal/auditoria/entrada/auditoria/pis" className="block">
            <Card
              hover
              padding="lg"
              className="flex flex-col items-center text-center gap-2 min-h-[140px]"
            >
              <div className="p-4 rounded-2xl bg-error-600 text-white">
                <Coins className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-medium">PIS</h3>
            </Card>
          </Link>

          <Link to="/fiscal/auditoria/entrada/auditoria/cofins" className="block">
            <Card
              hover
              padding="lg"
              className="flex flex-col items-center text-center gap-2 min-h-[140px]"
            >
              <div className="p-4 rounded-2xl bg-secondary-600 text-white">
                <Coins className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-medium">COFINS</h3>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default AuditoriaEntradaPage
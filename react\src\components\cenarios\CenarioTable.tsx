import { useState, useEffect, useCallback } from 'react'
import type { CenarioTributo, TipoTributo } from '@/types/cenarios'
import { CenarioModal } from './CenarioModal'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { ICMSTable } from './tables/ICMSTable'
import { ICMSSTTable } from './tables/ICMSSTTable'
import { IPITable } from './tables/IPITable'
import { PISTable } from './tables/PISTable'
import { COFINSTable } from './tables/COFINSTable'
import { DIFALTable } from './tables/DIFALTable'
import { cenarioActionsService } from '@/services/cenarioActionsService'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { BulkEditModal } from './BulkEditModal'
import { GlobalSelectionModal } from './GlobalSelectionModal'

interface CenarioTableProps {
  cenarios: CenarioTributo[]
  tipoTributo: TipoTributo
  isLoading?: boolean
  status?: 'novo' | 'producao' | 'inconsistente'
  totalCount?: number
}

export function CenarioTable({ cenarios, tipoTributo, isLoading, status = 'novo', totalCount: initialTotal = 0 }: CenarioTableProps) {
  const [filteredCenarios, setFilteredCenarios] = useState<CenarioTributo[]>(cenarios)
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedCenario, setSelectedCenario] = useState<CenarioTributo | null>(null)
  const [selectedCenarios, setSelectedCenarios] = useState<number[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [bulkEditOpen, setBulkEditOpen] = useState(false)
  const [bulkEditScope, setBulkEditScope] = useState<'selected' | 'all'>('selected')
  const [currentFilters, setCurrentFilters] = useState<any>({})
  const [totalCount, setTotalCount] = useState(initialTotal)
  const [hasMore, setHasMore] = useState(initialTotal > cenarios.length)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [globalSelectionModal, setGlobalSelectionModal] = useState<{
    isOpen: boolean
    actionType: 'production' | 'inconsistent' | 'delete' | 'edit'
  }>({ isOpen: false, actionType: 'production' })
  const empresaId = useSelectedCompany()

  // Sincronizar cenários recebidos com o estado interno
  useEffect(() => {
    setFilteredCenarios(cenarios)
    const total = initialTotal || cenarios.length
    setTotalCount(total)
    setHasMore(total > cenarios.length)
  }, [cenarios, initialTotal])
  // Função para lidar com dados filtrados
  const handleFilteredDataChange = useCallback((
    newFilters: any,
    newFilteredData: CenarioTributo[],
    total: number,
    hasMoreData: boolean
  ) => {
    // Forçar atualização dos dados filtrados
    setFilteredCenarios([...newFilteredData]) // Criar nova referência para forçar re-render
    setTotalCount(total || newFilteredData.length) // Usar length se total for 0
    setHasMore(hasMoreData)
    setCurrentFilters(newFilters)

    // Limpar seleções quando os dados mudarem
    setSelectedCenarios([])
  }, [])

  // Função para lidar com ações globais
  const handleGlobalAction = useCallback(async (
    actionType: 'production' | 'inconsistent' | 'delete',
    applyToAll: boolean
  ) => {
    if (!empresaId) return

    setIsProcessing(true)
    try {
      if (applyToAll) {
        switch (actionType) {
          case 'production':
            await cenarioActionsService.updateStatusByFilters(
              tipoTributo,
              { ...currentFilters, status },
              'producao',
              empresaId
            )
            break
          case 'inconsistent':
            await cenarioActionsService.updateStatusByFilters(
              tipoTributo,
              { ...currentFilters, status },
              'inconsistente',
              empresaId
            )
            break
          case 'delete':
            await cenarioActionsService.deleteByFilters(
              tipoTributo,
              { ...currentFilters, status },
              empresaId
            )
            break
        }
      } else {
        switch (actionType) {
          case 'production':
            await cenarioActionsService.batchUpdateStatus(
              tipoTributo,
              selectedCenarios,
              'producao',
              empresaId
            )
            break
          case 'inconsistent':
            await cenarioActionsService.batchUpdateStatus(
              tipoTributo,
              selectedCenarios,
              'inconsistente',
              empresaId
            )
            break
          case 'delete':
            for (const id of selectedCenarios) {
              await cenarioActionsService.deleteCenario(tipoTributo, id, empresaId)
            }
            break
        }
      }

      // Recarregar dados
      window.location.reload()
    } catch (error) {
      alert(`Erro ao executar a ação. Tente novamente.`)
    } finally {
      setIsProcessing(false)
    }
  }, [empresaId, selectedCenarios, tipoTributo, currentFilters, status])

  // Função para carregar mais dados
  const handleLoadMore = useCallback(async () => {
    if (!hasMore || isLoadingMore) return

    setIsLoadingMore(true)
    try {
      // Esta função será implementada nas tabelas específicas
      // TODO: Implementar loadMore do hook useAdvancedFilters
    } catch (error) {
      alert('Erro ao carregar mais dados. Tente novamente.')
    } finally {
      setIsLoadingMore(false)
    }
  }, [hasMore, isLoadingMore])

  const handleEdit = (cenario: CenarioTributo) => {
    setSelectedCenario(cenario)
    setModalOpen(true)
  }

  const handleSave = async (cenario: CenarioTributo) => {
    if (!empresaId) {
      alert('Por favor, selecione uma empresa antes de salvar o cenário.')
      return
    }

    setIsProcessing(true)
    try {
      const result = await cenarioActionsService.updateCenario(
        tipoTributo,
        cenario,
        empresaId
      )

      if (result.success) {
        let message = result.message || 'Cenário atualizado com sucesso!'
        if (result.recalculo_realizado) {
          message += `\n\nOs valores de ${
            result.tipo_tributo_recalculado?.toUpperCase() || ''
          } foram recalculados automaticamente devido às alterações em campos críticos.`
        }
        alert(message)
        window.location.reload()
      } else {
        alert(result.message || 'Erro ao atualizar cenário.')
      }
    } catch (error: any) {
      alert(error.message || 'Erro ao atualizar cenário.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSendToProduction = async (cenario: CenarioTributo) => {
    if (!empresaId) {
      alert('Por favor, selecione uma empresa antes de atualizar o status do cenário.')
      return
    }

    // Se há cenários não visíveis e há seleções, mostrar modal de seleção global
    if (totalCount > filteredCenarios.length && selectedCenarios.length > 0) {
      setGlobalSelectionModal({ isOpen: true, actionType: 'production' })
      return
    }

    const statusText = 'produção'
    if (!confirm(`Deseja realmente enviar este cenário para ${statusText}?`)) {
      return
    }

    setIsProcessing(true)
    try {
      const result = await cenarioActionsService.updateCenarioStatus(
        tipoTributo,
        cenario.id,
        'producao',
        empresaId
      )

      if (result.success) {
        alert(result.message || 'Status atualizado com sucesso!')
        // TODO: Recarregar dados das tabelas
        window.location.reload()
      } else {
        alert(result.message || 'Erro ao atualizar status do cenário.')
      }
    } catch (error: any) {
      alert(error.message || 'Erro ao atualizar status do cenário.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleMarkAsInconsistent = async (cenario: CenarioTributo) => {
    if (!empresaId) {
      alert('Por favor, selecione uma empresa antes de atualizar o status do cenário.')
      return
    }

    const statusText = 'inconsistente'
    if (!confirm(`Deseja realmente marcar este cenário como ${statusText}?`)) {
      return
    }

    setIsProcessing(true)
    try {
      const result = await cenarioActionsService.updateCenarioStatus(
        tipoTributo,
        cenario.id,
        'inconsistente',
        empresaId
      )

      if (result.success) {
        alert(result.message || 'Status atualizado com sucesso!')
        // TODO: Recarregar dados das tabelas
        window.location.reload()
      } else {
        alert(result.message || 'Erro ao atualizar status do cenário.')
      }
    } catch (error: any) {
      alert(error.message || 'Erro ao atualizar status do cenário.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleDelete = async (cenario: CenarioTributo) => {
    if (!empresaId) {
      alert('Por favor, selecione uma empresa antes de excluir o cenário.')
      return
    }

    if (!confirm('Tem certeza que deseja excluir este cenário? Esta ação não pode ser desfeita.')) {
      return
    }

    setIsProcessing(true)
    try {
      const result = await cenarioActionsService.deleteCenario(
        tipoTributo,
        cenario.id,
        empresaId
      )

      if (result.success) {
        alert(result.message || 'Cenário excluído com sucesso!')
        // TODO: Recarregar dados das tabelas
        window.location.reload()
      } else {
        alert(result.message || 'Erro ao excluir cenário.')
      }
    } catch (error: any) {
      alert(error.message || 'Erro ao excluir cenário.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCenarios(filteredCenarios.map(c => c.id))
    } else {
      setSelectedCenarios([])
    }
  }

  const handleSelectCenario = (cenarioId: number, checked: boolean) => {
    if (checked) {
      setSelectedCenarios(prev => [...prev, cenarioId])
    } else {
      setSelectedCenarios(prev => prev.filter(id => id !== cenarioId))
    }
  }

  const handleBulkSendToProduction = async () => {
    if (!empresaId) {
      alert('Por favor, selecione uma empresa antes de atualizar os cenários.')
      return
    }

    if (selectedCenarios.length === 0) {
      alert('Nenhum cenário selecionado.')
      return
    }

    if (totalCount > filteredCenarios.length) {
      setGlobalSelectionModal({ isOpen: true, actionType: 'production' })
      return
    }

    const statusText = 'produção'
    if (!confirm(`Deseja realmente enviar ${selectedCenarios.length} cenários para ${statusText}? Esta ação não pode ser desfeita.`)) {
      return
    }

    setIsProcessing(true)
    try {
      const result = await cenarioActionsService.batchUpdateStatus(
        tipoTributo,
        selectedCenarios,
        'producao',
        empresaId
      )

      if (result.success) {
        const updatedCount = result.updated_count || selectedCenarios.length
        let message = `${updatedCount} cenários foram atualizados para ${statusText} com sucesso!`

        if (result.task_id) {
          message += ' A operação está sendo processada em segundo plano.'
        }

        alert(message)

        // Limpar seleções e recarregar dados
        setSelectedCenarios([])
        window.location.reload()
      } else {
        alert(result.message || 'Erro ao atualizar cenários.')
      }
    } catch (error: any) {
      alert(error.message || 'Erro ao atualizar cenários.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBulkMarkAsInconsistent = async () => {
    if (!empresaId) {
      alert('Por favor, selecione uma empresa antes de atualizar os cenários.')
      return
    }

    if (selectedCenarios.length === 0) {
      alert('Nenhum cenário selecionado.')
      return
    }

    if (totalCount > filteredCenarios.length) {
      setGlobalSelectionModal({ isOpen: true, actionType: 'inconsistent' })
      return
    }

    const statusText = 'inconsistente'
    if (!confirm(`Deseja realmente marcar ${selectedCenarios.length} cenários como ${statusText}?`)) {
      return
    }

    setIsProcessing(true)
    try {
      const result = await cenarioActionsService.batchUpdateStatus(
        tipoTributo,
        selectedCenarios,
        'inconsistente',
        empresaId
      )

      if (result.success) {
        const updatedCount = result.updated_count || selectedCenarios.length
        let message = `${updatedCount} cenários foram marcados como ${statusText} com sucesso!`

        if (result.task_id) {
          message += ' A operação está sendo processada em segundo plano.'
        }

        alert(message)

        // Limpar seleções e recarregar dados
        setSelectedCenarios([])
        window.location.reload()
      } else {
        alert(result.message || 'Erro ao atualizar cenários.')
      }
    } catch (error: any) {
      alert(error.message || 'Erro ao atualizar cenários.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBulkEdit = () => {
    if (selectedCenarios.length === 0) {
      alert('Nenhum cenário selecionado.')
      return
    }
    if (totalCount > filteredCenarios.length) {
      setGlobalSelectionModal({ isOpen: true, actionType: 'edit' })
      return
    }
    setBulkEditScope('selected')
    setBulkEditOpen(true)
  }

  const handleBulkEditSave = async (changes: { ncm?: string; cst?: string; aliquota?: number }) => {
    if (!empresaId) {
      alert('Por favor, selecione uma empresa antes de atualizar os cenários.')
      return
    }

    if (bulkEditScope === 'selected' && selectedCenarios.length === 0) {
      alert('Nenhum cenário selecionado.')
      return
    }

    setIsProcessing(true)
    try {
      let result
      if (bulkEditScope === 'all') {
        result = await cenarioActionsService.bulkEditByFilters(
          tipoTributo,
          { ...currentFilters, status },
          changes,
          empresaId
        )
      } else {
        result = await cenarioActionsService.bulkEditCenarios(
          tipoTributo,
          selectedCenarios,
          changes,
          empresaId
        )
      }

      if (result.success) {
        const updatedCount =
          result.updated_count || (bulkEditScope === 'all' ? totalCount : selectedCenarios.length)
        alert(`${updatedCount} cenários atualizados com sucesso!`)
        setSelectedCenarios([])
        window.location.reload()
      } else {
        alert(result.message || 'Erro ao atualizar cenários.')
      }
    } catch (error: any) {
      alert(error.message || 'Erro ao atualizar cenários.')
    } finally {
      setIsProcessing(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <div className="ml-4 text-sm text-gray-600">
          Carregando cenários...
        </div>
      </div>
    )
  }

  if (!cenarios || cenarios.length === 0) {
    return (
      <div className="text-center py-12">
        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          Nenhum cenário encontrado
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Não há cenários para exibir nesta categoria.
        </p>
      </div>
    )
  }

  // Renderizar tabela específica baseada no tipo de tributo
  const renderTable = () => {
    const commonProps = {
      cenarios: filteredCenarios,
      selectedCenarios,
      onEdit: handleEdit,
      onSendToProduction: handleSendToProduction,
      onMarkAsInconsistent: handleMarkAsInconsistent,
      onDelete: handleDelete,
      onSelectCenario: handleSelectCenario,
      onSelectAll: handleSelectAll,
      status,
      onFilteredDataChange: handleFilteredDataChange,
      totalCount
    }

    switch (tipoTributo) {
      case 'icms':
        return <ICMSTable {...commonProps} />
      case 'icms_st':
        return <ICMSSTTable {...commonProps} />
      case 'ipi':
        return <IPITable {...commonProps} />
      case 'pis':
        return <PISTable {...commonProps} />
      case 'cofins':
        return <COFINSTable {...commonProps} />
      case 'difal':
        return <DIFALTable {...commonProps} />
      default:
        return <div>Tipo de tributo não suportado</div>
    }
  }

  return (
    <>
      {/* Toolbar de Ações */}
      <Card className="mb-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-primary-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {selectedCenarios.length} cenário(s) selecionado(s)
              </span>
            </div>
            
            {selectedCenarios.length > 0 && (
              <div className="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
            )}
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="secondary"
              size="sm"
              onClick={() => handleSelectAll(selectedCenarios.length !== cenarios.length)}
              disabled={isProcessing}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            >
              {selectedCenarios.length === cenarios.length ? 'Desmarcar Todos' : 'Selecionar Todos'}
            </Button>
            
            <Button
              variant="success"
              size="sm"
              onClick={handleBulkSendToProduction}
              disabled={selectedCenarios.length === 0 || isProcessing}
              loading={isProcessing}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              }
              glow={selectedCenarios.length > 0}
            >
              Enviar para Produção
            </Button>
            
            <Button
              variant="warning"
              size="sm"
              onClick={handleBulkMarkAsInconsistent}
              disabled={selectedCenarios.length === 0 || isProcessing}
              loading={isProcessing}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              }
            >
              Marcar Inconsistente
            </Button>
            
            <Button
              variant="blue"
              size="sm"
              onClick={handleBulkEdit}
              disabled={selectedCenarios.length === 0 || isProcessing}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              }
            >
              Editar em Massa
            </Button>
          </div>
        </div>
      </Card>

      <div className="w-full">
        {renderTable()}
      </div>

      {/* Informações de paginação e botão carregar mais */}
      {totalCount > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Mostrando {filteredCenarios.length} de {totalCount} cenários
              {selectedCenarios.length > 0 && (
                <span className="ml-2 text-primary-600 dark:text-primary-400">
                  • {selectedCenarios.length} selecionados
                </span>
              )}
            </div>

            <div className="flex items-center gap-3">
              {hasMore && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLoadMore}
                  disabled={isLoading || isLoadingMore}
                  loading={isLoadingMore}
                >
                  {isLoadingMore ? 'Carregando...' : 'Carregar Mais'}
                </Button>
              )}

              {selectedCenarios.length > 0 && totalCount > filteredCenarios.length && (
                <div className="text-xs bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 px-3 py-2 rounded-md">
                  ⚠️ Há mais {totalCount - filteredCenarios.length} cenários não visíveis com os filtros aplicados
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* Modal de edição */}
      <CenarioModal
        cenario={selectedCenario}
        tipoTributo={tipoTributo}
        isOpen={modalOpen}
        onClose={() => {
          setModalOpen(false)
          setSelectedCenario(null)
        }}
        onSave={handleSave}
      />
      
        <BulkEditModal
          isOpen={bulkEditOpen}
          onClose={() => setBulkEditOpen(false)}
          onSave={handleBulkEditSave}
        />

      {/* Modal de seleção global */}
      <GlobalSelectionModal
        isOpen={globalSelectionModal.isOpen}
        onClose={() => setGlobalSelectionModal({ ...globalSelectionModal, isOpen: false })}
        totalFilteredCount={totalCount}
        visibleCount={filteredCenarios.length}
        selectedCount={selectedCenarios.length}
        actionType={globalSelectionModal.actionType}
        onConfirm={(applyToAll) => {
          if (globalSelectionModal.actionType === 'edit') {
            setBulkEditScope(applyToAll ? 'all' : 'selected')
            setGlobalSelectionModal({ ...globalSelectionModal, isOpen: false })
            setBulkEditOpen(true)
          } else {
            handleGlobalAction(globalSelectionModal.actionType, applyToAll)
          }
        }}
      />
    </>
  )
}
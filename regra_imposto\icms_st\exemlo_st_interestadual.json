// ALém do óbvio precisamos salvar na base de dados, as informações de destino_produto, descricao, segmento, vigencia_inicial, vigencia_final, base_legal_st, norma_base_calculo, aplicabilidade e não aplicabilidade.

{
    "registros": 1,
    "resposta": [
        {
            "codigo": "269144",
            // estado empresa
            "sigla_estado_origem": "SP", 
            // estadp cliente
            "sigla_estado_destino": "MG",
            "regime_origem": null,
            "regime_destino": null,
            "destino_produto": "Op. Subsequente - Comercialização",
            // ncm pode vir assim, com pontuação ou completo
            "ncm": "8708",
            // cest do produto
            "cest": "01.075.00",
            "descricao": "Partes e acessórios dos veículos automóveis das posições 8701 a 8705",
            "observacao": "",
            "segmento": "Autopeças",
            "codigo_segmento": "27",
            // aliquota de fosse de sp para sp (nao precisamos)
            "aliquota_interna": "18.00",
            // aliquota de SP para MG
            "aliquota_interestadual": "12.00",
            "fundo_pobreza": null,
            // mva se fosse aliquota interna (nao precisamos)
            "mva": "71.78",
            // mva se for de SP para MG (exemplo)
            "mva_ajustada": 84.35,
            // mva se fosse de SP para MG e a aliquota_interestadual fosse 4
            "mva_ajustada_4": 101.11,
            "mva_positiva": null,
            "mva_negativa": null,
            "mva_neutra": null,
            "vigencia_inicial": "01/07/2023",
            "vigencia_final": null,
            "base_legal_st": "Protocolo ICMS Nº 41 DE 04/04/2008",
            "data_efeito_st": "01/05/2008",
            "norma_observacao_st": null,
            "norma_base_calculo": "Preventivamente deve ser considerado o MVA/IVA regulamentado internamente pelo estado.",
            "norma_prazo_recolhimento": null,
            "base_legal_int": "Capítulo 1, Parte 2, Anexo VII do RICMS/MG",
            "observacao_int": null,
            "base_calculo_int": "",
            "prazo_recolhimento_int": null,
            "aplicabilidade": "Aplica-se às operações com peças, partes, componentes, acessórios e demais produtos mencionados no caput da cláusula primeira do Protocolo nº 41/2008, de uso especificamente automotivo, assim compreendidos os que, em qualquer etapa do ciclo econômico do setor automotivo, sejam adquiridos ou revendidos por estabelecimento de indústria ou comércio de veículos automotores terrestres, bem como de veículos, máquinas e equipamentos agrícolas ou rodoviários, ou de suas peças, partes, componentes e acessórios, desde que a mercadoria objeto da operação interestadual esteja sujeita ao regime da substituição tributária nas operações internas no Estado de destino.\r\n\r\nAplica-se, também, às operações com os produtos mencionados destinados à:\r\n\r\nI - aplicação na renovação, recondicionamento ou beneficiamento de peças partes ou equipamentos;\r\n\r\nII - integração ao ativo imobilizado ou ao uso ou consumo do destinatário, relativamente ao imposto correspondente ao diferencial de alíquotas.",
            "nao_aplicabilidade": "Não se aplica às remessas de mercadoria com destino a:\r\n\r\nI - estabelecimento industrial;\r\n\r\nII - outro estabelecimento do mesmo titular, desde que não varejista, salvo se a unidade federada de destino dispuser de forma diferente em sua legislação.\r\n\r\nIII - estabelecimento localizado no Estado de São Paulo e que tenham origem no Distrito Federal.",
            "variacao_mva": [
                {
                    "descricao": "MVA aplicada nas saídas das mercadorias amparadas pelo contrato de fidelidade de compra de que trata o art. 8º da Lei Federal nº 6.729, de 28 de novembro de 1979.\r\n\r\nNos casos em que a alíquota interna for 12%, as MVA's serão as seguintes:\r\n- original: 36,56%\r\n- ajustada a 12%: 46,55%\r\n- ajustada a 4%: 48,97%",
                    "mva": "36.56",
                    "mva_ajustada": "",
                    "mva_ajustada_4": "59.88",
                    "mva_ajustada_7": "54.88",
                    "mva_ajustada_12": "46.55",
                    "mva_positiva": "",
                    "mva_negativa": "",
                    "mva_neutra": "",
                    "base_calculo": ""
                }
            ],
            "reducao_mva": ""
        }
    ]
}
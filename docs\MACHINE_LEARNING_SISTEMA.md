# Sistema de Machine Learning - Auditoria Fiscal

## 🤖 Visão Geral

O sistema de Machine Learning implementado no projeto de auditoria fiscal é projetado para **aprender padrões de auditoria** aprovados pelos usuários e **sugerir configurações automáticas** para futuras auditorias, aumentando significativamente a eficiência e precisão do processo.

## 🧠 Como Funciona

### 1. **Aprendizado de Padrões**

Quando um auditor **aprova uma configuração** de tributos durante a auditoria:

1. **Captura do Padrão**: Sistema registra a combinação:
   - Empresa + Cliente + Produto + CFOP + NCM
   - Alíquotas aprovadas (ICMS, ICMS-ST, IPI, PIS, COFINS)
   - Contexto da aprovação

2. **Armazenamento Inteligente**: Dados são salvos na tabela `historico_auditoria_entrada` com:
   - **Frequência de uso**: Quantas vezes foi usado
   - **Confiabilidade**: Score de 0.0 a 1.0
   - **Aprovação do usuário**: Confirmação manual

3. **Evolução Contínua**: A cada nova aprovação:
   - Frequência aumenta
   - Confiabilidade melhora
   - Padrão se torna mais "inteligente"

### 2. **Sistema de Sugestões**

O ML oferece **4 níveis de sugestões** por ordem de precisão:

#### 🎯 **Nível 1: Correspondência Exata**
- **Critério**: Mesmo cliente + produto + CFOP + NCM
- **Confiabilidade**: 90-100%
- **Uso**: Configuração idêntica já aprovada anteriormente

#### 🎯 **Nível 2: Produto + CFOP**
- **Critério**: Mesmo produto + CFOP (clientes diferentes)
- **Confiabilidade**: 70-80%
- **Uso**: Produto similar em operações similares

#### 🎯 **Nível 3: NCM + CFOP**
- **Critério**: Mesmo NCM + CFOP (produtos similares)
- **Confiabilidade**: 50-70%
- **Uso**: Produtos da mesma categoria fiscal

#### 🎯 **Nível 4: Padrões do Cliente**
- **Critério**: Mesmo cliente (produtos diferentes)
- **Confiabilidade**: 40-60%
- **Uso**: Padrões gerais do cliente

### 3. **Detecção de Anomalias**

O sistema **identifica automaticamente** situações suspeitas:

- **Desvios Significativos**: Alíquotas muito diferentes do padrão histórico
- **Configurações Incomuns**: Combinações nunca vistas antes
- **Alertas Inteligentes**: Notificações para revisão manual

## 🔧 Implementação Técnica

### **Serviço Principal**: `MachineLearningService`

```python
# Localização: back/services/machine_learning_service.py

class MachineLearningService:
    def registrar_padrao_aprovado(auditoria_id, usuario_id)
    def sugerir_configuracao(cliente_id, produto_id, cfop, ncm)
    def analisar_anomalias(mes, ano)
    def obter_estatisticas_ml()
```

### **APIs Disponíveis**

1. **`POST /api/auditoria-entrada/ml/aprovar-padrao`**
   - Registra padrão aprovado pelo usuário
   - Atualiza histórico de aprendizado

2. **`GET /api/auditoria-entrada/ml/sugestoes`**
   - Retorna sugestões baseadas em ML
   - Parâmetros: empresa_id, cliente_id, produto_id, cfop, ncm

3. **`GET /api/auditoria-entrada/ml/anomalias`**
   - Analisa anomalias no período
   - Parâmetros: empresa_id, mes, ano

4. **`GET /api/auditoria-entrada/ml/estatisticas`**
   - Estatísticas do sistema de ML
   - Parâmetros: empresa_id

## 📊 Estrutura de Dados

### **Tabela**: `historico_auditoria_entrada`

```sql
-- Chave de identificação do padrão
empresa_id, cliente_id, produto_id, cfop, ncm

-- Alíquotas padrão aprendidas
icms_aliquota_padrao, icms_st_aliquota_padrao, 
ipi_aliquota_padrao, pis_aliquota_padrao, cofins_aliquota_padrao

-- Métricas de aprendizado
frequencia_uso          -- Quantas vezes foi usado
confiabilidade          -- Score 0.0 a 1.0
aprovado_usuario        -- Confirmação manual
ultima_utilizacao       -- Timestamp da última vez
```

## 🚀 Como Utilizar

### **1. Para Auditores**

#### **Aprovando Padrões**:
```javascript
// Quando auditor aprova uma configuração
fetch('/api/auditoria-entrada/ml/aprovar-padrao', {
    method: 'POST',
    body: JSON.stringify({
        auditoria_id: 123
    })
});
```

#### **Obtendo Sugestões**:
```javascript
// Ao iniciar nova auditoria
fetch('/api/auditoria-entrada/ml/sugestoes?empresa_id=1&cliente_id=5&produto_id=10&cfop=1102&ncm=12345678')
.then(response => response.json())
.then(data => {
    // data.sugestoes contém as recomendações
    console.log('Sugestões ML:', data.sugestoes);
});
```

### **2. Para Gestores**

#### **Monitorando Anomalias**:
```javascript
// Verificar anomalias do mês
fetch('/api/auditoria-entrada/ml/anomalias?empresa_id=1&mes=10&ano=2024')
.then(response => response.json())
.then(data => {
    console.log('Anomalias encontradas:', data.anomalias);
});
```

#### **Estatísticas do Sistema**:
```javascript
// Dashboard de ML
fetch('/api/auditoria-entrada/ml/estatisticas?empresa_id=1')
.then(response => response.json())
.then(data => {
    console.log('Total de padrões:', data.total_padroes);
    console.log('Confiabilidade média:', data.confiabilidade_media);
    console.log('Taxa de aprovação:', data.taxa_aprovacao);
});
```

## 📈 Benefícios Práticos

### **Para Auditores**
- ⚡ **Velocidade**: Sugestões automáticas reduzem tempo de configuração
- 🎯 **Precisão**: Baseado em histórico real de aprovações
- 🔍 **Consistência**: Padrões uniformes entre auditorias
- 📚 **Aprendizado**: Sistema melhora com o uso

### **Para Gestores**
- 📊 **Visibilidade**: Estatísticas de uso e eficiência
- 🚨 **Alertas**: Detecção automática de anomalias
- 📈 **Melhoria Contínua**: Identificação de padrões de negócio
- 💰 **ROI**: Redução significativa de tempo de auditoria

### **Para a Empresa**
- 🤖 **Automação**: Menos intervenção manual necessária
- 📋 **Compliance**: Maior consistência nas auditorias
- 🔄 **Escalabilidade**: Sistema melhora automaticamente
- 💡 **Inteligência**: Insights baseados em dados reais

## 🔮 Evolução Futura

### **Fase 2: ML Avançado**
- **Algoritmos de Clustering**: Agrupamento automático de padrões similares
- **Redes Neurais**: Predição mais sofisticada de alíquotas
- **Análise Temporal**: Consideração de sazonalidade e tendências

### **Fase 3: IA Generativa**
- **Explicações Automáticas**: IA explica por que sugeriu determinada configuração
- **Relatórios Inteligentes**: Geração automática de relatórios de auditoria
- **Chatbot Especializado**: Assistente IA para dúvidas de auditoria

## 🛡️ Segurança e Confiabilidade

### **Validações Implementadas**
- ✅ **Aprovação Humana**: Todos os padrões precisam de confirmação manual
- ✅ **Limites de Confiabilidade**: Sugestões só aparecem acima de threshold mínimo
- ✅ **Rastreabilidade**: Histórico completo de todas as decisões
- ✅ **Reversibilidade**: Possibilidade de "desaprovar" padrões incorretos

### **Controles de Qualidade**
- 📊 **Métricas de Performance**: Acompanhamento da precisão das sugestões
- 🔍 **Auditoria do ML**: Revisão periódica dos padrões aprendidos
- 🚨 **Alertas de Desvio**: Notificação quando confiabilidade cai
- 📈 **Melhoria Contínua**: Ajustes baseados em feedback dos usuários

## 📝 Exemplo Prático

### **Cenário**: Auditoria de Nota de Entrada

1. **Situação**: Nova nota fiscal de entrada
   - Cliente: Fornecedor ABC Ltda
   - Produto: Matéria-prima XYZ
   - CFOP: 1102 (Compra para industrialização)
   - NCM: 12345678

2. **ML em Ação**:
   ```json
   {
     "sugestoes": [
       {
         "tipo": "exato",
         "confiabilidade": 0.95,
         "frequencia": 15,
         "icms_aliquota": 18.0,
         "icms_st_aliquota": 0.0,
         "ipi_aliquota": 5.0,
         "descricao": "Configuração exata encontrada no histórico"
       }
     ]
   }
   ```

3. **Resultado**: Auditor vê sugestão com 95% de confiabilidade e pode:
   - ✅ **Aceitar**: Aplicar automaticamente
   - ✏️ **Ajustar**: Modificar e aprovar novo padrão
   - ❌ **Rejeitar**: Configurar manualmente

4. **Aprendizado**: Sistema registra a decisão para futuras sugestões

---

**O sistema de ML está totalmente funcional e pronto para uso!** 🚀

Ele começará a aprender desde a primeira aprovação e ficará mais inteligente a cada uso, proporcionando uma experiência de auditoria cada vez mais eficiente e precisa.

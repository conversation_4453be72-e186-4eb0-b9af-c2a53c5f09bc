-- Migration to create tables for monthly tax apuração
BEGIN;

CREATE TABLE IF NOT EXISTS apuracao_icms (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id),
    ano INTEGER NOT NULL,
    mes INTEGER NOT NULL,
    tipo VARCHAR(10) NOT NULL,
    vl_tot_debitos NUMERIC(15,2),
    vl_aj_debitos NUMERIC(15,2),
    vl_tot_aj_debitos NUMERIC(15,2),
    vl_estornos_cred NUMERIC(15,2),
    vl_tot_creditos NUMERIC(15,2),
    vl_aj_creditos NUMERIC(15,2),
    vl_tot_aj_creditos NUMERIC(15,2),
    vl_estornos_deb NUMERIC(15,2),
    vl_sld_credor_ant NUMERIC(15,2),
    vl_sld_apurado NUMERIC(15,2),
    vl_tot_ded NUMERIC(15,2),
    vl_icms_recolher NUMERIC(15,2),
    vl_sld_credor_transportar NUMERIC(15,2),
    deb_esp NUMERIC(15,2),
    UNIQUE(empresa_id, ano, mes, tipo)
);

CREATE TABLE IF NOT EXISTS apuracao_ipi (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id),
    ano INTEGER NOT NULL,
    mes INTEGER NOT NULL,
    cfop VARCHAR(4),
    cst_ipi VARCHAR(2),
    vl_cont_ipi NUMERIC(15,2),
    vl_bc_ipi NUMERIC(15,2),
    vl_ipi NUMERIC(15,2),
    UNIQUE(empresa_id, ano, mes, cfop, cst_ipi)
);

COMMIT;
-- Migração para criar tabelas de cache e validação PIS/COFINS

CREATE TABLE IF NOT EXISTS pis_cofins_cache (
    id SERIAL PRIMARY KEY,
    ncm VARCHAR(20) NOT NULL,
    ncm_norm VARCHAR(20) NOT NULL,

    regime_tributario_origem_code VARCHAR(5) NOT NULL,
    atividade_origem_code VARCHAR(5) NOT NULL,
    atividade_destino_code VARCHAR(5),

    tributo VARCHAR(10) NOT NULL,
    cst_code VARCHAR(5),
    cst_desc TEXT,
    valor DECIMAL(10,4),
    tipo_valor VARCHAR(20),

    exibir_aliquota_padrao VARCHAR(5),
    aliquota_padrao_json JSON,

    regra VARCHAR(100),
    aplicabilidade TEXT,
    descricao_produto TEXT,
    id_base_legal VARCHAR(20),
    desc_base_legal TEXT,

    hash_consulta VARCHAR(64) NOT NULL,
    data_consulta TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_pis_cofins_lookup ON pis_cofins_cache (ncm_norm, regime_tributario_origem_code, atividade_origem_code);
CREATE INDEX IF NOT EXISTS idx_pis_cofins_hash ON pis_cofins_cache (hash_consulta);
CREATE INDEX IF NOT EXISTS idx_pis_cofins_data ON pis_cofins_cache (data_consulta);

-- Tabela para histórico de validações PIS/COFINS
CREATE TABLE IF NOT EXISTS pis_cofins_validation_results (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER NOT NULL REFERENCES empresa(id),
    cenario_id INTEGER NOT NULL,
    
    -- Dados originais do cenário (JSON)
    dados_originais JSONB NOT NULL,
    
    -- Sugestões de correção (JSON)
    sugestoes JSONB NOT NULL,
    
    -- Status da validação
    status VARCHAR(20) DEFAULT 'pendente' CHECK (status IN ('pendente', 'aplicado', 'ignorado')),
    
    -- Metadados
    data_validacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    aplicado_em TIMESTAMP,
    aplicado_por VARCHAR(100),
    
    -- Observações
    observacoes TEXT
);

-- Índices para histórico
CREATE INDEX IF NOT EXISTS idx_pis_cofins_validation_empresa ON pis_cofins_validation_results (empresa_id);
CREATE INDEX IF NOT EXISTS idx_pis_cofins_validation_cenario ON pis_cofins_validation_results (cenario_id);
CREATE INDEX IF NOT EXISTS idx_pis_cofins_validation_status ON pis_cofins_validation_results (status);
CREATE INDEX IF NOT EXISTS idx_pis_cofins_validation_data ON pis_cofins_validation_results (data_validacao);

-- Comentários nas tabelas
COMMENT ON TABLE pis_cofins_cache IS 'Cache local para consultas da API PIS/COFINS para evitar custos desnecessários';
COMMENT ON TABLE pis_cofins_validation_results IS 'Histórico de validações e correções aplicadas nos cenários PIS/COFINS';

-- Comentários nas colunas principais
COMMENT ON COLUMN pis_cofins_cache.hash_consulta IS 'Hash único da consulta para evitar duplicatas';
COMMENT ON COLUMN pis_cofins_cache.data_consulta IS 'Data da consulta para controle de expiração do cache';
COMMENT ON COLUMN pis_cofins_validation_results.dados_originais IS 'Dados originais do cenário antes da correção';
COMMENT ON COLUMN pis_cofins_validation_results.sugestoes IS 'Sugestões de correção aplicadas ou pendentes';
# 🧪 Teste de Autenticação - React

## Como testar o sistema de autenticação React

### 1. Iniciar o sistema

```bash
# Terminal 1: Iniciar <PERSON>k (backend)
cd back
python wsgi.py

# Terminal 2: Iniciar React (frontend)
cd react
npm install
npm run dev
```

### 2. Acessar página de teste

Abra o navegador em: **http://localhost:3000/test-auth**

### 3. Opções de teste

#### 🔄 **Simular Fluxo Completo**
- Clica no botão "Simular Fluxo Completo"
- Define o cookie com o token JWT
- Redireciona para `/login`
- LoginPage valida o token via `/fiscal/api/portal-login`
- Se válido, redireciona para `/dashboard`

#### ⚡ **Login Direto**
- Clica no botão "Login Direto"
- Define o cookie e chama a API diretamente
- Salva no localStorage
- Redireciona direto para `/dashboard`

### 4. Fluxo de autenticação

```mermaid
graph TD
    A[/test-auth] --> B[Define cookie com token]
    B --> C[/login]
    C --> D[LoginPage valida token]
    D --> E{Token válido?}
    E -->|Sim| F[Salva no store + localStorage]
    E -->|Não| G[Mostra botão do portal]
    F --> H[/dashboard]
    G --> I[Redireciona para portal]
```

### 5. Verificações

#### ✅ **Token válido**
- Usuário: Ricardo Samogin
- Role: admin
- Escritório: CONTTROLARE
- Permissão: Fiscal = true

#### ❌ **Token inválido**
- Mostra mensagem de erro
- Exibe botão "Ir para o Portal"

### 6. Debugging

#### **Console do navegador**
```javascript
// Verificar token no cookie
document.cookie

// Verificar localStorage
localStorage.getItem('token')
localStorage.getItem('currentUser')

// Verificar store do Zustand
// (use React DevTools)
```

#### **Network tab**
- Requisição para `/fiscal/api/portal-login`
- Headers incluem o cookie
- Resposta com `access_token` e dados do usuário

### 7. Compatibilidade

O sistema React mantém **100% compatibilidade** com o sistema atual:

- ✅ Mesma API: `/fiscal/api/portal-login`
- ✅ Mesmo cookie: `token`
- ✅ Mesmo localStorage: `token` + `currentUser`
- ✅ Mesmo JWT: validação com chave SSH
- ✅ Mesma estrutura de usuário

### 8. Próximos passos

Após validar a autenticação:

1. **Dashboard funcional** ✅
2. **Navegação entre páginas** (próximo)
3. **Integração com APIs** (próximo)
4. **WebSocket real-time** (próximo)

### 9. Troubleshooting

#### **Erro: Token inválido**
- Verificar se o Flask está rodando
- Verificar se a chave SSH está configurada
- Verificar se o token não expirou

#### **Erro: CORS**
- Verificar proxy no `vite.config.ts`
- Flask deve estar na porta 5000

#### **Erro: Cookie não enviado**
- Verificar `credentials: 'include'`
- Verificar `samesite=lax` no cookie

### 10. URLs importantes

- **Teste**: http://localhost:3000/test-auth
- **Login**: http://localhost:3000/login  
- **Dashboard**: http://localhost:3000/dashboard
- **API**: http://localhost:5000/fiscal/api/portal-login

---

## 🎯 Objetivo

Validar que o sistema React consegue:
1. ✅ Receber token via cookie
2. ✅ Validar com backend Flask
3. ✅ Gerenciar estado de autenticação
4. ✅ Redirecionar corretamente
5. ✅ Manter compatibilidade total
export interface XMLItem {
    id: number
    numero_nf: string
    data_emissao?: string
    data_entrada?: string
    participante_razao_social?: string
    participante_cnpj?: string
    status_validacao?: string
  }
  
  export interface NotaFaltante {
    id: number
    tipo: string
    chave_nf?: string
    numero_nf?: string
    numeros_faltantes?: number[]
    data_emissao?: string
    data_entrada?: string
    origem?: string
    observacao?: string
  }
  
  export interface XMLsResponse {
    success: boolean
    message?: string
    xmls?: XMLItem[]
    notas?: NotaFaltante[]
    primeira_nota?: { numero_nf?: string; data?: string }
    ultima_nota?: { numero_nf?: string; data?: string }
    total_faltantes?: number
  }
  
  export interface XMLDetalhesResponse {
    success: boolean
    xml: {
      id: number
      numero_nf: string
      data_emissao?: string
      data_entrada?: string
      tipo_nota?: string
      status_validacao?: string
      participante_razao_social?: string
      participante_cnpj?: string
    }
    produtos: {
      produto_nome: string
      cliente_nome?: string
      item: {
        cfop?: string
        ncm?: string
        quantidade?: number
        valor_unitario?: string
        valor_total?: string
      }
      tributo?: {
        icms_valor?: string
        icms_aliquota?: string
        icms_st_valor?: string
        ipi_valor?: string
        ipi_aliquota?: string
        pis_valor?: string
        pis_aliquota?: string
        cofins_valor?: string
        cofins_aliquota?: string
      }
    }[]
    totais: {
      valor_total_nota: number
      total_icms: number
      total_icms_st: number
      total_ipi: number
      total_pis: number
      total_cofins: number
    }
    estatisticas: {
      total_produtos: number
    }
  }
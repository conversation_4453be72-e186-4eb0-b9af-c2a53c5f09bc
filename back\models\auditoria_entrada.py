from .escritorio import db
from sqlalchemy.sql import func

class AuditoriaEntrada(db.Model):
    __tablename__ = 'auditoria_entrada'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.<PERSON>, db.<PERSON>('escritorio.id'), nullable=False)
    usuario_id = db.Column(db.Integer, db.<PERSON><PERSON>('usuario.id'))
    chave_nf = db.Column(db.String(44), nullable=False)
    numero_nf = db.Column(db.String(20))
    cliente_id = db.Column(db.Integer, db.ForeignKey('cliente.id'))
    produto_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>('produto.id'))
    
    # Dados do XML
    xml_data_emissao = db.Column(db.Date)
    xml_data_entrada = db.Column(db.Date)
    xml_cfop = db.Column(db.String(10))
    xml_ncm = db.Column(db.String(20))
    xml_valor_total = db.Column(db.Numeric(15, 2))
    xml_quantidade = db.Column(db.Numeric(15, 4))
    
    # Dados do SPED
    sped_data_documento = db.Column(db.Date)
    sped_data_entrada_saida = db.Column(db.Date)
    sped_cfop = db.Column(db.String(10))
    sped_ncm = db.Column(db.String(20))
    sped_valor_item = db.Column(db.Numeric(15, 2))
    sped_quantidade = db.Column(db.Numeric(15, 4))
    
    # Tributos XML
    xml_icms_aliquota = db.Column(db.Numeric(5, 2))
    xml_icms_valor = db.Column(db.Numeric(15, 2))
    xml_icms_st_aliquota = db.Column(db.Numeric(5, 2))
    xml_icms_st_valor = db.Column(db.Numeric(15, 2))
    xml_ipi_aliquota = db.Column(db.Numeric(5, 2))
    xml_ipi_valor = db.Column(db.Numeric(15, 2))
    xml_pis_aliquota = db.Column(db.Numeric(5, 4))
    xml_pis_valor = db.Column(db.Numeric(15, 2))
    xml_cofins_aliquota = db.Column(db.Numeric(5, 4))
    xml_cofins_valor = db.Column(db.Numeric(15, 2))
    
    # Tributos SPED
    sped_icms_aliquota = db.Column(db.Numeric(5, 2))
    sped_icms_valor = db.Column(db.Numeric(15, 2))
    sped_icms_st_aliquota = db.Column(db.Numeric(5, 2))
    sped_icms_st_valor = db.Column(db.Numeric(15, 2))
    sped_ipi_aliquota = db.Column(db.Numeric(5, 2))
    sped_ipi_valor = db.Column(db.Numeric(15, 2))
    sped_pis_aliquota = db.Column(db.Numeric(5, 4))
    sped_pis_valor = db.Column(db.Numeric(15, 2))
    sped_cofins_aliquota = db.Column(db.Numeric(5, 4))
    sped_cofins_valor = db.Column(db.Numeric(15, 2))
    
    # Tributos Cenário
    cenario_icms_aliquota = db.Column(db.Numeric(5, 2))
    cenario_icms_valor = db.Column(db.Numeric(15, 2))
    cenario_icms_st_aliquota = db.Column(db.Numeric(5, 2))
    cenario_icms_st_valor = db.Column(db.Numeric(15, 2))
    cenario_ipi_aliquota = db.Column(db.Numeric(5, 2))
    cenario_ipi_valor = db.Column(db.Numeric(15, 2))
    cenario_pis_aliquota = db.Column(db.Numeric(5, 4))
    cenario_pis_valor = db.Column(db.Numeric(15, 2))
    cenario_cofins_aliquota = db.Column(db.Numeric(5, 4))
    cenario_cofins_valor = db.Column(db.Numeric(15, 2))
    
    # Status de auditoria por tributo
    status_icms = db.Column(db.String(20), default='pendente')
    status_icms_st = db.Column(db.String(20), default='pendente')
    status_ipi = db.Column(db.String(20), default='pendente')
    status_pis = db.Column(db.String(20), default='pendente')
    status_cofins = db.Column(db.String(20), default='pendente')
    
    # Observações por tributo
    obs_icms = db.Column(db.Text)
    obs_icms_st = db.Column(db.Text)
    obs_ipi = db.Column(db.Text)
    obs_pis = db.Column(db.Text)
    obs_cofins = db.Column(db.Text)
    
    # Controle geral
    status_geral = db.Column(db.String(20), default='pendente')
    data_auditoria = db.Column(db.DateTime)
    usuario_auditoria = db.Column(db.Integer, db.ForeignKey('usuario.id'))

    # Campos para auditoria de escrituração
    status_escrituracao = db.Column(db.String(20), default='pendente')  # pendente, conforme, divergente, aprovado
    justificativa_escrituracao = db.Column(db.Text)
    data_aprovacao_escrituracao = db.Column(db.DateTime)
    usuario_aprovacao_escrituracao = db.Column(db.Integer, db.ForeignKey('usuario.id'))
    xml_valor_total_nota = db.Column(db.Numeric(15, 2))  # Valor total da nota no XML
    sped_valor_total_nota = db.Column(db.Numeric(15, 2))  # Valor total da nota no SPED
    divergencia_valor_total = db.Column(db.Numeric(15, 2))  # Diferença entre XML e SPED
    percentual_divergencia = db.Column(db.Numeric(5, 2))  # Percentual de divergência

    mes_referencia = db.Column(db.Integer, nullable=False)
    ano_referencia = db.Column(db.Integer, nullable=False)
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    data_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    empresa = db.relationship('Empresa', backref='auditorias_entrada')
    escritorio = db.relationship('Escritorio', backref='auditorias_entrada')
    usuario = db.relationship('Usuario', foreign_keys=[usuario_id], backref='auditorias_entrada_criadas')
    usuario_auditor = db.relationship('Usuario', foreign_keys=[usuario_auditoria], backref='auditorias_entrada_realizadas')
    usuario_aprovador_escrituracao = db.relationship('Usuario', foreign_keys=[usuario_aprovacao_escrituracao], backref='auditorias_entrada_aprovadas')
    cliente = db.relationship('Cliente', backref='auditorias_entrada')
    produto = db.relationship('Produto', backref='auditorias_entrada')
    
    # Constraint única
    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'chave_nf', 'produto_id', name='uq_auditoria_entrada_empresa_chave_produto'),
    )
    
    def __repr__(self):
        return f"<AuditoriaEntrada {self.numero_nf} - {self.chave_nf}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'chave_nf': self.chave_nf,
            'numero_nf': self.numero_nf,
            'cliente_id': self.cliente_id,
            'produto_id': self.produto_id,
            
            # Dados XML
            'xml_data_emissao': self.xml_data_emissao.isoformat() if self.xml_data_emissao else None,
            'xml_data_entrada': self.xml_data_entrada.isoformat() if self.xml_data_entrada else None,
            'xml_cfop': self.xml_cfop,
            'xml_ncm': self.xml_ncm,
            'xml_valor_total': float(self.xml_valor_total) if self.xml_valor_total else None,
            'xml_quantidade': float(self.xml_quantidade) if self.xml_quantidade else None,
            
            # Dados SPED
            'sped_data_documento': self.sped_data_documento.isoformat() if self.sped_data_documento else None,
            'sped_data_entrada_saida': self.sped_data_entrada_saida.isoformat() if self.sped_data_entrada_saida else None,
            'sped_cfop': self.sped_cfop,
            'sped_ncm': self.sped_ncm,
            'sped_valor_item': float(self.sped_valor_item) if self.sped_valor_item else None,
            'sped_quantidade': float(self.sped_quantidade) if self.sped_quantidade else None,
            
            # Tributos XML
            'xml_icms_aliquota': float(self.xml_icms_aliquota) if self.xml_icms_aliquota else None,
            'xml_icms_valor': float(self.xml_icms_valor) if self.xml_icms_valor else None,
            'xml_icms_st_aliquota': float(self.xml_icms_st_aliquota) if self.xml_icms_st_aliquota else None,
            'xml_icms_st_valor': float(self.xml_icms_st_valor) if self.xml_icms_st_valor else None,
            'xml_ipi_aliquota': float(self.xml_ipi_aliquota) if self.xml_ipi_aliquota else None,
            'xml_ipi_valor': float(self.xml_ipi_valor) if self.xml_ipi_valor else None,
            'xml_pis_aliquota': float(self.xml_pis_aliquota) if self.xml_pis_aliquota else None,
            'xml_pis_valor': float(self.xml_pis_valor) if self.xml_pis_valor else None,
            'xml_cofins_aliquota': float(self.xml_cofins_aliquota) if self.xml_cofins_aliquota else None,
            'xml_cofins_valor': float(self.xml_cofins_valor) if self.xml_cofins_valor else None,
            
            # Tributos SPED
            'sped_icms_aliquota': float(self.sped_icms_aliquota) if self.sped_icms_aliquota else None,
            'sped_icms_valor': float(self.sped_icms_valor) if self.sped_icms_valor else None,
            'sped_icms_st_aliquota': float(self.sped_icms_st_aliquota) if self.sped_icms_st_aliquota else None,
            'sped_icms_st_valor': float(self.sped_icms_st_valor) if self.sped_icms_st_valor else None,
            'sped_ipi_aliquota': float(self.sped_ipi_aliquota) if self.sped_ipi_aliquota else None,
            'sped_ipi_valor': float(self.sped_ipi_valor) if self.sped_ipi_valor else None,
            'sped_pis_aliquota': float(self.sped_pis_aliquota) if self.sped_pis_aliquota else None,
            'sped_pis_valor': float(self.sped_pis_valor) if self.sped_pis_valor else None,
            'sped_cofins_aliquota': float(self.sped_cofins_aliquota) if self.sped_cofins_aliquota else None,
            'sped_cofins_valor': float(self.sped_cofins_valor) if self.sped_cofins_valor else None,
            
            # Tributos Cenário
            'cenario_icms_aliquota': float(self.cenario_icms_aliquota) if self.cenario_icms_aliquota else None,
            'cenario_icms_valor': float(self.cenario_icms_valor) if self.cenario_icms_valor else None,
            'cenario_icms_st_aliquota': float(self.cenario_icms_st_aliquota) if self.cenario_icms_st_aliquota else None,
            'cenario_icms_st_valor': float(self.cenario_icms_st_valor) if self.cenario_icms_st_valor else None,
            'cenario_ipi_aliquota': float(self.cenario_ipi_aliquota) if self.cenario_ipi_aliquota else None,
            'cenario_ipi_valor': float(self.cenario_ipi_valor) if self.cenario_ipi_valor else None,
            'cenario_pis_aliquota': float(self.cenario_pis_aliquota) if self.cenario_pis_aliquota else None,
            'cenario_pis_valor': float(self.cenario_pis_valor) if self.cenario_pis_valor else None,
            'cenario_cofins_aliquota': float(self.cenario_cofins_aliquota) if self.cenario_cofins_aliquota else None,
            'cenario_cofins_valor': float(self.cenario_cofins_valor) if self.cenario_cofins_valor else None,
            
            # Status
            'status_icms': self.status_icms,
            'status_icms_st': self.status_icms_st,
            'status_ipi': self.status_ipi,
            'status_pis': self.status_pis,
            'status_cofins': self.status_cofins,
            'status_geral': self.status_geral,

            # Observações
            'obs_icms': self.obs_icms,
            'obs_icms_st': self.obs_icms_st,
            'obs_ipi': self.obs_ipi,
            'obs_pis': self.obs_pis,
            'obs_cofins': self.obs_cofins,

            # Escrituração
            'status_escrituracao': self.status_escrituracao,
            'justificativa_escrituracao': self.justificativa_escrituracao,
            'data_aprovacao_escrituracao': self.data_aprovacao_escrituracao.isoformat() if self.data_aprovacao_escrituracao else None,
            'usuario_aprovacao_escrituracao': self.usuario_aprovacao_escrituracao,
            'xml_valor_total_nota': float(self.xml_valor_total_nota) if self.xml_valor_total_nota else None,
            'sped_valor_total_nota': float(self.sped_valor_total_nota) if self.sped_valor_total_nota else None,
            'divergencia_valor_total': float(self.divergencia_valor_total) if self.divergencia_valor_total else None,
            'percentual_divergencia': float(self.percentual_divergencia) if self.percentual_divergencia else None,

            # Controle
            'mes_referencia': self.mes_referencia,
            'ano_referencia': self.ano_referencia,
            'data_auditoria': self.data_auditoria.isoformat() if self.data_auditoria else None,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None
        }

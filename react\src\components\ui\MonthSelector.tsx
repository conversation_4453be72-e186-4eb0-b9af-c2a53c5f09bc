import { useFilterStore } from '@/store/filterStore'
import { Dropdown } from '@/components/ui/Dropdown'

const months = [
  { value: 1, label: 'Janeiro' },
  { value: 2, label: 'Fevereiro' },
  { value: 3, label: '<PERSON><PERSON><PERSON>' },
  { value: 4, label: '<PERSON><PERSON><PERSON>' },
  { value: 5, label: '<PERSON><PERSON>' },
  { value: 6, label: '<PERSON><PERSON>' },
  { value: 7, label: '<PERSON><PERSON>' },
  { value: 8, label: 'Agosto' },
  { value: 9, label: 'Setem<PERSON>' },
  { value: 10, label: 'Outubro' },
  { value: 11, label: 'Nov<PERSON>bro' },
  { value: 12, label: 'Dezembro' },
]

export function MonthSelector() {
  const { selectedMonth, setMonth } = useFilterStore()

  const options = months.map((month) => ({
    value: month.value.toString(),
    label: month.label,
  }))

  const handleChange = (value: string | string[]) => {
    if (Array.isArray(value)) return
    setMonth(parseInt(value))
  }

  return (
    <Dropdown
      options={options}
      value={selectedMonth.toString()}
      onChange={handleChange}
      className="w-32"
    />
  )
}
import React from 'react'
import type { CenarioTributo } from '@/types/cenarios'
import { FilterRow } from '../filters/FilterRow'
import { useAdvancedFilters } from '@/hooks/useAdvancedFilters'
import { useSelectedCompany } from '@/hooks/useSelectedCompany'
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll'
import { TableScrollContainer } from '@/components/ui/TableScrollContainer'
import { Edit, Check, AlertTriangle, Trash2 } from 'lucide-react'

interface ICMSSTTableProps {
  cenarios: CenarioTributo[]
  selectedCenarios: number[]
  onEdit: (cenario: CenarioTributo) => void
  onSendToProduction: (cenario: CenarioTributo) => void
  onMarkAsInconsistent: (cenario: CenarioTributo) => void
  onDelete: (cenario: CenarioTributo) => void
  onSelectCenario: (cenarioId: number, checked: boolean) => void
  onSelectAll: (checked: boolean) => void
  status?: 'novo' | 'producao' | 'inconsistente'
  onFilteredDataChange?: (filters: any, filteredCenarios: CenarioTributo[], total: number, hasMore: boolean) => void
  totalCount?: number
}

// Função para formatar percentual
function formatPercentage(value: number | null | undefined): string {
  return `${value ?? 0}%`
}

// Função para renderizar os 4 botões de ação
function renderActionButtons(
  cenario: CenarioTributo, 
  onEdit: (cenario: CenarioTributo) => void,
  onSendToProduction: (cenario: CenarioTributo) => void,
  onMarkAsInconsistent: (cenario: CenarioTributo) => void,
  onDelete: (cenario: CenarioTributo) => void
) {
  return (
    <div className="flex justify-end space-x-1">
      <button 
        onClick={() => onEdit(cenario)}
        className="p-1 text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 hover:bg-primary-50 rounded"
        title="Editar"
      >
        <Edit className="w-4 h-4" />
      </button>
      <button 
        onClick={() => onSendToProduction(cenario)}
        className="p-1 text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 rounded"
        title="Enviar para Produção"
      >
        <Check className="w-4 h-4" />
      </button>
      <button 
        onClick={() => onMarkAsInconsistent(cenario)}
        className="p-1 text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300 hover:bg-yellow-50 rounded"
        title="Marcar como Inconsistente"
      >
        <AlertTriangle className="w-4 h-4" />
      </button>
      <button 
        onClick={() => onDelete(cenario)}
        className="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 rounded"
        title="Excluir"
      >
        <Trash2 className="w-4 h-4" />
      </button>
    </div>
  )
}

export function ICMSSTTable({
  cenarios,
  selectedCenarios,
  onEdit,
  onSendToProduction,
  onMarkAsInconsistent,
  onDelete,
  onSelectCenario,
  onSelectAll,
  status = 'novo',
  onFilteredDataChange,
  totalCount
}: ICMSSTTableProps) {
  const empresaId = useSelectedCompany()
  const allSelected = cenarios.length > 0 && selectedCenarios.length === cenarios.length
  const someSelected = selectedCenarios.length > 0 && selectedCenarios.length < cenarios.length



  // Hook para filtros avançados
  const {
    filters,
    options,
    isLoading: filtersLoading,
    hasActiveFilters,
    hasMore,
    loadMore,
    updateFilter,
    updateTextFilter,
    clearAllFilters,
    getFilteredOptions
  } = useAdvancedFilters({
    tipoTributo: 'icms_st',
    empresaId: empresaId || undefined,
    status,
    onFiltersChange: (newFilters, filteredData, total, hasMoreData) => {
      // Dados já vêm filtrados do servidor
      onFilteredDataChange?.(newFilters, filteredData, total, hasMoreData)
    },
    initialData: cenarios,
    initialTotal: totalCount
  })

  // Hook para scroll infinito
  const { loadingRef } = useInfiniteScroll({
    hasMore,
    isLoading: filtersLoading,
    onLoadMore: loadMore
  })

  // Os cenários já vêm filtrados do servidor quando há filtros ativos
  const displayedCenarios = cenarios

  return (
    <TableScrollContainer>
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-900">
          <tr>
            <th className="px-3 py-3 text-left">
              <input 
                type="checkbox" 
                className="rounded border-gray-300"
                checked={allSelected}
                ref={input => {
                  if (input) input.indeterminate = someSelected
                }}
                onChange={(e) => onSelectAll(e.target.checked)}
              />
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Produto
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Descrição
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              CFOP
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              NCM
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              CEST
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Origem
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              CST
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Estado
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              % ICMS
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              % Red. BC ICMS
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              % ICMS-ST
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              % MVA
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              % Red. BC ST
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Cliente
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Simples Nacional
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Atividade
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Destinação
            </th>
            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Ações
            </th>
          </tr>

          {/* Linha de filtros */}
          <FilterRow
            tipoTributo="icms_st"
            filters={filters}
            options={options}
            isLoading={filtersLoading}
            hasActiveFilters={hasActiveFilters}
            onUpdateFilter={updateFilter}
            onUpdateTextFilter={updateTextFilter}
            onClearAllFilters={clearAllFilters}
            getFilteredOptions={getFilteredOptions}
          />
        </thead>
        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {cenarios.length === 0 ? (
            <tr>
              <td colSpan={19} className="px-6 py-12 text-center">
                <div className="flex flex-col items-center">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    {hasActiveFilters ? 'Nenhum cenário encontrado com os filtros aplicados' : 'Nenhum cenário ICMS-ST encontrado'}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {hasActiveFilters ? 'Tente ajustar os filtros para ver mais resultados.' : 'Não há cenários para exibir nesta categoria.'}
                  </p>
                </div>
              </td>
            </tr>
          ) : (
            cenarios.map((cenario) => {
              const stCenario = cenario as any
              const isSelected = selectedCenarios.includes(cenario.id)
            
              return (
              <tr 
                key={cenario.id} 
                className={`hover:bg-gray-50 dark:hover:bg-gray-700 ${
                  isSelected ? 'bg-primary-50 dark:bg-primary-900/20' : ''
                }`}
              >
                <td className="px-3 py-4">
                  <input 
                    type="checkbox" 
                    className="rounded border-gray-300"
                    checked={isSelected}
                    onChange={(e) => onSelectCenario(cenario.id, e.target.checked)}
                  />
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cenario.produto?.codigo || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white max-w-xs truncate">
                  {cenario.produto?.descricao || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cenario.cfop || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cenario.ncm || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {stCenario.cest || cenario.produto?.cest || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {stCenario.origem || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {stCenario.cst || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {cenario.cliente?.uf || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {formatPercentage(stCenario.aliquota)}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {formatPercentage(stCenario.p_red_bc)}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {formatPercentage(stCenario.icms_st_aliquota)}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {formatPercentage(stCenario.icms_st_p_mva)}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {formatPercentage(stCenario.icms_st_p_red_bc)}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  <div>
                    <div className="font-medium">{cenario.cliente?.razao_social || '-'}</div>
                    <div className="text-gray-500 text-xs">{cenario.cliente?.cnpj || '-'}</div>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    stCenario.simples_nacional || cenario.cliente?.simples_nacional 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {stCenario.simples_nacional || cenario.cliente?.simples_nacional ? 'Sim' : 'Não'}
                  </span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {stCenario.atividade || cenario.cliente?.atividade || '-'}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {stCenario.destinacao || cenario.cliente?.destinacao || (cenario.direcao === 'entrada' ? 'Entrada' : 'Saída')}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                  {renderActionButtons(cenario, onEdit, onSendToProduction, onMarkAsInconsistent, onDelete)}
                </td>
              </tr>
            )
            })
          )}
        </tbody>
      </table>

      {/* Elemento para scroll infinito */}
      {hasMore && (
        <div ref={loadingRef} className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
          <span className="ml-2 text-sm text-gray-600">Carregando mais...</span>
        </div>
      )}
    </TableScrollContainer>
  )
}
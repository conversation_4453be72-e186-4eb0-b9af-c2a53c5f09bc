import psycopg2
import time

start = time.time()

try:
    conn = psycopg2.connect(
        host="ls-b16157f927669437cc517b230584a2f450c5c29e.cqpsgquqmlm8.us-east-1.rds.amazonaws.com",
        dbname="postgres",
        user="audittei",
        password="#Ud1tt31",
        port=5432,
        sslmode="require"
    )
    cur = conn.cursor()
    cur.execute("SELECT now();")
    print("Resultado:", cur.fetchone()[0])
    cur.close()
    conn.close()
except Exception as e:
    print("Erro:", e)

end = time.time()
print(f"Tempo total: {round((end - start)*1000)} ms")

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface FilterState {
  selectedCompany: number | null
  selectedYear: number
  selectedMonth: number
  setCompany: (companyId: number | null) => void
  setYear: (year: number) => void
  setMonth: (month: number) => void
  setFilters: (filters: { company?: number | null; year?: number; month?: number }) => void
}

export const useFilterStore = create<FilterState>()(
  persist(
    (set) => ({
      selectedCompany: null,
      selectedYear: new Date().getFullYear(),
      selectedMonth: new Date().getMonth() + 1, // 1-12
      
      setCompany: (companyId) => {
        set({ selectedCompany: companyId })
      },
      
      setYear: (year) => {
        set({ selectedYear: year })
      },
      
      setMonth: (month) => {
        set({ selectedMonth: month })
      },
      
      setFilters: (filters) => {
        set((state) => ({
          selectedCompany: filters.company !== undefined ? filters.company : state.selectedCompany,
          selectedYear: filters.year !== undefined ? filters.year : state.selectedYear,
          selectedMonth: filters.month !== undefined ? filters.month : state.selectedMonth,
        }))
      },
    }),
    {
      name: 'filter-storage',
      partialize: (state) => ({
        selectedCompany: state.selectedCompany,
        selectedYear: state.selectedYear,
        selectedMonth: state.selectedMonth,
      }),
    }
  )
)
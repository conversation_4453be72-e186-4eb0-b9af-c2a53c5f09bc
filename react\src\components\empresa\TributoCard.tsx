import { CardTributo } from '@/services/dashboardService'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

// Lucide React Icons
import { 
  Download, 
  Coins, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RotateCcw
} from 'lucide-react'

interface TributoCardProps {
  card: CardTributo
  tipo: 'saida' | 'entrada'
  empresaId?: string
  selectedYear?: number
  selectedMonth?: number
  onNavigateToAuditoria?: (tributo: string) => void
  onMarcarNaoAplicavel?: (tributo: string) => void
  onReverterStatus?: (tributo: string) => void
}

export function TributoCard({ 
  card, 
  tipo, 
  empresaId,
  selectedYear,
  selectedMonth,
  onNavigateToAuditoria, 
  onMarcarNaoAplicavel, 
  onReverterStatus 
}: TributoCardProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value)
  }

  const isClickable = card.status_tipo !== 'nao_aplicavel' && tipo === 'saida'
  const statusClass = card.auditado ? 'border-green-500' : 'border-yellow-500'
  const statusIcon = card.auditado ? 'text-green-600' : 'text-yellow-600'
  const statusText = card.auditado ? 'Auditado' : 'Pendente'

  const handleClick = () => {
    if (isClickable && onNavigateToAuditoria) {
      onNavigateToAuditoria(card.tipo_tributo)
    }
  }

  const handleMarcarNaoAplicavel = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onMarcarNaoAplicavel) {
      onMarcarNaoAplicavel(card.tipo_tributo)
    }
  }

  const handleReverter = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onReverterStatus) {
      onReverterStatus(card.tipo_tributo)
    }
  }

  // Card para entrada (mais simples)
  if (tipo === 'entrada') {
    return (
      <Card hover gradient className="text-center">
        <div className="p-6">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white mx-auto mb-4 shadow-lg">
            <Download className="w-6 h-6" />
          </div>
          
          <h6 className="text-lg font-bold text-gray-900 dark:text-white mb-2 gradient-text">
            📥 {card.nome_tributo}
          </h6>
          
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {formatCurrency(card.valor_tributo || 0)}
          </div>
          
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            Valor total do tributo
          </p>
        </div>
      </Card>
    )
  }

  // Card para saída (completo)
  return (
    <Card 
      hover={isClickable}
      gradient
      className={`relative overflow-hidden group ${isClickable ? 'cursor-pointer' : 'cursor-default'}`}
      onClick={handleClick}
    >
      {/* Background Pattern */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-100/20 to-transparent rounded-full -mr-10 -mt-10"></div>
      
      <div className="relative z-10 p-6">
        {/* Header do card */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 rounded-xl flex items-center justify-center text-white shadow-lg ${
              card.auditado 
                ? 'bg-gradient-to-br from-success-500 to-success-600'
                : 'bg-gradient-to-br from-warning-500 to-warning-600'
            }`}>
              <Coins className="w-5 h-5" />
            </div>
            
            <div>
              <h6 className="text-lg font-bold text-gray-900 dark:text-white gradient-text">
                💰 {card.nome_tributo}
              </h6>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                card.auditado 
                  ? 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400'
                  : 'bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-400'
              }`}>
                {card.auditado ? (
                  <CheckCircle className={`w-3 h-3 mr-1 ${statusIcon}`} />
                ) : (
                  <Clock className={`w-3 h-3 mr-1 ${statusIcon}`} />
                )}
                {statusText}
              </span>
            </div>
          </div>
          
          <div className="text-center p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
            <div className="text-lg font-bold text-primary-600 dark:text-primary-400">
              {card.total_notas}
            </div>
            <div className="text-xs text-primary-600/70 dark:text-primary-400/70">
              Notas
            </div>
          </div>
        </div>

        {/* Conteúdo baseado no status */}
        {card.auditado ? (
          card.status_tipo === 'nao_aplicavel' ? (
            // Não aplicável
            <div className="text-center py-6">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <XCircle className="w-8 h-8 text-gray-400" />
              </div>
              
              <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                ❌ Não Aplicável
              </h4>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 px-2">
                {card.status_manual?.motivo || 'Empresa não possui operações deste tributo'}
              </p>
              
              <Button
                variant="secondary"
                size="sm"
                onClick={handleReverter}
                icon={
                  <RotateCcw className="w-4 h-4" />
                }
              >
                Reverter Status
              </Button>
            </div>
          ) : (
            // Auditado com dados
            <div className="space-y-4">
              {/* Stats Grid */}
              <div className="grid grid-cols-3 gap-3">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {card.total_produtos}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Operações
                  </div>
                </div>
                
                <div className="text-center p-3 bg-success-50 dark:bg-success-900/20 rounded-lg">
                  <div className="text-lg font-bold text-success-600 dark:text-success-400">
                    {card.total_conforme}
                  </div>
                  <div className="text-xs text-success-600/70 dark:text-success-400/70">
                    Conformes
                  </div>
                </div>
                
                <div className="text-center p-3 bg-error-50 dark:bg-error-900/20 rounded-lg">
                  <div className="text-lg font-bold text-error-600 dark:text-error-400">
                    {card.total_inconsistente}
                  </div>
                  <div className="text-xs text-error-600/70 dark:text-error-400/70">
                    Erros
                  </div>
                </div>
              </div>

              {/* Valor Inconsistente */}
              <div className="text-center p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  💸 Valor das Inconsistências
                </div>
                {card.total_inconsistente > 0 ? (
                  <div className="text-xl font-bold text-error-600 dark:text-error-400">
                    {formatCurrency((card.valor_inconsistente_maior || 0) + (card.valor_inconsistente_menor || 0))}
                  </div>
                ) : (
                  <div className="text-xl font-bold text-success-600 dark:text-success-400">
                    ✅ Sem inconsistências!
                  </div>
                )}
              </div>
            </div>
          )
        ) : (
          // Pendente
          <div className="text-center py-6">
            <div className="w-16 h-16 bg-warning-100 dark:bg-warning-900/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="w-8 h-8 text-warning-600 dark:text-warning-400" />
            </div>
            
            <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
              Auditoria Pendente
            </h4>
            
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 px-2">
              Auditoria não realizada para este tributo
            </p>
            
            <Button
              variant="secondary"
              size="sm"
              onClick={handleMarcarNaoAplicavel}
              icon={
                <XCircle className="w-4 h-4" />
              }
            >
              Marcar como Não Aplicável
            </Button>
          </div>
        )}
      </div>
    </Card>
  )
}
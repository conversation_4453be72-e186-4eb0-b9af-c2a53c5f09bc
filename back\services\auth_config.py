from dataclasses import dataclass
import os
from typing import Optional

@dataclass
class AuthConfig:
    """Configuration class for the Authentication Service"""
    
    portal_url: str = "https://www.audittei.com.br/api/autenticacao/auth/login"
    escritorio_email: str = ""
    escritorio_password: str = ""
    public_key_path: str = ""
    database_url: str = ""
    refresh_interval: int = 3600  # 1 hour in seconds
    
    @classmethod
    def from_environment(cls) -> 'AuthConfig':
        """Create AuthConfig from environment variables"""
        return cls(
            portal_url=os.getenv('PORTAL_URL', cls.portal_url),
            escritorio_email=os.getenv('ESCRITORIO_EMAIL', ''),
            escritorio_password=os.getenv('ESCRITORIO_PASSWORD', ''),
            public_key_path=os.getenv('PORTAL_JWT_PUBLIC_KEY_FILE', ''),
            database_url=os.getenv('DATABASE_URL', ''),
            refresh_interval=int(os.getenv('AUTH_REFRESH_INTERVAL', '3600'))
        )
    
    def validate(self) -> bool:
        """Validate that all required configuration is present"""
        required_fields = [
            'escritorio_email',
            'escritorio_password', 
            'database_url'
        ]
        
        for field in required_fields:
            if not getattr(self, field):
                return False
                
        # Check if public key exists
        if self.public_key_path and not os.path.exists(self.public_key_path):
            return False
            
        return True
    
    def get_missing_fields(self) -> list:
        """Get list of missing required configuration fields"""
        missing = []
        
        if not self.escritorio_email:
            missing.append('ESCRITORIO_EMAIL')
        if not self.escritorio_password:
            missing.append('ESCRITORIO_PASSWORD')
        if not self.database_url:
            missing.append('DATABASE_URL')
        if self.public_key_path and not os.path.exists(self.public_key_path):
            missing.append('PORTAL_JWT_PUBLIC_KEY_FILE (file not found)')
            
        return missing
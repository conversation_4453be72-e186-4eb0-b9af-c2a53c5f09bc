import React from 'react'
import { Card } from '@/components/ui/Card'
import { But<PERSON> } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'

interface ProgressoAuditoriaComparativaProps {
  auditId: string
  status: 'processing' | 'completed' | 'error' | 'cancelled'
  progress?: number
  message?: string
  currentStep?: string
  totalSteps?: number
  onCancel?: () => void
}

export function ProgressoAuditoriaComparativa({
  auditId,
  status,
  progress = 0,
  message,
  currentStep,
  totalSteps,
  onCancel
}: ProgressoAuditoriaComparativaProps) {
  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'blue'
      case 'completed':
        return 'green'
      case 'error':
        return 'red'
      case 'cancelled':
        return 'gray'
      default:
        return 'blue'
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'processing':
        return 'Processando'
      case 'completed':
        return 'Concluída'
      case 'error':
        return 'Erro'
      case 'cancelled':
        return 'Cancelada'
      default:
        return 'Processando'
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return (
          <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        )
      case 'completed':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      case 'error':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      case 'cancelled':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      default:
        return null
    }
  }

  return (
    <Card className="p-6 border-l-4 border-l-primary-500">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`text-${getStatusColor()}-600 dark:text-${getStatusColor()}-400`}>
              {getStatusIcon()}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Auditoria Comparativa
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                ID: {auditId}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge variant={getStatusColor() as any}>
              {getStatusText()}
            </Badge>
            
            {status === 'processing' && onCancel && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancel}
                icon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                }
              >
                Cancelar
              </Button>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        {status === 'processing' && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                {message || 'Processando auditoria comparativa...'}
              </span>
              <span className="text-gray-600 dark:text-gray-400">
                {Math.round(progress)}%
              </span>
            </div>
            
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${Math.min(progress, 100)}%` }}
              />
            </div>
            
            {currentStep && totalSteps && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Etapa {currentStep} de {totalSteps}
              </div>
            )}
          </div>
        )}

        {/* Success Message */}
        {status === 'completed' && (
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <div className="flex items-center gap-2 text-green-800 dark:text-green-200">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">Auditoria comparativa concluída com sucesso!</span>
            </div>
            <p className="text-green-700 dark:text-green-300 text-sm mt-1">
              {message || 'Os dados foram processados e estão disponíveis na tabela abaixo.'}
            </p>
          </div>
        )}

        {/* Error Message */}
        {status === 'error' && (
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
            <div className="flex items-center gap-2 text-red-800 dark:text-red-200">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">Erro na auditoria comparativa</span>
            </div>
            <p className="text-red-700 dark:text-red-300 text-sm mt-1">
              {message || 'Ocorreu um erro durante o processamento. Tente novamente.'}
            </p>
          </div>
        )}

        {/* Cancelled Message */}
        {status === 'cancelled' && (
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="flex items-center gap-2 text-gray-800 dark:text-gray-200">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">Auditoria comparativa cancelada</span>
            </div>
            <p className="text-gray-700 dark:text-gray-300 text-sm mt-1">
              {message || 'A auditoria foi cancelada pelo usuário.'}
            </p>
          </div>
        )}

        {/* Processing Details */}
        {status === 'processing' && (
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-blue-800 dark:text-blue-200">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="font-medium text-sm">Processamento em andamento</span>
              </div>
              <ul className="text-blue-700 dark:text-blue-300 text-xs space-y-1 ml-6">
                <li>• Comparando dados entre XML, SPED e Cenários</li>
                <li>• Identificando divergências fiscais</li>
                <li>• Calculando matches automáticos</li>
                <li>• Gerando relatório de inconsistências</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}
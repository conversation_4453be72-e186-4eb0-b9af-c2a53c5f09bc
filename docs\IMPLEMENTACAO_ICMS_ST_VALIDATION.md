# Implementação da Validação ICMS-ST

## Visão Geral

Esta implementação adiciona uma funcionalidade avançada de validação de cenários ICMS-ST que consulta APIs externas para verificar se os dados configurados estão corretos conforme a legislação vigente.

## Funcionalidades Implementadas

### 1. Validação ICMS-ST Completa

- **Botão**: "Validar ICMS-ST" (verde com ícone de check duplo)
- **Função**: Valida cenários ICMS-ST completos usando API externa
- **Verifica**: Alíquotas, MVA, CEST baseados na legislação oficial

### 2. Validação CFOP x CST (Existente)

- **Botão**: "Validar CFOP x CST" (azul com ícone de branch)
- **Função**: Valida combinações CFOP x CST x Alíquota
- **Verifica**: Regras de negócio internas

## Arquitetura da Solução

### Backend

#### Modelos de Dados

1. **ICMSSTCacheModel** (`back/models/icms_st_cache.py`)

   - Cache das consultas à API externa
   - Evita requisições desnecessárias (custo por consulta)
   - Suporte para ST interna e interestadual
   - Múltiplos CEST por NCM

2. **ICMSSTValidationResultModel** (`back/models/icms_st_validation_result.py`)
   - Histórico de validações realizadas
   - Rastreamento de correções aplicadas
   - Metadados de auditoria

#### Serviços

1. **ICMSSTValidationService** (`back/services/icms_st_validation_service.py`)
   - Lógica principal de validação
   - Integração com APIs externas
   - Gerenciamento de cache inteligente
   - Comparação com cenários existentes

#### APIs Externas Utilizadas

1. **ST Interna**: `https://www.legisweb.com.br/api/st-interna/`

   - Parâmetros: token, cliente_id, estado, ncm
   - Uso: Quando empresa e cliente estão no mesmo estado

2. **ST Interestadual**: `https://www.legisweb.com.br/api/st-interestadual/`
   - Parâmetros: token, cliente_id, estado_origem, estado_destino, destinacao_mercadoria, ncm
   - Uso: Quando empresa e cliente estão em estados diferentes

#### Rotas da API

- `POST /api/cenarios/icms-st/validate` - Validação completa
- `POST /api/cenarios/icms-st/apply-suggestion` - Aplicar correções
- `GET /api/cenarios/icms-st/historico/<empresa_id>` - Histórico

### Frontend

#### JavaScript (`front/static/js/icms_st_validation.js`)

- Dois botões de validação na interface
- Modais para exibir resultados
- Aplicação de correções em lote
- Sistema de filtros integrado
- Visualização de detalhes da API

## Lógica de Funcionamento

### 1. Determinação do Tipo de ST

```javascript
// Lógica no serviço
if (estado_empresa == estado_cliente) {
  tipo_st = "interna";
  endpoint = API_BASE_URL_INTERNA;
} else {
  tipo_st = "interestadual";
  endpoint = API_BASE_URL_INTERESTADUAL;
}
```

### 2. Mapeamento de Destinação de Mercadoria

```python
DESTINACAO_MAPPING = {
    'Revenda': '1',  # Op. Subsequente - Comercialização
    'Ativo Imobilizado': '2',  # Ativo Fixo ou Imobilizado
    'Uso e Consumo': '3',  # Uso e Consumo
    'Varejista': '4'  # Transferência para Varejista
}
```

### 3. Cache Inteligente

- Consulta primeiro o cache local
- Cache válido por 30 dias
- Hash único por consulta para evitar duplicatas
- Múltiplos registros por NCM (diferentes CEST)

### 4. Validação e Sugestões

- Compara alíquotas ICMS-ST
- Compara MVA (Margem de Valor Agregado)
- Sugere CEST correto
- Identifica divergências com base legal

## Instalação e Configuração

### 1. Executar Migração do Banco

```sql
-- Executar o arquivo de migração
psql -d sua_base_dados -f db/migration_icms_st_validation.sql
```

### 2. Verificar Imports dos Modelos

Os novos modelos já foram adicionados ao `back/models/__init__.py`:

```python
from .icms_st_cache import ICMSSTCacheModel
from .icms_st_validation_result import ICMSSTValidationResultModel
```

### 3. Configuração da API

As credenciais da API estão configuradas no serviço:

```python
API_TOKEN = "8741902a3f20e527c0b76c5b3a918b3f"
CLIENTE_ID = "65597"
```

## Como Usar

### 1. Acessar Cenários ICMS-ST

- Navegar para: Cenários > Saída > ICMS-ST
- Selecionar empresa

### 2. Executar Validação

- **Validação Completa**: Clique em "Validar ICMS-ST" (botão verde)
- **Validação CFOP x CST**: Clique em "Validar CFOP x CST" (botão azul)

### 3. Revisar Resultados

- Modal mostra problemas encontrados
- Detalhes de cada divergência
- Sugestões de correção baseadas na API

### 4. Aplicar Correções

- **Aplicar Todas**: Corrige todos os problemas automaticamente
- **Aplicar Selecionadas**: Corrige apenas os selecionados
- **Individual**: Corrige um cenário específico

### 5. Filtrar Cenários

- **Filtrar Selecionados**: Mostra apenas cenários com problemas
- **Filtrar Específico**: Foca em um cenário particular
- **Limpar Filtros**: Remove todos os filtros

## Dados Salvos no Cache

### ST Interna

- Código, CEST, descrição, segmento
- Alíquota interna, MVA e variações
- Base legal, vigência, aplicabilidade

### ST Interestadual

- Todos os campos da ST interna, mais:
- Estado origem/destino
- Destinação da mercadoria
- Alíquota interestadual
- Normas específicas para operações interestaduais

## Monitoramento e Logs

### Logs de Validação

- Todas as validações são registradas
- Histórico de correções aplicadas
- Rastreamento por usuário e data

### Cache de Performance

- Evita consultas desnecessárias à API
- Reduz custos (API é paga por requisição)
- Atualização mensal recomendada

## Troubleshooting

### Problemas Comuns

1. **Empresa sem estado definido**

   - Verificar campo `estado` na tabela `empresa`

2. **Cliente sem UF**

   - Verificar campo `uf` na tabela `cliente`

3. **Destinação não mapeada**

   - Verificar se `cliente.destinacao` está nos valores aceitos

4. **API não responde**
   - Verificar conectividade
   - Validar token e cliente_id

### Logs de Debug

```python
# Ativar logs detalhados no serviço
print(f"[DEBUG] Encontrados {len(cenarios)} cenários ICMS-ST para empresa {empresa_id}")
```

## Extensões Futuras

### Possíveis Melhorias

1. **Cache Automático**: Atualização automática mensal
2. **Múltiplas APIs**: Integração com outras fontes de dados
3. **Validação Customizada**: Regras específicas por empresa
4. **Relatórios**: Dashboard de conformidade ICMS-ST
5. **Alertas**: Notificações de mudanças na legislação

### Integração com Outros Módulos

- **Auditoria**: Incluir validação ICMS-ST no processo de auditoria
- **Importação**: Validar automaticamente na importação de XMLs
- **Relatórios**: Incluir métricas de conformidade ICMS-ST

## Considerações de Performance

### Otimizações Implementadas

- Cache inteligente com TTL de 30 dias
- Índices otimizados nas tabelas
- Consultas em lote quando possível
- Timeout configurado para APIs externas

### Monitoramento Recomendado

- Taxa de hit do cache
- Tempo de resposta das APIs
- Volume de correções aplicadas
- Erros de validação por empresa

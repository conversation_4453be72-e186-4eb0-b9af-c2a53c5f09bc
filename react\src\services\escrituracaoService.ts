import api from './authService'

export interface EscrituracaoResumo {
  notas_xml: number
  notas_sped: number
  divergentes: number
  total_xml: number
  total_sped: number
  valor_divergente: number
}

export interface EscrituracaoItem {
  id: number
  numero_nf: string
  chave_nf: string
  participante_razao_social: string
  participante_cnpj: string
  data_entrada: string
  data_emissao?: string
  valor_xml: number
  valor_sped: number
  divergencia_valor: number
  percentual_divergencia?: number
  status_conformidade: 'conforme' | 'divergente'
  status_escrituracao: 'pendente' | 'aprovado'
  justificativa?: string
  data_aprovacao?: string
  usuario_aprovacao?: string
}

export interface EscrituracaoData {
  auditorias: EscrituracaoItem[]
  resumo: EscrituracaoResumo
  total_registros: number
}

export const escrituracaoService = {
  async buscarEscrituracao({
    empresaId,
    ano,
    mes,
  }: {
    empresaId: number
    ano: number
    mes: number
  }): Promise<EscrituracaoData> {
    const response = await api.get('/auditoria-entrada/escrituracao', {
      params: {
        empresa_id: empresaId,
        ano,
        mes,
      },
    })
    
    const data = response.data || {}

    const auditorias: EscrituracaoItem[] = (data.auditorias || []).map((auditoria: any) => ({
      id: auditoria.id,
      numero_nf: auditoria.numero_nf,
      chave_nf: auditoria.chave_nf,
      participante_razao_social:
        auditoria.participante_nome || auditoria.participante_razao_social || '',
      participante_cnpj: auditoria.participante_cnpj,
      data_entrada: auditoria.xml_data_entrada || auditoria.data_entrada,
      data_emissao: auditoria.xml_data_emissao || auditoria.data_emissao,
      valor_xml:
        auditoria.xml_valor_total_nota ??
        auditoria.xml_valor_total ??
        0,
      valor_sped:
        auditoria.sped_valor_total_nota ??
        auditoria.sped_valor_item ??
        0,
      divergencia_valor:
        auditoria.divergencia_valor ?? auditoria.divergencia_valor_total ?? 0,
      percentual_divergencia: auditoria.percentual_divergencia,
      status_conformidade: auditoria.status_conformidade,
      status_escrituracao: auditoria.status_escrituracao || 'pendente',
      justificativa: auditoria.justificativa_escrituracao,
      data_aprovacao: auditoria.data_aprovacao_escrituracao,
      usuario_aprovacao: auditoria.usuario_aprovacao_escrituracao,
    }))

    return {
      auditorias,
      resumo: {
        notas_xml: data.resumo?.notas_xml || 0,
        notas_sped: data.resumo?.notas_sped || 0,
        divergentes: data.resumo?.divergentes || 0,
        total_xml: data.resumo?.total_xml || 0,
        total_sped: data.resumo?.total_sped || 0,
        valor_divergente: data.resumo?.valor_divergente || 0,
      },
      total_registros: data.total || data.total_registros || auditorias.length,
    }
  },

  async gerarEscrituracao({
    empresaId,
    mes,
    ano,
    forcarRecalculo = true,
    tolerancia = 0.01,
  }: {
    empresaId: number
    mes: number
    ano: number
    forcarRecalculo?: boolean
    tolerancia?: number
  }) {
    const response = await api.post('/auditoria-entrada/gerar-escrituracao', {
      empresa_id: empresaId,
      mes,
      ano,
      forcar_recalculo: forcarRecalculo,
      tolerancia,
    })
    return response.data
  },

  async aprovarEscrituracao({
    auditoriaIds,
    justificativa,
  }: {
    auditoriaIds: number[]
    justificativa?: string
  }) {
    const response = await api.post('/auditoria-entrada/aprovar-escrituracao', {
      auditoria_ids: auditoriaIds,
      justificativa,
    })
    return response.data
  },

  async obterDetalhes(id: number) {
    const response = await api.get(`/auditoria-entrada/detalhes-comparativo/${id}`)
    return response.data
  },
}
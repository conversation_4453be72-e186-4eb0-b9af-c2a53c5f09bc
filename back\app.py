from flask import Flask, request, jsonify, render_template, Blueprint, redirect
from flask_socketio import SocketIO
from models import db, Escritorio, Empresa, Usuario, Cliente, Produto, Tributo, ImportacaoXML, TributoHistorico
from models.auditoria_status_manual import AuditoriaStatusManual
from routes import cliente_bp, produto_bp, importacao_bp, tributo_bp, cenario_bp, auditoria_bp, system_bp, perfil_bp, relatorio_bp, chatbot_bp
from routes.apuracao_routes import apuracao_bp
from routes.dashboard_routes import dashboard_bp
from routes.auditoria_entrada_routes import auditoria_entrada_bp
from routes.auditoria_comparativa_routes import auditoria_comparativa_bp
from routes.matching_manual_routes import matching_manual_bp
from services.websocket_service import init_websocket_service
from services.queue_manager import init_queue_manager
from services.transaction_manager import init_transaction_manager
import os
from dotenv import load_dotenv
from flask_jwt_extended import J<PERSON><PERSON><PERSON><PERSON>, create_access_token, jwt_required, get_jwt_identity
from flask_cors import CORS
import bcrypt
from datetime import datetime, timedelta
import jwt as pyjwt

def get_public_key():
    """
    Obtém a chave pública do portal a partir do arquivo de segredos ou da variável de ambiente.
    """
    # Tenta ler do arquivo de segredos primeiro
    key_file = os.getenv('PORTAL_JWT_PUBLIC_KEY_FILE')
    if key_file and os.path.exists(key_file):
        try:
            with open(key_file, 'r') as f:
                key_content = f.read().strip()
                print(f"Chave pública lida do arquivo {key_file}")
                print(f"Tamanho da chave: {len(key_content)} caracteres")
                print(f"Primeiros 50 caracteres: {key_content[:50]}...")
                return key_content
        except Exception as e:
            print(f"Erro ao ler chave pública do arquivo {key_file}: {str(e)}")
    
    # Fallback para variável de ambiente (apenas para desenvolvimento)
    key_from_env = os.getenv('PORTAL_JWT_PUBLIC_KEY')
    if key_from_env:
        print("Aviso: Usando chave pública da variável de ambiente (não recomendado para produção)")
        print(f"Tamanho da chave: {len(key_from_env)} caracteres")
        print(f"Primeiros 50 caracteres: {key_from_env[:50]}...")
        return key_from_env
    
    raise ValueError("Nenhuma chave pública JWT encontrada. Verifique as configurações.")

# Carregar variáveis de ambiente
load_dotenv()

def create_app():
    app = Flask(__name__, static_folder='../front/static', template_folder='../front/templates', static_url_path='/fiscal/static')

    # Configuração do CORS
    CORS(app, resources={
        r"/fiscal/api/*": {
            "origins": [
                "https://www.audittei.com.br",
                "https://audittei.com.br",
                "http://localhost:5000"
            ],
            "supports_credentials": True,
            "allow_headers": ["Content-Type", "Authorization"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        },
        r"http://cdn.datatables.net/*": {"origins": "*"}
    })

    # Configuração do banco de dados
    db_url = os.getenv('DATABASE_URL')
    if not db_url:
        raise ValueError("DATABASE_URL não configurada nas variáveis de ambiente")

    # Configurações do SQLAlchemy
    app.config['SQLALCHEMY_DATABASE_URI'] = db_url
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Configurações do pool de conexões
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_size': 4,          # Reduzido de 5 para 4 (ainda adequado)
        'max_overflow': 8,       # Reduzido de 10 para 8 (menos picos)
        'pool_timeout': 30,      # Tempo para esperar conexão disponível
        'pool_recycle': 1800,    # 30 min - PERFEITO, mantenha assim
        'pool_pre_ping': True,   # Verifica saúde da conexão
        'connect_args': {
            'connect_timeout': 10,
            'application_name': 'auditoria_fiscal_app',
            'options': '-c statement_timeout=90000'  # 30 segundos
        }
    }

    # Inicializar o SQLAlchemy
    try:
        db.init_app(app)

        # Testar conexão
        with app.app_context():
            from sqlalchemy import text

            conn = db.engine.connect()

            # Testar conexão com uma consulta simples
            result = conn.execute(text("SELECT version()"))
            version = result.scalar()

            # Verificar se a tabela de usuários existe
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'usuario'
                )
            """))
            table_exists = result.scalar()

            if table_exists:
                result = conn.execute(text("SELECT COUNT(*) FROM usuario"))
                user_count = result.scalar()

            conn.close()

    except Exception as e:
        raise

    # Configurações de pool já definidas anteriormente

    # Configuração do JWT
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'sua_chave_secreta_aqui')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=4)  # Token válido por 24 horas


    # Inicializar JWT
    jwt = JWTManager(app)

    # Configurar SocketIO com opções adicionais para melhor estabilidade
    socketio = SocketIO(
        app,
        cors_allowed_origins="*",
        async_mode='threading',
        engineio_logger=False,
        logger=False,
        ping_timeout=300,
        ping_interval=25,
        max_http_buffer_size=1e8,  # 100MB
        http_compression=True,
        allow_upgrades=True,
        transports=['websocket', 'polling'],
        path='/fiscal/socket.io'
    )

    # Inicializar serviços de concorrência
    with app.app_context():
        # Inicializar gerenciadores
        queue_manager = init_queue_manager(max_workers=4, max_queue_size=100, app=app)
        transaction_manager = init_transaction_manager()

        # Inicializar serviço WebSocket
        websocket_service = init_websocket_service(socketio)
        
        # Configurar tratamento de erros do Socket.IO
        @socketio.on_error()
        def error_handler(e):
            print(f"Erro no Socket.IO: {str(e)}")
            return 'Erro interno do servidor', 500

    # Criar o Blueprint principal com o prefixo /fiscal
    main_bp = Blueprint('main', __name__)

    # Registrar blueprints existentes no blueprint principal
    main_bp.register_blueprint(cliente_bp)
    main_bp.register_blueprint(produto_bp)
    main_bp.register_blueprint(importacao_bp)
    main_bp.register_blueprint(tributo_bp)
    main_bp.register_blueprint(cenario_bp)
    main_bp.register_blueprint(auditoria_bp)
    main_bp.register_blueprint(system_bp)
    main_bp.register_blueprint(dashboard_bp)
    main_bp.register_blueprint(perfil_bp)
    main_bp.register_blueprint(relatorio_bp)
    main_bp.register_blueprint(chatbot_bp)
    main_bp.register_blueprint(auditoria_entrada_bp)
    main_bp.register_blueprint(auditoria_comparativa_bp)
    main_bp.register_blueprint(matching_manual_bp)
    main_bp.register_blueprint(apuracao_bp)

    # Rotas para o frontend
    @main_bp.route('/')
    @main_bp.route('/web')
    def index():
        return render_template('index.html')

    @main_bp.route('/dashboard')
    def dashboard():
        return render_template('dashboard.html')

    # Rotas de Auditoria
    @main_bp.route('/auditoria/entrada')
    def auditoria_entrada():
        return render_template('dashboard.html')

    @main_bp.route('/auditoria/entrada/auditoria')
    def auditoria_entrada_auditoria():
        return render_template('dashboard.html')

    # Rota de Gestão de XMLs
    @main_bp.route('/gestao-xmls')
    def gestao_xmls():
        return render_template('dashboard.html')

    @main_bp.route('/auditoria/saida')
    def auditoria_saida():
        return render_template('dashboard.html')

    # Rotas de detalhes de Auditoria
    @main_bp.route('/auditoria/entrada/<tributo>')
    def auditoria_entrada_tributo(tributo):
        return render_template('dashboard.html')

    @main_bp.route('/auditoria/saida/<tributo>')
    def auditoria_saida_tributo(tributo):
        return render_template('dashboard.html')

    @main_bp.route('/cenarios/saida')
    def cenarios_saida():
        return render_template('dashboard.html')

    @main_bp.route('/cenarios/saida/<tributo>')
    def cenarios_saida_tributo(tributo):
        return render_template('dashboard.html')

    # Outras rotas
    @main_bp.route('/clientes')
    def clientes():
        return render_template('dashboard.html')

    @main_bp.route('/produto')
    def produto():
        return render_template('dashboard.html')

    @main_bp.route('/importacao')
    def importacao():
        return render_template('dashboard.html')

    @main_bp.route('/apuracao')
    def apuracao_page():
        return render_template('dashboard.html')

    # Rotas administrativas
    @main_bp.route('/empresas', endpoint='empresas_page')
    def empresas_page():
        return render_template('dashboard.html')

    @main_bp.route('/usuarios', endpoint='usuarios_page')
    def usuarios_page():
        return render_template('dashboard.html')

    @main_bp.route('/escritorios', endpoint='escritorios_page')
    def escritorios_page():
        return render_template('escritorios.html')

    @main_bp.route('/perfil', endpoint='perfil_page')
    def perfil_page():
       # Redireciona para o dashboard usando a seção de perfil
        return redirect('/fiscal/dashboard?section=perfil')

    # Rota para dashboard específico da empresa
    @main_bp.route('/dashboard/empresa/<int:empresa_id>')
    def dashboard_empresa(empresa_id):
        return render_template('dashboard.html')

    # Rota para dashboard específico da empresa (entrada)
    @main_bp.route('/dashboard/empresa/<int:empresa_id>/entrada')
    def dashboard_empresa_entrada(empresa_id):
        return render_template('dashboard.html')

    # API - Autenticação via portal
    @main_bp.route("/api/portal-login", methods=["GET", "POST"])
    def portal_login():
        try:
            print("\n=== Nova requisição de login ===")
            print(f"Método: {request.method}")
            print(f"Headers: {dict(request.headers)}")
            print(f"Cookies: {request.cookies}")
            print(f"Args: {request.args}")

            # Prioriza o cookie httpOnly enviado pelo portal
            token = request.cookies.get("token")

            # Fallback para corpo JSON ou query string
            if not token:
                data = request.get_json(silent=True) or {}
                token = data.get("token") or request.args.get("token")

            print(f"Token recebido: {'Sim' if token else 'Não'}")
            print(f"Token (início): {token[:30]}..." if token else "")

            if not token:
                print("Erro: Nenhum token fornecido")
                return {"message": "Token não fornecido"}, 400

            try:
                print("Obtendo chave pública...")
                portal_key = get_public_key()
                print(f"Chave pública obtida: {bool(portal_key)}")
                print("Chave pública carregada:", bool(portal_key))
                
                payload = pyjwt.decode(token, portal_key, algorithms=["RS256"])
            except Exception:
                return {"message": "Token inválido"}, 401

            user_info = payload.get("user") or payload
            permissions = user_info.get("permissions") or user_info.get("permitions", {})
            if not permissions.get("Fiscal"):
                return {"message": "Acesso não permitido"}, 403

            office_id = user_info.get("officeId")
            office_cnpj = user_info.get("officeCnpj")
            escritorio = None
            if office_id:
                escritorio = Escritorio.query.filter_by(portal_office_id=office_id).first()

            if not escritorio and office_cnpj:
                escritorio = Escritorio.query.filter_by(cnpj=office_cnpj).first()

            if not escritorio:
                escritorio = Escritorio(
                    portal_office_id=office_id,
                    cnpj=office_cnpj,
                    nome=f"Escritório {office_id}"
                )
                db.session.add(escritorio)
                db.session.commit()

            usuario = Usuario.query.filter_by(portal_user_id=user_info.get("id")).first()
            
            role = user_info.get("role", "normal")
            tipo_usuario = "escritorio" if role == "admin" else role
            is_admin = role == "super_admin"
            if not usuario:
                usuario = Usuario(
                    portal_user_id=user_info.get("id"),
                    nome=user_info.get("name"),
                    escritorio_id=escritorio.id,
                    tipo_usuario=tipo_usuario,
                    is_admin=is_admin
                )
                db.session.add(usuario)
                db.session.commit()
            else:
                updated = False
                if usuario.tipo_usuario != tipo_usuario:
                    usuario.tipo_usuario = tipo_usuario
                    updated = True
                if usuario.is_admin != is_admin:
                    usuario.is_admin = is_admin
                    updated = True
                if usuario.escritorio_id != escritorio.id:
                    usuario.escritorio_id = escritorio.id
                    updated = True
                if updated:
                    db.session.commit()

            access_token = create_access_token(identity=str(usuario.id))

            return {
                "access_token": access_token,
                "usuario_id": usuario.id,
                "nome": usuario.nome,
                "escritorio_id": usuario.escritorio_id,
                "tipo_usuario": usuario.tipo_usuario,
                "is_admin": usuario.is_admin,
            }
        except Exception as e:
            print(f"Erro ao processar token do portal: {str(e)}")
            return {"message": "Erro ao processar token"}, 500

    # API - Depuração: decodificar token do portal
    @main_bp.route("/api/debug-portal-token", methods=["POST"])
    def debug_portal_token():
        data = request.get_json() or {}
        token = (
            data.get("token")
            or request.args.get("token")
            or request.cookies.get("token")
        )

        if not token:
            return {"message": "Token não fornecido"}, 400

        portal_key = os.getenv("PORTAL_JWT_PUBLIC_KEY")
        try:
            payload = pyjwt.decode(token, portal_key, algorithms=["RS256"])
            return {"payload": payload}
        except Exception as e:
            return {"message": "Token inválido", "error": str(e)}, 401

    # API - Informações do usuário logado
    @main_bp.route("/me", methods=["GET"])
    @jwt_required()
    def get_user_info():
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar o nome do escritório se o usuário estiver vinculado a um
            escritorio_nome = None
            if usuario.escritorio_id:
                escritorio = Escritorio.query.get(usuario.escritorio_id)
                if escritorio:
                    escritorio_nome = escritorio.nome

            return {
                "id": usuario.id,
                "nome": usuario.nome,
                "escritorio_id": usuario.escritorio_id,
                "escritorio_nome": escritorio_nome,
                "is_admin": usuario.is_admin,
                "tipo_usuario": usuario.tipo_usuario,
                "empresas_permitidas": usuario.empresas_permitidas or []
            }
        except Exception as e:
            print(f"Erro ao buscar informações do usuário: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Escritório
    @main_bp.route("/api/escritorios", methods=["GET"])
    @jwt_required()
    def listar_escritorios():
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Filtrar escritórios com base no tipo de usuário
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores veem todos os escritórios
                escritorios = Escritorio.query.all()
            elif usuario.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório veem apenas seu próprio escritório
                escritorios = [Escritorio.query.get(usuario.escritorio_id)] if usuario.escritorio_id else []
            else:
                # Usuários comuns veem apenas o escritório ao qual estão vinculados
                escritorios = [Escritorio.query.get(usuario.escritorio_id)] if usuario.escritorio_id else []

            return {"escritorios": [e.to_dict() for e in escritorios if e]}
        except Exception as e:
            print(f"Erro ao listar escritórios: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Escritório - Criar
    @main_bp.route("/api/escritorios", methods=["POST"])
    @jwt_required()
    def criar_escritorio():
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Apenas administradores podem criar escritórios
            if not usuario.is_admin and usuario.tipo_usuario != 'admin':
                return {"message": "Você não tem permissão para criar escritórios"}, 403

            # Validar dados
            nome = data.get("nome")
            cnpj = data.get("cnpj")

            if not nome or not cnpj:
                return {"message": "Nome e CNPJ são obrigatórios"}, 400

            # Verificar se já existe escritório com o mesmo CNPJ
            if Escritorio.query.filter_by(cnpj=cnpj).first():
                return {"message": "Já existe um escritório com este CNPJ"}, 400

            # Criar o escritório
            novo = Escritorio(
                nome=nome,
                cnpj=cnpj,
                endereco=data.get("endereco"),
                responsavel=data.get("responsavel")
            )
            db.session.add(novo)
            db.session.commit()

            return {"message": "Escritório criado com sucesso", "id": novo.id}, 201
        except Exception as e:
            print(f"Erro ao criar escritório: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Empresa - Listar
    @main_bp.route("/api/empresas", methods=["GET"])
    @jwt_required()
    def listar_empresas():
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Filtrar empresas com base no tipo de usuário
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores veem todas as empresas
                empresas = Empresa.query.all()
            elif usuario.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório veem empresas do seu escritório
                empresas = Empresa.query.filter_by(escritorio_id=usuario.escritorio_id).all()
            else:
                # Usuários comuns veem apenas empresas permitidas
                empresas_permitidas = usuario.empresas_permitidas or []
                empresas = Empresa.query.filter(Empresa.id.in_(empresas_permitidas)).all()

            return {"empresas": [e.to_dict() for e in empresas]}
        except Exception as e:
            print(f"Erro ao listar empresas: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Empresa - Criar
    @main_bp.route("/api/empresas", methods=["POST"])
    @jwt_required()
    def criar_empresa():
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Determinar o escritório_id com base no tipo de usuário
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem especificar o escritório
                escritorio_id = data.get("escritorio_id")
                if not escritorio_id:
                    return {"message": "ID do escritório é obrigatório"}, 400
            elif usuario.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório só podem criar empresas para seu próprio escritório
                escritorio_id = usuario.escritorio_id
            else:
                # Usuários comuns não podem criar empresas
                return {"message": "Você não tem permissão para criar empresas"}, 403

            # Validar dados obrigatórios
            razao_social = data.get("razao_social")
            cnpj = data.get("cnpj")

            if not razao_social or not cnpj:
                return {"message": "CNPJ e Razão Social são obrigatórios"}, 400

            # Verificar se já existe empresa com o mesmo CNPJ no mesmo escritório
            if Empresa.query.filter_by(cnpj=cnpj, escritorio_id=escritorio_id).first():
                return {"message": "Já existe uma empresa com este CNPJ neste escritório"}, 400

            # Criar a empresa com todos os campos
            nova = Empresa(
                razao_social=razao_social,
                cnpj=cnpj,
                escritorio_id=escritorio_id,
                inscricao_estadual=data.get("inscricao_estadual"),
                nome_fantasia=data.get("nome_fantasia"),
                email=data.get("email"),
                responsavel=data.get("responsavel"),
                cep=data.get("cep"),
                logradouro=data.get("logradouro"),
                numero=data.get("numero"),
                complemento=data.get("complemento"),
                bairro=data.get("bairro"),
                cidade=data.get("cidade"),
                estado=data.get("estado"),
                cnae=data.get("cnae"),
                tributacao=data.get("tributacao"),
                atividade=data.get("atividade"),
                pis_cofins=data.get("pis_cofins"),
                observacoes=data.get("observacoes")
            )
            db.session.add(nova)
            db.session.commit()

            # Se for um usuário comum, atribuir automaticamente a empresa a ele
            if usuario.tipo_usuario == 'usuario':
                empresas_permitidas = usuario.empresas_permitidas or []
                if nova.id not in empresas_permitidas:
                    empresas_permitidas.append(nova.id)
                    usuario.empresas_permitidas = empresas_permitidas
                    db.session.commit()

            return {"message": "Empresa criada com sucesso", "id": nova.id}, 201
        except Exception as e:
            print(f"Erro ao criar empresa: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Empresa - Obter
    @main_bp.route("/api/empresas/<int:empresa_id>", methods=["GET"])
    @jwt_required()
    def obter_empresa(empresa_id):
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar a empresa
            empresa = Empresa.query.get(empresa_id)
            if not empresa:
                return {"message": "Empresa não encontrada"}, 404

            # Verificar permissões para visualizar a empresa
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem ver qualquer empresa
                pass
            elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem ver empresas do seu próprio escritório
                pass
            elif usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas:
                # Usuários comuns só podem ver empresas permitidas
                pass
            else:
                return {"message": "Você não tem permissão para visualizar esta empresa"}, 403

            return {"empresa": empresa.to_dict()}, 200
        except Exception as e:
            print(f"Erro ao obter empresa: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Empresa - Atualizar
    @main_bp.route("/api/empresas/<int:empresa_id>", methods=["PUT"])
    @jwt_required()
    def atualizar_empresa(empresa_id):
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar a empresa
            empresa = Empresa.query.get(empresa_id)
            if not empresa:
                return {"message": "Empresa não encontrada"}, 404

            # Verificar permissões para editar a empresa
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem editar qualquer empresa
                pass
            elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem editar empresas do seu próprio escritório
                pass
            else:
                # Usuários comuns não podem editar empresas
                return {"message": "Você não tem permissão para editar esta empresa"}, 403

            # Validar dados obrigatórios
            razao_social = data.get("razao_social")
            cnpj = data.get("cnpj")

            if not cnpj or not razao_social:
                return {"message": "CNPJ e Razão Social são obrigatórios"}, 400

            # Verificar se já existe outra empresa com o mesmo CNPJ no mesmo escritório
            empresa_existente = Empresa.query.filter_by(cnpj=cnpj, escritorio_id=empresa.escritorio_id).first()
            if empresa_existente and empresa_existente.id != empresa_id:
                return {"message": "Já existe outra empresa com este CNPJ neste escritório"}, 400

            # Atualizar os campos da empresa
            empresa.razao_social = razao_social
            empresa.cnpj = cnpj
            empresa.inscricao_estadual = data.get("inscricao_estadual")
            empresa.nome_fantasia = data.get("nome_fantasia")
            empresa.email = data.get("email")
            empresa.responsavel = data.get("responsavel")
            empresa.cep = data.get("cep")
            empresa.logradouro = data.get("logradouro")
            empresa.numero = data.get("numero")
            empresa.complemento = data.get("complemento")
            empresa.bairro = data.get("bairro")
            empresa.cidade = data.get("cidade")
            empresa.estado = data.get("estado")
            empresa.cnae = data.get("cnae")
            empresa.tributacao = data.get("tributacao")
            empresa.atividade = data.get("atividade")
            empresa.pis_cofins = data.get("pis_cofins")
            empresa.observacoes = data.get("observacoes")

            db.session.commit()

            return {"message": "Empresa atualizada com sucesso"}, 200
        except Exception as e:
            print(f"Erro ao atualizar empresa: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # API - Consultar CNPJ
    @main_bp.route("/api/consultar-cnpj/<cnpj>", methods=["GET"])
    @jwt_required()
    def consultar_cnpj(cnpj):
        try:
            from utils.api_client import fetch_cnpj_data
            
            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Apenas usuários com permissão para criar empresas podem consultar CNPJ
            if not (usuario.is_admin or usuario.tipo_usuario in ['admin', 'escritorio']):
                return {"message": "Você não tem permissão para consultar CNPJ"}, 403

            # Limpar CNPJ (remover caracteres especiais)
            cnpj_limpo = ''.join(filter(str.isdigit, cnpj))
            
            if len(cnpj_limpo) != 14:
                return {"message": "CNPJ deve ter 14 dígitos"}, 400

            # Verificar se já existe empresa com este CNPJ
            empresa_existente = None
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Admin: priorizar empresa do próprio escritório, senão buscar globalmente
                empresa_existente = Empresa.query.filter_by(cnpj=cnpj_limpo, escritorio_id=usuario.escritorio_id).first()
                if not empresa_existente:
                    empresa_existente = Empresa.query.filter_by(cnpj=cnpj_limpo).first()
            elif usuario.tipo_usuario == 'escritorio':
                empresa_existente = Empresa.query.filter_by(
                    cnpj=cnpj_limpo, 
                    escritorio_id=usuario.escritorio_id
                ).first()

            if empresa_existente:
                return {
                    "message": "Empresa já cadastrada",
                    "empresa_existente": True,
                    "empresa": empresa_existente.to_dict()
                }, 409

            # Buscar dados na API
            dados_cnpj = fetch_cnpj_data(cnpj_limpo)
            
            if not dados_cnpj:
                return {"message": "CNPJ não encontrado ou erro na consulta"}, 404

            # Extrair dados da resposta da API para o formato esperado pelo frontend
            resultado = {
                "cnpj": cnpj_limpo,
                "razao_social": dados_cnpj.get('razao_social', ''),
                "nome_fantasia": dados_cnpj.get('nome_fantasia', ''),
                "email": dados_cnpj.get('email', ''),
                "cnae": dados_cnpj.get('cnae', ''),
                "atividade": dados_cnpj.get('atividade', ''),
                "tributacao": "Simples Nacional" if dados_cnpj.get('simples_nacional') else "",
                # Dados de endereço
                "cep": dados_cnpj.get('cep', ''),
                "logradouro": dados_cnpj.get('logradouro', ''),
                "numero": dados_cnpj.get('numero', ''),
                "complemento": dados_cnpj.get('complemento', ''),
                "bairro": dados_cnpj.get('bairro', ''),
                "cidade": dados_cnpj.get('cidade', ''),
                "estado": dados_cnpj.get('estado', ''),
                # Dados adicionais
                "inscricoes_estaduais": dados_cnpj.get('inscricoes_estaduais', [])
            }

            return {"success": True, "dados": resultado}, 200

        except Exception as e:
            print(f"Erro ao consultar CNPJ: {str(e)}")
            return {"message": "Erro ao consultar CNPJ"}, 500

    # API - Consultar CEP
    @main_bp.route("/api/consultar-cep/<cep>", methods=["GET"])
    @jwt_required()
    def consultar_cep(cep):
        try:
            from utils.api_client import fetch_cep_data
            
            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Limpar CEP (remover caracteres especiais)
            cep_limpo = ''.join(filter(str.isdigit, cep))
            
            if len(cep_limpo) != 8:
                return {"message": "CEP deve ter 8 dígitos"}, 400

            # Buscar dados na API ViaCEP
            dados_cep = fetch_cep_data(cep_limpo)
            
            if not dados_cep:
                return {"message": "CEP não encontrado"}, 404

            return {"success": True, "dados": dados_cep}, 200

        except Exception as e:
            print(f"Erro ao consultar CEP: {str(e)}")
            return {"message": "Erro ao consultar CEP"}, 500

    # CRUD Empresa - Excluir
    @main_bp.route("/api/empresas/<int:empresa_id>", methods=["DELETE"])
    @jwt_required()
    def excluir_empresa(empresa_id):
        try:
            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar a empresa
            empresa = Empresa.query.get(empresa_id)
            if not empresa:
                return {"message": "Empresa não encontrada"}, 404

            # Verificar permissões para excluir a empresa
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem excluir qualquer empresa
                pass
            elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem excluir empresas do seu próprio escritório
                pass
            else:
                # Usuários comuns não podem excluir empresas
                return {"message": "Você não tem permissão para excluir esta empresa"}, 403

            # Excluir a empresa
            db.session.delete(empresa)
            db.session.commit()

            return {"message": "Empresa excluída com sucesso"}, 200
        except Exception as e:
            print(f"Erro ao excluir empresa: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Usuário - Listar
    @main_bp.route("/api/usuarios", methods=["GET"])
    @jwt_required()
    def listar_usuarios():
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Filtrar usuários com base no tipo de usuário
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores veem todos os usuários
                usuarios = Usuario.query.all()
            elif usuario.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório veem apenas usuários do seu escritório
                usuarios = Usuario.query.filter_by(escritorio_id=usuario.escritorio_id).all()
            else:
                # Usuários comuns veem apenas seu próprio usuário
                usuarios = [usuario]

            return {"usuarios": [u.to_dict() for u in usuarios]}
        except Exception as e:
            print(f"Erro ao listar usuários: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Usuário - Criar
    @main_bp.route("/api/usuarios", methods=["POST"])
    @jwt_required()
    def criar_usuario():
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario_atual = db.session.get(Usuario, usuario_id)

            if not usuario_atual:
                return {"message": "Usuário não encontrado"}, 404

            # Validar dados
            nome = data.get("nome")
            portal_user_id = data.get("portal_user_id")

            if not nome or not portal_user_id:
                return {"message": "Nome e portal_user_id são obrigatórios"}, 400

            if Usuario.query.filter_by(portal_user_id=portal_user_id).first():
                return {"message": "Usuário já existe"}, 400

            # Determinar o escritório_id e tipo_usuario com base no tipo de usuário atual
            if usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin':
                # Administradores podem especificar o escritório e tipo de usuário
                escritorio_id = data.get("escritorio_id")
                tipo_usuario = data.get("tipo_usuario", "usuario")
            elif usuario_atual.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório só podem criar usuários para seu próprio escritório
                # e apenas do tipo 'usuario'
                escritorio_id = usuario_atual.escritorio_id
                tipo_usuario = "usuario"
            else:
                # Usuários comuns não podem criar outros usuários
                return {"message": "Você não tem permissão para criar usuários"}, 403

            # Verificar permissões com base no tipo de usuário atual
            if usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin':
                # Administradores podem criar qualquer tipo de usuário
                pass
            elif usuario_atual.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório só podem criar usuários comuns para seu escritório
                if tipo_usuario != 'usuario':
                    return {"message": "Você só pode criar usuários do tipo comum"}, 403

                # Forçar o escritorio_id para o escritório do usuário atual
                if escritorio_id != usuario_atual.escritorio_id:
                    return {"message": "Você só pode criar usuários para o seu próprio escritório"}, 403
            else:
                # Usuários comuns não podem criar outros usuários
                return {"message": "Você não tem permissão para criar usuários"}, 403

            # Se for admin, garantir que is_admin seja True
            is_admin = data.get("is_admin", False)
            if tipo_usuario == "admin":
                is_admin = True

                # Apenas administradores podem criar outros administradores
                if not (usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin'):
                    return {"message": "Você não tem permissão para criar usuários administradores"}, 403

            # Criar o usuário
            novo = Usuario(
                nome=nome,
                portal_user_id=portal_user_id,
                escritorio_id=escritorio_id,
                is_admin=is_admin,
                tipo_usuario=tipo_usuario,
                empresas_permitidas=data.get("empresas_permitidas", [])
            )
            db.session.add(novo)
            db.session.commit()

            return {"message": "Usuário criado com sucesso", "id": novo.id}, 201
        except Exception as e:
            print(f"Erro ao criar usuário: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Usuário - Obter
    @main_bp.route("/api/usuarios/<int:usuario_id>", methods=["GET"])
    @jwt_required()
    def obter_usuario(usuario_id):
        try:
            usuario_atual_id = get_jwt_identity()
            usuario_atual = db.session.get(Usuario, usuario_atual_id)

            if not usuario_atual:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar o usuário solicitado
            usuario = db.session.get(Usuario, usuario_id)
            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Verificar permissões para visualizar o usuário
            if usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin':
                # Administradores podem ver qualquer usuário
                pass
            elif usuario_atual.tipo_usuario == 'escritorio' and usuario.escritorio_id == usuario_atual.escritorio_id:
                # Usuários do tipo escritório só podem ver usuários do seu próprio escritório
                pass
            elif usuario_atual.id == usuario_id:
                # Usuários podem ver seu próprio perfil
                pass
            else:
                return {"message": "Você não tem permissão para visualizar este usuário"}, 403

            return {"usuario": usuario.to_dict()}, 200
        except Exception as e:
            print(f"Erro ao obter usuário: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Usuário - Atualizar
    @main_bp.route("/api/usuarios/<int:usuario_id>", methods=["PUT"])
    @jwt_required()
    def atualizar_usuario(usuario_id):
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_atual_id = get_jwt_identity()
            usuario_atual = db.session.get(Usuario, usuario_atual_id)

            if not usuario_atual:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar o usuário a ser atualizado
            usuario = db.session.get(Usuario, usuario_id)
            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Verificar permissões para editar o usuário
            if usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin':
                # Administradores podem editar qualquer usuário
                pass
            elif usuario_atual.tipo_usuario == 'escritorio' and usuario.escritorio_id == usuario_atual.escritorio_id:
                # Usuários do tipo escritório só podem editar usuários do seu próprio escritório
                # E não podem alterar o tipo de usuário para admin ou escritório
                if data.get("tipo_usuario") in ['admin', 'escritorio'] and usuario.tipo_usuario == 'usuario':
                    return {"message": "Você não tem permissão para alterar o tipo deste usuário"}, 403
            elif usuario_atual.id == usuario_id:
                # Usuários podem editar seu próprio perfil, mas não podem alterar o tipo
                if data.get("tipo_usuario") != usuario.tipo_usuario:
                    return {"message": "Você não tem permissão para alterar seu tipo de usuário"}, 403
            else:
                return {"message": "Você não tem permissão para editar este usuário"}, 403

            # Atualizar nome se fornecido
            nome = data.get("nome")
            if nome is not None:
                usuario.nome = nome

            # Atualizar tipo de usuário se permitido
            if (usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin') and data.get("tipo_usuario"):
                usuario.tipo_usuario = data.get("tipo_usuario")
                # Se for admin, garantir que is_admin seja True
                if data.get("tipo_usuario") == "admin":
                    usuario.is_admin = True
                else:
                    usuario.is_admin = data.get("is_admin", False)

            # Atualizar empresas permitidas
            if data.get("empresas_permitidas") is not None:
                usuario.empresas_permitidas = data.get("empresas_permitidas")

            db.session.commit()

            return {"message": "Usuário atualizado com sucesso"}, 200
        except Exception as e:
            print(f"Erro ao atualizar usuário: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # Eventos WebSocket
    @socketio.on('connect')
    def handle_connect(auth=None):
        """
        Evento de conexão WebSocket
        """
        print('Novo cliente WebSocket conectado')

    @socketio.on('disconnect')
    def handle_disconnect(data=None):
        """
        Evento de desconexão WebSocket
        
        Args:
            data: Dados opcionais enviados pelo cliente
        """
        print('Cliente WebSocket desconectado')

    @socketio.on('join_import')
    def handle_join_import(data):
        """
        Evento para entrar na sala de uma importação
        """
        try:
            token = data.get('token')
            import_id = data.get('import_id')

            if not token or not import_id:
                return {'error': 'Token e import_id são obrigatórios'}

            user_id = websocket_service.authenticate_user(token)
            if not user_id:
                return {'error': 'Token inválido'}

            room = websocket_service.join_import_room(user_id, import_id)
            return {'success': True, 'room': room}

        except Exception as e:
            print(f"Erro ao entrar na sala de importação: {str(e)}")
            return {'error': 'Erro interno do servidor'}

    @socketio.on('join_audit')
    def handle_join_audit(data):
        """
        Evento para entrar na sala de uma auditoria
        """
        try:
            token = data.get('token')
            audit_id = data.get('audit_id')

            if not token or not audit_id:
                return {'error': 'Token e audit_id são obrigatórios'}

            user_id = websocket_service.authenticate_user(token)
            if not user_id:
                return {'error': 'Token inválido'}


            room = websocket_service.join_audit_room(user_id, audit_id)
            return {'success': True, 'room': room}

        except Exception as e:
            print(f"Erro ao entrar na sala de auditoria: {str(e)}")
            return {'error': 'Erro interno do servidor'}

    # Rota para servir arquivos de upload
    @main_bp.route('/static/uploads/logos/<path:filename>')
    def uploaded_file(filename):
        """
        Rota para servir arquivos de upload de logos
        """
        from flask import send_from_directory
        import os
        uploads_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'front', 'static', 'uploads', 'logos')
        return send_from_directory(uploads_dir, filename)

    # Registrar o blueprint principal na aplicação
    app.register_blueprint(main_bp, url_prefix='/fiscal')

    # Adicionar redirecionamento da raiz para /fiscal/
    @app.route('/')
    def root_redirect():
        return redirect('/fiscal/', code=302)

    return app, socketio

if __name__ == '__main__':
    app, socketio = create_app()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
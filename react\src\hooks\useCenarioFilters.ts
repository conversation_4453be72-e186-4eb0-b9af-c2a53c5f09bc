import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { cenariosService } from '@/services/cenariosService'
import type { TipoTributo } from '@/types/cenarios'

interface FilterOption {
  value: string
  label: string
  related?: Record<string, string[]>
}

interface FilterState {
  cfops: string[]
  ncms: string[]
  csts: string[]
  estados: string[]
  aliquotas: string[]
  aliquotas_st: string[]
  reducoes: string[]
  reducoes_st: string[]
  atividades: string[]
  destinacoes: string[]
  mvas: string[]
}

interface UseCenarioFiltersProps {
  tipoTributo: TipoTributo
  empresaId: number | null
  status: 'novo' | 'producao' | 'inconsistente'
}

export function useCenarioFilters({
  tipoTributo,
  empresaId,
  status
}: UseCenarioFiltersProps) {
  const [filters, setFilters] = useState<FilterState>({
    cfops: [],
    ncms: [],
    csts: [],
    estados: [],
    aliquotas: [],
    aliquotas_st: [],
    reducoes: [],
    reducoes_st: [],
    atividades: [],
    destinacoes: [],
    mvas: []
  })

  // Fetch filter options
  const { data: filterOptions, isLoading: isLoadingOptions } = useQuery({
    queryKey: ['cenario-filter-options', tipoTributo, empresaId, status],
    queryFn: async () => {
      if (!empresaId) return null
      
      const result = await cenariosService.getFilterOptions(tipoTributo, {
        empresa_id: empresaId,
        status,
        direcao: 'saida'
      })
      
      return result.opcoes || {}
    },
    enabled: !!empresaId,
    refetchOnWindowFocus: false
  })

  // Apply filters to update data
  const applyFilters = () => {
    // This will be handled by the parent component
    // We're just managing the state here
  }

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      cfops: [],
      ncms: [],
      csts: [],
      estados: [],
      aliquotas: [],
      aliquotas_st: [],
      reducoes: [],
      reducoes_st: [],
      atividades: [],
      destinacoes: [],
      mvas: []
    })
  }

  // Update a specific filter
  const updateFilter = (filterType: keyof FilterState, values: string[]) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: values
    }))
  }

  // Get filter parameters for API calls
  const getFilterParams = () => {
    const params: Record<string, string> = {}
    
    if (filters.cfops.length > 0) params.cfop = filters.cfops.join(',')
    if (filters.ncms.length > 0) params.ncm = filters.ncms.join(',')
    if (filters.csts.length > 0) params.cst = filters.csts.join(',')
    if (filters.estados.length > 0) params.uf = filters.estados.join(',')
    if (filters.aliquotas.length > 0) params.aliquota = filters.aliquotas.join(',')
    if (filters.aliquotas_st.length > 0) params.aliquota_st = filters.aliquotas_st.join(',')
    if (filters.reducoes.length > 0) params.reducao = filters.reducoes.join(',')
    if (filters.reducoes_st.length > 0) params.reducao_st = filters.reducoes_st.join(',')
    if (filters.atividades.length > 0) params.atividade = filters.atividades.join(',')
    if (filters.destinacoes.length > 0) params.destinacao = filters.destinacoes.join(',')
    if (filters.mvas.length > 0) params.mva = filters.mvas.join(',')
    
    return params
  }

  return {
    filters,
    filterOptions: filterOptions || {},
    isLoadingOptions,
    applyFilters,
    clearFilters,
    updateFilter,
    getFilterParams
  }
}
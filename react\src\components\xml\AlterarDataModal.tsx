import { useState, useEffect } from 'react'

interface Props {
  isOpen: boolean
  chaves: string[]
  onClose: () => void
  onSave: (dataEntrada: string, motivo?: string) => void
}

export function AlterarDataModal({ isOpen, chaves, onClose, onSave }: Props) {
  const [dataEntrada, setDataEntrada] = useState('')
  const [motivo, setMotivo] = useState('')

  useEffect(() => {
    if (isOpen) {
      setDataEntrada('')
      setMotivo('')
    }
  }, [isOpen])

  if (!isOpen) return null

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!dataEntrada) return
    onSave(dataEntrada, motivo)
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md">
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Alterar Data
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">×</button>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Nova Data de Entrada *
            </label>
            <input
              type="text"
              placeholder="dd/mm/aaaa"
              value={dataEntrada}
              onChange={(e) => setDataEntrada(e.target.value)}
              required
              pattern="\d{2}/\d{2}/\d{4}"
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Use o formato brasileiro: dd/mm/aaaa
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Motivo da Alteração
            </label>
            <textarea
              value={motivo}
              onChange={(e) => setMotivo(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {chaves.length} nota(s) selecionada(s)
          </div>
          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 rounded-md bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="px-4 py-2 rounded-md bg-primary-600 text-white hover:bg-primary-700"
            >
              Salvar
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
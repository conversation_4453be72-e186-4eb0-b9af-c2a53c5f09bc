"""
Modelo para armazenar matches manuais temporários
"""

from models import db
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy import func
import json
from services.transactional import transactional_session


class MatchingManualTemporario(db.Model):
    """
    Modelo para armazenar matches manuais temporários antes de serem processados
    Permite relacionar 1 item SPED com N itens XML
    """
    __tablename__ = 'matching_manual_temporario'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.<PERSON>Key('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.Integer, db.<PERSON>ey('escritorio.id'), nullable=False)
    usuario_id = db.Column(db.Integer, db.<PERSON>ey('usuario.id'), nullable=False)
    
    # Item SPED principal (1)
    sped_item_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>ey('item_nota_entrada.id'), nullable=False)
    sped_descricao = db.Column(db.String(255))
    sped_codigo = db.Column(db.String(50))
    
    # Itens XML relacionados (N)
    xml_items_ids = db.Column(ARRAY(db.Integer), nullable=False)  # Array de IDs
    xml_items_data = db.Column(db.JSON, nullable=False)  # Dados detalhados
    
    # Informações do matching
    justificativa = db.Column(db.Text)
    confianca_usuario = db.Column(db.Integer, default=5)  # 1-5
    
    # Controle
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    data_processamento = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='pendente')  # pendente, processado, cancelado
    
    def __repr__(self):
        return f"<MatchingManualTemporario {self.id} - SPED:{self.sped_item_id} -> XML:{len(self.xml_items_ids)} itens>"
    
    def to_dict(self):
        """Converte o modelo para dicionário"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'usuario_id': self.usuario_id,
            'sped_item_id': self.sped_item_id,
            'sped_descricao': self.sped_descricao,
            'sped_codigo': self.sped_codigo,
            'xml_items_ids': self.xml_items_ids,
            'xml_items_data': self.xml_items_data,
            'justificativa': self.justificativa,
            'confianca_usuario': self.confianca_usuario,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_processamento': self.data_processamento.isoformat() if self.data_processamento else None,
            'status': self.status
        }
    
    @classmethod
    def criar_match_temporario(cls, empresa_id, escritorio_id, usuario_id, 
                              sped_item_id, sped_descricao, sped_codigo,
                              xml_items_data, justificativa=None, confianca=5):
        """
        Cria um novo match manual temporário
        
        Args:
            empresa_id: ID da empresa
            escritorio_id: ID do escritório
            usuario_id: ID do usuário
            sped_item_id: ID do item SPED
            sped_descricao: Descrição do item SPED
            sped_codigo: Código do item SPED
            xml_items_data: Lista de dicionários com dados dos itens XML
            justificativa: Justificativa do usuário
            confianca: Nível de confiança (1-5)
        
        Returns:
            MatchingManualTemporario: Instância criada
        """
        # Extrair IDs dos itens XML
        xml_items_ids = [item['id'] for item in xml_items_data]
        
        # Verificar se já existe match pendente para este SPED
        existing = cls.query.filter_by(
            sped_item_id=sped_item_id,
            usuario_id=usuario_id,
            status='pendente'
        ).first()
        
        if existing:
            # Atualizar o existente
            existing.xml_items_ids = xml_items_ids
            existing.xml_items_data = xml_items_data
            existing.justificativa = justificativa
            existing.confianca_usuario = confianca
            existing.data_criacao = func.now()
            return existing
        else:
            # Criar novo
            match_temporario = cls(
                empresa_id=empresa_id,
                escritorio_id=escritorio_id,
                usuario_id=usuario_id,
                sped_item_id=sped_item_id,
                sped_descricao=sped_descricao,
                sped_codigo=sped_codigo,
                xml_items_ids=xml_items_ids,
                xml_items_data=xml_items_data,
                justificativa=justificativa,
                confianca_usuario=confianca
            )
            
            db.session.add(match_temporario)
            return match_temporario
    
    def processar_match(self):
        """
        Processa o match temporário, criando registros na auditoria_comparativa_impostos
        """
        try:
            with transactional_session():
                # Marcar como processado
                self.status = 'processado'
                self.data_processamento = func.now()

                # Importar aqui para evitar importação circular
                from models.auditoria_comparativa_impostos import AuditoriaComparativaImpostos
                from models.nota_fiscal_item import NotaFiscalItem
                from models.item_nota_entrada import ItemNotaEntrada

                # Processar cada item XML com o SPED
                for xml_item_data in self.xml_items_data:
                    # Buscar dados completos do item XML
                    xml_item = NotaFiscalItem.query.get(xml_item_data['id'])
                    sped_item = ItemNotaEntrada.query.get(self.sped_item_id)

                    if not xml_item or not sped_item:
                        continue

                # Verificar se já existe um registro para este SPED item (unmatched_sped)
                # Priorizar registros unmatched_sped para atualizar
                auditoria_existente = AuditoriaComparativaImpostos.query.filter_by(
                    sped_item_id=sped_item.id,
                    empresa_id=self.empresa_id,
                    match_type='unmatched_sped'
                ).first()

                # Se não encontrou unmatched_sped, verificar se existe registro para este XML
                if not auditoria_existente:
                    auditoria_existente = AuditoriaComparativaImpostos.query.filter_by(
                        xml_item_id=xml_item.id,
                        empresa_id=self.empresa_id
                    ).first()

                if auditoria_existente:
                    # Atualizar registro existente com o novo match manual
                    auditoria = auditoria_existente

                    # Se era unmatched_sped, agora adicionar o XML
                    if auditoria.match_type == 'unmatched_sped':
                        auditoria.xml_item_id = xml_item.id

                    # Se era unmatched_xml ou outro tipo, atualizar o SPED
                    auditoria.sped_item_id = sped_item.id
                    auditoria.match_type = 'manual'
                    auditoria.confidence_score = self.confianca_usuario / 5.0
                    auditoria.match_details = {
                        'reason': 'Match manual pelo usuário',
                        'justificativa': self.justificativa,
                        'confianca_usuario': self.confianca_usuario,
                        'data_match': self.data_criacao.isoformat() if self.data_criacao else None
                    }
                    auditoria.status_auditoria = 'em_analise'
                    auditoria.usuario_auditoria = self.usuario_id
                else:
                    # Criar novo registro de auditoria com match manual
                    auditoria = AuditoriaComparativaImpostos(
                        empresa_id=self.empresa_id,
                        escritorio_id=self.escritorio_id,
                        chave_nf=xml_item_data.get('chave_nf'),
                        numero_nf=xml_item_data.get('numero_nf'),
                        xml_item_id=xml_item.id,
                        sped_item_id=sped_item.id,
                        cliente_id=xml_item.cliente_id,  # Usar cliente_id diretamente
                        match_type='manual',
                        confidence_score=self.confianca_usuario / 5.0,  # Converter para 0-1
                        match_details={
                            'reason': 'Match manual pelo usuário',
                            'justificativa': self.justificativa,
                            'confianca_usuario': self.confianca_usuario,
                            'data_match': self.data_criacao.isoformat() if self.data_criacao else None
                        },
                        status_auditoria='em_analise',
                        usuario_auditoria=self.usuario_id
                    )
                    db.session.add(auditoria)

                # Preencher dados XML (sempre atualizar)
                auditoria.xml_descricao = xml_item.produto.descricao if xml_item.produto else xml_item_data.get('descricao')
                auditoria.xml_quantidade = xml_item.quantidade
                auditoria.xml_valor_unitario = xml_item.valor_unitario
                auditoria.xml_valor_total = xml_item.valor_total
                auditoria.xml_unidade = xml_item.unidade_comercial
                auditoria.xml_ncm = xml_item.ncm
                auditoria.xml_cfop = xml_item.cfop

                # Atualizar campos básicos do XML se estava vazio (caso unmatched_sped)
                if not auditoria.chave_nf:
                    auditoria.chave_nf = xml_item_data.get('chave_nf')
                if not auditoria.numero_nf:
                    auditoria.numero_nf = xml_item_data.get('numero_nf')
                if not auditoria.cliente_id:
                    auditoria.cliente_id = xml_item.cliente_id

                # Preencher dados SPED (sempre atualizar)
                auditoria.sped_descricao = sped_item.descricao_complementar or (sped_item.produto_entrada.descricao if sped_item.produto_entrada else None)
                auditoria.sped_quantidade = sped_item.quantidade
                auditoria.sped_valor_item = sped_item.valor_item
                auditoria.sped_unidade = sped_item.unidade
                auditoria.sped_cfop = sped_item.cfop
                auditoria.sped_cod_item = sped_item.cod_item

            return True

        except Exception as e:
            self.status = 'erro'
            print(f"Erro ao processar match temporário {self.id}: {str(e)}")
            return False

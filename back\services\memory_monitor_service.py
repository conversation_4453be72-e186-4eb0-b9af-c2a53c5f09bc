"""
Memory monitoring service for virtual scrolling optimization
"""

import psutil
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MemoryStats:
    """Memory statistics data class"""
    total_memory: int
    available_memory: int
    used_memory: int
    memory_percent: float
    process_memory: int
    process_memory_percent: float

class MemoryMonitorService:
    """Service for monitoring memory usage and dataset size limits"""
    
    # Memory thresholds (in MB)
    DEFAULT_MAX_DATASET_SIZE = 50000  # Maximum records for full load
    DEFAULT_MEMORY_THRESHOLD = 80.0   # Memory usage percentage threshold
    DEFAULT_PROCESS_MEMORY_LIMIT = 512  # Process memory limit in MB
    
    def __init__(self, 
                 max_dataset_size: int = DEFAULT_MAX_DATASET_SIZE,
                 memory_threshold: float = DEFAULT_MEMORY_THRESHOLD,
                 process_memory_limit: int = DEFAULT_PROCESS_MEMORY_LIMIT):
        self.max_dataset_size = max_dataset_size
        self.memory_threshold = memory_threshold
        self.process_memory_limit = process_memory_limit
    
    def get_memory_stats(self) -> MemoryStats:
        """Get current memory statistics"""
        try:
            # System memory
            memory = psutil.virtual_memory()
            
            # Process memory
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return MemoryStats(
                total_memory=memory.total,
                available_memory=memory.available,
                used_memory=memory.used,
                memory_percent=memory.percent,
                process_memory=process_memory.rss,
                process_memory_percent=(process_memory.rss / memory.total) * 100
            )
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            # Return default stats if monitoring fails
            return MemoryStats(
                total_memory=0,
                available_memory=0,
                used_memory=0,
                memory_percent=0.0,
                process_memory=0,
                process_memory_percent=0.0
            )
    
    def check_memory_constraints(self, record_count: int) -> Dict[str, Any]:
        """
        Check if dataset can be loaded based on memory constraints
        
        Args:
            record_count: Number of records to be loaded
            
        Returns:
            Dict with constraint check results
        """
        stats = self.get_memory_stats()
        
        # Check dataset size limit
        if record_count > self.max_dataset_size:
            return {
                'can_load_all': False,
                'reason': 'dataset_too_large',
                'message': f'Dataset too large ({record_count:,} records). Maximum allowed: {self.max_dataset_size:,}',
                'suggested_action': 'use_progressive_loading',
                'record_count': record_count,
                'max_allowed': self.max_dataset_size,
                'memory_stats': stats
            }
        
        # Check system memory usage
        if stats.memory_percent > self.memory_threshold:
            return {
                'can_load_all': False,
                'reason': 'high_memory_usage',
                'message': f'System memory usage too high ({stats.memory_percent:.1f}%). Threshold: {self.memory_threshold}%',
                'suggested_action': 'use_pagination',
                'record_count': record_count,
                'memory_stats': stats
            }
        
        # Check process memory limit
        process_memory_mb = stats.process_memory / (1024 * 1024)
        if process_memory_mb > self.process_memory_limit:
            return {
                'can_load_all': False,
                'reason': 'process_memory_limit',
                'message': f'Process memory usage too high ({process_memory_mb:.1f}MB). Limit: {self.process_memory_limit}MB',
                'suggested_action': 'use_pagination',
                'record_count': record_count,
                'memory_stats': stats
            }
        
        # All checks passed
        return {
            'can_load_all': True,
            'reason': 'memory_ok',
            'message': f'Memory constraints OK for {record_count:,} records',
            'suggested_action': 'proceed',
            'record_count': record_count,
            'memory_stats': stats
        }
    
    def estimate_memory_usage(self, record_count: int, avg_record_size: int = 1024) -> Dict[str, Any]:
        """
        Estimate memory usage for a given dataset
        
        Args:
            record_count: Number of records
            avg_record_size: Average size per record in bytes
            
        Returns:
            Dict with memory usage estimation
        """
        estimated_bytes = record_count * avg_record_size
        estimated_mb = estimated_bytes / (1024 * 1024)
        
        stats = self.get_memory_stats()
        available_mb = stats.available_memory / (1024 * 1024)
        
        return {
            'estimated_memory_mb': estimated_mb,
            'available_memory_mb': available_mb,
            'memory_sufficient': estimated_mb < (available_mb * 0.8),  # Use 80% of available
            'record_count': record_count,
            'avg_record_size': avg_record_size,
            'current_stats': stats
        }
    
    def get_recommended_batch_size(self, total_records: int) -> int:
        """
        Get recommended batch size for progressive loading
        
        Args:
            total_records: Total number of records
            
        Returns:
            Recommended batch size
        """
        stats = self.get_memory_stats()
        available_mb = stats.available_memory / (1024 * 1024)
        
        # Base batch size on available memory
        if available_mb > 1000:  # > 1GB available
            return min(10000, total_records)
        elif available_mb > 500:  # > 500MB available
            return min(5000, total_records)
        else:  # < 500MB available
            return min(2000, total_records)
    
    def log_memory_usage(self, operation: str, record_count: int = 0):
        """Log current memory usage for debugging"""
        stats = self.get_memory_stats()
        logger.info(
            f"Memory usage - Operation: {operation}, "
            f"Records: {record_count:,}, "
            f"System: {stats.memory_percent:.1f}%, "
            f"Process: {stats.process_memory_percent:.1f}%, "
            f"Available: {stats.available_memory / (1024*1024):.1f}MB"
        )
import api from './authService'
import type { XMLsResponse, XMLDetalhesResponse } from '@/types/xmls'

export type GestaoTab = 'entrada' | 'saida' | 'faltantes-entrada' | 'faltantes-saida'

export const xmlsService = {
  async getDados(
    empresaId: number,
    tipo: GestaoTab,
    ano?: number,
    mes?: number
  ): Promise<XMLsResponse> {
    const params = new URLSearchParams()
    params.append('empresa_id', empresaId.toString())
    params.append('tipo', tipo)
    if (ano) params.append('ano', ano.toString())
    if (mes) params.append('mes', mes.toString())
    const response = await api.get(`/auditoria-entrada/xmls?${params.toString()}`)
    return response.data
  },

  async identificarNotasFaltantes(
    empresaId: number,
    tipo: 'faltantes-entrada' | 'faltantes-saida',
    ano: number,
    mes: number
  ) {
    const tipoNota = tipo === 'faltantes-entrada' ? '0' : '1'
    const response = await api.post('/auditoria-entrada/identificar-faltantes', {
      empresa_id: empresaId,
      ano,
      mes,
      tipo_nota: tipoNota,
    })
    return response.data
  },

  async cancelarXML(id: number) {
    const response = await api.post(`/auditoria-entrada/cancelar-xml/${id}`)
    return response.data
  },

  async excluirXML(id: number) {
    const response = await api.delete(`/auditoria-entrada/xmls/${id}`)
    return response.data
  },

  async excluirNotaFaltante(id: number) {
    const response = await api.delete(`/auditoria-entrada/notas-faltantes/${id}`)
    return response.data
  },

  async getDetalhes(id: number): Promise<XMLDetalhesResponse> {
    const response = await api.get(`/auditoria-entrada/xml-detalhes/${id}`)
    return response.data
  },

  async alterarDataNotas(
    chaves: string[],
    novaDataEntrada: string,
    motivo?: string
  ) {
    const response = await api.post('/auditoria-entrada/alterar-data-nota-real', {
      chaves_nf: chaves,
      nova_data_entrada: novaDataEntrada,
      motivo,
    })
    return response.data
  },

  async excluirNotasSistema(chaves: string[]) {
    const response = await api.post('/auditoria-entrada/excluir-nota-real', {
      chaves_nf: chaves,
    })
    return response.data
  },
}

export default xmlsService
-- Migration to add auditoria columns to tributo table

-- Add columns for ICMS audit values
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_icms_vbc DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_icms_valor DECIMAL(10, 2);

-- Add columns for ICMS-ST audit values
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_icms_st_vbc DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_icms_st_valor DECIMAL(10, 2);

-- Add columns for IPI audit values
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_ipi_vbc DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_ipi_valor DECIMAL(10, 2);

-- Add columns for PIS audit values
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_pis_vbc DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_pis_valor DECIMAL(10, 2);

-- Add columns for COFINS audit values
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_cofins_vbc DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_cofins_valor DECIMAL(10, 2);

-- Add columns for DIFAL audit values
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_difal_valor DECIMAL(10, 2);

-- Add columns for audit status and date
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_status VARCHAR(20) DEFAULT 'pendente';
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS auditoria_data TIMESTAMP;

-- Add column for audit cenario reference
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_icms_id INTEGER REFERENCES cenario_icms(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_icms_st_id INTEGER REFERENCES cenario_icms_st(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_ipi_id INTEGER REFERENCES cenario_ipi(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_pis_id INTEGER REFERENCES cenario_pis(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_cofins_id INTEGER REFERENCES cenario_cofins(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_difal_id INTEGER REFERENCES cenario_difal(id);

-- Add comments to explain the columns
COMMENT ON COLUMN tributo.cenario_icms_vbc IS 'Base de cálculo do ICMS calculada pelo cenário';
COMMENT ON COLUMN tributo.cenario_icms_valor IS 'Valor do ICMS calculado pelo cenário';
COMMENT ON COLUMN tributo.cenario_icms_st_vbc IS 'Base de cálculo do ICMS-ST calculada pelo cenário';
COMMENT ON COLUMN tributo.cenario_icms_st_valor IS 'Valor do ICMS-ST calculado pelo cenário';
COMMENT ON COLUMN tributo.cenario_ipi_vbc IS 'Base de cálculo do IPI calculada pelo cenário';
COMMENT ON COLUMN tributo.cenario_ipi_valor IS 'Valor do IPI calculado pelo cenário';
COMMENT ON COLUMN tributo.cenario_pis_vbc IS 'Base de cálculo do PIS calculada pelo cenário';
COMMENT ON COLUMN tributo.cenario_pis_valor IS 'Valor do PIS calculado pelo cenário';
COMMENT ON COLUMN tributo.cenario_cofins_vbc IS 'Base de cálculo do COFINS calculada pelo cenário';
COMMENT ON COLUMN tributo.cenario_cofins_valor IS 'Valor do COFINS calculado pelo cenário';
COMMENT ON COLUMN tributo.cenario_difal_valor IS 'Valor do DIFAL calculado pelo cenário';
COMMENT ON COLUMN tributo.auditoria_status IS 'Status da auditoria (pendente, realizada)';
COMMENT ON COLUMN tributo.auditoria_data IS 'Data da última auditoria';
COMMENT ON COLUMN tributo.cenario_icms_id IS 'ID do cenário de ICMS utilizado na auditoria';
COMMENT ON COLUMN tributo.cenario_icms_st_id IS 'ID do cenário de ICMS-ST utilizado na auditoria';
COMMENT ON COLUMN tributo.cenario_ipi_id IS 'ID do cenário de IPI utilizado na auditoria';
COMMENT ON COLUMN tributo.cenario_pis_id IS 'ID do cenário de PIS utilizado na auditoria';
COMMENT ON COLUMN tributo.cenario_cofins_id IS 'ID do cenário de COFINS utilizado na auditoria';
COMMENT ON COLUMN tributo.cenario_difal_id IS 'ID do cenário de DIFAL utilizado na auditoria';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tributo_auditoria_status ON tributo(auditoria_status);
CREATE INDEX IF NOT EXISTS idx_tributo_auditoria_data ON tributo(auditoria_data);

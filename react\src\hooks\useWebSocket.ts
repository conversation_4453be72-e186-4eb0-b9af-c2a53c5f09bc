import { useEffect, useState, useRef, useCallback } from 'react'
import { io, Socket } from 'socket.io-client'

interface UseWebSocketOptions {
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: any) => void
}

interface WebSocketState {
  connected: boolean
  connecting: boolean
  error: string | null
}

export function useWebSocket(options: UseWebSocketOptions = {}, onConnect?: () => void) {
  const [state, setState] = useState<WebSocketState>({
    connected: false,
    connecting: false,
    error: null
  })
  const [socket, setSocket] = useState<Socket | null>(null)

  const socketRef = useRef<Socket | null>(null)
  const listenersRef = useRef<Map<string, Function[]>>(new Map())

  useEffect(() => {
    // Conectar ao WebSocket
    setState(prev => ({ ...prev, connecting: true, error: null }))

    const socketInstance = io({
      transports: ['websocket', 'polling'],
      auth: {
        token: localStorage.getItem('token')
      },
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      timeout: 60000,
      forceNew: false,
      path: '/fiscal/socket.io'
    })

    socketInstance.on('connect', () => {
      console.log('🔌 WebSocket conectado')
      setState({ connected: true, connecting: false, error: null })
      options.onConnect?.()
      onConnect?.()
    })

    socketInstance.on('disconnect', () => {
      console.log('🔌 WebSocket desconectado')
      setState(prev => ({ ...prev, connected: false, connecting: false }))
      options.onDisconnect?.()
    })

    socketInstance.on('connect_error', (error) => {
      console.error('❌ Erro de conexão WebSocket:', error)
      setState({ connected: false, connecting: false, error: error.message })
      options.onError?.(error)
    })

    socketRef.current = socketInstance
    setSocket(socketInstance)

    return () => {
      // Limpar todos os listeners
      listenersRef.current.clear()
      socketInstance.close()
      socketRef.current = null
      setSocket(null)
    }
  }, [])

  const joinRoom = useCallback((roomId: string) => {
    if (socketRef.current?.connected) {
      console.log(`🔗 Entrando na sala: ${roomId}`)
      socketRef.current.emit('join_import', { 
        token: localStorage.getItem('token'),
        import_id: roomId 
      })
    }
  }, [])

  const leaveRoom = useCallback((roomId: string) => {
    if (socketRef.current?.connected) {
      console.log(`🔗 Saindo da sala: ${roomId}`)
      socketRef.current.emit('leave_import', { import_id: roomId })
    }
  }, [])

  const on = useCallback((event: string, callback: Function) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback)
      
      // Manter registro dos listeners para cleanup
      if (!listenersRef.current.has(event)) {
        listenersRef.current.set(event, [])
      }
      listenersRef.current.get(event)?.push(callback)
    }
  }, [])

  const off = useCallback((event: string, callback?: Function) => {
    if (socketRef.current) {
      if (callback) {
        socketRef.current.off(event, callback)
        
        // Remover do registro
        const listeners = listenersRef.current.get(event)
        if (listeners) {
          const index = listeners.indexOf(callback)
          if (index > -1) {
            listeners.splice(index, 1)
          }
        }
      } else {
        socketRef.current.off(event)
        listenersRef.current.delete(event)
      }
    }
  }, [])

  const connect = useCallback(() => {
    if (socketRef.current && !socketRef.current.connected) {
      console.log('🔌 Conectando WebSocket...')
      setState(prev => ({ ...prev, connecting: true, error: null }))
      socketRef.current.connect()
    }
  }, [])

  const disconnect = useCallback(() => {
    if (socketRef.current?.connected) {
      console.log('🔌 Desconectando WebSocket...')
      socketRef.current.disconnect()
    }
  }, [])

  const testConnection = useCallback(() => {
    return socketRef.current?.connected || false
  }, [])

  return {
    socket,
    state,
    joinRoom,
    leaveRoom,
    on,
    off,
    connect,
    disconnect,
    testConnection
  }
}
import { useState, useEffect, useCallback } from 'react'

// Estados dos filtros para auditoria
export interface AuditoriaFilterState {
  cfops: string[]
  ncms: string[]
  csts: string[]
  estados: string[]
  clientes: string[]
  produtos: string[]
  notas_fiscais: string[]
  status: string[]
  tipos_item: string[]
}

// Opções disponíveis para cada filtro
export interface AuditoriaFilterOptions {
  cfops: Array<{ value: string; label: string }>
  ncms: Array<{ value: string; label: string }>
  csts: Array<{ value: string; label: string }>
  estados: Array<{ value: string; label: string }>
  clientes: Array<{ value: string; label: string }>
  produtos: Array<{ value: string; label: string }>
  notas_fiscais: Array<{ value: string; label: string }>
  status: Array<{ value: string; label: string }>
  tipos_item: Array<{ value: string; label: string }>
}

const initialFilterState: AuditoriaFilterState = {
  cfops: [],
  ncms: [],
  csts: [],
  estados: [],
  clientes: [],
  produtos: [],
  notas_fiscais: [],
  status: [],
  tipos_item: []
}

const initialFilterOptions: AuditoriaFilterOptions = {
  cfops: [],
  ncms: [],
  csts: [],
  estados: [],
  clientes: [],
  produtos: [],
  notas_fiscais: [],
  status: [],
  tipos_item: []
}

interface UseAuditoriaFiltersProps {
  tipoAuditoria: 'escrituracao' | 'icms' | 'icms_st' | 'ipi' | 'pis' | 'cofins'
  empresaId?: number
  onFiltersChange?: (filters: AuditoriaFilterState, filteredData: any[], total: number, hasMore: boolean) => void
  pageSize?: number
}

export function useAuditoriaFilters({
  tipoAuditoria,
  empresaId,
  onFiltersChange,
  pageSize = 50
}: UseAuditoriaFiltersProps) {
  const [filters, setFilters] = useState<AuditoriaFilterState>(initialFilterState)
  const [options, setOptions] = useState<AuditoriaFilterOptions>(initialFilterOptions)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [hasMore, setHasMore] = useState(false)
  const [allLoadedData, setAllLoadedData] = useState<any[]>([])

  // Verificar se há filtros ativos
  const hasActiveFilters = Object.values(filters).some(filterArray => filterArray.length > 0)

  // Carregar opções de filtros do servidor
  const loadFilterOptions = useCallback(async () => {
    if (!empresaId) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(
        `/auditoria/api/entrada/${tipoAuditoria}/filter-options?empresa_id=${empresaId}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (!response.ok) {
        throw new Error(`Erro na requisição: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setOptions(data.options || initialFilterOptions)
      } else {
        throw new Error(data.message || 'Erro ao carregar opções de filtros')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      setError(errorMessage)
      console.error('Erro ao carregar opções de filtros:', err)
    } finally {
      setIsLoading(false)
    }
  }, [tipoAuditoria, empresaId])

  // Buscar dados filtrados do servidor
  const loadFilteredData = useCallback(async (filterState: AuditoriaFilterState, page: number = 1, append: boolean = false) => {
    if (!empresaId) return { data: [], total: 0, hasMore: false }

    setIsLoadingData(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        empresa_id: empresaId.toString(),
        page: page.toString(),
        limit: pageSize.toString()
      })

      // Mapeamento correto dos filtros para o backend
      const filterMapping: Record<string, string> = {
        'cfops': 'cfop',
        'ncms': 'ncm', 
        'csts': 'cst',
        'estados': 'uf',
        'clientes': 'cliente',
        'produtos': 'produto',
        'notas_fiscais': 'nota_fiscal',
        'status': 'status',
        'tipos_item': 'tipo_item'
      }

      // Adicionar filtros aos parâmetros com mapeamento correto
      Object.entries(filterState).forEach(([key, values]) => {
        if (values.length > 0) {
          const backendKey = filterMapping[key] || key
          params.append(backendKey, values.join(','))
          console.log(`🔗 Mapeando filtro auditoria: ${key} -> ${backendKey} = ${values.join(',')}`)
        }
      })

      const response = await fetch(
        `/auditoria/api/entrada/${tipoAuditoria}?${params.toString()}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (!response.ok) {
        throw new Error(`Erro na requisição: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        const items = data.items || []
        const total = data.total || items.length
        const hasMoreData = (page * pageSize) < total

        // Atualizar estados
        setTotalCount(total)
        setHasMore(hasMoreData)
        setCurrentPage(page)

        console.log(`✅ Dados de auditoria carregados: ${items.length} itens`)
        return { 
          data: items, 
          total, 
          hasMore: hasMoreData 
        }
      } else {
        throw new Error(data.message || 'Erro ao carregar dados filtrados')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      setError(errorMessage)
      console.error('Erro ao carregar dados filtrados:', err)
      return { data: [], total: 0, hasMore: false }
    } finally {
      setIsLoadingData(false)
    }
  }, [tipoAuditoria, empresaId, pageSize])

  // Atualizar um filtro específico
  const updateFilter = useCallback(async (filterType: keyof AuditoriaFilterState, values: string[]) => {
    console.log('🔍 Aplicando filtro auditoria:', { filterType, values })
    const newFilters = { ...filters, [filterType]: values }
    setFilters(newFilters)
    
    // Resetar dados acumulados quando filtros mudam
    setAllLoadedData([])
    setCurrentPage(1)
    
    // Buscar dados filtrados do servidor (sempre página 1 quando filtros mudam)
    const result = await loadFilteredData(newFilters, 1)
    console.log('📊 Resultado do filtro auditoria:', result)
    
    // Inicializar dados acumulados
    setAllLoadedData(result.data)
    onFiltersChange?.(newFilters, result.data, result.total, result.hasMore)
  }, [filters, loadFilteredData, onFiltersChange])

  // Limpar todos os filtros
  const clearAllFilters = useCallback(async () => {
    setFilters(initialFilterState)
    setAllLoadedData([])
    setCurrentPage(1)
    
    // Buscar dados sem filtros
    const result = await loadFilteredData(initialFilterState, 1)
    onFiltersChange?.(initialFilterState, result.data, result.total, result.hasMore)
  }, [loadFilteredData, onFiltersChange])

  // Carregar mais dados (próxima página)
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingData) return []
    
    const nextPage = currentPage + 1
    const result = await loadFilteredData(filters, nextPage, true)
    
    if (result.data.length > 0) {
      // Concatenar novos dados aos existentes
      const newAllData = [...allLoadedData, ...result.data]
      setAllLoadedData(newAllData)
      
      // Retornar os novos dados concatenados para o componente
      onFiltersChange?.(filters, newAllData, result.total, result.hasMore)
      return result.data
    }
    
    return []
  }, [filters, currentPage, hasMore, isLoadingData, loadFilteredData, allLoadedData, onFiltersChange])

  // Obter opções filtradas para cascata
  const getFilteredOptions = useCallback((filterType: keyof AuditoriaFilterOptions) => {
    return options[filterType] || []
  }, [options])

  // Carregar opções quando os parâmetros mudarem
  useEffect(() => {
    loadFilterOptions()
  }, [loadFilterOptions])

  return {
    filters,
    options,
    isLoading,
    isLoadingData,
    error,
    hasActiveFilters,
    totalCount,
    hasMore,
    currentPage,
    updateFilter,
    clearAllFilters,
    loadMore,
    getFilteredOptions,
    reloadOptions: loadFilterOptions
  }
}

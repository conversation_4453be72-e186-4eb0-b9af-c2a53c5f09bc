import { useState, useCallback, useEffect } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useFilterStore } from '@/store/filterStore'
import {
  auditoriaService,
  DashboardResponse,
} from '@/services/auditoriaService'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { HelpButton } from '@/components/ui/HelpButton'
import { HelpModal } from '@/components/ui/HelpModal'
import { CardsResumo } from '@/components/auditoria/CardsResumo'
import {
  FiltrosAvancados,
  FiltrosState,
} from '@/components/auditoria/FiltrosAvancados'
import { TabelaDetalhamento } from '@/components/auditoria/TabelaDetalhamento'
import { ExecutarAuditoriaButton } from '@/components/auditoria/ExecutarAuditoriaButton'
import { RelatoriosAuditoria } from '@/components/auditoria/RelatoriosAuditoria'
import { ProgressoAuditoria } from '@/components/auditoria/ProgressoAuditoria'
import { useAuditoriaWebSocket } from '@/hooks/useAuditoriaWebSocket'
import { TesteWebSocket } from '@/components/auditoria/TesteWebSocket'

export function AuditoriaDashboardPage() {
  const { tipoTributo } = useParams()
  const [searchParams] = useSearchParams()
  const {
    selectedCompany: empresaId,
    selectedYear,
    selectedMonth,
    setCompany,
    setYear,
    setMonth,
  } = useFilterStore()
  useEffect(() => {
    const empresaParam = searchParams.get('empresaId')
    const yearParam = searchParams.get('year')
    const monthParam = searchParams.get('month')
    if (empresaParam) setCompany(parseInt(empresaParam))
    if (yearParam) setYear(parseInt(yearParam))
    if (monthParam) setMonth(parseInt(monthParam))
  }, [searchParams, setCompany, setYear, setMonth])
  const queryClient = useQueryClient()
  const [activeTab, setActiveTab] = useState<'resumo' | 'detalhamento'>(
    'resumo'
  )
  const [currentAuditId, setCurrentAuditId] = useState<string | null>(null)
  const [isHelpOpen, setIsHelpOpen] = useState(false)
  const [filtros, setFiltros] = useState<FiltrosState>({
    analista_visualizou: '',
    atividade: '',
    destinacao: '',
    numero: '',
    produto_numero: '',
    cfops: [],
    ncms: [],
    csts: [],
    aliquotas: [],
  })

  // WebSocket para acompanhar progresso da auditoria
  const { getAuditStatus, isAuditProcessing } = useAuditoriaWebSocket({
    onComplete: () => {
      // Quando a auditoria terminar, recarregar os dados
      queryClient.invalidateQueries({ queryKey: ['auditoria-dashboard'] })
      setCurrentAuditId(null)
    },
    onError: (error) => {
      console.error('Erro na auditoria:', error)
      setCurrentAuditId(null)
    },
  })

  // Query para dados do dashboard
  const {
    data: dashboard,
    isLoading,
    error,
  } = useQuery<DashboardResponse>({
    queryKey: [
      'auditoria-dashboard',
      empresaId,
      tipoTributo,
      selectedYear,
      selectedMonth,
    ],
    queryFn: () =>
      auditoriaService.getDashboard({
        empresaId: empresaId!,
        tipoTributo: tipoTributo!,
        year: selectedYear,
        month: selectedMonth,
      }),
    enabled: !!empresaId && !!tipoTributo,
    retry: (failureCount, error: any) => {
      // Não tentar novamente se for 404 (auditoria não executada)
      if (error?.response?.status === 404) {
        return false
      }
      return failureCount < 2
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  })

  // Query para opções de filtros
  const { data: filtroOptions, isLoading: loadingFiltros } = useQuery({
    queryKey: [
      'auditoria-filtros',
      empresaId,
      tipoTributo,
      selectedYear,
      selectedMonth,
    ],
    queryFn: () =>
      auditoriaService.getDashboardFiltros({
        empresaId: empresaId!,
        tipoTributo: tipoTributo!,
        year: selectedYear,
        month: selectedMonth,
      }),
    enabled: activeTab === 'detalhamento' && !!empresaId && !!tipoTributo,
    staleTime: 10 * 60 * 1000, // 10 minutos
  })

  // Query para detalhamento com filtros
  const { data: detalhamento, isLoading: loadingDetalhamento } = useQuery({
    queryKey: [
      'auditoria-detalhamento',
      empresaId,
      tipoTributo,
      selectedYear,
      selectedMonth,
      filtros,
    ],
    queryFn: () =>
      auditoriaService.getDashboardDetalhamento({
        empresaId: empresaId!,
        tipoTributo: tipoTributo!,
        year: selectedYear,
        month: selectedMonth,
        status: 'inconsistente',
        cfop: filtros.cfops.length > 0 ? filtros.cfops : undefined,
        ncm: filtros.ncms.length > 0 ? filtros.ncms : undefined,
        cst: filtros.csts.length > 0 ? filtros.csts : undefined,
        aliquota: filtros.aliquotas.length > 0 ? filtros.aliquotas : undefined,
        origem: filtros.numero ? 'nfe' : undefined,
        numero: filtros.numero || undefined,
        produto_numero: filtros.produto_numero || undefined,
        atividade: filtros.atividade || undefined,
        destinacao: filtros.destinacao || undefined,
        analista_visualizou: filtros.analista_visualizou || undefined,
      }),
    enabled: activeTab === 'detalhamento' && !!empresaId && !!tipoTributo,
    staleTime: 2 * 60 * 1000, // 2 minutos
  })

  const handleFiltrosChange = useCallback((novosFiltros: FiltrosState) => {
    setFiltros((prev) => {
      // Só atualiza se realmente mudou
      if (JSON.stringify(prev) !== JSON.stringify(novosFiltros)) {
        return novosFiltros
      }
      return prev
    })
  }, [])

  const handleInconsistenciaClick = useCallback(() => {
    setActiveTab('detalhamento')
  }, [])

  const getTributoDisplayName = (tipo: string) => {
    const names: Record<string, string> = {
      icms: 'ICMS',
      'icms-st': 'ICMS-ST',
      icms_st: 'ICMS-ST',
      ipi: 'IPI',
      pis: 'PIS',
      cofins: 'COFINS',
      difal: 'DIFAL',
    }
    return names[tipo] || tipo.toUpperCase()
  }

  const helpTabs = [
    {
      label: 'Dashboard',
      content: (
        <div className="space-y-2">
          <p>
            Utilize os cartões para visualizar inconsistências identificadas
            na auditoria.
          </p>
          <p>Use o botão "Executar auditoria" para atualizar os dados.</p>
        </div>
      ),
    },
    {
      label: 'Inconsistências',
      content: (
        <div className="space-y-2">
          <p>
            Aplique os filtros avançados para refinar os resultados
            apresentados.
          </p>
          <p>
            A tabela exibe o detalhamento das inconsistências encontradas.
          </p>
        </div>
      ),
    },
  ]

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Carregando dashboard de auditoria...
          </p>
        </div>
      </div>
    )
  }

  // Error state ou No data state - Mostrar interface para executar auditoria
  if (error || !dashboard) {
    const isError = !!error

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Dashboard de Auditoria - {getTributoDisplayName(tipoTributo!)}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {new Date(selectedYear, selectedMonth - 1).toLocaleDateString(
                'pt-BR',
                {
                  month: 'long',
                  year: 'numeric',
                }
              )}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <HelpButton onClick={() => setIsHelpOpen(true)} />
            {empresaId && (
              <ExecutarAuditoriaButton
                empresaId={empresaId}
                tipoTributo={tipoTributo!}
                year={selectedYear}
                month={selectedMonth}
                onSuccess={() => {
                  queryClient.invalidateQueries({
                    queryKey: ['auditoria-dashboard'],
                  })
                }}
                onAuditStart={(auditId) => {
                  setCurrentAuditId(auditId)
                }}
              />
            )}
          </div>
        </div>

        {/* Estado de auditoria não executada */}
        <div className="flex justify-center items-center h-96">
          <Card className="p-8 text-center max-w-lg">
            <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
              <svg
                className="w-10 h-10 text-blue-600 dark:text-blue-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>

            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              {isError ? 'Auditoria não encontrada' : 'Auditoria não executada'}
            </h3>

            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {isError
                ? `Não foram encontrados dados de auditoria de ${getTributoDisplayName(tipoTributo!)} para este período. Execute a auditoria para visualizar o dashboard.`
                : `Execute a auditoria de ${getTributoDisplayName(tipoTributo!)} para ${new Date(selectedYear, selectedMonth - 1).toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })} para visualizar os resultados.`}
            </p>

            {empresaId ? (
              <div className="space-y-4">
                <ExecutarAuditoriaButton
                  empresaId={empresaId}
                  tipoTributo={tipoTributo!}
                  year={selectedYear}
                  month={selectedMonth}
                  onSuccess={() => {
                    queryClient.invalidateQueries({
                      queryKey: ['auditoria-dashboard'],
                    })
                  }}
                />

                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p>
                    A auditoria pode levar alguns minutos para ser concluída.
                  </p>
                  <p>Os resultados aparecerão automaticamente nesta página.</p>
                </div>
              </div>
            ) : (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                  <span className="font-medium">
                    Selecione uma empresa para continuar
                  </span>
                </div>
              </div>
            )}
          </Card>
        </div>
        <HelpModal
          isOpen={isHelpOpen}
          onClose={() => setIsHelpOpen(false)}
          title="Ajuda"
          tabs={helpTabs}
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Dashboard de Auditoria - {getTributoDisplayName(tipoTributo!)}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {new Date(selectedYear, selectedMonth - 1).toLocaleDateString(
              'pt-BR',
              {
                month: 'long',
                year: 'numeric',
              }
            )}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <HelpButton onClick={() => setIsHelpOpen(true)} />
          <ExecutarAuditoriaButton
            empresaId={empresaId!}
            tipoTributo={tipoTributo!}
            year={selectedYear}
            month={selectedMonth}
            onSuccess={() => {
              // Invalidar queries para recarregar dados
              queryClient.invalidateQueries({ queryKey: ['auditoria-dashboard'] })
            }}
            onAuditStart={(auditId) => {
              setCurrentAuditId(auditId)
            }}
          />
        </div>
      </div>

      {/* Tabs */}
      <Card className="p-2 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
        <div className="flex gap-2">
          <Button
            variant={activeTab === 'resumo' ? 'primary' : 'ghost'}
            className="flex-1 justify-center"
            onClick={() => setActiveTab('resumo')}
            icon={
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            }
          >
            Dashboard
          </Button>
          <Button
            variant={activeTab === 'detalhamento' ? 'primary' : 'ghost'}
            className="flex-1 justify-center"
            onClick={() => setActiveTab('detalhamento')}
            icon={
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
            }
          >
            Inconsistências
          </Button>
        </div>
      </Card>

      {/* Progresso da auditoria em execução */}
      {currentAuditId && (
        <ProgressoAuditoria
          isVisible={true}
          progress={
            getAuditStatus(currentAuditId)?.progress ??
            getAuditStatus(currentAuditId)?.percentage ??
            0
          }
          message={
            getAuditStatus(currentAuditId)?.message ||
            'Processando auditoria...'
          }
          currentStep={getAuditStatus(currentAuditId)?.current_step}
          totalSteps={getAuditStatus(currentAuditId)?.total_steps}
          onCancel={() => setCurrentAuditId(null)}
        />
      )}

      {/* Conteúdo das tabs */}
      {activeTab === 'resumo' && (
        <div className="space-y-6">
          <CardsResumo
            sumario={dashboard.sumario}
            onInconsistenciaClick={handleInconsistenciaClick}
          />

          {/* Seção de Relatórios */}
          <RelatoriosAuditoria tipoTributo={tipoTributo!} />
        </div>
      )}

      {activeTab === 'detalhamento' && (
        <div className="space-y-6">
          {/* Filtros */}
          <FiltrosAvancados
            filtroOptions={filtroOptions}
            onFiltrosChange={handleFiltrosChange}
            loading={loadingFiltros}
          />

          {/* Tabela de detalhamento */}
          <TabelaDetalhamento
            data={detalhamento?.resultados || []}
            loading={loadingDetalhamento}
            tipoTributo={tipoTributo!}
          />
        </div>
      )}
      <HelpModal
        isOpen={isHelpOpen}
        onClose={() => setIsHelpOpen(false)}
        title="Ajuda"
        tabs={helpTabs}
      />
    </div>
  )
}

export default AuditoriaDashboardPage
"""
Utilitário para fazer requisições a APIs externas
"""
import requests
import os
from dotenv import load_dotenv
from .cnae_mapper import get_activity_and_destination


# Carregar variáveis de ambiente
load_dotenv()

def fetch_cnpj_data(cnpj):
    """
    Busca dados de um CNPJ na API CNPJ.ws

    Args:
        cnpj (str): CNPJ a ser consultado (apenas números)

    Returns:
        dict: Dados do CNPJ ou None em caso de erro
    """
    # Remover caracteres não numéricos do CNPJ
    cnpj = ''.join(filter(str.isdigit, cnpj))

    if not cnpj or len(cnpj) != 14:
        return None

    # Token da API (deve ser configurado no .env)
    token = os.getenv('CNPJ_WS_TOKEN', 'DAsEVpQfzFs0egLMxAYORJdnlKqVE9u9HBgdyxpUB7c7')

    if not token:
        return None

    # URL da API
    url = f"https://comercial.cnpj.ws/cnpj/{cnpj}?token={token}"

    try:
        response = requests.get(url, timeout=15)

        # Verificar se a requisição foi bem-sucedida
        if response.status_code == 200:
            try:
                data = response.json()

                # Verificar se a resposta contém os dados esperados
                if 'estabelecimento' not in data:
                    return None

                estabelecimento = data.get('estabelecimento', {})

                # Extrair dados básicos da empresa
                result = {
                    'razao_social': data.get('razao_social', ''),
                    'nome_fantasia': estabelecimento.get('nome_fantasia', ''),
                    'email': estabelecimento.get('email', ''),
                    'cnae': None,
                    'descricao': None,
                    'simples_nacional': False,
                    'atividade': None,
                    'destinacao': None,
                    'divisao_cnae': None,
                    'natureza_juridica': {
                        'id': None,
                        'descricao': None
                    },
                    # Dados de endereço
                    'cep': estabelecimento.get('cep', ''),
                    'logradouro': estabelecimento.get('logradouro', ''),
                    'numero': estabelecimento.get('numero', ''),
                    'complemento': estabelecimento.get('complemento', ''),
                    'bairro': estabelecimento.get('bairro', ''),
                    'cidade': estabelecimento.get('cidade', {}).get('nome', '') if estabelecimento.get('cidade') else '',
                    'estado': estabelecimento.get('estado', {}).get('sigla', '') if estabelecimento.get('estado') else '',
                    # Inscrições estaduais
                    'inscricoes_estaduais': estabelecimento.get('inscricoes_estaduais', [])
                }

                # Extrair natureza jurídica se existir
                if ('natureza_juridica' in data and data['natureza_juridica'] is not None):
                    result['natureza_juridica'] = {
                        'id': data['natureza_juridica'].get('id'),
                        'descricao': data['natureza_juridica'].get('descricao')
                    }

                # Extrair CNAE e descrição do campo atividade_principal
                atividade_principal = estabelecimento.get('atividade_principal')
                if isinstance(atividade_principal, dict) and 'id' in atividade_principal:
                    cnae_code = atividade_principal.get('id')
                    result['cnae'] = cnae_code
                    result['descricao'] = atividade_principal.get('descricao')
                else:
                    cnae_code = None

                # Se não houver descrição no atividade_principal, tentar nas atividades secundárias
                atividades_secundarias = estabelecimento.get('atividades_secundarias', [])
                if atividades_secundarias:
                    # Se não houver CNAE definido, usar o primeiro da lista
                    if not cnae_code:
                        primeiro = atividades_secundarias[0]
                        cnae_code = primeiro.get('id')
                        result['cnae'] = cnae_code
                        result['descricao'] = primeiro.get('descricao')
                    elif not result['descricao']:
                        # Procurar descrição que corresponda ao CNAE obtido
                        for sec in atividades_secundarias:
                            if sec.get('id') == cnae_code:
                                result['descricao'] = sec.get('descricao')
                                break

                # Extrair divisão do CNAE (primeiros 2 dígitos)
                if cnae_code and len(cnae_code) >= 2:
                    result['divisao_cnae'] = cnae_code[:2]

                    # Obter atividade e destinação sugeridas
                    atividade, destinacao = get_activity_and_destination(cnae_code)

                    # Verificar se a natureza jurídica é 'Produtor Rural' e tem prioridade
                    if (
                        result['natureza_juridica']
                        and result['natureza_juridica']['descricao']
                        and 'Produtor Rural' in result['natureza_juridica']['descricao']
                    ):
                        result['atividade'] = 'Produtor Rural'
                    elif (
                        result['natureza_juridica']
                        and result['natureza_juridica']['descricao']
                        and result['natureza_juridica']['descricao'].lower().startswith('órgão público')
                    ):
                        result['atividade'] = 'Orgão Público'
                    else:
                        result['atividade'] = atividade
                        result['destinacao'] = destinacao

                # Extrair status do Simples Nacional
                if ('simples' in data and data['simples'] is not None):
                    # A API pode retornar null para simples, então verificamos se existe
                    simples_data = data['simples']
                    if simples_data and 'simples' in simples_data:
                        result['simples_nacional'] = simples_data['simples'] == 'Sim'

                return result

            except ValueError as e:
                print(f"Erro ao processar JSON da API CNPJ: {str(e)}")
                return None

        else:
            print(f"Erro na API CNPJ: Status {response.status_code}")
            return None

    except Exception as e:
        print(f"Erro ao consultar CNPJ {cnpj}: {str(e)}")
        return None

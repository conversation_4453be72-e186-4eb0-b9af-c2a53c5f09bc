-- Migração para adicionar tabelas de validação ICMS-ST
-- Data: 2025-01-08
-- Descrição: Cria tabelas para cache e resultados de validação ICMS-ST

-- Tabela de cache para consultas ICMS-ST da API externa
CREATE TABLE IF NOT EXISTS icms_st_cache (
    id SERIAL PRIMARY KEY,
    
    -- Identificação da consulta
    tipo_st VARCHAR(20) NOT NULL, -- 'interna' ou 'interestadual'
    ncm VARCHAR(20) NOT NULL,
    estado_origem VARCHAR(2), -- Para ST interestadual
    estado_destino VARCHAR(2), -- Para ST interestadual
    estado VARCHAR(2), -- Para ST interna
    destinacao_mercadoria VARCHAR(50), -- Para ST interestadual
    
    -- Dados do produto
    codigo VARCHAR(20),
    cest VARCHAR(20) NOT NULL,
    descricao TEXT,
    observacao TEXT,
    segmento VARCHAR(100),
    codigo_segmento VARCHAR(10),
    
    -- Alíquotas
    aliquota_interna NUMERIC(10, 4),
    aliquota_interestadual NUMERIC(10, 4),
    fundo_pobreza VARCHAR(10),
    
    -- MVAs
    mva NUMERIC(10, 4),
    mva_ajustada NUMERIC(10, 4),
    mva_ajustada_4 NUMERIC(10, 4),
    mva_ajustada_7 NUMERIC(10, 4),
    mva_ajustada_12 NUMERIC(10, 4),
    mva_positiva NUMERIC(10, 4),
    mva_negativa NUMERIC(10, 4),
    mva_neutra NUMERIC(10, 4),
    
    -- Vigência
    vigencia_inicial VARCHAR(20),
    vigencia_final VARCHAR(20),
    
    -- Base legal
    base_legal_st TEXT,
    data_efeito_st VARCHAR(20),
    base_calculo TEXT,
    norma_base_calculo TEXT,
    prazo_recolhimento TEXT,
    
    -- Aplicabilidade
    aplicabilidade TEXT,
    nao_aplicabilidade TEXT,
    
    -- Campos específicos para ST interestadual
    regime_origem VARCHAR(50),
    regime_destino VARCHAR(50),
    destino_produto VARCHAR(100),
    base_legal_int TEXT,
    observacao_int TEXT,
    base_calculo_int TEXT,
    prazo_recolhimento_int TEXT,
    norma_observacao_st TEXT,
    norma_prazo_recolhimento TEXT,
    
    -- Variações de MVA (JSON para flexibilidade)
    variacao_mva TEXT, -- JSON string
    reducao_mva TEXT,
    
    -- Controle de cache
    data_consulta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    hash_consulta VARCHAR(64) NOT NULL,
    
    CONSTRAINT unique_hash_consulta UNIQUE (hash_consulta)
);

-- Índices para performance na tabela de cache
CREATE INDEX IF NOT EXISTS idx_icms_st_lookup_interna ON icms_st_cache (tipo_st, ncm, estado);
CREATE INDEX IF NOT EXISTS idx_icms_st_lookup_inter ON icms_st_cache (tipo_st, ncm, estado_origem, estado_destino, destinacao_mercadoria);
CREATE INDEX IF NOT EXISTS idx_icms_st_hash ON icms_st_cache (hash_consulta);
CREATE INDEX IF NOT EXISTS idx_icms_st_data ON icms_st_cache (data_consulta);
CREATE INDEX IF NOT EXISTS idx_icms_st_cest ON icms_st_cache (cest);

-- Tabela de resultados de validação ICMS-ST
CREATE TABLE IF NOT EXISTS icms_st_validation_results (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER NOT NULL REFERENCES empresa(id),
    cenario_id INTEGER NOT NULL,
    
    -- Dados originais do cenário (JSON)
    dados_originais JSONB NOT NULL,
    
    -- Sugestões de correção (JSON)
    sugestoes JSONB NOT NULL,
    
    -- Dados da API consultada (JSON)
    dados_api JSONB,
    
    -- Tipo de validação
    tipo_validacao VARCHAR(50) DEFAULT 'ICMS_ST_COMPLETA',
    
    -- Status da validação
    status VARCHAR(20) DEFAULT 'pendente', -- pendente, aplicado, ignorado
    
    -- Metadados
    data_validacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    aplicado_em TIMESTAMP,
    aplicado_por VARCHAR(100),
    
    -- Observações
    observacoes TEXT
);

-- Índices para performance na tabela de resultados
CREATE INDEX IF NOT EXISTS idx_icms_st_validation_empresa ON icms_st_validation_results (empresa_id);
CREATE INDEX IF NOT EXISTS idx_icms_st_validation_cenario ON icms_st_validation_results (cenario_id);
CREATE INDEX IF NOT EXISTS idx_icms_st_validation_status ON icms_st_validation_results (status);
CREATE INDEX IF NOT EXISTS idx_icms_st_validation_data ON icms_st_validation_results (data_validacao);

-- Comentários nas tabelas
COMMENT ON TABLE icms_st_cache IS 'Cache de consultas ICMS-ST da API externa para evitar requisições desnecessárias';
COMMENT ON TABLE icms_st_validation_results IS 'Histórico de validações ICMS-ST realizadas e suas correções';

-- Comentários em campos importantes
COMMENT ON COLUMN icms_st_cache.tipo_st IS 'Tipo de ST: interna (mesmo estado) ou interestadual (estados diferentes)';
COMMENT ON COLUMN icms_st_cache.hash_consulta IS 'Hash único da consulta para controle de cache';
COMMENT ON COLUMN icms_st_cache.destinacao_mercadoria IS 'Destinação da mercadoria para ST interestadual (Revenda, Ativo Imobilizado, etc.)';
COMMENT ON COLUMN icms_st_validation_results.dados_originais IS 'Dados originais do cenário antes da correção (JSON)';
COMMENT ON COLUMN icms_st_validation_results.sugestoes IS 'Sugestões de correção propostas (JSON)';
COMMENT ON COLUMN icms_st_validation_results.dados_api IS 'Dados retornados pela API externa (JSON)';

-- Verificar se as tabelas foram criadas corretamente
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'icms_st_cache') AND
       EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'icms_st_validation_results') THEN
        RAISE NOTICE 'Migração ICMS-ST executada com sucesso!';
        RAISE NOTICE 'Tabelas criadas: icms_st_cache, icms_st_validation_results';
    ELSE
        RAISE EXCEPTION 'Erro na migração ICMS-ST: tabelas não foram criadas corretamente';
    END IF;
END $$;
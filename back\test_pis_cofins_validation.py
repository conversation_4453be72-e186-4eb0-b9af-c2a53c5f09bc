"""Testes para o serviço de validação de PIS/COFINS"""
import unittest
from unittest.mock import patch, MagicMock

from services.pis_cofins_validation_service import PisCofinValidationService
from models.pis_cofins_cache import PisCofinsCacheModel
from models.pis_cofins_validation_result import PisCofinValidationResultModel


class TestPisCofinValidationService(unittest.TestCase):
    def setUp(self):
        self.service = PisCofinValidationService()

    def test_norm_ncm(self):
        self.assertEqual(self.service._norm_ncm('40.10.39-00'), '40103900')

    def test_parse_cst(self):
        codigo, desc = self.service._parse_cst('06 - Operação Tributável a Alíquota Zero')
        self.assertEqual(codigo, '06')
        self.assertEqual(desc, 'Operação Tributável a Alíquota Zero')

    def test_processar_resposta_tipo1(self):
        resposta = {
            "registros": 2,
            "resposta": [
                {
                    "regime_tributario": "Lucro Real",
                    "tributo": "Pis",
                    "valor": "1.65",
                    "cst": "01 - Operação Tributável com Alíquota Básica"
                },
                {
                    "regime_tributario": "Lucro Real",
                    "tributo": "Cofins",
                    "valor": "7.60",
                    "cst": "01 - Operação Tributável com Alíquota Básica"
                }
            ]
        }
        dados = self.service._processar_resposta_tipo1(resposta, '40103900')
        self.assertEqual(len(dados), 2)
        pis = [d for d in dados if d['tributo'] == 'PIS'][0]
        cofins = [d for d in dados if d['tributo'] == 'COFINS'][0]
        self.assertEqual(pis['cst_code'], '01')
        self.assertEqual(pis['valor'], 1.65)
        self.assertEqual(cofins['cst_code'], '01')
        self.assertEqual(cofins['valor'], 7.60)

    def test_processar_resposta_tipo2_3(self):
        resposta = {
            "registros": 1,
            "resposta": [
                {
                    "regra": "Alíquota 0%",
                    "aplicabilidade": "Redução",
                    "trib": [
                        {
                            "ncm": "40",
                            "descricao": "Almofadas antiescaras",
                            "regime_tributario_origem": "Lucro Real",
                            "atividade_origem": "Atacadista",
                            "atividade_destino": "Atacadista",
                            "tributo": "Pis",
                            "tipo_valor": "Porcentagem",
                            "valor": "0.00",
                            "cst_pis": "06 - Operação Tributável a Alíquota Zero"
                        }
                    ]
                }
            ]
        }
        dados = self.service._processar_resposta_tipo2_3(resposta)
        self.assertEqual(len(dados), 1)
        d = dados[0]
        self.assertEqual(d['ncm_original'], '40')
        self.assertEqual(d['cst_code'], '06')
        self.assertEqual(d['valor'], 0.0)
        self.assertEqual(d['regra'], 'Alíquota 0%')

    @patch('services.pis_cofins_validation_service.requests.Session.get')
    def test_consultar_api(self, mock_get):
        mock_response = MagicMock()
        mock_response.json.return_value = {"registros": 1, "resposta": []}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        self.service._consultar_api('40103900', 'Lucro Real', 'Atacadista')
        args, kwargs = mock_get.call_args
        params = kwargs['params']
        self.assertEqual(params['ncm'], '40103900')
        self.assertEqual(params['regime_tributario_origem'], '2')
        self.assertEqual(params['atividade_origem'], '1')


class TestPisCofinsCacheModel(unittest.TestCase):
    def test_to_dict(self):
        cache = PisCofinsCacheModel(
            id=1,
            ncm_original='40103900',
            ncm_norm='40103900',
            regime_tributario_origem_code='2',
            atividade_origem_code='1',
            atividade_destino_code='4',
            tributo='PIS',
            cst_code='01',
            cst_desc='Operação Tributável com Alíquota Básica',
            valor=1.65,
            tipo_valor='Porcentagem',
            exibir_aliquota_padrao='1',
            hash_consulta='abc'
        )
        d = cache.to_dict()
        self.assertEqual(d['ncm_norm'], '40103900')
        self.assertEqual(d['cst_code'], '01')
        self.assertEqual(d['valor'], 1.65)
        self.assertEqual(d['tributo'], 'PIS')


class TestPisCofinValidationResultModel(unittest.TestCase):
    def test_to_dict(self):
        res = PisCofinValidationResultModel(
            id=1,
            empresa_id=1,
            cenario_id=2,
            dados_originais={'aliquota': 1.5},
            sugestoes=[{'campo': 'aliquota', 'valor_sugerido': 1.65}],
            status='pendente',
            observacoes=''
        )
        d = res.to_dict()
        self.assertEqual(d['empresa_id'], 1)
        self.assertEqual(d['cenario_id'], 2)
        self.assertEqual(d['status'], 'pendente')
        self.assertEqual(d['dados_originais']['aliquota'], 1.5)


if __name__ == '__main__':
    unittest.main()
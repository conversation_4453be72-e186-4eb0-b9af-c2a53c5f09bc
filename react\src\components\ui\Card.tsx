import React from 'react'
import { cn } from '@/utils/cn'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  border?: boolean
  hover?: boolean
  glass?: boolean
  gradient?: boolean
}

interface CardHeaderProps {
  children: React.ReactNode
  className?: string
  actions?: React.ReactNode
}

interface CardBodyProps {
  children: React.ReactNode
  className?: string
}

interface CardFooterProps {
  children: React.ReactNode
  className?: string
}

const cardPadding = {
  none: '',
  sm: 'p-3',
  md: 'p-4',
  lg: 'p-6',
  xl: 'p-8'
}

const cardShadow = {
  none: '',
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl'
}

const cardRounded = {
  none: '',
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
  '2xl': 'rounded-2xl'
}

export function Card({
                       children,
                       className,
                       padding = 'md',
                       shadow = 'md',
                       rounded = 'xl',
                       border = true,
                       hover = false,
                       glass = false,
                       gradient = false,
                       ...props
                     }: CardProps) {
  return (
    <div
      {...props}
      className={cn(
        'bg-white dark:bg-gray-800 transition-all duration-200',
        cardPadding[padding],
        cardShadow[shadow],
        cardRounded[rounded],
        border && 'border border-gray-200 dark:border-gray-700',
        hover && 'hover:shadow-lg hover:-translate-y-1 cursor-pointer',
        glass && 'glass-effect',
        gradient && 'bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900',
        className
      )}
    >
      {children}
    </div>
  )
}

export function CardHeader({ children, className, actions }: CardHeaderProps) {
  return (
    <div className={cn('flex items-center justify-between mb-4', className)}>
      <div className="flex-1">{children}</div>
      {actions && <div className="flex items-center gap-2">{actions}</div>}
    </div>
  )
}

export function CardBody({ children, className }: CardBodyProps) {
  return (
    <div className={cn('flex-1', className)}>
      {children}
    </div>
  )
}

export function CardFooter({ children, className }: CardFooterProps) {
  return (
    <div className={cn('mt-4 pt-4 border-t border-gray-200 dark:border-gray-700', className)}>
      {children}
    </div>
  )
}

// Stats Card Component
interface StatsCardProps {
  title: string
  value: string | number
  fullValue?: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period?: string
  }
  icon?: React.ReactNode
  color?: 'primary' | 'success' | 'warning' | 'error' | 'blue'
  loading?: boolean
  className?: string
}

export function StatsCard({
                            title,
                            value,
                            fullValue,
                            change,
                            icon,
                            color = 'primary',
                            loading = false,
                            className
                          }: StatsCardProps) {
  const colorClasses = {
    primary: {
      bg: 'bg-primary-500',
      text: 'text-primary-600 dark:text-primary-400',
      light: 'bg-primary-50 dark:bg-primary-900/20'
    },
    success: {
      bg: 'bg-success-500',
      text: 'text-success-600 dark:text-success-400',
      light: 'bg-success-50 dark:bg-success-900/20'
    },
    warning: {
      bg: 'bg-warning-500',
      text: 'text-warning-600 dark:text-warning-400',
      light: 'bg-warning-50 dark:bg-warning-900/20'
    },
    error: {
      bg: 'bg-error-500',
      text: 'text-error-600 dark:text-error-400',
      light: 'bg-error-50 dark:bg-error-900/20'
    },
    blue: {
      bg: 'bg-blue-500',
      text: 'text-blue-600 dark:text-blue-400',
      light: 'bg-blue-50 dark:bg-blue-900/20'
    }
  }

  if (loading) {
    return (
      <Card className={cn('animate-pulse', className)}>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
          </div>
          <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
        </div>
      </Card>
    )
  }

  return (
    <Card
      className={cn('hover:shadow-lg transition-all duration-200 group relative', className)}
      hover
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
            {title}
          </p>
          <p
            className="text-2xl font-bold text-gray-900 dark:text-white mb-2 truncate"
            title={fullValue?.toString() || value.toString()}
          >
            {value}
          </p>
          {change && (
            <div className="flex items-center gap-1">
              <div className={cn(
                'flex items-center gap-1 text-xs font-medium px-2 py-1 rounded-full',
                change.type === 'increase'
                  ? 'text-success-700 bg-success-100 dark:text-success-400 dark:bg-success-900/20'
                  : 'text-error-700 bg-error-100 dark:text-error-400 dark:bg-error-900/20'
              )}>
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  {change.type === 'increase' ? (
                    <path fillRule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  ) : (
                    <path fillRule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  )}
                </svg>
                {Math.abs(change.value)}%
              </div>
              {change.period && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {change.period}
                </span>
              )}
            </div>
          )}
        </div>
        {icon && (
          <div className={cn(
            'w-12 h-12 rounded-xl flex items-center justify-center text-white flex-shrink-0 transition-all duration-200',
            'group-hover:opacity-0',
            colorClasses[color].bg
          )}>
            {icon}
          </div>
        )}
      </div>
      {/* Tooltip with full value on hover */}
      {fullValue && (
        <div className={cn(
          'absolute inset-0 bg-black/80 rounded-xl flex items-center justify-center transition-all duration-200 opacity-0 pointer-events-none',
          'group-hover:opacity-100'
        )}>
          <div className="text-white text-center px-2">
            <p className="text-xs opacity-75">Valor completo</p>
            <p className="font-bold text-lg">{fullValue}</p>
          </div>
        </div>
      )}
    </Card>
  )
}
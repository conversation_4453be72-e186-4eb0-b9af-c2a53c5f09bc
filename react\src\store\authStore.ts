import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: number
  nome: string
  email: string
  tipo_usuario: string
  escritorio_id?: number
  is_admin?: boolean
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (token: string, user: User) => void
  logout: () => void
  setLoading: (loading: boolean) => void
  checkAuth: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,
      
      login: (token: string, user: User) => {
        // Salva no localStorage também (compatibilidade com sistema atual)
        localStorage.setItem('token', token)
        localStorage.setItem('currentUser', JSON.stringify(user))
        
        set({
          token,
          user,
          isAuthenticated: true,
          isLoading: false,
        })
      },
      
      logout: () => {
        // Remove do localStorage também
        localStorage.removeItem('token')
        localStorage.removeItem('currentUser')
        
        // Remove cookie se existir
        document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        })
      },
      
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },
      
      checkAuth: async () => {
        try {
          const state = get()
          
          // Se já está autenticado, não precisa verificar novamente
          if (state.isAuthenticated && state.token) {
            set({ isLoading: false })
            return
          }
          
          // Verifica se existe token no localStorage (compatibilidade)
          const storedToken = localStorage.getItem('token')
          const storedUser = localStorage.getItem('currentUser')
          
          if (storedToken && storedUser) {
            try {
              const userData = JSON.parse(storedUser)
              // Assume que o token é válido se existe no localStorage
              // A validação será feita nas requisições subsequentes
              set({
                token: storedToken,
                user: userData,
                isAuthenticated: true,
                isLoading: false,
              })
              return
            } catch (error) {
              console.error('Erro ao parsear dados do usuário:', error)
              // Remove dados corrompidos
              localStorage.removeItem('token')
              localStorage.removeItem('currentUser')
            }
          }
          
          // Se chegou até aqui, não está autenticado
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          })
        } catch (error) {
          console.error('Erro na verificação de autenticação:', error)
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // Após hidratar o estado, verifica a autenticação
        if (state) {
          state.checkAuth()
        }
      },
    }
  )
)
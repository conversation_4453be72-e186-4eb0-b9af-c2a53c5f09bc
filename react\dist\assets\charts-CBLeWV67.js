import{c as ui,g as ue,r as B,a as P}from"./vendor-BKU87Gzz.js";function ch(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=ch(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function ee(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=ch(e))&&(n&&(n+=" "),n+=t);return n}var By=Array.isArray,De=By,Ry=typeof ui=="object"&&ui&&ui.Object===Object&&ui,sh=Ry,Ly=sh,Fy=typeof self=="object"&&self&&self.Object===Object&&self,zy=Ly||Fy||Function("return this")(),ut=zy,Wy=ut,Uy=Wy.Symbol,Jn=Uy,ms=Jn,lh=Object.prototype,qy=lh.hasOwnProperty,Hy=lh.toString,Qr=ms?ms.toStringTag:void 0;function Gy(e){var t=qy.call(e,Qr),r=e[Qr];try{e[Qr]=void 0;var n=!0}catch{}var i=Hy.call(e);return n&&(t?e[Qr]=r:delete e[Qr]),i}var Ky=Gy,Xy=Object.prototype,Vy=Xy.toString;function Yy(e){return Vy.call(e)}var Zy=Yy,gs=Jn,Jy=Ky,Qy=Zy,em="[object Null]",tm="[object Undefined]",bs=gs?gs.toStringTag:void 0;function rm(e){return e==null?e===void 0?tm:em:bs&&bs in Object(e)?Jy(e):Qy(e)}var xt=rm;function nm(e){return e!=null&&typeof e=="object"}var wt=nm,im=xt,am=wt,om="[object Symbol]";function um(e){return typeof e=="symbol"||am(e)&&im(e)==om}var Lr=um,cm=De,sm=Lr,lm=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,fm=/^\w*$/;function pm(e,t){if(cm(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||sm(e)?!0:fm.test(e)||!lm.test(e)||t!=null&&e in Object(t)}var oc=pm;function hm(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Mt=hm;const Fr=ue(Mt);var dm=xt,vm=Mt,ym="[object AsyncFunction]",mm="[object Function]",gm="[object GeneratorFunction]",bm="[object Proxy]";function xm(e){if(!vm(e))return!1;var t=dm(e);return t==mm||t==gm||t==ym||t==bm}var uc=xm;const V=ue(uc);var wm=ut,Om=wm["__core-js_shared__"],Sm=Om,co=Sm,xs=function(){var e=/[^.]+$/.exec(co&&co.keys&&co.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Am(e){return!!xs&&xs in e}var Pm=Am,_m=Function.prototype,$m=_m.toString;function Tm(e){if(e!=null){try{return $m.call(e)}catch{}try{return e+""}catch{}}return""}var fh=Tm,Em=uc,jm=Pm,Mm=Mt,Cm=fh,Im=/[\\^$.*+?()[\]{}|]/g,km=/^\[object .+?Constructor\]$/,Dm=Function.prototype,Nm=Object.prototype,Bm=Dm.toString,Rm=Nm.hasOwnProperty,Lm=RegExp("^"+Bm.call(Rm).replace(Im,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Fm(e){if(!Mm(e)||jm(e))return!1;var t=Em(e)?Lm:km;return t.test(Cm(e))}var zm=Fm;function Wm(e,t){return e==null?void 0:e[t]}var Um=Wm,qm=zm,Hm=Um;function Gm(e,t){var r=Hm(e,t);return qm(r)?r:void 0}var tr=Gm,Km=tr,Xm=Km(Object,"create"),wa=Xm,ws=wa;function Vm(){this.__data__=ws?ws(null):{},this.size=0}var Ym=Vm;function Zm(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Jm=Zm,Qm=wa,eg="__lodash_hash_undefined__",tg=Object.prototype,rg=tg.hasOwnProperty;function ng(e){var t=this.__data__;if(Qm){var r=t[e];return r===eg?void 0:r}return rg.call(t,e)?t[e]:void 0}var ig=ng,ag=wa,og=Object.prototype,ug=og.hasOwnProperty;function cg(e){var t=this.__data__;return ag?t[e]!==void 0:ug.call(t,e)}var sg=cg,lg=wa,fg="__lodash_hash_undefined__";function pg(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=lg&&t===void 0?fg:t,this}var hg=pg,dg=Ym,vg=Jm,yg=ig,mg=sg,gg=hg;function zr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}zr.prototype.clear=dg;zr.prototype.delete=vg;zr.prototype.get=yg;zr.prototype.has=mg;zr.prototype.set=gg;var bg=zr;function xg(){this.__data__=[],this.size=0}var wg=xg;function Og(e,t){return e===t||e!==e&&t!==t}var cc=Og,Sg=cc;function Ag(e,t){for(var r=e.length;r--;)if(Sg(e[r][0],t))return r;return-1}var Oa=Ag,Pg=Oa,_g=Array.prototype,$g=_g.splice;function Tg(e){var t=this.__data__,r=Pg(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():$g.call(t,r,1),--this.size,!0}var Eg=Tg,jg=Oa;function Mg(e){var t=this.__data__,r=jg(t,e);return r<0?void 0:t[r][1]}var Cg=Mg,Ig=Oa;function kg(e){return Ig(this.__data__,e)>-1}var Dg=kg,Ng=Oa;function Bg(e,t){var r=this.__data__,n=Ng(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var Rg=Bg,Lg=wg,Fg=Eg,zg=Cg,Wg=Dg,Ug=Rg;function Wr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Wr.prototype.clear=Lg;Wr.prototype.delete=Fg;Wr.prototype.get=zg;Wr.prototype.has=Wg;Wr.prototype.set=Ug;var Sa=Wr,qg=tr,Hg=ut,Gg=qg(Hg,"Map"),sc=Gg,Os=bg,Kg=Sa,Xg=sc;function Vg(){this.size=0,this.__data__={hash:new Os,map:new(Xg||Kg),string:new Os}}var Yg=Vg;function Zg(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var Jg=Zg,Qg=Jg;function eb(e,t){var r=e.__data__;return Qg(t)?r[typeof t=="string"?"string":"hash"]:r.map}var Aa=eb,tb=Aa;function rb(e){var t=tb(this,e).delete(e);return this.size-=t?1:0,t}var nb=rb,ib=Aa;function ab(e){return ib(this,e).get(e)}var ob=ab,ub=Aa;function cb(e){return ub(this,e).has(e)}var sb=cb,lb=Aa;function fb(e,t){var r=lb(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var pb=fb,hb=Yg,db=nb,vb=ob,yb=sb,mb=pb;function Ur(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Ur.prototype.clear=hb;Ur.prototype.delete=db;Ur.prototype.get=vb;Ur.prototype.has=yb;Ur.prototype.set=mb;var lc=Ur,ph=lc,gb="Expected a function";function fc(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(gb);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=e.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(fc.Cache||ph),r}fc.Cache=ph;var hh=fc;const bb=ue(hh);var xb=hh,wb=500;function Ob(e){var t=xb(e,function(n){return r.size===wb&&r.clear(),n}),r=t.cache;return t}var Sb=Ob,Ab=Sb,Pb=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,_b=/\\(\\)?/g,$b=Ab(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(Pb,function(r,n,i,a){t.push(i?a.replace(_b,"$1"):n||r)}),t}),Tb=$b;function Eb(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var pc=Eb,Ss=Jn,jb=pc,Mb=De,Cb=Lr,As=Ss?Ss.prototype:void 0,Ps=As?As.toString:void 0;function dh(e){if(typeof e=="string")return e;if(Mb(e))return jb(e,dh)+"";if(Cb(e))return Ps?Ps.call(e):"";var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var Ib=dh,kb=Ib;function Db(e){return e==null?"":kb(e)}var vh=Db,Nb=De,Bb=oc,Rb=Tb,Lb=vh;function Fb(e,t){return Nb(e)?e:Bb(e,t)?[e]:Rb(Lb(e))}var yh=Fb,zb=Lr;function Wb(e){if(typeof e=="string"||zb(e))return e;var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var Pa=Wb,Ub=yh,qb=Pa;function Hb(e,t){t=Ub(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[qb(t[r++])];return r&&r==n?e:void 0}var hc=Hb,Gb=hc;function Kb(e,t,r){var n=e==null?void 0:Gb(e,t);return n===void 0?r:n}var mh=Kb;const Ge=ue(mh);function Xb(e){return e==null}var Vb=Xb;const J=ue(Vb);var Yb=xt,Zb=De,Jb=wt,Qb="[object String]";function e0(e){return typeof e=="string"||!Zb(e)&&Jb(e)&&Yb(e)==Qb}var t0=e0;const Yt=ue(t0);var gh={exports:{}},ne={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dc=Symbol.for("react.element"),vc=Symbol.for("react.portal"),_a=Symbol.for("react.fragment"),$a=Symbol.for("react.strict_mode"),Ta=Symbol.for("react.profiler"),Ea=Symbol.for("react.provider"),ja=Symbol.for("react.context"),r0=Symbol.for("react.server_context"),Ma=Symbol.for("react.forward_ref"),Ca=Symbol.for("react.suspense"),Ia=Symbol.for("react.suspense_list"),ka=Symbol.for("react.memo"),Da=Symbol.for("react.lazy"),n0=Symbol.for("react.offscreen"),bh;bh=Symbol.for("react.module.reference");function Xe(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case dc:switch(e=e.type,e){case _a:case Ta:case $a:case Ca:case Ia:return e;default:switch(e=e&&e.$$typeof,e){case r0:case ja:case Ma:case Da:case ka:case Ea:return e;default:return t}}case vc:return t}}}ne.ContextConsumer=ja;ne.ContextProvider=Ea;ne.Element=dc;ne.ForwardRef=Ma;ne.Fragment=_a;ne.Lazy=Da;ne.Memo=ka;ne.Portal=vc;ne.Profiler=Ta;ne.StrictMode=$a;ne.Suspense=Ca;ne.SuspenseList=Ia;ne.isAsyncMode=function(){return!1};ne.isConcurrentMode=function(){return!1};ne.isContextConsumer=function(e){return Xe(e)===ja};ne.isContextProvider=function(e){return Xe(e)===Ea};ne.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===dc};ne.isForwardRef=function(e){return Xe(e)===Ma};ne.isFragment=function(e){return Xe(e)===_a};ne.isLazy=function(e){return Xe(e)===Da};ne.isMemo=function(e){return Xe(e)===ka};ne.isPortal=function(e){return Xe(e)===vc};ne.isProfiler=function(e){return Xe(e)===Ta};ne.isStrictMode=function(e){return Xe(e)===$a};ne.isSuspense=function(e){return Xe(e)===Ca};ne.isSuspenseList=function(e){return Xe(e)===Ia};ne.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===_a||e===Ta||e===$a||e===Ca||e===Ia||e===n0||typeof e=="object"&&e!==null&&(e.$$typeof===Da||e.$$typeof===ka||e.$$typeof===Ea||e.$$typeof===ja||e.$$typeof===Ma||e.$$typeof===bh||e.getModuleId!==void 0)};ne.typeOf=Xe;gh.exports=ne;var i0=gh.exports,a0=xt,o0=wt,u0="[object Number]";function c0(e){return typeof e=="number"||o0(e)&&a0(e)==u0}var xh=c0;const s0=ue(xh);var l0=xh;function f0(e){return l0(e)&&e!=+e}var p0=f0;const qr=ue(p0);var Qe=function(t){return t===0?0:t>0?1:-1},Ut=function(t){return Yt(t)&&t.indexOf("%")===t.length-1},R=function(t){return s0(t)&&!qr(t)},h0=function(t){return J(t)},Oe=function(t){return R(t)||Yt(t)},d0=0,Qn=function(t){var r=++d0;return"".concat(t||"").concat(r)},Zt=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!R(t)&&!Yt(t))return n;var a;if(Ut(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return qr(a)&&(a=n),i&&a>r&&(a=r),a},_t=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},v0=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},qe=function(t,r){return R(t)&&R(r)?function(n){return t+n*(r-t)}:function(){return r}};function Oi(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ge(n,t))===r})}var y0=function(t,r){return R(t)&&R(r)?t-r:Yt(t)&&Yt(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function hr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Bo(e){"@babel/helpers - typeof";return Bo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bo(e)}var m0=["viewBox","children"],g0=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],_s=["points","pathLength"],so={svg:m0,polygon:_s,polyline:_s},yc=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Si=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(B.isValidElement(t)&&(n=t.props),!Fr(n))return null;var i={};return Object.keys(n).forEach(function(a){yc.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},b0=function(t,r,n){return function(i){return t(r,n,i),null}},Ai=function(t,r,n){if(!Fr(t)||Bo(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];yc.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=b0(o,r,n))}),i},x0=["children"],w0=["children"];function $s(e,t){if(e==null)return{};var r=O0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function O0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ro(e){"@babel/helpers - typeof";return Ro=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ro(e)}var Ts={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},ht=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Es=null,lo=null,mc=function e(t){if(t===Es&&Array.isArray(lo))return lo;var r=[];return B.Children.forEach(t,function(n){J(n)||(i0.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),lo=r,Es=t,r};function et(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return ht(i)}):n=[ht(t)],mc(e).forEach(function(i){var a=Ge(i,"type.displayName")||Ge(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function Re(e,t){var r=et(e,t);return r&&r[0]}var js=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!R(n)||n<=0||!R(i)||i<=0)},S0=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],A0=function(t){return t&&t.type&&Yt(t.type)&&S0.indexOf(t.type)>=0},P0=function(t){return t&&Ro(t)==="object"&&"clipDot"in t},_0=function(t,r,n,i){var a,o=(a=so==null?void 0:so[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!V(t)&&(i&&o.includes(r)||g0.includes(r))||n&&yc.includes(r)},Z=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(B.isValidElement(t)&&(i=t.props),!Fr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;_0((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},Lo=function e(t,r){if(t===r)return!0;var n=B.Children.count(t);if(n!==B.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Ms(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Ms(a,o))return!1}return!0},Ms=function(t,r){if(J(t)&&J(r))return!0;if(!J(t)&&!J(r)){var n=t.props||{},i=n.children,a=$s(n,x0),o=r.props||{},u=o.children,c=$s(o,w0);return i&&u?hr(a,c)&&Lo(i,u):!i&&!u?hr(a,c):!1}return!1},Cs=function(t,r){var n=[],i={};return mc(t).forEach(function(a,o){if(A0(a))n.push(a);else if(a){var u=ht(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},$0=function(t){var r=t&&t.type;return r&&Ts[r]?Ts[r]:null},T0=function(t,r){return mc(r).indexOf(t)},E0=["children","width","height","viewBox","className","style","title","desc"];function Fo(){return Fo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fo.apply(this,arguments)}function j0(e,t){if(e==null)return{};var r=M0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function M0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function zo(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=j0(e,E0),f=i||{width:r,height:n,x:0,y:0},l=ee("recharts-surface",a);return P.createElement("svg",Fo({},Z(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),P.createElement("title",null,u),P.createElement("desc",null,c),t)}var C0=["children","className"];function Wo(){return Wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wo.apply(this,arguments)}function I0(e,t){if(e==null)return{};var r=k0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function k0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var pe=P.forwardRef(function(e,t){var r=e.children,n=e.className,i=I0(e,C0),a=ee("recharts-layer",n);return P.createElement("g",Wo({className:a},Z(i,!0),{ref:t}),r)}),dt=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function D0(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}var N0=D0,B0=N0;function R0(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:B0(e,t,r)}var L0=R0,F0="\\ud800-\\udfff",z0="\\u0300-\\u036f",W0="\\ufe20-\\ufe2f",U0="\\u20d0-\\u20ff",q0=z0+W0+U0,H0="\\ufe0e\\ufe0f",G0="\\u200d",K0=RegExp("["+G0+F0+q0+H0+"]");function X0(e){return K0.test(e)}var wh=X0;function V0(e){return e.split("")}var Y0=V0,Oh="\\ud800-\\udfff",Z0="\\u0300-\\u036f",J0="\\ufe20-\\ufe2f",Q0="\\u20d0-\\u20ff",ex=Z0+J0+Q0,tx="\\ufe0e\\ufe0f",rx="["+Oh+"]",Uo="["+ex+"]",qo="\\ud83c[\\udffb-\\udfff]",nx="(?:"+Uo+"|"+qo+")",Sh="[^"+Oh+"]",Ah="(?:\\ud83c[\\udde6-\\uddff]){2}",Ph="[\\ud800-\\udbff][\\udc00-\\udfff]",ix="\\u200d",_h=nx+"?",$h="["+tx+"]?",ax="(?:"+ix+"(?:"+[Sh,Ah,Ph].join("|")+")"+$h+_h+")*",ox=$h+_h+ax,ux="(?:"+[Sh+Uo+"?",Uo,Ah,Ph,rx].join("|")+")",cx=RegExp(qo+"(?="+qo+")|"+ux+ox,"g");function sx(e){return e.match(cx)||[]}var lx=sx,fx=Y0,px=wh,hx=lx;function dx(e){return px(e)?hx(e):fx(e)}var vx=dx,yx=L0,mx=wh,gx=vx,bx=vh;function xx(e){return function(t){t=bx(t);var r=mx(t)?gx(t):void 0,n=r?r[0]:t.charAt(0),i=r?yx(r,1).join(""):t.slice(1);return n[e]()+i}}var wx=xx,Ox=wx,Sx=Ox("toUpperCase"),Ax=Sx;const Na=ue(Ax);function oe(e){return function(){return e}}const Th=Math.cos,Pi=Math.sin,tt=Math.sqrt,_i=Math.PI,Ba=2*_i,Ho=Math.PI,Go=2*Ho,Ft=1e-6,Px=Go-Ft;function Eh(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function _x(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Eh;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class $x{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Eh:_x(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,p=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(p>Ft)if(!(Math.abs(l*c-s*f)>Ft)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let h=n-o,y=i-u,v=c*c+s*s,d=h*h+y*y,x=Math.sqrt(v),w=Math.sqrt(p),b=a*Math.tan((Ho-Math.acos((v+p-d)/(2*x*w)))/2),O=b/w,m=b/x;Math.abs(O-1)>Ft&&this._append`L${t+O*f},${r+O*l}`,this._append`A${a},${a},0,0,${+(l*h>f*y)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,p=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>Ft||Math.abs(this._y1-f)>Ft)&&this._append`L${s},${f}`,n&&(p<0&&(p=p%Go+Go),p>Px?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:p>Ft&&this._append`A${n},${n},0,${+(p>=Ho)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function gc(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new $x(t)}function bc(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function jh(e){this._context=e}jh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Ra(e){return new jh(e)}function Mh(e){return e[0]}function Ch(e){return e[1]}function Ih(e,t){var r=oe(!0),n=null,i=Ra,a=null,o=gc(u);e=typeof e=="function"?e:e===void 0?Mh:oe(e),t=typeof t=="function"?t:t===void 0?Ch:oe(t);function u(c){var s,f=(c=bc(c)).length,l,p=!1,h;for(n==null&&(a=i(h=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===p&&((p=!p)?a.lineStart():a.lineEnd()),p&&a.point(+e(l,s,c),+t(l,s,c));if(h)return a=null,h+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:oe(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:oe(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:oe(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function ci(e,t,r){var n=null,i=oe(!0),a=null,o=Ra,u=null,c=gc(s);e=typeof e=="function"?e:e===void 0?Mh:oe(+e),t=typeof t=="function"?t:oe(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?Ch:oe(+r);function s(l){var p,h,y,v=(l=bc(l)).length,d,x=!1,w,b=new Array(v),O=new Array(v);for(a==null&&(u=o(w=c())),p=0;p<=v;++p){if(!(p<v&&i(d=l[p],p,l))===x)if(x=!x)h=p,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=p-1;y>=h;--y)u.point(b[y],O[y]);u.lineEnd(),u.areaEnd()}x&&(b[p]=+e(d,p,l),O[p]=+t(d,p,l),u.point(n?+n(d,p,l):b[p],r?+r(d,p,l):O[p]))}if(w)return u=null,w+""||null}function f(){return Ih().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:oe(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:oe(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:oe(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class kh{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function Tx(e){return new kh(e,!0)}function Ex(e){return new kh(e,!1)}const xc={draw(e,t){const r=tt(t/_i);e.moveTo(r,0),e.arc(0,0,r,0,Ba)}},jx={draw(e,t){const r=tt(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Dh=tt(1/3),Mx=Dh*2,Cx={draw(e,t){const r=tt(t/Mx),n=r*Dh;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},Ix={draw(e,t){const r=tt(t),n=-r/2;e.rect(n,n,r,r)}},kx=.8908130915292852,Nh=Pi(_i/10)/Pi(7*_i/10),Dx=Pi(Ba/10)*Nh,Nx=-Th(Ba/10)*Nh,Bx={draw(e,t){const r=tt(t*kx),n=Dx*r,i=Nx*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=Ba*a/5,u=Th(o),c=Pi(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},fo=tt(3),Rx={draw(e,t){const r=-tt(t/(fo*3));e.moveTo(0,r*2),e.lineTo(-fo*r,-r),e.lineTo(fo*r,-r),e.closePath()}},ze=-.5,We=tt(3)/2,Ko=1/tt(12),Lx=(Ko/2+1)*3,Fx={draw(e,t){const r=tt(t/Lx),n=r/2,i=r*Ko,a=n,o=r*Ko+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(ze*n-We*i,We*n+ze*i),e.lineTo(ze*a-We*o,We*a+ze*o),e.lineTo(ze*u-We*c,We*u+ze*c),e.lineTo(ze*n+We*i,ze*i-We*n),e.lineTo(ze*a+We*o,ze*o-We*a),e.lineTo(ze*u+We*c,ze*c-We*u),e.closePath()}};function zx(e,t){let r=null,n=gc(i);e=typeof e=="function"?e:oe(e||xc),t=typeof t=="function"?t:oe(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:oe(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:oe(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function $i(){}function Ti(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function Bh(e){this._context=e}Bh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Ti(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Ti(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Wx(e){return new Bh(e)}function Rh(e){this._context=e}Rh.prototype={areaStart:$i,areaEnd:$i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Ti(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Ux(e){return new Rh(e)}function Lh(e){this._context=e}Lh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Ti(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function qx(e){return new Lh(e)}function Fh(e){this._context=e}Fh.prototype={areaStart:$i,areaEnd:$i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function Hx(e){return new Fh(e)}function Is(e){return e<0?-1:1}function ks(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(Is(a)+Is(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function Ds(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function po(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Ei(e){this._context=e}Ei.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:po(this,this._t0,Ds(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,po(this,Ds(this,r=ks(this,e,t)),r);break;default:po(this,this._t0,r=ks(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function zh(e){this._context=new Wh(e)}(zh.prototype=Object.create(Ei.prototype)).point=function(e,t){Ei.prototype.point.call(this,t,e)};function Wh(e){this._context=e}Wh.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function Gx(e){return new Ei(e)}function Kx(e){return new zh(e)}function Uh(e){this._context=e}Uh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=Ns(e),i=Ns(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function Ns(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function Xx(e){return new Uh(e)}function La(e,t){this._context=e,this._t=t}La.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function Vx(e){return new La(e,.5)}function Yx(e){return new La(e,0)}function Zx(e){return new La(e,1)}function mr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function Xo(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function Jx(e,t){return e[t]}function Qx(e){const t=[];return t.key=e,t}function ew(){var e=oe([]),t=Xo,r=mr,n=Jx;function i(a){var o=Array.from(e.apply(this,arguments),Qx),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=bc(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:oe(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:oe(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?Xo:typeof a=="function"?a:oe(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??mr,i):r},i}function tw(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}mr(e,t)}}function rw(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}mr(e,t)}}function nw(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,p=f[n-1][1]||0,h=(l-p)/2,y=0;y<u;++y){var v=e[t[y]],d=v[n][1]||0,x=v[n-1][1]||0;h+=d-x}c+=l,s+=h*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,mr(e,t)}}function yn(e){"@babel/helpers - typeof";return yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yn(e)}var iw=["type","size","sizeType"];function Vo(){return Vo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vo.apply(this,arguments)}function Bs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bs(Object(r),!0).forEach(function(n){aw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function aw(e,t,r){return t=ow(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ow(e){var t=uw(e,"string");return yn(t)=="symbol"?t:t+""}function uw(e,t){if(yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function cw(e,t){if(e==null)return{};var r=sw(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function sw(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var qh={symbolCircle:xc,symbolCross:jx,symbolDiamond:Cx,symbolSquare:Ix,symbolStar:Bx,symbolTriangle:Rx,symbolWye:Fx},lw=Math.PI/180,fw=function(t){var r="symbol".concat(Na(t));return qh[r]||xc},pw=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*lw;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},hw=function(t,r){qh["symbol".concat(Na(t))]=r},wc=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=cw(t,iw),s=Rs(Rs({},c),{},{type:n,size:a,sizeType:u}),f=function(){var d=fw(n),x=zx().type(d).size(pw(a,u,n));return x()},l=s.className,p=s.cx,h=s.cy,y=Z(s,!0);return p===+p&&h===+h&&a===+a?P.createElement("path",Vo({},y,{className:ee("recharts-symbols",l),transform:"translate(".concat(p,", ").concat(h,")"),d:f()})):null};wc.registerSymbol=hw;function gr(e){"@babel/helpers - typeof";return gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gr(e)}function Yo(){return Yo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yo.apply(this,arguments)}function Ls(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function dw(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ls(Object(r),!0).forEach(function(n){mn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ls(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vw(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function yw(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Gh(n.key),n)}}function mw(e,t,r){return t&&yw(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function gw(e,t,r){return t=ji(t),bw(e,Hh()?Reflect.construct(t,r||[],ji(e).constructor):t.apply(e,r))}function bw(e,t){if(t&&(gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return xw(e)}function xw(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Hh(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Hh=function(){return!!e})()}function ji(e){return ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ji(e)}function ww(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zo(e,t)}function Zo(e,t){return Zo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Zo(e,t)}function mn(e,t,r){return t=Gh(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Gh(e){var t=Ow(e,"string");return gr(t)=="symbol"?t:t+""}function Ow(e,t){if(gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ue=32,Oc=function(e){function t(){return vw(this,t),gw(this,t,arguments)}return ww(t,e),mw(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Ue/2,o=Ue/6,u=Ue/3,c=n.inactive?i:n.color;if(n.type==="plainline")return P.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Ue,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return P.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Ue,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return P.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Ue/8,"h").concat(Ue,"v").concat(Ue*3/4,"h").concat(-Ue,"z"),className:"recharts-legend-icon"});if(P.isValidElement(n.legendIcon)){var s=dw({},n);return delete s.legendIcon,P.cloneElement(n.legendIcon,s)}return P.createElement(wc,{fill:c,cx:a,cy:a,size:Ue,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:Ue,height:Ue},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},p={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(h,y){var v=h.formatter||c,d=ee(mn(mn({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",h.inactive));if(h.type==="none")return null;var x=V(h.value)?null:h.value;dt(!V(h.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var w=h.inactive?s:h.color;return P.createElement("li",Yo({className:d,style:l,key:"legend-item-".concat(y)},Ai(n.props,h,y)),P.createElement(zo,{width:o,height:o,viewBox:f,style:p},n.renderIcon(h)),P.createElement("span",{className:"recharts-legend-item-text",style:{color:w}},v?v(x,h,y):x))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return P.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(B.PureComponent);mn(Oc,"displayName","Legend");mn(Oc,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var Sw=Sa;function Aw(){this.__data__=new Sw,this.size=0}var Pw=Aw;function _w(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var $w=_w;function Tw(e){return this.__data__.get(e)}var Ew=Tw;function jw(e){return this.__data__.has(e)}var Mw=jw,Cw=Sa,Iw=sc,kw=lc,Dw=200;function Nw(e,t){var r=this.__data__;if(r instanceof Cw){var n=r.__data__;if(!Iw||n.length<Dw-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new kw(n)}return r.set(e,t),this.size=r.size,this}var Bw=Nw,Rw=Sa,Lw=Pw,Fw=$w,zw=Ew,Ww=Mw,Uw=Bw;function Hr(e){var t=this.__data__=new Rw(e);this.size=t.size}Hr.prototype.clear=Lw;Hr.prototype.delete=Fw;Hr.prototype.get=zw;Hr.prototype.has=Ww;Hr.prototype.set=Uw;var Kh=Hr,qw="__lodash_hash_undefined__";function Hw(e){return this.__data__.set(e,qw),this}var Gw=Hw;function Kw(e){return this.__data__.has(e)}var Xw=Kw,Vw=lc,Yw=Gw,Zw=Xw;function Mi(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new Vw;++t<r;)this.add(e[t])}Mi.prototype.add=Mi.prototype.push=Yw;Mi.prototype.has=Zw;var Xh=Mi;function Jw(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var Vh=Jw;function Qw(e,t){return e.has(t)}var Yh=Qw,eO=Xh,tO=Vh,rO=Yh,nO=1,iO=2;function aO(e,t,r,n,i,a){var o=r&nO,u=e.length,c=t.length;if(u!=c&&!(o&&c>u))return!1;var s=a.get(e),f=a.get(t);if(s&&f)return s==t&&f==e;var l=-1,p=!0,h=r&iO?new eO:void 0;for(a.set(e,t),a.set(t,e);++l<u;){var y=e[l],v=t[l];if(n)var d=o?n(v,y,l,t,e,a):n(y,v,l,e,t,a);if(d!==void 0){if(d)continue;p=!1;break}if(h){if(!tO(t,function(x,w){if(!rO(h,w)&&(y===x||i(y,x,r,n,a)))return h.push(w)})){p=!1;break}}else if(!(y===v||i(y,v,r,n,a))){p=!1;break}}return a.delete(e),a.delete(t),p}var Zh=aO,oO=ut,uO=oO.Uint8Array,cO=uO;function sO(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}var lO=sO;function fO(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var Sc=fO,Fs=Jn,zs=cO,pO=cc,hO=Zh,dO=lO,vO=Sc,yO=1,mO=2,gO="[object Boolean]",bO="[object Date]",xO="[object Error]",wO="[object Map]",OO="[object Number]",SO="[object RegExp]",AO="[object Set]",PO="[object String]",_O="[object Symbol]",$O="[object ArrayBuffer]",TO="[object DataView]",Ws=Fs?Fs.prototype:void 0,ho=Ws?Ws.valueOf:void 0;function EO(e,t,r,n,i,a,o){switch(r){case TO:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case $O:return!(e.byteLength!=t.byteLength||!a(new zs(e),new zs(t)));case gO:case bO:case OO:return pO(+e,+t);case xO:return e.name==t.name&&e.message==t.message;case SO:case PO:return e==t+"";case wO:var u=dO;case AO:var c=n&yO;if(u||(u=vO),e.size!=t.size&&!c)return!1;var s=o.get(e);if(s)return s==t;n|=mO,o.set(e,t);var f=hO(u(e),u(t),n,i,a,o);return o.delete(e),f;case _O:if(ho)return ho.call(e)==ho.call(t)}return!1}var jO=EO;function MO(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}var Jh=MO,CO=Jh,IO=De;function kO(e,t,r){var n=t(e);return IO(e)?n:CO(n,r(e))}var DO=kO;function NO(e,t){for(var r=-1,n=e==null?0:e.length,i=0,a=[];++r<n;){var o=e[r];t(o,r,e)&&(a[i++]=o)}return a}var BO=NO;function RO(){return[]}var LO=RO,FO=BO,zO=LO,WO=Object.prototype,UO=WO.propertyIsEnumerable,Us=Object.getOwnPropertySymbols,qO=Us?function(e){return e==null?[]:(e=Object(e),FO(Us(e),function(t){return UO.call(e,t)}))}:zO,HO=qO;function GO(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var KO=GO,XO=xt,VO=wt,YO="[object Arguments]";function ZO(e){return VO(e)&&XO(e)==YO}var JO=ZO,qs=JO,QO=wt,Qh=Object.prototype,e1=Qh.hasOwnProperty,t1=Qh.propertyIsEnumerable,r1=qs(function(){return arguments}())?qs:function(e){return QO(e)&&e1.call(e,"callee")&&!t1.call(e,"callee")},Ac=r1,Ci={exports:{}};function n1(){return!1}var i1=n1;Ci.exports;(function(e,t){var r=ut,n=i1,i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s})(Ci,Ci.exports);var ed=Ci.exports,a1=9007199254740991,o1=/^(?:0|[1-9]\d*)$/;function u1(e,t){var r=typeof e;return t=t??a1,!!t&&(r=="number"||r!="symbol"&&o1.test(e))&&e>-1&&e%1==0&&e<t}var Pc=u1,c1=9007199254740991;function s1(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=c1}var _c=s1,l1=xt,f1=_c,p1=wt,h1="[object Arguments]",d1="[object Array]",v1="[object Boolean]",y1="[object Date]",m1="[object Error]",g1="[object Function]",b1="[object Map]",x1="[object Number]",w1="[object Object]",O1="[object RegExp]",S1="[object Set]",A1="[object String]",P1="[object WeakMap]",_1="[object ArrayBuffer]",$1="[object DataView]",T1="[object Float32Array]",E1="[object Float64Array]",j1="[object Int8Array]",M1="[object Int16Array]",C1="[object Int32Array]",I1="[object Uint8Array]",k1="[object Uint8ClampedArray]",D1="[object Uint16Array]",N1="[object Uint32Array]",se={};se[T1]=se[E1]=se[j1]=se[M1]=se[C1]=se[I1]=se[k1]=se[D1]=se[N1]=!0;se[h1]=se[d1]=se[_1]=se[v1]=se[$1]=se[y1]=se[m1]=se[g1]=se[b1]=se[x1]=se[w1]=se[O1]=se[S1]=se[A1]=se[P1]=!1;function B1(e){return p1(e)&&f1(e.length)&&!!se[l1(e)]}var R1=B1;function L1(e){return function(t){return e(t)}}var td=L1,Ii={exports:{}};Ii.exports;(function(e,t){var r=sh,n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u})(Ii,Ii.exports);var F1=Ii.exports,z1=R1,W1=td,Hs=F1,Gs=Hs&&Hs.isTypedArray,U1=Gs?W1(Gs):z1,rd=U1,q1=KO,H1=Ac,G1=De,K1=ed,X1=Pc,V1=rd,Y1=Object.prototype,Z1=Y1.hasOwnProperty;function J1(e,t){var r=G1(e),n=!r&&H1(e),i=!r&&!n&&K1(e),a=!r&&!n&&!i&&V1(e),o=r||n||i||a,u=o?q1(e.length,String):[],c=u.length;for(var s in e)(t||Z1.call(e,s))&&!(o&&(s=="length"||i&&(s=="offset"||s=="parent")||a&&(s=="buffer"||s=="byteLength"||s=="byteOffset")||X1(s,c)))&&u.push(s);return u}var Q1=J1,eS=Object.prototype;function tS(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||eS;return e===r}var rS=tS;function nS(e,t){return function(r){return e(t(r))}}var nd=nS,iS=nd,aS=iS(Object.keys,Object),oS=aS,uS=rS,cS=oS,sS=Object.prototype,lS=sS.hasOwnProperty;function fS(e){if(!uS(e))return cS(e);var t=[];for(var r in Object(e))lS.call(e,r)&&r!="constructor"&&t.push(r);return t}var pS=fS,hS=uc,dS=_c;function vS(e){return e!=null&&dS(e.length)&&!hS(e)}var ei=vS,yS=Q1,mS=pS,gS=ei;function bS(e){return gS(e)?yS(e):mS(e)}var Fa=bS,xS=DO,wS=HO,OS=Fa;function SS(e){return xS(e,OS,wS)}var AS=SS,Ks=AS,PS=1,_S=Object.prototype,$S=_S.hasOwnProperty;function TS(e,t,r,n,i,a){var o=r&PS,u=Ks(e),c=u.length,s=Ks(t),f=s.length;if(c!=f&&!o)return!1;for(var l=c;l--;){var p=u[l];if(!(o?p in t:$S.call(t,p)))return!1}var h=a.get(e),y=a.get(t);if(h&&y)return h==t&&y==e;var v=!0;a.set(e,t),a.set(t,e);for(var d=o;++l<c;){p=u[l];var x=e[p],w=t[p];if(n)var b=o?n(w,x,p,t,e,a):n(x,w,p,e,t,a);if(!(b===void 0?x===w||i(x,w,r,n,a):b)){v=!1;break}d||(d=p=="constructor")}if(v&&!d){var O=e.constructor,m=t.constructor;O!=m&&"constructor"in e&&"constructor"in t&&!(typeof O=="function"&&O instanceof O&&typeof m=="function"&&m instanceof m)&&(v=!1)}return a.delete(e),a.delete(t),v}var ES=TS,jS=tr,MS=ut,CS=jS(MS,"DataView"),IS=CS,kS=tr,DS=ut,NS=kS(DS,"Promise"),BS=NS,RS=tr,LS=ut,FS=RS(LS,"Set"),id=FS,zS=tr,WS=ut,US=zS(WS,"WeakMap"),qS=US,Jo=IS,Qo=sc,eu=BS,tu=id,ru=qS,ad=xt,Gr=fh,Xs="[object Map]",HS="[object Object]",Vs="[object Promise]",Ys="[object Set]",Zs="[object WeakMap]",Js="[object DataView]",GS=Gr(Jo),KS=Gr(Qo),XS=Gr(eu),VS=Gr(tu),YS=Gr(ru),zt=ad;(Jo&&zt(new Jo(new ArrayBuffer(1)))!=Js||Qo&&zt(new Qo)!=Xs||eu&&zt(eu.resolve())!=Vs||tu&&zt(new tu)!=Ys||ru&&zt(new ru)!=Zs)&&(zt=function(e){var t=ad(e),r=t==HS?e.constructor:void 0,n=r?Gr(r):"";if(n)switch(n){case GS:return Js;case KS:return Xs;case XS:return Vs;case VS:return Ys;case YS:return Zs}return t});var ZS=zt,vo=Kh,JS=Zh,QS=jO,eA=ES,Qs=ZS,el=De,tl=ed,tA=rd,rA=1,rl="[object Arguments]",nl="[object Array]",si="[object Object]",nA=Object.prototype,il=nA.hasOwnProperty;function iA(e,t,r,n,i,a){var o=el(e),u=el(t),c=o?nl:Qs(e),s=u?nl:Qs(t);c=c==rl?si:c,s=s==rl?si:s;var f=c==si,l=s==si,p=c==s;if(p&&tl(e)){if(!tl(t))return!1;o=!0,f=!1}if(p&&!f)return a||(a=new vo),o||tA(e)?JS(e,t,r,n,i,a):QS(e,t,c,r,n,i,a);if(!(r&rA)){var h=f&&il.call(e,"__wrapped__"),y=l&&il.call(t,"__wrapped__");if(h||y){var v=h?e.value():e,d=y?t.value():t;return a||(a=new vo),i(v,d,r,n,a)}}return p?(a||(a=new vo),eA(e,t,r,n,i,a)):!1}var aA=iA,oA=aA,al=wt;function od(e,t,r,n,i){return e===t?!0:e==null||t==null||!al(e)&&!al(t)?e!==e&&t!==t:oA(e,t,r,n,od,i)}var $c=od,uA=Kh,cA=$c,sA=1,lA=2;function fA(e,t,r,n){var i=r.length,a=i,o=!n;if(e==null)return!a;for(e=Object(e);i--;){var u=r[i];if(o&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++i<a;){u=r[i];var c=u[0],s=e[c],f=u[1];if(o&&u[2]){if(s===void 0&&!(c in e))return!1}else{var l=new uA;if(n)var p=n(s,f,c,e,t,l);if(!(p===void 0?cA(f,s,sA|lA,n,l):p))return!1}}return!0}var pA=fA,hA=Mt;function dA(e){return e===e&&!hA(e)}var ud=dA,vA=ud,yA=Fa;function mA(e){for(var t=yA(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,vA(i)]}return t}var gA=mA;function bA(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var cd=bA,xA=pA,wA=gA,OA=cd;function SA(e){var t=wA(e);return t.length==1&&t[0][2]?OA(t[0][0],t[0][1]):function(r){return r===e||xA(r,e,t)}}var AA=SA;function PA(e,t){return e!=null&&t in Object(e)}var _A=PA,$A=yh,TA=Ac,EA=De,jA=Pc,MA=_c,CA=Pa;function IA(e,t,r){t=$A(t,e);for(var n=-1,i=t.length,a=!1;++n<i;){var o=CA(t[n]);if(!(a=e!=null&&r(e,o)))break;e=e[o]}return a||++n!=i?a:(i=e==null?0:e.length,!!i&&MA(i)&&jA(o,i)&&(EA(e)||TA(e)))}var kA=IA,DA=_A,NA=kA;function BA(e,t){return e!=null&&NA(e,t,DA)}var RA=BA,LA=$c,FA=mh,zA=RA,WA=oc,UA=ud,qA=cd,HA=Pa,GA=1,KA=2;function XA(e,t){return WA(e)&&UA(t)?qA(HA(e),t):function(r){var n=FA(r,e);return n===void 0&&n===t?zA(r,e):LA(t,n,GA|KA)}}var VA=XA;function YA(e){return e}var Kr=YA;function ZA(e){return function(t){return t==null?void 0:t[e]}}var JA=ZA,QA=hc;function eP(e){return function(t){return QA(t,e)}}var tP=eP,rP=JA,nP=tP,iP=oc,aP=Pa;function oP(e){return iP(e)?rP(aP(e)):nP(e)}var uP=oP,cP=AA,sP=VA,lP=Kr,fP=De,pP=uP;function hP(e){return typeof e=="function"?e:e==null?lP:typeof e=="object"?fP(e)?sP(e[0],e[1]):cP(e):pP(e)}var Ct=hP;function dP(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return-1}var sd=dP;function vP(e){return e!==e}var yP=vP;function mP(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}var gP=mP,bP=sd,xP=yP,wP=gP;function OP(e,t,r){return t===t?wP(e,t,r):bP(e,xP,r)}var SP=OP,AP=SP;function PP(e,t){var r=e==null?0:e.length;return!!r&&AP(e,t,0)>-1}var _P=PP;function $P(e,t,r){for(var n=-1,i=e==null?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}var TP=$P;function EP(){}var jP=EP,yo=id,MP=jP,CP=Sc,IP=1/0,kP=yo&&1/CP(new yo([,-0]))[1]==IP?function(e){return new yo(e)}:MP,DP=kP,NP=Xh,BP=_P,RP=TP,LP=Yh,FP=DP,zP=Sc,WP=200;function UP(e,t,r){var n=-1,i=BP,a=e.length,o=!0,u=[],c=u;if(r)o=!1,i=RP;else if(a>=WP){var s=t?null:FP(e);if(s)return zP(s);o=!1,i=LP,c=new NP}else c=t?[]:u;e:for(;++n<a;){var f=e[n],l=t?t(f):f;if(f=r||f!==0?f:0,o&&l===l){for(var p=c.length;p--;)if(c[p]===l)continue e;t&&c.push(l),u.push(f)}else i(c,l,r)||(c!==u&&c.push(l),u.push(f))}return u}var qP=UP,HP=Ct,GP=qP;function KP(e,t){return e&&e.length?GP(e,HP(t)):[]}var XP=KP;const ol=ue(XP);function ld(e,t,r){return t===!0?ol(e,r):V(t)?ol(e,t):e}function br(e){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},br(e)}var VP=["ref"];function ul(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ct(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ul(Object(r),!0).forEach(function(n){za(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ul(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function YP(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function cl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pd(n.key),n)}}function ZP(e,t,r){return t&&cl(e.prototype,t),r&&cl(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function JP(e,t,r){return t=ki(t),QP(e,fd()?Reflect.construct(t,r||[],ki(e).constructor):t.apply(e,r))}function QP(e,t){if(t&&(br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return e_(e)}function e_(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function fd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(fd=function(){return!!e})()}function ki(e){return ki=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ki(e)}function t_(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nu(e,t)}function nu(e,t){return nu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},nu(e,t)}function za(e,t,r){return t=pd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pd(e){var t=r_(e,"string");return br(t)=="symbol"?t:t+""}function r_(e,t){if(br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function n_(e,t){if(e==null)return{};var r=i_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function i_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function a_(e){return e.value}function o_(e,t){if(P.isValidElement(e))return P.cloneElement(e,t);if(typeof e=="function")return P.createElement(e,t);t.ref;var r=n_(t,VP);return P.createElement(Oc,r)}var sl=1,dr=function(e){function t(){var r;YP(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=JP(this,t,[].concat(i)),za(r,"lastBoundingBox",{width:-1,height:-1}),r}return t_(t,e),ZP(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>sl||Math.abs(i.height-this.lastBoundingBox.height)>sl)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?ct({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,p;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var h=this.getBBoxSnapshot();l={left:((s||0)-h.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();p={top:((f||0)-y.height)/2}}else p=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return ct(ct({},l),p)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=ct(ct({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return P.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(h){n.wrapperNode=h}},o_(a,ct(ct({},this.props),{},{payload:ld(f,s,a_)})))}}],[{key:"getWithHeight",value:function(n,i){var a=ct(ct({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&R(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(B.PureComponent);za(dr,"displayName","Legend");za(dr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ll=Jn,u_=Ac,c_=De,fl=ll?ll.isConcatSpreadable:void 0;function s_(e){return c_(e)||u_(e)||!!(fl&&e&&e[fl])}var l_=s_,f_=Jh,p_=l_;function hd(e,t,r,n,i){var a=-1,o=e.length;for(r||(r=p_),i||(i=[]);++a<o;){var u=e[a];t>0&&r(u)?t>1?hd(u,t-1,r,n,i):f_(i,u):n||(i[i.length]=u)}return i}var dd=hd;function h_(e){return function(t,r,n){for(var i=-1,a=Object(t),o=n(t),u=o.length;u--;){var c=o[e?u:++i];if(r(a[c],c,a)===!1)break}return t}}var d_=h_,v_=d_,y_=v_(),m_=y_,g_=m_,b_=Fa;function x_(e,t){return e&&g_(e,t,b_)}var vd=x_,w_=ei;function O_(e,t){return function(r,n){if(r==null)return r;if(!w_(r))return e(r,n);for(var i=r.length,a=t?i:-1,o=Object(r);(t?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}var S_=O_,A_=vd,P_=S_,__=P_(A_),Tc=__,$_=Tc,T_=ei;function E_(e,t){var r=-1,n=T_(e)?Array(e.length):[];return $_(e,function(i,a,o){n[++r]=t(i,a,o)}),n}var yd=E_;function j_(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}var M_=j_,pl=Lr;function C_(e,t){if(e!==t){var r=e!==void 0,n=e===null,i=e===e,a=pl(e),o=t!==void 0,u=t===null,c=t===t,s=pl(t);if(!u&&!s&&!a&&e>t||a&&o&&c&&!u&&!s||n&&o&&c||!r&&c||!i)return 1;if(!n&&!a&&!s&&e<t||s&&r&&i&&!n&&!a||u&&r&&i||!o&&i||!c)return-1}return 0}var I_=C_,k_=I_;function D_(e,t,r){for(var n=-1,i=e.criteria,a=t.criteria,o=i.length,u=r.length;++n<o;){var c=k_(i[n],a[n]);if(c){if(n>=u)return c;var s=r[n];return c*(s=="desc"?-1:1)}}return e.index-t.index}var N_=D_,mo=pc,B_=hc,R_=Ct,L_=yd,F_=M_,z_=td,W_=N_,U_=Kr,q_=De;function H_(e,t,r){t.length?t=mo(t,function(a){return q_(a)?function(o){return B_(o,a.length===1?a[0]:a)}:a}):t=[U_];var n=-1;t=mo(t,z_(R_));var i=L_(e,function(a,o,u){var c=mo(t,function(s){return s(a)});return{criteria:c,index:++n,value:a}});return F_(i,function(a,o){return W_(a,o,r)})}var G_=H_;function K_(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var X_=K_,V_=X_,hl=Math.max;function Y_(e,t,r){return t=hl(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,a=hl(n.length-t,0),o=Array(a);++i<a;)o[i]=n[t+i];i=-1;for(var u=Array(t+1);++i<t;)u[i]=n[i];return u[t]=r(o),V_(e,this,u)}}var Z_=Y_;function J_(e){return function(){return e}}var Q_=J_,e$=tr,t$=function(){try{var e=e$(Object,"defineProperty");return e({},"",{}),e}catch{}}(),md=t$,r$=Q_,dl=md,n$=Kr,i$=dl?function(e,t){return dl(e,"toString",{configurable:!0,enumerable:!1,value:r$(t),writable:!0})}:n$,a$=i$,o$=800,u$=16,c$=Date.now;function s$(e){var t=0,r=0;return function(){var n=c$(),i=u$-(n-r);if(r=n,i>0){if(++t>=o$)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var l$=s$,f$=a$,p$=l$,h$=p$(f$),d$=h$,v$=Kr,y$=Z_,m$=d$;function g$(e,t){return m$(y$(e,t,v$),e+"")}var b$=g$,x$=cc,w$=ei,O$=Pc,S$=Mt;function A$(e,t,r){if(!S$(r))return!1;var n=typeof t;return(n=="number"?w$(r)&&O$(t,r.length):n=="string"&&t in r)?x$(r[t],e):!1}var Wa=A$,P$=dd,_$=G_,$$=b$,vl=Wa,T$=$$(function(e,t){if(e==null)return[];var r=t.length;return r>1&&vl(e,t[0],t[1])?t=[]:r>2&&vl(t[0],t[1],t[2])&&(t=[t[0]]),_$(e,P$(t,1),[])}),E$=T$;const Ec=ue(E$);function gn(e){"@babel/helpers - typeof";return gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gn(e)}function iu(){return iu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},iu.apply(this,arguments)}function j$(e,t){return k$(e)||I$(e,t)||C$(e,t)||M$()}function M$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function C$(e,t){if(e){if(typeof e=="string")return yl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yl(e,t)}}function yl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function I$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function k$(e){if(Array.isArray(e))return e}function ml(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function go(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ml(Object(r),!0).forEach(function(n){D$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ml(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function D$(e,t,r){return t=N$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function N$(e){var t=B$(e,"string");return gn(t)=="symbol"?t:t+""}function B$(e,t){if(gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function R$(e){return Array.isArray(e)&&Oe(e[0])&&Oe(e[1])?e.join(" ~ "):e}var L$=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,p=t.itemSorter,h=t.wrapperClassName,y=t.labelClassName,v=t.label,d=t.labelFormatter,x=t.accessibilityLayer,w=x===void 0?!1:x,b=function(){if(f&&f.length){var T={padding:0,margin:0},C=(p?Ec(f,p):f).map(function(I,M){if(I.type==="none")return null;var k=go({display:"block",paddingTop:4,paddingBottom:4,color:I.color||"#000"},u),N=I.formatter||l||R$,L=I.value,F=I.name,q=L,G=F;if(N&&q!=null&&G!=null){var W=N(L,F,I,M,f);if(Array.isArray(W)){var K=j$(W,2);q=K[0],G=K[1]}else q=W}return P.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(M),style:k},Oe(G)?P.createElement("span",{className:"recharts-tooltip-item-name"},G):null,Oe(G)?P.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,P.createElement("span",{className:"recharts-tooltip-item-value"},q),P.createElement("span",{className:"recharts-tooltip-item-unit"},I.unit||""))});return P.createElement("ul",{className:"recharts-tooltip-item-list",style:T},C)}return null},O=go({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=go({margin:0},s),g=!J(v),S=g?v:"",A=ee("recharts-default-tooltip",h),_=ee("recharts-tooltip-label",y);g&&d&&f!==void 0&&f!==null&&(S=d(v,f));var j=w?{role:"status","aria-live":"assertive"}:{};return P.createElement("div",iu({className:A,style:O},j),P.createElement("p",{className:_,style:m},P.isValidElement(S)?S:"".concat(S)),b())};function bn(e){"@babel/helpers - typeof";return bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bn(e)}function li(e,t,r){return t=F$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F$(e){var t=z$(e,"string");return bn(t)=="symbol"?t:t+""}function z$(e,t){if(bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var en="recharts-tooltip-wrapper",W$={visibility:"hidden"};function U$(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return ee(en,li(li(li(li({},"".concat(en,"-right"),R(r)&&t&&R(t.x)&&r>=t.x),"".concat(en,"-left"),R(r)&&t&&R(t.x)&&r<t.x),"".concat(en,"-bottom"),R(n)&&t&&R(t.y)&&n>=t.y),"".concat(en,"-top"),R(n)&&t&&R(t.y)&&n<t.y))}function gl(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&R(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var p=f,h=c[n];return p<h?Math.max(l,c[n]):Math.max(f,c[n])}var y=l+u,v=c[n]+s;return y>v?Math.max(f,c[n]):Math.max(l,c[n])}function q$(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function H$(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=gl({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=gl({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=q$({translateX:f,translateY:l,useTranslate3d:u})):s=W$,{cssProperties:s,cssClasses:U$({translateX:f,translateY:l,coordinate:r})}}function xr(e){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xr(e)}function bl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bl(Object(r),!0).forEach(function(n){ou(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function G$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function K$(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,bd(n.key),n)}}function X$(e,t,r){return t&&K$(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function V$(e,t,r){return t=Di(t),Y$(e,gd()?Reflect.construct(t,r||[],Di(e).constructor):t.apply(e,r))}function Y$(e,t){if(t&&(xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Z$(e)}function Z$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function gd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(gd=function(){return!!e})()}function Di(e){return Di=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Di(e)}function J$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&au(e,t)}function au(e,t){return au=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},au(e,t)}function ou(e,t,r){return t=bd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bd(e){var t=Q$(e,"string");return xr(t)=="symbol"?t:t+""}function Q$(e,t){if(xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var wl=1,eT=function(e){function t(){var r;G$(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=V$(this,t,[].concat(i)),ou(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),ou(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return J$(t,e),X$(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>wl||Math.abs(n.height-this.state.lastBoundingBox.height)>wl)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,p=i.isAnimationActive,h=i.offset,y=i.position,v=i.reverseDirection,d=i.useTranslate3d,x=i.viewBox,w=i.wrapperStyle,b=H$({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:h,position:y,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:d,viewBox:x}),O=b.cssClasses,m=b.cssProperties,g=xl(xl({transition:p&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},w);return P.createElement("div",{tabIndex:-1,className:O,style:g,ref:function(A){n.wrapperNode=A}},s)}}])}(B.PureComponent),tT=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},Xr={isSsr:tT()};function wr(e){"@babel/helpers - typeof";return wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wr(e)}function Ol(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Sl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ol(Object(r),!0).forEach(function(n){jc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ol(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function nT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,wd(n.key),n)}}function iT(e,t,r){return t&&nT(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function aT(e,t,r){return t=Ni(t),oT(e,xd()?Reflect.construct(t,r||[],Ni(e).constructor):t.apply(e,r))}function oT(e,t){if(t&&(wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return uT(e)}function uT(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(xd=function(){return!!e})()}function Ni(e){return Ni=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ni(e)}function cT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&uu(e,t)}function uu(e,t){return uu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},uu(e,t)}function jc(e,t,r){return t=wd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wd(e){var t=sT(e,"string");return wr(t)=="symbol"?t:t+""}function sT(e,t){if(wr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function lT(e){return e.dataKey}function fT(e,t){return P.isValidElement(e)?P.cloneElement(e,t):typeof e=="function"?P.createElement(e,t):P.createElement(L$,t)}var st=function(e){function t(){return rT(this,t),aT(this,t,arguments)}return cT(t,e),iT(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,p=i.isAnimationActive,h=i.offset,y=i.payload,v=i.payloadUniqBy,d=i.position,x=i.reverseDirection,w=i.useTranslate3d,b=i.viewBox,O=i.wrapperStyle,m=y??[];l&&m.length&&(m=ld(y.filter(function(S){return S.value!=null&&(S.hide!==!0||n.props.includeHidden)}),v,lT));var g=m.length>0;return P.createElement(eT,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:p,active:a,coordinate:f,hasPayload:g,offset:h,position:d,reverseDirection:x,useTranslate3d:w,viewBox:b,wrapperStyle:O},fT(s,Sl(Sl({},this.props),{},{payload:m})))}}])}(B.PureComponent);jc(st,"displayName","Tooltip");jc(st,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Xr.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var pT=ut,hT=function(){return pT.Date.now()},dT=hT,vT=/\s/;function yT(e){for(var t=e.length;t--&&vT.test(e.charAt(t)););return t}var mT=yT,gT=mT,bT=/^\s+/;function xT(e){return e&&e.slice(0,gT(e)+1).replace(bT,"")}var wT=xT,OT=wT,Al=Mt,ST=Lr,Pl=NaN,AT=/^[-+]0x[0-9a-f]+$/i,PT=/^0b[01]+$/i,_T=/^0o[0-7]+$/i,$T=parseInt;function TT(e){if(typeof e=="number")return e;if(ST(e))return Pl;if(Al(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Al(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=OT(e);var r=PT.test(e);return r||_T.test(e)?$T(e.slice(2),r?2:8):AT.test(e)?Pl:+e}var Od=TT,ET=Mt,bo=dT,_l=Od,jT="Expected a function",MT=Math.max,CT=Math.min;function IT(e,t,r){var n,i,a,o,u,c,s=0,f=!1,l=!1,p=!0;if(typeof e!="function")throw new TypeError(jT);t=_l(t)||0,ET(r)&&(f=!!r.leading,l="maxWait"in r,a=l?MT(_l(r.maxWait)||0,t):a,p="trailing"in r?!!r.trailing:p);function h(g){var S=n,A=i;return n=i=void 0,s=g,o=e.apply(A,S),o}function y(g){return s=g,u=setTimeout(x,t),f?h(g):o}function v(g){var S=g-c,A=g-s,_=t-S;return l?CT(_,a-A):_}function d(g){var S=g-c,A=g-s;return c===void 0||S>=t||S<0||l&&A>=a}function x(){var g=bo();if(d(g))return w(g);u=setTimeout(x,v(g))}function w(g){return u=void 0,p&&n?h(g):(n=i=void 0,o)}function b(){u!==void 0&&clearTimeout(u),s=0,n=c=i=u=void 0}function O(){return u===void 0?o:w(bo())}function m(){var g=bo(),S=d(g);if(n=arguments,i=this,c=g,S){if(u===void 0)return y(c);if(l)return clearTimeout(u),u=setTimeout(x,t),h(c)}return u===void 0&&(u=setTimeout(x,t)),o}return m.cancel=b,m.flush=O,m}var kT=IT,DT=kT,NT=Mt,BT="Expected a function";function RT(e,t,r){var n=!0,i=!0;if(typeof e!="function")throw new TypeError(BT);return NT(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),DT(e,t,{leading:n,maxWait:t,trailing:i})}var LT=RT;const Sd=ue(LT);function xn(e){"@babel/helpers - typeof";return xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xn(e)}function $l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$l(Object(r),!0).forEach(function(n){FT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$l(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function FT(e,t,r){return t=zT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zT(e){var t=WT(e,"string");return xn(t)=="symbol"?t:t+""}function WT(e,t){if(xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function UT(e,t){return KT(e)||GT(e,t)||HT(e,t)||qT()}function qT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function HT(e,t){if(e){if(typeof e=="string")return Tl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Tl(e,t)}}function Tl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function GT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function KT(e){if(Array.isArray(e))return e}var w3=B.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,p=e.maxHeight,h=e.children,y=e.debounce,v=y===void 0?0:y,d=e.id,x=e.className,w=e.onResize,b=e.style,O=b===void 0?{}:b,m=B.useRef(null),g=B.useRef();g.current=w,B.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var S=B.useState({containerWidth:i.width,containerHeight:i.height}),A=UT(S,2),_=A[0],j=A[1],$=B.useCallback(function(C,I){j(function(M){var k=Math.round(C),N=Math.round(I);return M.containerWidth===k&&M.containerHeight===N?M:{containerWidth:k,containerHeight:N}})},[]);B.useEffect(function(){var C=function(F){var q,G=F[0].contentRect,W=G.width,K=G.height;$(W,K),(q=g.current)===null||q===void 0||q.call(g,W,K)};v>0&&(C=Sd(C,v,{trailing:!0,leading:!1}));var I=new ResizeObserver(C),M=m.current.getBoundingClientRect(),k=M.width,N=M.height;return $(k,N),I.observe(m.current),function(){I.disconnect()}},[$,v]);var T=B.useMemo(function(){var C=_.containerWidth,I=_.containerHeight;if(C<0||I<0)return null;dt(Ut(o)||Ut(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),dt(!r||r>0,"The aspect(%s) must be greater than zero.",r);var M=Ut(o)?C:o,k=Ut(c)?I:c;r&&r>0&&(M?k=M/r:k&&(M=k*r),p&&k>p&&(k=p)),dt(M>0||k>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,M,k,o,c,f,l,r);var N=!Array.isArray(h)&&ht(h.type).endsWith("Chart");return P.Children.map(h,function(L){return P.isValidElement(L)?B.cloneElement(L,fi({width:M,height:k},N?{style:fi({height:"100%",width:"100%",maxHeight:k,maxWidth:M},L.props.style)}:{})):L})},[r,h,c,p,l,f,_,o]);return P.createElement("div",{id:d?"".concat(d):void 0,className:ee("recharts-responsive-container",x),style:fi(fi({},O),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:p}),ref:m},T)}),Ad=function(t){return null};Ad.displayName="Cell";function wn(e){"@babel/helpers - typeof";return wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wn(e)}function El(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function cu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?El(Object(r),!0).forEach(function(n){XT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):El(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function XT(e,t,r){return t=VT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function VT(e){var t=YT(e,"string");return wn(t)=="symbol"?t:t+""}function YT(e,t){if(wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ur={widthCache:{},cacheCount:0},ZT=2e3,JT={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},jl="recharts_measurement_span";function QT(e){var t=cu({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var fn=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||Xr.isSsr)return{width:0,height:0};var n=QT(r),i=JSON.stringify({text:t,copyStyle:n});if(ur.widthCache[i])return ur.widthCache[i];try{var a=document.getElementById(jl);a||(a=document.createElement("span"),a.setAttribute("id",jl),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=cu(cu({},JT),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return ur.widthCache[i]=c,++ur.cacheCount>ZT&&(ur.cacheCount=0,ur.widthCache={}),c}catch{return{width:0,height:0}}},eE=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function On(e){"@babel/helpers - typeof";return On=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},On(e)}function Bi(e,t){return iE(e)||nE(e,t)||rE(e,t)||tE()}function tE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function rE(e,t){if(e){if(typeof e=="string")return Ml(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ml(e,t)}}function Ml(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function nE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function iE(e){if(Array.isArray(e))return e}function aE(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Cl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,uE(n.key),n)}}function oE(e,t,r){return t&&Cl(e.prototype,t),r&&Cl(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function uE(e){var t=cE(e,"string");return On(t)=="symbol"?t:t+""}function cE(e,t){if(On(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(On(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Il=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,kl=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,sE=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,lE=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Pd={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},fE=Object.keys(Pd),lr="NaN";function pE(e,t){return e*Pd[t]}var pi=function(){function e(t,r){aE(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!sE.test(r)&&(this.num=NaN,this.unit=""),fE.includes(r)&&(this.num=pE(t,r),this.unit="px")}return oE(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=lE.exec(r))!==null&&n!==void 0?n:[],a=Bi(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function _d(e){if(e.includes(lr))return lr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=Il.exec(t))!==null&&r!==void 0?r:[],i=Bi(n,4),a=i[1],o=i[2],u=i[3],c=pi.parse(a??""),s=pi.parse(u??""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return lr;t=t.replace(Il,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,p=(l=kl.exec(t))!==null&&l!==void 0?l:[],h=Bi(p,4),y=h[1],v=h[2],d=h[3],x=pi.parse(y??""),w=pi.parse(d??""),b=v==="+"?x.add(w):x.subtract(w);if(b.isNaN())return lr;t=t.replace(kl,b.toString())}return t}var Dl=/\(([^()]*)\)/;function hE(e){for(var t=e;t.includes("(");){var r=Dl.exec(t),n=Bi(r,2),i=n[1];t=t.replace(Dl,_d(i))}return t}function dE(e){var t=e.replace(/\s+/g,"");return t=hE(t),t=_d(t),t}function vE(e){try{return dE(e)}catch{return lr}}function xo(e){var t=vE(e.slice(5,-1));return t===lr?"":t}var yE=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],mE=["dx","dy","angle","className","breakAll"];function su(){return su=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},su.apply(this,arguments)}function Nl(e,t){if(e==null)return{};var r=gE(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function gE(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Bl(e,t){return OE(e)||wE(e,t)||xE(e,t)||bE()}function bE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xE(e,t){if(e){if(typeof e=="string")return Rl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Rl(e,t)}}function Rl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function wE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function OE(e){if(Array.isArray(e))return e}var $d=/[ \f\n\r\t\v\u2028\u2029]+/,Td=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];J(r)||(n?a=r.toString().split(""):a=r.toString().split($d));var o=a.map(function(c){return{word:c,width:fn(c,i).width}}),u=n?0:fn(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},SE=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=R(o),l=u,p=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return M.reduce(function(k,N){var L=N.word,F=N.width,q=k[k.length-1];if(q&&(i==null||a||q.width+F+n<Number(i)))q.words.push(L),q.width+=F+n;else{var G={words:[L],width:F};k.push(G)}return k},[])},h=p(r),y=function(M){return M.reduce(function(k,N){return k.width>N.width?k:N})};if(!f)return h;for(var v="…",d=function(M){var k=l.slice(0,M),N=Td({breakAll:s,style:c,children:k+v}).wordsWithComputedWidth,L=p(N),F=L.length>o||y(L).width>Number(i);return[F,L]},x=0,w=l.length-1,b=0,O;x<=w&&b<=l.length-1;){var m=Math.floor((x+w)/2),g=m-1,S=d(g),A=Bl(S,2),_=A[0],j=A[1],$=d(m),T=Bl($,1),C=T[0];if(!_&&!C&&(x=m+1),_&&C&&(w=m-1),!_&&C){O=j;break}b++}return O||h},Ll=function(t){var r=J(t)?[]:t.toString().split($d);return[{words:r}]},AE=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!Xr.isSsr){var c,s,f=Td({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,p=f.spaceWidth;c=l,s=p}else return Ll(i);return SE({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return Ll(i)},Fl="#808080",Ri=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,p=t.textAnchor,h=p===void 0?"start":p,y=t.verticalAnchor,v=y===void 0?"end":y,d=t.fill,x=d===void 0?Fl:d,w=Nl(t,yE),b=B.useMemo(function(){return AE({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:l,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,l,w.style,w.width]),O=w.dx,m=w.dy,g=w.angle,S=w.className,A=w.breakAll,_=Nl(w,mE);if(!Oe(n)||!Oe(a))return null;var j=n+(R(O)?O:0),$=a+(R(m)?m:0),T;switch(v){case"start":T=xo("calc(".concat(s,")"));break;case"middle":T=xo("calc(".concat((b.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:T=xo("calc(".concat(b.length-1," * -").concat(u,")"));break}var C=[];if(l){var I=b[0].width,M=w.width;C.push("scale(".concat((R(M)?M/I:1)/I,")"))}return g&&C.push("rotate(".concat(g,", ").concat(j,", ").concat($,")")),C.length&&(_.transform=C.join(" ")),P.createElement("text",su({},Z(_,!0),{x:j,y:$,className:ee("recharts-text",S),textAnchor:h,fill:x.includes("url")?Fl:x}),b.map(function(k,N){var L=k.words.join(A?"":" ");return P.createElement("tspan",{x:j,dy:N===0?T:u,key:"".concat(L,"-").concat(N)},L)}))};function Et(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function PE(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Mc(e){let t,r,n;e.length!==2?(t=Et,r=(u,c)=>Et(e(u),c),n=(u,c)=>e(u)-c):(t=e===Et||e===PE?e:_E,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function _E(){return 0}function Ed(e){return e===null?NaN:+e}function*$E(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const TE=Mc(Et),ti=TE.right;Mc(Ed).center;class zl extends Map{constructor(t,r=ME){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(Wl(this,t))}has(t){return super.has(Wl(this,t))}set(t,r){return super.set(EE(this,t),r)}delete(t){return super.delete(jE(this,t))}}function Wl({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function EE({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function jE({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function ME(e){return e!==null&&typeof e=="object"?e.valueOf():e}function CE(e=Et){if(e===Et)return jd;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function jd(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const IE=Math.sqrt(50),kE=Math.sqrt(10),DE=Math.sqrt(2);function Li(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=IE?10:a>=kE?5:a>=DE?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?Li(e,t,r*2):[u,c,s]}function lu(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Li(t,e,r):Li(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function fu(e,t,r){return t=+t,e=+e,r=+r,Li(e,t,r)[2]}function pu(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?fu(t,e,r):fu(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Ul(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function ql(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function Md(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?jd:CE(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),p=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),h=Math.max(r,Math.floor(t-s*l/c+p)),y=Math.min(n,Math.floor(t+(c-s)*l/c+p));Md(e,t,h,y,i)}const a=e[t];let o=r,u=n;for(tn(e,r,t),i(e[n],a)>0&&tn(e,r,n);o<u;){for(tn(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?tn(e,r,u):(++u,tn(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function tn(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function NE(e,t,r){if(e=Float64Array.from($E(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return ql(e);if(t>=1)return Ul(e);var n,i=(n-1)*t,a=Math.floor(i),o=Ul(Md(e,a).subarray(0,a+1)),u=ql(e.subarray(a+1));return o+(u-o)*(i-a)}}function BE(e,t,r=Ed){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function RE(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function Ve(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function Ot(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const hu=Symbol("implicit");function Cc(){var e=new zl,t=[],r=[],n=hu;function i(a){let o=e.get(a);if(o===void 0){if(n!==hu)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new zl;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Cc(t,r).unknown(n)},Ve.apply(i,arguments),i}function Sn(){var e=Cc().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var p=t().length,h=i<n,y=h?i:n,v=h?n:i;a=(v-y)/Math.max(1,p-c+s*2),u&&(a=Math.floor(a)),y+=(v-y-a*(p-c))*f,o=a*(1-c),u&&(y=Math.round(y),o=Math.round(o));var d=RE(p).map(function(x){return y+a*x});return r(h?d.reverse():d)}return e.domain=function(p){return arguments.length?(t(p),l()):t()},e.range=function(p){return arguments.length?([n,i]=p,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(p){return[n,i]=p,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(p){return arguments.length?(u=!!p,l()):u},e.padding=function(p){return arguments.length?(c=Math.min(1,s=+p),l()):c},e.paddingInner=function(p){return arguments.length?(c=Math.min(1,p),l()):c},e.paddingOuter=function(p){return arguments.length?(s=+p,l()):s},e.align=function(p){return arguments.length?(f=Math.max(0,Math.min(1,p)),l()):f},e.copy=function(){return Sn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},Ve.apply(l(),arguments)}function Cd(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return Cd(t())},e}function pn(){return Cd(Sn.apply(null,arguments).paddingInner(1))}function Ic(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function Id(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function ri(){}var An=.7,Fi=1/An,vr="\\s*([+-]?\\d+)\\s*",Pn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",it="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",LE=/^#([0-9a-f]{3,8})$/,FE=new RegExp(`^rgb\\(${vr},${vr},${vr}\\)$`),zE=new RegExp(`^rgb\\(${it},${it},${it}\\)$`),WE=new RegExp(`^rgba\\(${vr},${vr},${vr},${Pn}\\)$`),UE=new RegExp(`^rgba\\(${it},${it},${it},${Pn}\\)$`),qE=new RegExp(`^hsl\\(${Pn},${it},${it}\\)$`),HE=new RegExp(`^hsla\\(${Pn},${it},${it},${Pn}\\)$`),Hl={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Ic(ri,_n,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Gl,formatHex:Gl,formatHex8:GE,formatHsl:KE,formatRgb:Kl,toString:Kl});function Gl(){return this.rgb().formatHex()}function GE(){return this.rgb().formatHex8()}function KE(){return kd(this).formatHsl()}function Kl(){return this.rgb().formatRgb()}function _n(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=LE.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Xl(t):r===3?new ke(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?hi(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?hi(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=FE.exec(e))?new ke(t[1],t[2],t[3],1):(t=zE.exec(e))?new ke(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=WE.exec(e))?hi(t[1],t[2],t[3],t[4]):(t=UE.exec(e))?hi(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=qE.exec(e))?Zl(t[1],t[2]/100,t[3]/100,1):(t=HE.exec(e))?Zl(t[1],t[2]/100,t[3]/100,t[4]):Hl.hasOwnProperty(e)?Xl(Hl[e]):e==="transparent"?new ke(NaN,NaN,NaN,0):null}function Xl(e){return new ke(e>>16&255,e>>8&255,e&255,1)}function hi(e,t,r,n){return n<=0&&(e=t=r=NaN),new ke(e,t,r,n)}function XE(e){return e instanceof ri||(e=_n(e)),e?(e=e.rgb(),new ke(e.r,e.g,e.b,e.opacity)):new ke}function du(e,t,r,n){return arguments.length===1?XE(e):new ke(e,t,r,n??1)}function ke(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}Ic(ke,du,Id(ri,{brighter(e){return e=e==null?Fi:Math.pow(Fi,e),new ke(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?An:Math.pow(An,e),new ke(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ke(Xt(this.r),Xt(this.g),Xt(this.b),zi(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Vl,formatHex:Vl,formatHex8:VE,formatRgb:Yl,toString:Yl}));function Vl(){return`#${qt(this.r)}${qt(this.g)}${qt(this.b)}`}function VE(){return`#${qt(this.r)}${qt(this.g)}${qt(this.b)}${qt((isNaN(this.opacity)?1:this.opacity)*255)}`}function Yl(){const e=zi(this.opacity);return`${e===1?"rgb(":"rgba("}${Xt(this.r)}, ${Xt(this.g)}, ${Xt(this.b)}${e===1?")":`, ${e})`}`}function zi(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Xt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function qt(e){return e=Xt(e),(e<16?"0":"")+e.toString(16)}function Zl(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new Je(e,t,r,n)}function kd(e){if(e instanceof Je)return new Je(e.h,e.s,e.l,e.opacity);if(e instanceof ri||(e=_n(e)),!e)return new Je;if(e instanceof Je)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new Je(o,u,c,e.opacity)}function YE(e,t,r,n){return arguments.length===1?kd(e):new Je(e,t,r,n??1)}function Je(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}Ic(Je,YE,Id(ri,{brighter(e){return e=e==null?Fi:Math.pow(Fi,e),new Je(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?An:Math.pow(An,e),new Je(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new ke(wo(e>=240?e-240:e+120,i,n),wo(e,i,n),wo(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new Je(Jl(this.h),di(this.s),di(this.l),zi(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=zi(this.opacity);return`${e===1?"hsl(":"hsla("}${Jl(this.h)}, ${di(this.s)*100}%, ${di(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Jl(e){return e=(e||0)%360,e<0?e+360:e}function di(e){return Math.max(0,Math.min(1,e||0))}function wo(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const kc=e=>()=>e;function ZE(e,t){return function(r){return e+r*t}}function JE(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function QE(e){return(e=+e)==1?Dd:function(t,r){return r-t?JE(t,r,e):kc(isNaN(t)?r:t)}}function Dd(e,t){var r=t-e;return r?ZE(e,r):kc(isNaN(e)?t:e)}const Ql=function e(t){var r=QE(t);function n(i,a){var o=r((i=du(i)).r,(a=du(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=Dd(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function ej(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function tj(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function rj(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Vr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function nj(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Wi(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function ij(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Vr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var vu=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Oo=new RegExp(vu.source,"g");function aj(e){return function(){return e}}function oj(e){return function(t){return e(t)+""}}function uj(e,t){var r=vu.lastIndex=Oo.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=vu.exec(e))&&(i=Oo.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:Wi(n,i)})),r=Oo.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?oj(c[0].x):aj(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function Vr(e,t){var r=typeof t,n;return t==null||r==="boolean"?kc(t):(r==="number"?Wi:r==="string"?(n=_n(t))?(t=n,Ql):uj:t instanceof _n?Ql:t instanceof Date?nj:tj(t)?ej:Array.isArray(t)?rj:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?ij:Wi)(e,t)}function Dc(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function cj(e,t){t===void 0&&(t=e,e=Vr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function sj(e){return function(){return e}}function Ui(e){return+e}var ef=[0,1];function Me(e){return e}function yu(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:sj(isNaN(t)?NaN:.5)}function lj(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function fj(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=yu(i,n),a=r(o,a)):(n=yu(n,i),a=r(a,o)),function(u){return a(n(u))}}function pj(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=yu(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=ti(e,u,1,n)-1;return a[c](i[c](u))}}function ni(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Ua(){var e=ef,t=ef,r=Vr,n,i,a,o=Me,u,c,s;function f(){var p=Math.min(e.length,t.length);return o!==Me&&(o=lj(e[0],e[p-1])),u=p>2?pj:fj,c=s=null,l}function l(p){return p==null||isNaN(p=+p)?a:(c||(c=u(e.map(n),t,r)))(n(o(p)))}return l.invert=function(p){return o(i((s||(s=u(t,e.map(n),Wi)))(p)))},l.domain=function(p){return arguments.length?(e=Array.from(p,Ui),f()):e.slice()},l.range=function(p){return arguments.length?(t=Array.from(p),f()):t.slice()},l.rangeRound=function(p){return t=Array.from(p),r=Dc,f()},l.clamp=function(p){return arguments.length?(o=p?!0:Me,f()):o!==Me},l.interpolate=function(p){return arguments.length?(r=p,f()):r},l.unknown=function(p){return arguments.length?(a=p,l):a},function(p,h){return n=p,i=h,f()}}function Nc(){return Ua()(Me,Me)}function hj(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function qi(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Or(e){return e=qi(Math.abs(e)),e?e[1]:NaN}function dj(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function vj(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var yj=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function $n(e){if(!(t=yj.exec(e)))throw new Error("invalid format: "+e);var t;return new Bc({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}$n.prototype=Bc.prototype;function Bc(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}Bc.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function mj(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var Nd;function gj(e,t){var r=qi(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Nd=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+qi(e,Math.max(0,t+a-1))[0]}function tf(e,t){var r=qi(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const rf={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:hj,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>tf(e*100,t),r:tf,s:gj,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function nf(e){return e}var af=Array.prototype.map,of=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function bj(e){var t=e.grouping===void 0||e.thousands===void 0?nf:dj(af.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?nf:vj(af.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=$n(l);var p=l.fill,h=l.align,y=l.sign,v=l.symbol,d=l.zero,x=l.width,w=l.comma,b=l.precision,O=l.trim,m=l.type;m==="n"?(w=!0,m="g"):rf[m]||(b===void 0&&(b=12),O=!0,m="g"),(d||p==="0"&&h==="=")&&(d=!0,p="0",h="=");var g=v==="$"?r:v==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",S=v==="$"?n:/[%p]/.test(m)?o:"",A=rf[m],_=/[defgprs%]/.test(m);b=b===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,b)):Math.max(0,Math.min(20,b));function j($){var T=g,C=S,I,M,k;if(m==="c")C=A($)+C,$="";else{$=+$;var N=$<0||1/$<0;if($=isNaN($)?c:A(Math.abs($),b),O&&($=mj($)),N&&+$==0&&y!=="+"&&(N=!1),T=(N?y==="("?y:u:y==="-"||y==="("?"":y)+T,C=(m==="s"?of[8+Nd/3]:"")+C+(N&&y==="("?")":""),_){for(I=-1,M=$.length;++I<M;)if(k=$.charCodeAt(I),48>k||k>57){C=(k===46?i+$.slice(I+1):$.slice(I))+C,$=$.slice(0,I);break}}}w&&!d&&($=t($,1/0));var L=T.length+$.length+C.length,F=L<x?new Array(x-L+1).join(p):"";switch(w&&d&&($=t(F+$,F.length?x-C.length:1/0),F=""),h){case"<":$=T+$+C+F;break;case"=":$=T+F+$+C;break;case"^":$=F.slice(0,L=F.length>>1)+T+$+C+F.slice(L);break;default:$=F+T+$+C;break}return a($)}return j.toString=function(){return l+""},j}function f(l,p){var h=s((l=$n(l),l.type="f",l)),y=Math.max(-8,Math.min(8,Math.floor(Or(p)/3)))*3,v=Math.pow(10,-y),d=of[8+y/3];return function(x){return h(v*x)+d}}return{format:s,formatPrefix:f}}var vi,Rc,Bd;xj({thousands:",",grouping:[3],currency:["$",""]});function xj(e){return vi=bj(e),Rc=vi.format,Bd=vi.formatPrefix,vi}function wj(e){return Math.max(0,-Or(Math.abs(e)))}function Oj(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(Or(t)/3)))*3-Or(Math.abs(e)))}function Sj(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Or(t)-Or(e))+1}function Rd(e,t,r,n){var i=pu(e,t,r),a;switch(n=$n(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=Oj(i,o))&&(n.precision=a),Bd(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=Sj(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=wj(i))&&(n.precision=a-(n.type==="%")*2);break}}return Rc(n)}function It(e){var t=e.domain;return e.ticks=function(r){var n=t();return lu(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return Rd(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=fu(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function Hi(){var e=Nc();return e.copy=function(){return ni(e,Hi())},Ve.apply(e,arguments),It(e)}function Ld(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Ui),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Ld(e).unknown(t)},e=arguments.length?Array.from(e,Ui):[0,1],It(r)}function Fd(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function uf(e){return Math.log(e)}function cf(e){return Math.exp(e)}function Aj(e){return-Math.log(-e)}function Pj(e){return-Math.exp(-e)}function _j(e){return isFinite(e)?+("1e"+e):e<0?0:e}function $j(e){return e===10?_j:e===Math.E?Math.exp:t=>Math.pow(e,t)}function Tj(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function sf(e){return(t,r)=>-e(-t,r)}function Lc(e){const t=e(uf,cf),r=t.domain;let n=10,i,a;function o(){return i=Tj(n),a=$j(n),r()[0]<0?(i=sf(i),a=sf(a),e(Aj,Pj)):e(uf,cf),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let p=i(s),h=i(f),y,v;const d=u==null?10:+u;let x=[];if(!(n%1)&&h-p<d){if(p=Math.floor(p),h=Math.ceil(h),s>0){for(;p<=h;++p)for(y=1;y<n;++y)if(v=p<0?y/a(-p):y*a(p),!(v<s)){if(v>f)break;x.push(v)}}else for(;p<=h;++p)for(y=n-1;y>=1;--y)if(v=p>0?y/a(-p):y*a(p),!(v<s)){if(v>f)break;x.push(v)}x.length*2<d&&(x=lu(s,f,d))}else x=lu(p,h,Math.min(h-p,d)).map(a);return l?x.reverse():x},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=$n(c)).precision==null&&(c.trim=!0),c=Rc(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(Fd(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function zd(){const e=Lc(Ua()).domain([1,10]);return e.copy=()=>ni(e,zd()).base(e.base()),Ve.apply(e,arguments),e}function lf(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function ff(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Fc(e){var t=1,r=e(lf(t),ff(t));return r.constant=function(n){return arguments.length?e(lf(t=+n),ff(t)):t},It(r)}function Wd(){var e=Fc(Ua());return e.copy=function(){return ni(e,Wd()).constant(e.constant())},Ve.apply(e,arguments)}function pf(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function Ej(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function jj(e){return e<0?-e*e:e*e}function zc(e){var t=e(Me,Me),r=1;function n(){return r===1?e(Me,Me):r===.5?e(Ej,jj):e(pf(r),pf(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},It(t)}function Wc(){var e=zc(Ua());return e.copy=function(){return ni(e,Wc()).exponent(e.exponent())},Ve.apply(e,arguments),e}function Mj(){return Wc.apply(null,arguments).exponent(.5)}function hf(e){return Math.sign(e)*e*e}function Cj(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function Ud(){var e=Nc(),t=[0,1],r=!1,n;function i(a){var o=Cj(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(hf(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Ui)).map(hf)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Ud(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},Ve.apply(i,arguments),It(i)}function qd(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=BE(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[ti(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Et),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return qd().domain(e).range(t).unknown(n)},Ve.apply(a,arguments)}function Hd(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[ti(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return Hd().domain([e,t]).range(i).unknown(a)},Ve.apply(It(o),arguments)}function Gd(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[ti(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Gd().domain(e).range(t).unknown(r)},Ve.apply(i,arguments)}const So=new Date,Ao=new Date;function Se(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>Se(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(So.setTime(+a),Ao.setTime(+o),e(So),e(Ao),Math.floor(r(So,Ao))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Gi=Se(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Gi.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?Se(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Gi);Gi.range;const lt=1e3,He=lt*60,ft=He*60,yt=ft*24,Uc=yt*7,df=yt*30,Po=yt*365,Ht=Se(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*lt)},(e,t)=>(t-e)/lt,e=>e.getUTCSeconds());Ht.range;const qc=Se(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*lt)},(e,t)=>{e.setTime(+e+t*He)},(e,t)=>(t-e)/He,e=>e.getMinutes());qc.range;const Hc=Se(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*He)},(e,t)=>(t-e)/He,e=>e.getUTCMinutes());Hc.range;const Gc=Se(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*lt-e.getMinutes()*He)},(e,t)=>{e.setTime(+e+t*ft)},(e,t)=>(t-e)/ft,e=>e.getHours());Gc.range;const Kc=Se(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*ft)},(e,t)=>(t-e)/ft,e=>e.getUTCHours());Kc.range;const ii=Se(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*He)/yt,e=>e.getDate()-1);ii.range;const qa=Se(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/yt,e=>e.getUTCDate()-1);qa.range;const Kd=Se(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/yt,e=>Math.floor(e/yt));Kd.range;function rr(e){return Se(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*He)/Uc)}const Ha=rr(0),Ki=rr(1),Ij=rr(2),kj=rr(3),Sr=rr(4),Dj=rr(5),Nj=rr(6);Ha.range;Ki.range;Ij.range;kj.range;Sr.range;Dj.range;Nj.range;function nr(e){return Se(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/Uc)}const Ga=nr(0),Xi=nr(1),Bj=nr(2),Rj=nr(3),Ar=nr(4),Lj=nr(5),Fj=nr(6);Ga.range;Xi.range;Bj.range;Rj.range;Ar.range;Lj.range;Fj.range;const Xc=Se(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Xc.range;const Vc=Se(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Vc.range;const mt=Se(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());mt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Se(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});mt.range;const gt=Se(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());gt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Se(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});gt.range;function Xd(e,t,r,n,i,a){const o=[[Ht,1,lt],[Ht,5,5*lt],[Ht,15,15*lt],[Ht,30,30*lt],[a,1,He],[a,5,5*He],[a,15,15*He],[a,30,30*He],[i,1,ft],[i,3,3*ft],[i,6,6*ft],[i,12,12*ft],[n,1,yt],[n,2,2*yt],[r,1,Uc],[t,1,df],[t,3,3*df],[e,1,Po]];function u(s,f,l){const p=f<s;p&&([s,f]=[f,s]);const h=l&&typeof l.range=="function"?l:c(s,f,l),y=h?h.range(s,+f+1):[];return p?y.reverse():y}function c(s,f,l){const p=Math.abs(f-s)/l,h=Mc(([,,d])=>d).right(o,p);if(h===o.length)return e.every(pu(s/Po,f/Po,l));if(h===0)return Gi.every(Math.max(pu(s,f,l),1));const[y,v]=o[p/o[h-1][2]<o[h][2]/p?h-1:h];return y.every(v)}return[u,c]}const[zj,Wj]=Xd(gt,Vc,Ga,Kd,Kc,Hc),[Uj,qj]=Xd(mt,Xc,Ha,ii,Gc,qc);function _o(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function $o(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function rn(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function Hj(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=nn(i),f=an(i),l=nn(a),p=an(a),h=nn(o),y=an(o),v=nn(u),d=an(u),x=nn(c),w=an(c),b={a:N,A:L,b:F,B:q,c:null,d:xf,e:xf,f:dM,g:AM,G:_M,H:fM,I:pM,j:hM,L:Vd,m:vM,M:yM,p:G,q:W,Q:Sf,s:Af,S:mM,u:gM,U:bM,V:xM,w:wM,W:OM,x:null,X:null,y:SM,Y:PM,Z:$M,"%":Of},O={a:K,A:ce,b:ve,B:Ne,c:null,d:wf,e:wf,f:MM,g:zM,G:UM,H:TM,I:EM,j:jM,L:Zd,m:CM,M:IM,p:Nt,q:Ce,Q:Sf,s:Af,S:kM,u:DM,U:NM,V:BM,w:RM,W:LM,x:null,X:null,y:FM,Y:WM,Z:qM,"%":Of},m={a:j,A:$,b:T,B:C,c:I,d:gf,e:gf,f:uM,g:mf,G:yf,H:bf,I:bf,j:nM,L:oM,m:rM,M:iM,p:_,q:tM,Q:sM,s:lM,S:aM,u:Yj,U:Zj,V:Jj,w:Vj,W:Qj,x:M,X:k,y:mf,Y:yf,Z:eM,"%":cM};b.x=g(r,b),b.X=g(n,b),b.c=g(t,b),O.x=g(r,O),O.X=g(n,O),O.c=g(t,O);function g(z,X){return function(Y){var D=[],he=-1,Q=0,ge=z.length,be,Ie,St;for(Y instanceof Date||(Y=new Date(+Y));++he<ge;)z.charCodeAt(he)===37&&(D.push(z.slice(Q,he)),(Ie=vf[be=z.charAt(++he)])!=null?be=z.charAt(++he):Ie=be==="e"?" ":"0",(St=X[be])&&(be=St(Y,Ie)),D.push(be),Q=he+1);return D.push(z.slice(Q,he)),D.join("")}}function S(z,X){return function(Y){var D=rn(1900,void 0,1),he=A(D,z,Y+="",0),Q,ge;if(he!=Y.length)return null;if("Q"in D)return new Date(D.Q);if("s"in D)return new Date(D.s*1e3+("L"in D?D.L:0));if(X&&!("Z"in D)&&(D.Z=0),"p"in D&&(D.H=D.H%12+D.p*12),D.m===void 0&&(D.m="q"in D?D.q:0),"V"in D){if(D.V<1||D.V>53)return null;"w"in D||(D.w=1),"Z"in D?(Q=$o(rn(D.y,0,1)),ge=Q.getUTCDay(),Q=ge>4||ge===0?Xi.ceil(Q):Xi(Q),Q=qa.offset(Q,(D.V-1)*7),D.y=Q.getUTCFullYear(),D.m=Q.getUTCMonth(),D.d=Q.getUTCDate()+(D.w+6)%7):(Q=_o(rn(D.y,0,1)),ge=Q.getDay(),Q=ge>4||ge===0?Ki.ceil(Q):Ki(Q),Q=ii.offset(Q,(D.V-1)*7),D.y=Q.getFullYear(),D.m=Q.getMonth(),D.d=Q.getDate()+(D.w+6)%7)}else("W"in D||"U"in D)&&("w"in D||(D.w="u"in D?D.u%7:"W"in D?1:0),ge="Z"in D?$o(rn(D.y,0,1)).getUTCDay():_o(rn(D.y,0,1)).getDay(),D.m=0,D.d="W"in D?(D.w+6)%7+D.W*7-(ge+5)%7:D.w+D.U*7-(ge+6)%7);return"Z"in D?(D.H+=D.Z/100|0,D.M+=D.Z%100,$o(D)):_o(D)}}function A(z,X,Y,D){for(var he=0,Q=X.length,ge=Y.length,be,Ie;he<Q;){if(D>=ge)return-1;if(be=X.charCodeAt(he++),be===37){if(be=X.charAt(he++),Ie=m[be in vf?X.charAt(he++):be],!Ie||(D=Ie(z,Y,D))<0)return-1}else if(be!=Y.charCodeAt(D++))return-1}return D}function _(z,X,Y){var D=s.exec(X.slice(Y));return D?(z.p=f.get(D[0].toLowerCase()),Y+D[0].length):-1}function j(z,X,Y){var D=h.exec(X.slice(Y));return D?(z.w=y.get(D[0].toLowerCase()),Y+D[0].length):-1}function $(z,X,Y){var D=l.exec(X.slice(Y));return D?(z.w=p.get(D[0].toLowerCase()),Y+D[0].length):-1}function T(z,X,Y){var D=x.exec(X.slice(Y));return D?(z.m=w.get(D[0].toLowerCase()),Y+D[0].length):-1}function C(z,X,Y){var D=v.exec(X.slice(Y));return D?(z.m=d.get(D[0].toLowerCase()),Y+D[0].length):-1}function I(z,X,Y){return A(z,t,X,Y)}function M(z,X,Y){return A(z,r,X,Y)}function k(z,X,Y){return A(z,n,X,Y)}function N(z){return o[z.getDay()]}function L(z){return a[z.getDay()]}function F(z){return c[z.getMonth()]}function q(z){return u[z.getMonth()]}function G(z){return i[+(z.getHours()>=12)]}function W(z){return 1+~~(z.getMonth()/3)}function K(z){return o[z.getUTCDay()]}function ce(z){return a[z.getUTCDay()]}function ve(z){return c[z.getUTCMonth()]}function Ne(z){return u[z.getUTCMonth()]}function Nt(z){return i[+(z.getUTCHours()>=12)]}function Ce(z){return 1+~~(z.getUTCMonth()/3)}return{format:function(z){var X=g(z+="",b);return X.toString=function(){return z},X},parse:function(z){var X=S(z+="",!1);return X.toString=function(){return z},X},utcFormat:function(z){var X=g(z+="",O);return X.toString=function(){return z},X},utcParse:function(z){var X=S(z+="",!0);return X.toString=function(){return z},X}}}var vf={"-":"",_:" ",0:"0"},Pe=/^\s*\d+/,Gj=/^%/,Kj=/[\\^$*+?|[\]().{}]/g;function te(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function Xj(e){return e.replace(Kj,"\\$&")}function nn(e){return new RegExp("^(?:"+e.map(Xj).join("|")+")","i")}function an(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function Vj(e,t,r){var n=Pe.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function Yj(e,t,r){var n=Pe.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function Zj(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function Jj(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function Qj(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function yf(e,t,r){var n=Pe.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function mf(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eM(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function tM(e,t,r){var n=Pe.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function rM(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function gf(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function nM(e,t,r){var n=Pe.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function bf(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function iM(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function aM(e,t,r){var n=Pe.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function oM(e,t,r){var n=Pe.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function uM(e,t,r){var n=Pe.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function cM(e,t,r){var n=Gj.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function sM(e,t,r){var n=Pe.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function lM(e,t,r){var n=Pe.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function xf(e,t){return te(e.getDate(),t,2)}function fM(e,t){return te(e.getHours(),t,2)}function pM(e,t){return te(e.getHours()%12||12,t,2)}function hM(e,t){return te(1+ii.count(mt(e),e),t,3)}function Vd(e,t){return te(e.getMilliseconds(),t,3)}function dM(e,t){return Vd(e,t)+"000"}function vM(e,t){return te(e.getMonth()+1,t,2)}function yM(e,t){return te(e.getMinutes(),t,2)}function mM(e,t){return te(e.getSeconds(),t,2)}function gM(e){var t=e.getDay();return t===0?7:t}function bM(e,t){return te(Ha.count(mt(e)-1,e),t,2)}function Yd(e){var t=e.getDay();return t>=4||t===0?Sr(e):Sr.ceil(e)}function xM(e,t){return e=Yd(e),te(Sr.count(mt(e),e)+(mt(e).getDay()===4),t,2)}function wM(e){return e.getDay()}function OM(e,t){return te(Ki.count(mt(e)-1,e),t,2)}function SM(e,t){return te(e.getFullYear()%100,t,2)}function AM(e,t){return e=Yd(e),te(e.getFullYear()%100,t,2)}function PM(e,t){return te(e.getFullYear()%1e4,t,4)}function _M(e,t){var r=e.getDay();return e=r>=4||r===0?Sr(e):Sr.ceil(e),te(e.getFullYear()%1e4,t,4)}function $M(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+te(t/60|0,"0",2)+te(t%60,"0",2)}function wf(e,t){return te(e.getUTCDate(),t,2)}function TM(e,t){return te(e.getUTCHours(),t,2)}function EM(e,t){return te(e.getUTCHours()%12||12,t,2)}function jM(e,t){return te(1+qa.count(gt(e),e),t,3)}function Zd(e,t){return te(e.getUTCMilliseconds(),t,3)}function MM(e,t){return Zd(e,t)+"000"}function CM(e,t){return te(e.getUTCMonth()+1,t,2)}function IM(e,t){return te(e.getUTCMinutes(),t,2)}function kM(e,t){return te(e.getUTCSeconds(),t,2)}function DM(e){var t=e.getUTCDay();return t===0?7:t}function NM(e,t){return te(Ga.count(gt(e)-1,e),t,2)}function Jd(e){var t=e.getUTCDay();return t>=4||t===0?Ar(e):Ar.ceil(e)}function BM(e,t){return e=Jd(e),te(Ar.count(gt(e),e)+(gt(e).getUTCDay()===4),t,2)}function RM(e){return e.getUTCDay()}function LM(e,t){return te(Xi.count(gt(e)-1,e),t,2)}function FM(e,t){return te(e.getUTCFullYear()%100,t,2)}function zM(e,t){return e=Jd(e),te(e.getUTCFullYear()%100,t,2)}function WM(e,t){return te(e.getUTCFullYear()%1e4,t,4)}function UM(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Ar(e):Ar.ceil(e),te(e.getUTCFullYear()%1e4,t,4)}function qM(){return"+0000"}function Of(){return"%"}function Sf(e){return+e}function Af(e){return Math.floor(+e/1e3)}var cr,Qd,ev;HM({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function HM(e){return cr=Hj(e),Qd=cr.format,cr.parse,ev=cr.utcFormat,cr.utcParse,cr}function GM(e){return new Date(e)}function KM(e){return e instanceof Date?+e:+new Date(+e)}function Yc(e,t,r,n,i,a,o,u,c,s){var f=Nc(),l=f.invert,p=f.domain,h=s(".%L"),y=s(":%S"),v=s("%I:%M"),d=s("%I %p"),x=s("%a %d"),w=s("%b %d"),b=s("%B"),O=s("%Y");function m(g){return(c(g)<g?h:u(g)<g?y:o(g)<g?v:a(g)<g?d:n(g)<g?i(g)<g?x:w:r(g)<g?b:O)(g)}return f.invert=function(g){return new Date(l(g))},f.domain=function(g){return arguments.length?p(Array.from(g,KM)):p().map(GM)},f.ticks=function(g){var S=p();return e(S[0],S[S.length-1],g??10)},f.tickFormat=function(g,S){return S==null?m:s(S)},f.nice=function(g){var S=p();return(!g||typeof g.range!="function")&&(g=t(S[0],S[S.length-1],g??10)),g?p(Fd(S,g)):f},f.copy=function(){return ni(f,Yc(e,t,r,n,i,a,o,u,c,s))},f}function XM(){return Ve.apply(Yc(Uj,qj,mt,Xc,Ha,ii,Gc,qc,Ht,Qd).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function VM(){return Ve.apply(Yc(zj,Wj,gt,Vc,Ga,qa,Kc,Hc,Ht,ev).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Ka(){var e=0,t=1,r,n,i,a,o=Me,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(p){var h,y;return arguments.length?([h,y]=p,o=l(h,y),s):[o(0),o(1)]}}return s.range=f(Vr),s.rangeRound=f(Dc),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function kt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function tv(){var e=It(Ka()(Me));return e.copy=function(){return kt(e,tv())},Ot.apply(e,arguments)}function rv(){var e=Lc(Ka()).domain([1,10]);return e.copy=function(){return kt(e,rv()).base(e.base())},Ot.apply(e,arguments)}function nv(){var e=Fc(Ka());return e.copy=function(){return kt(e,nv()).constant(e.constant())},Ot.apply(e,arguments)}function Zc(){var e=zc(Ka());return e.copy=function(){return kt(e,Zc()).exponent(e.exponent())},Ot.apply(e,arguments)}function YM(){return Zc.apply(null,arguments).exponent(.5)}function iv(){var e=[],t=Me;function r(n){if(n!=null&&!isNaN(n=+n))return t((ti(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Et),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>NE(e,a/n))},r.copy=function(){return iv(t).domain(e)},Ot.apply(r,arguments)}function Xa(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=Me,f,l=!1,p;function h(v){return isNaN(v=+v)?p:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:c),s(l?Math.max(0,Math.min(1,v)):v))}h.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h):[e,t,r]},h.clamp=function(v){return arguments.length?(l=!!v,h):l},h.interpolator=function(v){return arguments.length?(s=v,h):s};function y(v){return function(d){var x,w,b;return arguments.length?([x,w,b]=d,s=cj(v,[x,w,b]),h):[s(0),s(.5),s(1)]}}return h.range=y(Vr),h.rangeRound=y(Dc),h.unknown=function(v){return arguments.length?(p=v,h):p},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h}}function av(){var e=It(Xa()(Me));return e.copy=function(){return kt(e,av())},Ot.apply(e,arguments)}function ov(){var e=Lc(Xa()).domain([.1,1,10]);return e.copy=function(){return kt(e,ov()).base(e.base())},Ot.apply(e,arguments)}function uv(){var e=Fc(Xa());return e.copy=function(){return kt(e,uv()).constant(e.constant())},Ot.apply(e,arguments)}function Jc(){var e=zc(Xa());return e.copy=function(){return kt(e,Jc()).exponent(e.exponent())},Ot.apply(e,arguments)}function ZM(){return Jc.apply(null,arguments).exponent(.5)}const Pf=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Sn,scaleDiverging:av,scaleDivergingLog:ov,scaleDivergingPow:Jc,scaleDivergingSqrt:ZM,scaleDivergingSymlog:uv,scaleIdentity:Ld,scaleImplicit:hu,scaleLinear:Hi,scaleLog:zd,scaleOrdinal:Cc,scalePoint:pn,scalePow:Wc,scaleQuantile:qd,scaleQuantize:Hd,scaleRadial:Ud,scaleSequential:tv,scaleSequentialLog:rv,scaleSequentialPow:Zc,scaleSequentialQuantile:iv,scaleSequentialSqrt:YM,scaleSequentialSymlog:nv,scaleSqrt:Mj,scaleSymlog:Wd,scaleThreshold:Gd,scaleTime:XM,scaleUtc:VM,tickFormat:Rd},Symbol.toStringTag,{value:"Module"}));var JM=Lr;function QM(e,t,r){for(var n=-1,i=e.length;++n<i;){var a=e[n],o=t(a);if(o!=null&&(u===void 0?o===o&&!JM(o):r(o,u)))var u=o,c=a}return c}var cv=QM;function eC(e,t){return e>t}var tC=eC,rC=cv,nC=tC,iC=Kr;function aC(e){return e&&e.length?rC(e,iC,nC):void 0}var oC=aC;const $t=ue(oC);function uC(e,t){return e<t}var cC=uC,sC=cv,lC=cC,fC=Kr;function pC(e){return e&&e.length?sC(e,fC,lC):void 0}var hC=pC;const Va=ue(hC);var dC=pc,vC=Ct,yC=yd,mC=De;function gC(e,t){var r=mC(e)?dC:yC;return r(e,vC(t))}var bC=gC,xC=dd,wC=bC;function OC(e,t){return xC(wC(e,t),1)}var SC=OC;const AC=ue(SC);var PC=$c;function _C(e,t){return PC(e,t)}var $C=_C;const Tn=ue($C);var Yr=1e9,TC={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},es,fe=!0,Ke="[DecimalError] ",Vt=Ke+"Invalid argument: ",Qc=Ke+"Exponent out of range: ",Zr=Math.floor,Wt=Math.pow,EC=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Le,Ae=1e7,le=7,sv=9007199254740991,Vi=Zr(sv/le),U={};U.absoluteValue=U.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};U.comparedTo=U.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};U.decimalPlaces=U.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*le;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};U.dividedBy=U.div=function(e){return vt(this,new this.constructor(e))};U.dividedToIntegerBy=U.idiv=function(e){var t=this,r=t.constructor;return ae(vt(t,new r(e),0,1),r.precision)};U.equals=U.eq=function(e){return!this.cmp(e)};U.exponent=function(){return me(this)};U.greaterThan=U.gt=function(e){return this.cmp(e)>0};U.greaterThanOrEqualTo=U.gte=function(e){return this.cmp(e)>=0};U.isInteger=U.isint=function(){return this.e>this.d.length-2};U.isNegative=U.isneg=function(){return this.s<0};U.isPositive=U.ispos=function(){return this.s>0};U.isZero=function(){return this.s===0};U.lessThan=U.lt=function(e){return this.cmp(e)<0};U.lessThanOrEqualTo=U.lte=function(e){return this.cmp(e)<1};U.logarithm=U.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Le))throw Error(Ke+"NaN");if(r.s<1)throw Error(Ke+(r.s?"NaN":"-Infinity"));return r.eq(Le)?new n(0):(fe=!1,t=vt(En(r,a),En(e,a),a),fe=!0,ae(t,i))};U.minus=U.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?pv(t,e):lv(t,(e.s=-e.s,e))};U.modulo=U.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Ke+"NaN");return r.s?(fe=!1,t=vt(r,e,0,1).times(e),fe=!0,r.minus(t)):ae(new n(r),i)};U.naturalExponential=U.exp=function(){return fv(this)};U.naturalLogarithm=U.ln=function(){return En(this)};U.negated=U.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};U.plus=U.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?lv(t,e):pv(t,(e.s=-e.s,e))};U.precision=U.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Vt+e);if(t=me(i)+1,n=i.d.length-1,r=n*le+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};U.squareRoot=U.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Ke+"NaN")}for(e=me(u),fe=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=rt(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Zr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(vt(u,a,o+2)).times(.5),rt(a.d).slice(0,o)===(t=rt(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(ae(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return fe=!0,ae(n,r)};U.times=U.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,p=f.d,h=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=p.length,s=h.length,c<s&&(a=p,p=h,h=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+h[n]*p[i-n-1]+t,a[i--]=u%Ae|0,t=u/Ae|0;a[i]=(a[i]+t)%Ae|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,fe?ae(e,l.precision):e};U.toDecimalPlaces=U.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(ot(e,0,Yr),t===void 0?t=n.rounding:ot(t,0,8),ae(r,e+me(r)+1,t))};U.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Jt(n,!0):(ot(e,0,Yr),t===void 0?t=i.rounding:ot(t,0,8),n=ae(new i(n),e+1,t),r=Jt(n,!0,e+1)),r};U.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Jt(i):(ot(e,0,Yr),t===void 0?t=a.rounding:ot(t,0,8),n=ae(new a(i),e+me(i)+1,t),r=Jt(n.abs(),!1,e+me(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};U.toInteger=U.toint=function(){var e=this,t=e.constructor;return ae(new t(e),me(e)+1,t.rounding)};U.toNumber=function(){return+this};U.toPower=U.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(Le);if(u=new c(u),!u.s){if(e.s<1)throw Error(Ke+"Infinity");return u}if(u.eq(Le))return u;if(n=c.precision,e.eq(Le))return ae(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=sv){for(i=new c(Le),t=Math.ceil(n/le+4),fe=!1;r%2&&(i=i.times(u),$f(i.d,t)),r=Zr(r/2),r!==0;)u=u.times(u),$f(u.d,t);return fe=!0,e.s<0?new c(Le).div(i):ae(i,n)}}else if(a<0)throw Error(Ke+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,fe=!1,i=e.times(En(u,n+s)),fe=!0,i=fv(i),i.s=a,i};U.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=me(i),n=Jt(i,r<=a.toExpNeg||r>=a.toExpPos)):(ot(e,1,Yr),t===void 0?t=a.rounding:ot(t,0,8),i=ae(new a(i),e,t),r=me(i),n=Jt(i,e<=r||r<=a.toExpNeg,e)),n};U.toSignificantDigits=U.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(ot(e,1,Yr),t===void 0?t=n.rounding:ot(t,0,8)),ae(new n(r),e,t)};U.toString=U.valueOf=U.val=U.toJSON=U[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=me(e),r=e.constructor;return Jt(e,t<=r.toExpNeg||t>=r.toExpPos)};function lv(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),fe?ae(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/le),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/Ae|0,c[a]%=Ae;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,fe?ae(t,l):t}function ot(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Vt+e)}function rt(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=le-n.length,r&&(a+=Pt(r)),a+=n;o=e[t],n=o+"",r=le-n.length,r&&(a+=Pt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var vt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Ae|0,o=a/Ae|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Ae+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,p,h,y,v,d,x,w,b,O,m,g,S,A,_=n.constructor,j=n.s==i.s?1:-1,$=n.d,T=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(Ke+"Division by zero");for(c=n.e-i.e,S=T.length,m=$.length,h=new _(j),y=h.d=[],s=0;T[s]==($[s]||0);)++s;if(T[s]>($[s]||0)&&--c,a==null?w=a=_.precision:o?w=a+(me(n)-me(i))+1:w=a,w<0)return new _(0);if(w=w/le+2|0,s=0,S==1)for(f=0,T=T[0],w++;(s<m||f)&&w--;s++)b=f*Ae+($[s]||0),y[s]=b/T|0,f=b%T|0;else{for(f=Ae/(T[0]+1)|0,f>1&&(T=e(T,f),$=e($,f),S=T.length,m=$.length),O=S,v=$.slice(0,S),d=v.length;d<S;)v[d++]=0;A=T.slice(),A.unshift(0),g=T[0],T[1]>=Ae/2&&++g;do f=0,u=t(T,v,S,d),u<0?(x=v[0],S!=d&&(x=x*Ae+(v[1]||0)),f=x/g|0,f>1?(f>=Ae&&(f=Ae-1),l=e(T,f),p=l.length,d=v.length,u=t(l,v,p,d),u==1&&(f--,r(l,S<p?A:T,p))):(f==0&&(u=f=1),l=T.slice()),p=l.length,p<d&&l.unshift(0),r(v,l,d),u==-1&&(d=v.length,u=t(T,v,S,d),u<1&&(f++,r(v,S<d?A:T,d))),d=v.length):u===0&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[d++]=$[O]||0:(v=[$[O]],d=1);while((O++<m||v[0]!==void 0)&&w--)}return y[0]||y.shift(),h.e=c,ae(h,o?a+me(h)+1:a)}}();function fv(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(me(e)>16)throw Error(Qc+me(e));if(!e.s)return new f(Le);for(fe=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Wt(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Le),f.precision=u;;){if(i=ae(i.times(e),u),r=r.times(++c),o=a.plus(vt(i,r,u)),rt(o.d).slice(0,u)===rt(a.d).slice(0,u)){for(;s--;)a=ae(a.times(a),u);return f.precision=l,t==null?(fe=!0,ae(a,l)):a}a=o}}function me(e){for(var t=e.e*le,r=e.d[0];r>=10;r/=10)t++;return t}function To(e,t,r){if(t>e.LN10.sd())throw fe=!0,r&&(e.precision=r),Error(Ke+"LN10 precision limit exceeded");return ae(new e(e.LN10),t)}function Pt(e){for(var t="";e--;)t+="0";return t}function En(e,t){var r,n,i,a,o,u,c,s,f,l=1,p=10,h=e,y=h.d,v=h.constructor,d=v.precision;if(h.s<1)throw Error(Ke+(h.s?"NaN":"-Infinity"));if(h.eq(Le))return new v(0);if(t==null?(fe=!1,s=d):s=t,h.eq(10))return t==null&&(fe=!0),To(v,s);if(s+=p,v.precision=s,r=rt(y),n=r.charAt(0),a=me(h),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=rt(h.d),n=r.charAt(0),l++;a=me(h),n>1?(h=new v("0."+r),a++):h=new v(n+"."+r.slice(1))}else return c=To(v,s+2,d).times(a+""),h=En(new v(n+"."+r.slice(1)),s-p).plus(c),v.precision=d,t==null?(fe=!0,ae(h,d)):h;for(u=o=h=vt(h.minus(Le),h.plus(Le),s),f=ae(h.times(h),s),i=3;;){if(o=ae(o.times(f),s),c=u.plus(vt(o,new v(i),s)),rt(c.d).slice(0,s)===rt(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(To(v,s+2,d).times(a+""))),u=vt(u,new v(l),s),v.precision=d,t==null?(fe=!0,ae(u,d)):u;u=c,i+=2}}function _f(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=Zr(r/le),e.d=[],n=(r+1)%le,r<0&&(n+=le),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=le;n<i;)e.d.push(+t.slice(n,n+=le));t=t.slice(n),n=le-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),fe&&(e.e>Vi||e.e<-Vi))throw Error(Qc+r)}else e.s=0,e.e=0,e.d=[0];return e}function ae(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=le,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/le),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=le,i=n-le+o}if(r!==void 0&&(a=Wt(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/Wt(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=me(e),l.length=1,t=t-a-1,l[0]=Wt(10,(le-t%le)%le),e.e=Zr(-t/le)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Wt(10,le-n),l[f]=i>0?(s/Wt(10,o-i)%Wt(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==Ae&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Ae)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(fe&&(e.e>Vi||e.e<-Vi))throw Error(Qc+me(e));return e}function pv(e,t){var r,n,i,a,o,u,c,s,f,l,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),fe?ae(t,h):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(h/le),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=Ae-1;--c[a],c[i]+=Ae}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,fe?ae(t,h):t):new p(0)}function Jt(e,t,r){var n,i=me(e),a=rt(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+Pt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+Pt(-i-1)+a,r&&(n=r-o)>0&&(a+=Pt(n))):i>=o?(a+=Pt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+Pt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=Pt(n))),e.s<0?"-"+a:a}function $f(e,t){if(e.length>t)return e.length=t,!0}function hv(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Vt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return _f(o,a.toString())}else if(typeof a!="string")throw Error(Vt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,EC.test(a))_f(o,a);else throw Error(Vt+a)}if(i.prototype=U,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=hv,i.config=i.set=jC,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function jC(e){if(!e||typeof e!="object")throw Error(Ke+"Object expected");var t,r,n,i=["precision",1,Yr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(Zr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Vt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Vt+r+": "+n);return this}var es=hv(TC);Le=new es(1);const ie=es;function MC(e){return DC(e)||kC(e)||IC(e)||CC()}function CC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function IC(e,t){if(e){if(typeof e=="string")return mu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mu(e,t)}}function kC(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function DC(e){if(Array.isArray(e))return mu(e)}function mu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var NC=function(t){return t},dv={},vv=function(t){return t===dv},Tf=function(t){return function r(){return arguments.length===0||arguments.length===1&&vv(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},BC=function e(t,r){return t===1?r:Tf(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==dv}).length;return o>=t?r.apply(void 0,i):e(t-o,Tf(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return vv(l)?c.shift():l});return r.apply(void 0,MC(f).concat(c))}))})},Ya=function(t){return BC(t.length,t)},gu=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},RC=Ya(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),LC=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return NC;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},bu=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},yv=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function FC(e){var t;return e===0?t=1:t=Math.floor(new ie(e).abs().log(10).toNumber())+1,t}function zC(e,t,r){for(var n=new ie(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var WC=Ya(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),UC=Ya(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),qC=Ya(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const Za={rangeStep:zC,getDigitCount:FC,interpolateNumber:WC,uninterpolateNumber:UC,uninterpolateTruncation:qC};function xu(e){return KC(e)||GC(e)||mv(e)||HC()}function HC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GC(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function KC(e){if(Array.isArray(e))return wu(e)}function jn(e,t){return YC(e)||VC(e,t)||mv(e,t)||XC()}function XC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mv(e,t){if(e){if(typeof e=="string")return wu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wu(e,t)}}function wu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function VC(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function YC(e){if(Array.isArray(e))return e}function gv(e){var t=jn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function bv(e,t,r){if(e.lte(0))return new ie(0);var n=Za.getDigitCount(e.toNumber()),i=new ie(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ie(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ie(Math.ceil(c))}function ZC(e,t,r){var n=1,i=new ie(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ie(10).pow(Za.getDigitCount(e)-1),i=new ie(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ie(Math.floor(e)))}else e===0?i=new ie(Math.floor((t-1)/2)):r||(i=new ie(Math.floor(e)));var o=Math.floor((t-1)/2),u=LC(RC(function(c){return i.add(new ie(c-o).mul(n)).toNumber()}),gu);return u(0,t)}function xv(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ie(0),tickMin:new ie(0),tickMax:new ie(0)};var a=bv(new ie(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ie(0):(o=new ie(e).add(t).div(2),o=o.sub(new ie(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ie(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?xv(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ie(u).mul(a)),tickMax:o.add(new ie(c).mul(a))})}function JC(e){var t=jn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=gv([r,n]),c=jn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(xu(gu(0,i-1).map(function(){return 1/0}))):[].concat(xu(gu(0,i-1).map(function(){return-1/0})),[f]);return r>n?bu(l):l}if(s===f)return ZC(s,i,a);var p=xv(s,f,o,a),h=p.step,y=p.tickMin,v=p.tickMax,d=Za.rangeStep(y,v.add(new ie(.1).mul(h)),h);return r>n?bu(d):d}function QC(e,t){var r=jn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=gv([n,i]),u=jn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=bv(new ie(s).sub(c).div(f-1),a,0),p=[].concat(xu(Za.rangeStep(new ie(c),new ie(s).sub(new ie(.99).mul(l)),l)),[s]);return n>i?bu(p):p}var eI=yv(JC),tI=yv(QC),rI="Invariant failed";function Qt(e,t){throw new Error(rI)}var nI=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Pr(e){"@babel/helpers - typeof";return Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pr(e)}function Yi(){return Yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yi.apply(this,arguments)}function iI(e,t){return cI(e)||uI(e,t)||oI(e,t)||aI()}function aI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function oI(e,t){if(e){if(typeof e=="string")return Ef(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ef(e,t)}}function Ef(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function uI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function cI(e){if(Array.isArray(e))return e}function sI(e,t){if(e==null)return{};var r=lI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function lI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function fI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function pI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Sv(n.key),n)}}function hI(e,t,r){return t&&pI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function dI(e,t,r){return t=Zi(t),vI(e,wv()?Reflect.construct(t,r||[],Zi(e).constructor):t.apply(e,r))}function vI(e,t){if(t&&(Pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return yI(e)}function yI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function wv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(wv=function(){return!!e})()}function Zi(e){return Zi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Zi(e)}function mI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ou(e,t)}function Ou(e,t){return Ou=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ou(e,t)}function Ov(e,t,r){return t=Sv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Sv(e){var t=gI(e,"string");return Pr(t)=="symbol"?t:t+""}function gI(e,t){if(Pr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ja=function(e){function t(){return fI(this,t),dI(this,t,arguments)}return mI(t,e),hI(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,p=sI(n,nI),h=Z(p,!1);this.props.direction==="x"&&f.type!=="number"&&Qt();var y=c.map(function(v){var d=s(v,u),x=d.x,w=d.y,b=d.value,O=d.errorVal;if(!O)return null;var m=[],g,S;if(Array.isArray(O)){var A=iI(O,2);g=A[0],S=A[1]}else g=S=O;if(a==="vertical"){var _=f.scale,j=w+i,$=j+o,T=j-o,C=_(b-g),I=_(b+S);m.push({x1:I,y1:$,x2:I,y2:T}),m.push({x1:C,y1:j,x2:I,y2:j}),m.push({x1:C,y1:$,x2:C,y2:T})}else if(a==="horizontal"){var M=l.scale,k=x+i,N=k-o,L=k+o,F=M(b-g),q=M(b+S);m.push({x1:N,y1:q,x2:L,y2:q}),m.push({x1:k,y1:F,x2:k,y2:q}),m.push({x1:N,y1:F,x2:L,y2:F})}return P.createElement(pe,Yi({className:"recharts-errorBar",key:"bar-".concat(m.map(function(G){return"".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))},h),m.map(function(G){return P.createElement("line",Yi({},G,{key:"line-".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))}))});return P.createElement(pe,{className:"recharts-errorBars"},y)}}])}(P.Component);Ov(Ja,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});Ov(Ja,"displayName","ErrorBar");function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}function jf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Lt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jf(Object(r),!0).forEach(function(n){bI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function bI(e,t,r){return t=xI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xI(e){var t=wI(e,"string");return Mn(t)=="symbol"?t:t+""}function wI(e,t){if(Mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Av=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=Re(r,dr);if(!o)return null;var u=dr.defaultProps,c=u!==void 0?Lt(Lt({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var p=l.item,h=l.props,y=h.sectors||h.data||[];return f.concat(y.map(function(v){return{type:o.props.iconType||p.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):s=(n||[]).map(function(f){var l=f.item,p=l.type.defaultProps,h=p!==void 0?Lt(Lt({},p),l.props):{},y=h.dataKey,v=h.name,d=h.legendType,x=h.hide;return{inactive:x,dataKey:y,type:c.iconType||d||"square",color:ts(l),value:v||y,payload:h}}),Lt(Lt(Lt({},c),dr.getWithHeight(o,i)),{},{payload:s,item:o})};function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function Mf(e){return PI(e)||AI(e)||SI(e)||OI()}function OI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function SI(e,t){if(e){if(typeof e=="string")return Su(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Su(e,t)}}function AI(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function PI(e){if(Array.isArray(e))return Su(e)}function Su(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Cf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function de(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cf(Object(r),!0).forEach(function(n){yr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yr(e,t,r){return t=_I(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _I(e){var t=$I(e,"string");return Cn(t)=="symbol"?t:t+""}function $I(e,t){if(Cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Fe(e,t,r){return J(e)||J(t)?r:Oe(t)?Ge(e,t,r):V(t)?t(e):r}function hn(e,t,r,n){var i=AC(e,function(u){return Fe(u,t)});if(r==="number"){var a=i.filter(function(u){return R(u)||parseFloat(u)});return a.length?[Va(a),$t(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!J(u)}):i;return o.map(function(u){return Oe(u)||u instanceof Date?u:""})}var TI=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,p=s>=u-1?i[0].coordinate:i[s+1].coordinate,h=void 0;if(Qe(l-f)!==Qe(p-l)){var y=[];if(Qe(p-l)===Qe(c[1]-c[0])){h=p;var v=l+c[1]-c[0];y[0]=Math.min(v,(v+f)/2),y[1]=Math.max(v,(v+f)/2)}else{h=f;var d=p+c[1]-c[0];y[0]=Math.min(l,(d+l)/2),y[1]=Math.max(l,(d+l)/2)}var x=[Math.min(l,(h+l)/2),Math.max(l,(h+l)/2)];if(t>x[0]&&t<=x[1]||t>=y[0]&&t<=y[1]){o=i[s].index;break}}else{var w=Math.min(f,p),b=Math.max(f,p);if(t>(w+l)/2&&t<=(b+l)/2){o=i[s].index;break}}}else for(var O=0;O<u;O++)if(O===0&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O>0&&O<u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O===u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2){o=n[O].index;break}return o},ts=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},EI=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),p=0,h=l.length;p<h;p++){var y=f[l[p]],v=y.items,d=y.cateAxisId,x=v.filter(function(S){return ht(S.type).indexOf("Bar")>=0});if(x&&x.length){var w=x[0].type.defaultProps,b=w!==void 0?de(de({},w),x[0].props):x[0].props,O=b.barSize,m=b[d];o[m]||(o[m]=[]);var g=J(O)?r:O;o[m].push({item:x[0],stackList:x.slice(1),barSize:J(g)?void 0:Zt(g,n,0)})}}return o},jI=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Zt(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var p=!1,h=i/c,y=o.reduce(function(O,m){return O+m.barSize||0},0);y+=(c-1)*s,y>=i&&(y-=(c-1)*s,s=0),y>=i&&h>0&&(p=!0,h*=.9,y=c*h);var v=(i-y)/2>>0,d={offset:v-s,size:0};f=o.reduce(function(O,m){var g={item:m.item,position:{offset:d.offset+d.size+s,size:p?h:m.barSize}},S=[].concat(Mf(O),[g]);return d=S[S.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){S.push({item:A,position:d})}),S},l)}else{var x=Zt(n,i,0,!0);i-2*x-(c-1)*s<=0&&(s=0);var w=(i-2*x-(c-1)*s)/c;w>1&&(w>>=0);var b=u===+u?Math.min(w,u):w;f=o.reduce(function(O,m,g){var S=[].concat(Mf(O),[{item:m.item,position:{offset:x+(w+s)*g+(w-b)/2,size:b}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){S.push({item:A,position:S[S.length-1].position})}),S},l)}return f},MI=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=Av({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,p=f.height,h=s.align,y=s.verticalAlign,v=s.layout;if((v==="vertical"||v==="horizontal"&&y==="middle")&&h!=="center"&&R(t[h]))return de(de({},t),{},yr({},h,t[h]+(l||0)));if((v==="horizontal"||v==="vertical"&&h==="center")&&y!=="middle"&&R(t[y]))return de(de({},t),{},yr({},y,t[y]+(p||0)))}return t},CI=function(t,r,n){return J(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},Pv=function(t,r,n,i,a){var o=r.props.children,u=et(o,Ja).filter(function(s){return CI(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=Fe(f,n);if(J(l))return s;var p=Array.isArray(l)?[Va(l),$t(l)]:[l,l],h=c.reduce(function(y,v){var d=Fe(f,v,0),x=p[0]-Math.abs(Array.isArray(d)?d[0]:d),w=p[1]+Math.abs(Array.isArray(d)?d[1]:d);return[Math.min(x,y[0]),Math.max(w,y[1])]},[1/0,-1/0]);return[Math.min(h[0],s[0]),Math.max(h[1],s[1])]},[1/0,-1/0])}return null},II=function(t,r,n,i,a){var o=r.map(function(u){return Pv(t,u,n,a,i)}).filter(function(u){return!J(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},_v=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&Pv(t,c,s,i)||hn(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},$v=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},Tv=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},pt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Qe(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var p=a?a.indexOf(l):l;return{coordinate:i(p)+s,value:l,offset:s}});return f.filter(function(l){return!qr(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,p){return{coordinate:i(l)+s,value:l,index:p,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,p){return{coordinate:i(l)+s,value:a?a[l]:l,index:p,offset:s}})},Eo=new WeakMap,yi=function(t,r){if(typeof r!="function")return t;Eo.has(t)||Eo.set(t,new WeakMap);var n=Eo.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},kI=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Sn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:Hi(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:pn(),realScaleType:"point"}:a==="category"?{scale:Sn(),realScaleType:"band"}:{scale:Hi(),realScaleType:"linear"};if(Yt(i)){var c="scale".concat(Na(i));return{scale:(Pf[c]||pn)(),realScaleType:Pf[c]?c:"point"}}return V(i)?{scale:i}:{scale:pn(),realScaleType:"point"}},If=1e-4,DI=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-If,o=Math.max(i[0],i[1])+If,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},NI=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},BI=function(t,r){if(!r||r.length!==2||!R(r[0])||!R(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!R(t[0])||t[0]<n)&&(a[0]=n),(!R(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},RI=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=qr(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},LI=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=qr(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},FI={sign:RI,expand:tw,none:mr,silhouette:rw,wiggle:nw,positive:LI},zI=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=FI[n],o=ew().keys(i).value(function(u,c){return+Fe(u,c,0)}).order(Xo).offset(a);return o(t)},WI=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,p){var h,y=(h=p.type)!==null&&h!==void 0&&h.defaultProps?de(de({},p.type.defaultProps),p.props):p.props,v=y.stackId,d=y.hide;if(d)return l;var x=y[n],w=l[x]||{hasStack:!1,stackGroups:{}};if(Oe(v)){var b=w.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};b.items.push(p),w.hasStack=!0,w.stackGroups[v]=b}else w.stackGroups[Qn("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[p]};return de(de({},l),{},yr({},x,w))},c),f={};return Object.keys(s).reduce(function(l,p){var h=s[p];if(h.hasStack){var y={};h.stackGroups=Object.keys(h.stackGroups).reduce(function(v,d){var x=h.stackGroups[d];return de(de({},v),{},yr({},d,{numericAxisId:n,cateAxisId:i,items:x.items,stackedData:zI(t,x.items,a)}))},y)}return de(de({},l),{},yr({},p,h))},f)},UI=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=eI(s,a,u);return t.domain([Va(f),$t(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),p=tI(l,a,u);return{niceTicks:p}}return null};function kf(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!J(i[t.dataKey])){var u=Oi(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=Fe(i,J(o)?t.dataKey:o);return J(c)?null:t.scale(c)}var Df=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=Fe(o,r.dataKey,r.domain[u]);return J(c)?null:r.scale(c)-a/2+i},qI=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},HI=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(Oe(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},GI=function(t){return t.reduce(function(r,n){return[Va(n.concat([r[0]]).filter(R)),$t(n.concat([r[1]]).filter(R))]},[1/0,-1/0])},Ev=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=GI(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},Nf=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Bf=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Au=function(t,r,n){if(V(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(R(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(Nf.test(t[0])){var a=+Nf.exec(t[0])[1];i[0]=r[0]-a}else V(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(R(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(Bf.test(t[1])){var o=+Bf.exec(t[1])[1];i[1]=r[1]+o}else V(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},Ji=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=Ec(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},Rf=function(t,r,n){return!t||!t.length||Tn(t,Ge(n,"type.defaultProps.domain"))?r:t},jv=function(t,r){var n=t.type.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return de(de({},Z(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:ts(t),value:Fe(r,i),type:c,payload:r,chartType:s,hide:f})};function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}function Lf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ff(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lf(Object(r),!0).forEach(function(n){KI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function KI(e,t,r){return t=XI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XI(e){var t=VI(e,"string");return In(t)=="symbol"?t:t+""}function VI(e,t){if(In(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(In(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Qi=Math.PI/180,YI=function(t){return t*180/Math.PI},$e=function(t,r,n,i){return{x:t+Math.cos(-Qi*i)*n,y:r+Math.sin(-Qi*i)*n}},ZI=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},JI=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=ZI({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:YI(s),angleInRadian:s}},QI=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},e2=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},zf=function(t,r){var n=t.x,i=t.y,a=JI({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=QI(r),l=f.startAngle,p=f.endAngle,h=u,y;if(l<=p){for(;h>p;)h-=360;for(;h<l;)h+=360;y=h>=l&&h<=p}else{for(;h>l;)h-=360;for(;h<p;)h+=360;y=h>=p&&h<=l}return y?Ff(Ff({},r),{},{radius:o,angle:e2(h,r)}):null};function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}var t2=["offset"];function r2(e){return o2(e)||a2(e)||i2(e)||n2()}function n2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function i2(e,t){if(e){if(typeof e=="string")return Pu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pu(e,t)}}function a2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function o2(e){if(Array.isArray(e))return Pu(e)}function Pu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function u2(e,t){if(e==null)return{};var r=c2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function c2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Wf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function we(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wf(Object(r),!0).forEach(function(n){s2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function s2(e,t,r){return t=l2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l2(e){var t=f2(e,"string");return kn(t)=="symbol"?t:t+""}function f2(e,t){if(kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Dn(){return Dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Dn.apply(this,arguments)}var p2=function(t){var r=t.value,n=t.formatter,i=J(t.children)?r:t.children;return V(n)?n(i):i},h2=function(t,r){var n=Qe(r-t),i=Math.min(Math.abs(r-t),360);return n*i},d2=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,p=c.outerRadius,h=c.startAngle,y=c.endAngle,v=c.clockWise,d=(l+p)/2,x=h2(h,y),w=x>=0?1:-1,b,O;i==="insideStart"?(b=h+w*o,O=v):i==="insideEnd"?(b=y-w*o,O=!v):i==="end"&&(b=y+w*o,O=v),O=x<=0?O:!O;var m=$e(s,f,d,b),g=$e(s,f,d,b+(O?1:-1)*359),S="M".concat(m.x,",").concat(m.y,`
    A`).concat(d,",").concat(d,",0,1,").concat(O?0:1,`,
    `).concat(g.x,",").concat(g.y),A=J(t.id)?Qn("recharts-radial-line-"):t.id;return P.createElement("text",Dn({},n,{dominantBaseline:"central",className:ee("recharts-radial-bar-label",u)}),P.createElement("defs",null,P.createElement("path",{id:A,d:S})),P.createElement("textPath",{xlinkHref:"#".concat(A)},r))},v2=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,p=(f+l)/2;if(i==="outside"){var h=$e(o,u,s+n,p),y=h.x,v=h.y;return{x:y,y:v,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var d=(c+s)/2,x=$e(o,u,d,p),w=x.x,b=x.y;return{x:w,y:b,textAnchor:"middle",verticalAnchor:"middle"}},y2=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,p=l*i,h=l>0?"end":"start",y=l>0?"start":"end",v=s>=0?1:-1,d=v*i,x=v>0?"end":"start",w=v>0?"start":"end";if(a==="top"){var b={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:h};return we(we({},b),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var O={x:u+s/2,y:c+f+p,textAnchor:"middle",verticalAnchor:y};return we(we({},O),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var m={x:u-d,y:c+f/2,textAnchor:x,verticalAnchor:"middle"};return we(we({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var g={x:u+s+d,y:c+f/2,textAnchor:w,verticalAnchor:"middle"};return we(we({},g),n?{width:Math.max(n.x+n.width-g.x,0),height:f}:{})}var S=n?{width:s,height:f}:{};return a==="insideLeft"?we({x:u+d,y:c+f/2,textAnchor:w,verticalAnchor:"middle"},S):a==="insideRight"?we({x:u+s-d,y:c+f/2,textAnchor:x,verticalAnchor:"middle"},S):a==="insideTop"?we({x:u+s/2,y:c+p,textAnchor:"middle",verticalAnchor:y},S):a==="insideBottom"?we({x:u+s/2,y:c+f-p,textAnchor:"middle",verticalAnchor:h},S):a==="insideTopLeft"?we({x:u+d,y:c+p,textAnchor:w,verticalAnchor:y},S):a==="insideTopRight"?we({x:u+s-d,y:c+p,textAnchor:x,verticalAnchor:y},S):a==="insideBottomLeft"?we({x:u+d,y:c+f-p,textAnchor:w,verticalAnchor:h},S):a==="insideBottomRight"?we({x:u+s-d,y:c+f-p,textAnchor:x,verticalAnchor:h},S):Fr(a)&&(R(a.x)||Ut(a.x))&&(R(a.y)||Ut(a.y))?we({x:u+Zt(a.x,s),y:c+Zt(a.y,f),textAnchor:"end",verticalAnchor:"end"},S):we({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},S)},m2=function(t){return"cx"in t&&R(t.cx)};function Ee(e){var t=e.offset,r=t===void 0?5:t,n=u2(e,t2),i=we({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,p=i.textBreakAll;if(!a||J(u)&&J(c)&&!B.isValidElement(s)&&!V(s))return null;if(B.isValidElement(s))return B.cloneElement(s,i);var h;if(V(s)){if(h=B.createElement(s,i),B.isValidElement(h))return h}else h=p2(i);var y=m2(a),v=Z(i,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return d2(i,h,v);var d=y?v2(i):y2(i);return P.createElement(Ri,Dn({className:ee("recharts-label",l)},v,d,{breakAll:p}),h)}Ee.displayName="Label";var Mv=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,p=t.y,h=t.top,y=t.left,v=t.width,d=t.height,x=t.clockWise,w=t.labelViewBox;if(w)return w;if(R(v)&&R(d)){if(R(l)&&R(p))return{x:l,y:p,width:v,height:d};if(R(h)&&R(y))return{x:h,y,width:v,height:d}}return R(l)&&R(p)?{x:l,y:p,width:0,height:0}:R(r)&&R(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:x}:t.viewBox?t.viewBox:{}},g2=function(t,r){return t?t===!0?P.createElement(Ee,{key:"label-implicit",viewBox:r}):Oe(t)?P.createElement(Ee,{key:"label-implicit",viewBox:r,value:t}):B.isValidElement(t)?t.type===Ee?B.cloneElement(t,{key:"label-implicit",viewBox:r}):P.createElement(Ee,{key:"label-implicit",content:t,viewBox:r}):V(t)?P.createElement(Ee,{key:"label-implicit",content:t,viewBox:r}):Fr(t)?P.createElement(Ee,Dn({viewBox:r},t,{key:"label-implicit"})):null:null},b2=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=Mv(t),o=et(i,Ee).map(function(c,s){return B.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=g2(t.label,r||a);return[u].concat(r2(o))};Ee.parseViewBox=Mv;Ee.renderCallByParent=b2;function x2(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var w2=x2;const O2=ue(w2);function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}var S2=["valueAccessor"],A2=["data","dataKey","clockWise","id","textBreakAll"];function P2(e){return E2(e)||T2(e)||$2(e)||_2()}function _2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $2(e,t){if(e){if(typeof e=="string")return _u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _u(e,t)}}function T2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function E2(e){if(Array.isArray(e))return _u(e)}function _u(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ea(){return ea=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ea.apply(this,arguments)}function Uf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Uf(Object(r),!0).forEach(function(n){j2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function j2(e,t,r){return t=M2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function M2(e){var t=C2(e,"string");return Nn(t)=="symbol"?t:t+""}function C2(e,t){if(Nn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Hf(e,t){if(e==null)return{};var r=I2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function I2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var k2=function(t){return Array.isArray(t.value)?O2(t.value):t.value};function jt(e){var t=e.valueAccessor,r=t===void 0?k2:t,n=Hf(e,S2),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=Hf(n,A2);return!i||!i.length?null:P.createElement(pe,{className:"recharts-label-list"},i.map(function(f,l){var p=J(a)?r(f,l):Fe(f&&f.payload,a),h=J(u)?{}:{id:"".concat(u,"-").concat(l)};return P.createElement(Ee,ea({},Z(f,!0),s,h,{parentViewBox:f.parentViewBox,value:p,textBreakAll:c,viewBox:Ee.parseViewBox(J(o)?f:qf(qf({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}jt.displayName="LabelList";function D2(e,t){return e?e===!0?P.createElement(jt,{key:"labelList-implicit",data:t}):P.isValidElement(e)||V(e)?P.createElement(jt,{key:"labelList-implicit",data:t,content:e}):Fr(e)?P.createElement(jt,ea({data:t},e,{key:"labelList-implicit"})):null:null}function N2(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=et(n,jt).map(function(o,u){return B.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=D2(e.label,t);return[a].concat(P2(i))}jt.renderCallByParent=N2;function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}function $u(){return $u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$u.apply(this,arguments)}function Gf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Kf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gf(Object(r),!0).forEach(function(n){B2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function B2(e,t,r){return t=R2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function R2(e){var t=L2(e,"string");return Bn(t)=="symbol"?t:t+""}function L2(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var F2=function(t,r){var n=Qe(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},mi=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/Qi,p=s?a:a+o*l,h=$e(r,n,f,p),y=$e(r,n,i,p),v=s?a-o*l:a,d=$e(r,n,f*Math.cos(l*Qi),v);return{center:h,circleTangency:y,lineTangency:d,theta:l}},Cv=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=F2(o,u),s=o+c,f=$e(r,n,a,o),l=$e(r,n,a,s),p="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var h=$e(r,n,i,o),y=$e(r,n,i,s);p+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(r,",").concat(n," Z");return p},z2=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=Qe(f-s),p=mi({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),h=p.circleTangency,y=p.lineTangency,v=p.theta,d=mi({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),x=d.circleTangency,w=d.lineTangency,b=d.theta,O=c?Math.abs(s-f):Math.abs(s-f)-v-b;if(O<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):Cv({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var m="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(h.x,",").concat(h.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(O>180),",").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(w.x,",").concat(w.y,`
  `);if(i>0){var g=mi({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),S=g.circleTangency,A=g.lineTangency,_=g.theta,j=mi({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),$=j.circleTangency,T=j.lineTangency,C=j.theta,I=c?Math.abs(s-f):Math.abs(s-f)-_-C;if(I<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(T.x,",").concat(T.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat($.x,",").concat($.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(I>180),",").concat(+(l>0),",").concat(S.x,",").concat(S.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},W2={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Iv=function(t){var r=Kf(Kf({},W2),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,p=r.className;if(o<a||f===l)return null;var h=ee("recharts-sector",p),y=o-a,v=Zt(u,y,0,!0),d;return v>0&&Math.abs(f-l)<360?d=z2({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):d=Cv({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),P.createElement("path",$u({},Z(r,!0),{className:h,d,role:"img"}))};function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function Tu(){return Tu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Tu.apply(this,arguments)}function Xf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xf(Object(r),!0).forEach(function(n){U2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function U2(e,t,r){return t=q2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function q2(e){var t=H2(e,"string");return Rn(t)=="symbol"?t:t+""}function H2(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Yf={curveBasisClosed:Ux,curveBasisOpen:qx,curveBasis:Wx,curveBumpX:Tx,curveBumpY:Ex,curveLinearClosed:Hx,curveLinear:Ra,curveMonotoneX:Gx,curveMonotoneY:Kx,curveNatural:Xx,curveStep:Vx,curveStepAfter:Zx,curveStepBefore:Yx},gi=function(t){return t.x===+t.x&&t.y===+t.y},on=function(t){return t.x},un=function(t){return t.y},G2=function(t,r){if(V(t))return t;var n="curve".concat(Na(t));return(n==="curveMonotone"||n==="curveBump")&&r?Yf["".concat(n).concat(r==="vertical"?"Y":"X")]:Yf[n]||Ra},K2=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=G2(n,u),l=s?a.filter(function(v){return gi(v)}):a,p;if(Array.isArray(o)){var h=s?o.filter(function(v){return gi(v)}):o,y=l.map(function(v,d){return Vf(Vf({},v),{},{base:h[d]})});return u==="vertical"?p=ci().y(un).x1(on).x0(function(v){return v.base.x}):p=ci().x(on).y1(un).y0(function(v){return v.base.y}),p.defined(gi).curve(f),p(y)}return u==="vertical"&&R(o)?p=ci().y(un).x1(on).x0(o):R(o)?p=ci().x(on).y1(un).y0(o):p=Ih().x(on).y(un),p.defined(gi).curve(f),p(l)},dn=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?K2(t):i;return B.createElement("path",Tu({},Z(t,!1),Si(t),{className:ee("recharts-curve",r),d:o,ref:a}))},kv={exports:{}},X2="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",V2=X2,Y2=V2;function Dv(){}function Nv(){}Nv.resetWarningCache=Dv;var Z2=function(){function e(n,i,a,o,u,c){if(c!==Y2){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Nv,resetWarningCache:Dv};return r.PropTypes=r,r};kv.exports=Z2();var J2=kv.exports;const re=ue(J2);var Q2=Object.getOwnPropertyNames,ek=Object.getOwnPropertySymbols,tk=Object.prototype.hasOwnProperty;function Zf(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function bi(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function Jf(e){return Q2(e).concat(ek(e))}var rk=Object.hasOwn||function(e,t){return tk.call(e,t)};function ir(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var nk="__v",ik="__o",ak="_owner",Qf=Object.getOwnPropertyDescriptor,ep=Object.keys;function ok(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function uk(e,t){return ir(e.getTime(),t.getTime())}function ck(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function sk(e,t){return e===t}function tp(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var p=o.value,h=u.value;if(r.equals(p[0],h[0],c,l,e,t,r)&&r.equals(p[1],h[1],p[0],h[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var lk=ir;function fk(e,t,r){var n=ep(e),i=n.length;if(ep(t).length!==i)return!1;for(;i-- >0;)if(!Bv(e,t,r,n[i]))return!1;return!0}function cn(e,t,r){var n=Jf(e),i=n.length;if(Jf(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!Bv(e,t,r,a)||(o=Qf(e,a),u=Qf(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function pk(e,t){return ir(e.valueOf(),t.valueOf())}function hk(e,t){return e.source===t.source&&e.flags===t.flags}function rp(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function dk(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function vk(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function Bv(e,t,r,n){return(n===ak||n===ik||n===nk)&&(e.$$typeof||t.$$typeof)?!0:rk(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var yk="[object Arguments]",mk="[object Boolean]",gk="[object Date]",bk="[object Error]",xk="[object Map]",wk="[object Number]",Ok="[object Object]",Sk="[object RegExp]",Ak="[object Set]",Pk="[object String]",_k="[object URL]",$k=Array.isArray,np=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,ip=Object.assign,Tk=Object.prototype.toString.call.bind(Object.prototype.toString);function Ek(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,p=e.areUrlsEqual;return function(y,v,d){if(y===v)return!0;if(y==null||v==null)return!1;var x=typeof y;if(x!==typeof v)return!1;if(x!=="object")return x==="number"?o(y,v,d):x==="function"?i(y,v,d):!1;var w=y.constructor;if(w!==v.constructor)return!1;if(w===Object)return u(y,v,d);if($k(y))return t(y,v,d);if(np!=null&&np(y))return l(y,v,d);if(w===Date)return r(y,v,d);if(w===RegExp)return s(y,v,d);if(w===Map)return a(y,v,d);if(w===Set)return f(y,v,d);var b=Tk(y);return b===gk?r(y,v,d):b===Sk?s(y,v,d):b===xk?a(y,v,d):b===Ak?f(y,v,d):b===Ok?typeof y.then!="function"&&typeof v.then!="function"&&u(y,v,d):b===_k?p(y,v,d):b===bk?n(y,v,d):b===yk?u(y,v,d):b===mk||b===wk||b===Pk?c(y,v,d):!1}}function jk(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?cn:ok,areDatesEqual:uk,areErrorsEqual:ck,areFunctionsEqual:sk,areMapsEqual:n?Zf(tp,cn):tp,areNumbersEqual:lk,areObjectsEqual:n?cn:fk,arePrimitiveWrappersEqual:pk,areRegExpsEqual:hk,areSetsEqual:n?Zf(rp,cn):rp,areTypedArraysEqual:n?cn:dk,areUrlsEqual:vk};if(r&&(i=ip({},i,r(i))),t){var a=bi(i.areArraysEqual),o=bi(i.areMapsEqual),u=bi(i.areObjectsEqual),c=bi(i.areSetsEqual);i=ip({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function Mk(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function Ck(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,p=l===void 0?t?new WeakMap:void 0:l,h=f.meta;return r(c,s,{cache:p,equals:i,meta:h,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var Ik=Dt();Dt({strict:!0});Dt({circular:!0});Dt({circular:!0,strict:!0});Dt({createInternalComparator:function(){return ir}});Dt({strict:!0,createInternalComparator:function(){return ir}});Dt({circular:!0,createInternalComparator:function(){return ir}});Dt({circular:!0,createInternalComparator:function(){return ir},strict:!0});function Dt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=jk(e),c=Ek(u),s=n?n(c):Mk(c);return Ck({circular:r,comparator:c,createState:i,equals:s,strict:o})}function kk(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function ap(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):kk(i)};requestAnimationFrame(n)}function Eu(e){"@babel/helpers - typeof";return Eu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Eu(e)}function Dk(e){return Lk(e)||Rk(e)||Bk(e)||Nk()}function Nk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Bk(e,t){if(e){if(typeof e=="string")return op(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return op(e,t)}}function op(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Rk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Lk(e){if(Array.isArray(e))return e}function Fk(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=Dk(o),c=u[0],s=u.slice(1);if(typeof c=="number"){ap(i.bind(null,s),c);return}i(c),ap(i.bind(null,s));return}Eu(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function up(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function cp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?up(Object(r),!0).forEach(function(n){Rv(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):up(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Rv(e,t,r){return t=zk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zk(e){var t=Wk(e,"string");return Ln(t)==="symbol"?t:String(t)}function Wk(e,t){if(Ln(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Uk=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},qk=function(t){return t},Hk=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},vn=function(t,r){return Object.keys(r).reduce(function(n,i){return cp(cp({},n),{},Rv({},i,t(i,r[i])))},{})},sp=function(t,r,n){return t.map(function(i){return"".concat(Hk(i)," ").concat(r,"ms ").concat(n)}).join(",")};function Gk(e,t){return Vk(e)||Xk(e,t)||Lv(e,t)||Kk()}function Kk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Xk(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function Vk(e){if(Array.isArray(e))return e}function Yk(e){return Qk(e)||Jk(e)||Lv(e)||Zk()}function Zk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Lv(e,t){if(e){if(typeof e=="string")return ju(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ju(e,t)}}function Jk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Qk(e){if(Array.isArray(e))return ju(e)}function ju(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ta=1e-4,Fv=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},zv=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},lp=function(t,r){return function(n){var i=Fv(t,r);return zv(i,n)}},eD=function(t,r){return function(n){var i=Fv(t,r),a=[].concat(Yk(i.map(function(o,u){return o*u}).slice(1)),[0]);return zv(a,n)}},fp=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(d){return parseFloat(d)}),f=Gk(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=lp(i,o),p=lp(a,u),h=eD(i,o),y=function(x){return x>1?1:x<0?0:x},v=function(x){for(var w=x>1?1:x,b=w,O=0;O<8;++O){var m=l(b)-w,g=h(b);if(Math.abs(m-w)<ta||g<ta)return p(b);b=y(b-m/g)}return p(b)};return v.isStepper=!1,v},tD=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,p){var h=-(f-l)*n,y=p*a,v=p+(h-y)*u/1e3,d=p*u/1e3+f;return Math.abs(d-l)<ta&&Math.abs(v)<ta?[l,0]:[d,v]};return c.isStepper=!0,c.dt=u,c},rD=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return fp(i);case"spring":return tD();default:if(i.split("(")[0]==="cubic-bezier")return fp(i)}return typeof i=="function"?i:null};function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function pp(e){return aD(e)||iD(e)||Wv(e)||nD()}function nD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function iD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function aD(e){if(Array.isArray(e))return Cu(e)}function hp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _e(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hp(Object(r),!0).forEach(function(n){Mu(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Mu(e,t,r){return t=oD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oD(e){var t=uD(e,"string");return Fn(t)==="symbol"?t:String(t)}function uD(e,t){if(Fn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function cD(e,t){return fD(e)||lD(e,t)||Wv(e,t)||sD()}function sD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Wv(e,t){if(e){if(typeof e=="string")return Cu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Cu(e,t)}}function Cu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function lD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function fD(e){if(Array.isArray(e))return e}var ra=function(t,r,n){return t+(r-t)*n},Iu=function(t){var r=t.from,n=t.to;return r!==n},pD=function e(t,r,n){var i=vn(function(a,o){if(Iu(o)){var u=t(o.from,o.to,o.velocity),c=cD(u,2),s=c[0],f=c[1];return _e(_e({},o),{},{from:s,velocity:f})}return o},r);return n<1?vn(function(a,o){return Iu(o)?_e(_e({},o),{},{velocity:ra(o.velocity,i[a].velocity,n),from:ra(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const hD=function(e,t,r,n,i){var a=Uk(e,t),o=a.reduce(function(d,x){return _e(_e({},d),{},Mu({},x,[e[x],t[x]]))},{}),u=a.reduce(function(d,x){return _e(_e({},d),{},Mu({},x,{from:e[x],velocity:0,to:t[x]}))},{}),c=-1,s,f,l=function(){return null},p=function(){return vn(function(x,w){return w.from},u)},h=function(){return!Object.values(u).filter(Iu).length},y=function(x){s||(s=x);var w=x-s,b=w/r.dt;u=pD(r,u,b),i(_e(_e(_e({},e),t),p())),s=x,h()||(c=requestAnimationFrame(l))},v=function(x){f||(f=x);var w=(x-f)/n,b=vn(function(m,g){return ra.apply(void 0,pp(g).concat([r(w)]))},o);if(i(_e(_e(_e({},e),t),b)),w<1)c=requestAnimationFrame(l);else{var O=vn(function(m,g){return ra.apply(void 0,pp(g).concat([r(1)]))},o);i(_e(_e(_e({},e),t),O))}};return l=r.isStepper?y:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function _r(e){"@babel/helpers - typeof";return _r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_r(e)}var dD=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function vD(e,t){if(e==null)return{};var r=yD(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function yD(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function jo(e){return xD(e)||bD(e)||gD(e)||mD()}function mD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gD(e,t){if(e){if(typeof e=="string")return ku(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ku(e,t)}}function bD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function xD(e){if(Array.isArray(e))return ku(e)}function ku(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function dp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ye(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?dp(Object(r),!0).forEach(function(n){ln(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ln(e,t,r){return t=Uv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function OD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Uv(n.key),n)}}function SD(e,t,r){return t&&OD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Uv(e){var t=AD(e,"string");return _r(t)==="symbol"?t:String(t)}function AD(e,t){if(_r(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_r(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function PD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Du(e,t)}function Du(e,t){return Du=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Du(e,t)}function _D(e){var t=$D();return function(){var n=na(e),i;if(t){var a=na(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Nu(this,i)}}function Nu(e,t){if(t&&(_r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Bu(e)}function Bu(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $D(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function na(e){return na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},na(e)}var bt=function(e){PD(r,e);var t=_D(r);function r(n,i){var a;wD(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,p=o.children,h=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(Bu(a)),a.changeStyle=a.changeStyle.bind(Bu(a)),!u||h<=0)return a.state={style:{}},typeof p=="function"&&(a.state={style:f}),Nu(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof p=="function")return a.state={style:s},Nu(a);a.state={style:c?ln({},c,s):s}}else a.state={style:{}};return a}return SD(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,p=this.state.style;if(u){if(!o){var h={style:c?ln({},c,f):f};this.state&&p&&(c&&p[c]!==f||!c&&p!==f)&&this.setState(h);return}if(!(Ik(i.to,f)&&i.canBegin&&i.isActive)){var y=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=y||s?l:i.to;if(this.state&&p){var d={style:c?ln({},c,v):v};(c&&p[c]!==v||!c&&p!==v)&&this.setState(d)}this.runAnimation(Ye(Ye({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,p=i.onAnimationStart,h=hD(o,u,rD(s),c,this.changeStyle),y=function(){a.stopJSAnimation=h()};this.manager.start([p,f,y,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,p=l===void 0?0:l,h=function(v,d,x){if(x===0)return v;var w=d.duration,b=d.easing,O=b===void 0?"ease":b,m=d.style,g=d.properties,S=d.onAnimationEnd,A=x>0?o[x-1]:d,_=g||Object.keys(m);if(typeof O=="function"||O==="spring")return[].concat(jo(v),[a.runJSAnimation.bind(a,{from:A.style,to:m,duration:w,easing:O}),w]);var j=sp(_,w,O),$=Ye(Ye(Ye({},A.style),m),{},{transition:j});return[].concat(jo(v),[$,w,S]).filter(qk)};return this.manager.start([c].concat(jo(o.reduce(h,[f,Math.max(p,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=Fk());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,p=i.steps,h=i.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof s=="function"||typeof h=="function"||s==="spring"){this.runJSAnimation(i);return}if(p.length>1){this.runStepAnimation(i);return}var v=u?ln({},u,c):c,d=sp(Object.keys(v),o,s);y.start([f,a,Ye(Ye({},v),{},{transition:d}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=vD(i,dD),s=B.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(h){var y=h.props,v=y.style,d=v===void 0?{}:v,x=y.className,w=B.cloneElement(h,Ye(Ye({},c),{},{style:Ye(Ye({},d),f),className:x}));return w};return s===1?l(B.Children.only(a)):P.createElement("div",null,B.Children.map(a,function(p){return l(p)}))}}]),r}(B.PureComponent);bt.displayName="Animate";bt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};bt.propTypes={from:re.oneOfType([re.object,re.string]),to:re.oneOfType([re.object,re.string]),attributeName:re.string,duration:re.number,begin:re.number,easing:re.oneOfType([re.string,re.func]),steps:re.arrayOf(re.shape({duration:re.number.isRequired,style:re.object.isRequired,easing:re.oneOfType([re.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),re.func]),properties:re.arrayOf("string"),onAnimationEnd:re.func})),children:re.oneOfType([re.node,re.func]),isActive:re.bool,canBegin:re.bool,onAnimationEnd:re.func,shouldReAnimate:re.bool,onAnimationStart:re.func,onAnimationReStart:re.func};function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function ia(){return ia=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ia.apply(this,arguments)}function TD(e,t){return CD(e)||MD(e,t)||jD(e,t)||ED()}function ED(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jD(e,t){if(e){if(typeof e=="string")return vp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return vp(e,t)}}function vp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function MD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function CD(e){if(Array.isArray(e))return e}function yp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yp(Object(r),!0).forEach(function(n){ID(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ID(e,t,r){return t=kD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kD(e){var t=DD(e,"string");return zn(t)=="symbol"?t:t+""}function DD(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var gp=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],p=0,h=4;p<h;p++)l[p]=a[p]>o?o:a[p];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var y=Math.min(o,a);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+c*y,",").concat(r,`
            L `).concat(t+n-c*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+i-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n-c*y,",").concat(r+i,`
            L `).concat(t+c*y,",").concat(r+i,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},ND=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),p=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=p}return!1},BD={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},rs=function(t){var r=mp(mp({},BD),t),n=B.useRef(),i=B.useState(-1),a=TD(i,2),o=a[0],u=a[1];B.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var O=n.current.getTotalLength();O&&u(O)}catch{}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,p=r.radius,h=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,x=r.isAnimationActive,w=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var b=ee("recharts-rectangle",h);return w?P.createElement(bt,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:v,animationEasing:y,isActive:w},function(O){var m=O.width,g=O.height,S=O.x,A=O.y;return P.createElement(bt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,isActive:x,easing:y},P.createElement("path",ia({},Z(r,!0),{className:b,d:gp(S,A,m,g,p),ref:n})))}):P.createElement("path",ia({},Z(r,!0),{className:b,d:gp(c,s,f,l,p)}))};function Ru(){return Ru=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ru.apply(this,arguments)}var ns=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=ee("recharts-dot",a);return r===+r&&n===+n&&i===+i?B.createElement("circle",Ru({},Z(t,!1),Si(t),{className:o,cx:r,cy:n,r:i})):null};function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}var RD=["x","y","top","left","width","height","className"];function Lu(){return Lu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Lu.apply(this,arguments)}function bp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function LD(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bp(Object(r),!0).forEach(function(n){FD(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function FD(e,t,r){return t=zD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zD(e){var t=WD(e,"string");return Wn(t)=="symbol"?t:t+""}function WD(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function UD(e,t){if(e==null)return{};var r=qD(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function qD(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var HD=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},GD=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,p=t.height,h=p===void 0?0:p,y=t.className,v=UD(t,RD),d=LD({x:n,y:a,top:u,left:s,width:l,height:h},v);return!R(n)||!R(a)||!R(l)||!R(h)||!R(u)||!R(s)?null:P.createElement("path",Lu({},Z(d,!0),{className:ee("recharts-cross",y),d:HD(n,a,l,h,u,s)}))},KD=nd,XD=KD(Object.getPrototypeOf,Object),VD=XD,YD=xt,ZD=VD,JD=wt,QD="[object Object]",eN=Function.prototype,tN=Object.prototype,qv=eN.toString,rN=tN.hasOwnProperty,nN=qv.call(Object);function iN(e){if(!JD(e)||YD(e)!=QD)return!1;var t=ZD(e);if(t===null)return!0;var r=rN.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&qv.call(r)==nN}var aN=iN;const oN=ue(aN);var uN=xt,cN=wt,sN="[object Boolean]";function lN(e){return e===!0||e===!1||cN(e)&&uN(e)==sN}var fN=lN;const pN=ue(fN);function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function aa(){return aa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},aa.apply(this,arguments)}function hN(e,t){return mN(e)||yN(e,t)||vN(e,t)||dN()}function dN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vN(e,t){if(e){if(typeof e=="string")return xp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xp(e,t)}}function xp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function yN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function mN(e){if(Array.isArray(e))return e}function wp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Op(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wp(Object(r),!0).forEach(function(n){gN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gN(e,t,r){return t=bN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bN(e){var t=xN(e,"string");return Un(t)=="symbol"?t:t+""}function xN(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Sp=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},wN={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ON=function(t){var r=Op(Op({},wN),t),n=B.useRef(),i=B.useState(-1),a=hN(i,2),o=a[0],u=a[1];B.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var b=n.current.getTotalLength();b&&u(b)}catch{}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,p=r.height,h=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,x=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||p!==+p||f===0&&l===0||p===0)return null;var w=ee("recharts-trapezoid",h);return x?P.createElement(bt,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:p,x:c,y:s},duration:v,animationEasing:y,isActive:x},function(b){var O=b.upperWidth,m=b.lowerWidth,g=b.height,S=b.x,A=b.y;return P.createElement(bt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,easing:y},P.createElement("path",aa({},Z(r,!0),{className:w,d:Sp(S,A,O,m,g),ref:n})))}):P.createElement("g",null,P.createElement("path",aa({},Z(r,!0),{className:w,d:Sp(c,s,f,l,p)})))},SN=["option","shapeType","propTransformer","activeClassName","isActive"];function qn(e){"@babel/helpers - typeof";return qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qn(e)}function AN(e,t){if(e==null)return{};var r=PN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function PN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ap(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function oa(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ap(Object(r),!0).forEach(function(n){_N(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ap(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _N(e,t,r){return t=$N(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $N(e){var t=TN(e,"string");return qn(t)=="symbol"?t:t+""}function TN(e,t){if(qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function EN(e,t){return oa(oa({},t),e)}function jN(e,t){return e==="symbols"}function Pp(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return P.createElement(rs,r);case"trapezoid":return P.createElement(ON,r);case"sector":return P.createElement(Iv,r);case"symbols":if(jN(t))return P.createElement(wc,r);break;default:return null}}function MN(e){return B.isValidElement(e)?e.props:e}function CN(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?EN:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=AN(e,SN),s;if(B.isValidElement(t))s=B.cloneElement(t,oa(oa({},c),MN(t)));else if(V(t))s=t(c);else if(oN(t)&&!pN(t)){var f=i(t,c);s=P.createElement(Pp,{shapeType:r,elementProps:f})}else{var l=c;s=P.createElement(Pp,{shapeType:r,elementProps:l})}return u?P.createElement(pe,{className:o},s):s}function Qa(e,t){return t!=null&&"trapezoids"in e.props}function eo(e,t){return t!=null&&"sectors"in e.props}function Hn(e,t){return t!=null&&"points"in e.props}function IN(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function kN(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function DN(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function NN(e,t){var r;return Qa(e,t)?r=IN:eo(e,t)?r=kN:Hn(e,t)&&(r=DN),r}function BN(e,t){var r;return Qa(e,t)?r="trapezoids":eo(e,t)?r="sectors":Hn(e,t)&&(r="points"),r}function RN(e,t){if(Qa(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(eo(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return Hn(e,t)?t.payload:{}}function LN(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=BN(r,t),a=RN(r,t),o=n.filter(function(c,s){var f=Tn(a,c),l=r.props[i].filter(function(y){var v=NN(r,t);return v(y,t)}),p=r.props[i].indexOf(l[l.length-1]),h=s===p;return f&&h}),u=n.indexOf(o[o.length-1]);return u}var FN=Math.ceil,zN=Math.max;function WN(e,t,r,n){for(var i=-1,a=zN(FN((t-e)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=e,e+=r;return o}var UN=WN,qN=Od,_p=1/0,HN=17976931348623157e292;function GN(e){if(!e)return e===0?e:0;if(e=qN(e),e===_p||e===-_p){var t=e<0?-1:1;return t*HN}return e===e?e:0}var Hv=GN,KN=UN,XN=Wa,Mo=Hv;function VN(e){return function(t,r,n){return n&&typeof n!="number"&&XN(t,r,n)&&(r=n=void 0),t=Mo(t),r===void 0?(r=t,t=0):r=Mo(r),n=n===void 0?t<r?1:-1:Mo(n),KN(t,r,n,e)}}var YN=VN,ZN=YN,JN=ZN(),QN=JN;const ua=ue(QN);function Gn(e){"@babel/helpers - typeof";return Gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gn(e)}function $p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Tp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$p(Object(r),!0).forEach(function(n){Gv(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$p(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Gv(e,t,r){return t=eB(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eB(e){var t=tB(e,"string");return Gn(t)=="symbol"?t:t+""}function tB(e,t){if(Gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var rB=["Webkit","Moz","O","ms"],nB=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=rB.reduce(function(a,o){return Tp(Tp({},a),{},Gv({},o+n,r))},{});return i[t]=r,i};function $r(e){"@babel/helpers - typeof";return $r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$r(e)}function ca(){return ca=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ca.apply(this,arguments)}function Ep(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Co(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ep(Object(r),!0).forEach(function(n){Be(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ep(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function iB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Xv(n.key),n)}}function aB(e,t,r){return t&&jp(e.prototype,t),r&&jp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function oB(e,t,r){return t=sa(t),uB(e,Kv()?Reflect.construct(t,r||[],sa(e).constructor):t.apply(e,r))}function uB(e,t){if(t&&($r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cB(e)}function cB(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Kv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Kv=function(){return!!e})()}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function sB(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Fu(e,t)}function Fu(e,t){return Fu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Fu(e,t)}function Be(e,t,r){return t=Xv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xv(e){var t=lB(e,"string");return $r(t)=="symbol"?t:t+""}function lB(e,t){if($r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var fB=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=pn().domain(ua(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},Mp=function(t){return t.changedTouches&&!!t.changedTouches.length},Tr=function(e){function t(r){var n;return iB(this,t),n=oB(this,t,[r]),Be(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),Be(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),Be(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),Be(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),Be(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),Be(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),Be(n,"handleSlideDragStart",function(i){var a=Mp(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return sB(t,e),aB(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),p=Math.max(i,a),h=t.getIndexInRange(o,l),y=t.getIndexInRange(o,p);return{startIndex:h-h%c,endIndex:y===f?f:y-y%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=Fe(a[n],u,n);return V(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,p=c.startIndex,h=c.endIndex,y=c.onChange,v=n.pageX-a;v>0?v=Math.min(v,s+f-l-u,s+f-l-o):v<0&&(v=Math.max(v,s-o,s-u));var d=this.getIndex({startX:o+v,endX:u+v});(d.startIndex!==p||d.endIndex!==h)&&y&&y(d),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=Mp(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,p=f.width,h=f.travellerWidth,y=f.onChange,v=f.gap,d=f.data,x={startX:this.state.startX,endX:this.state.endX},w=n.pageX-a;w>0?w=Math.min(w,l+p-h-s):w<0&&(w=Math.max(w,l-s)),x[o]=s+w;var b=this.getIndex(x),O=b.startIndex,m=b.endIndex,g=function(){var A=d.length-1;return o==="startX"&&(u>c?O%v===0:m%v===0)||u<c&&m===A||o==="endX"&&(u>c?m%v===0:O%v===0)||u>c&&m===A};this.setState(Be(Be({},o,s+w),"brushMoveStartX",n.pageX),function(){y&&g()&&y(b)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var p=l+n;if(!(p===-1||p>=u.length)){var h=u[p];i==="startX"&&h>=s||i==="endX"&&h<=c||this.setState(Be({},i,h),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return P.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=B.Children.only(s);return l?P.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,p=c.traveller,h=c.ariaLabel,y=c.data,v=c.startIndex,d=c.endIndex,x=Math.max(n,this.props.x),w=Co(Co({},Z(this.props,!1)),{},{x,y:s,width:f,height:l}),b=h||"Min value: ".concat((a=y[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=y[d])===null||o===void 0?void 0:o.name);return P.createElement(pe,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(p,w))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return P.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,p=f.endX,h=5,y={pointerEvents:"none",fill:s};return P.createElement(pe,{className:"recharts-brush-texts"},P.createElement(Ri,ca({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,p)-h,y:o+u/2},y),this.getTextOfTick(i)),P.createElement(Ri,ca({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,p)+c+h,y:o+u/2},y),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,p=this.state,h=p.startX,y=p.endX,v=p.isTextActive,d=p.isSlideMoving,x=p.isTravellerMoving,w=p.isTravellerFocused;if(!i||!i.length||!R(u)||!R(c)||!R(s)||!R(f)||s<=0||f<=0)return null;var b=ee("recharts-brush",a),O=P.Children.count(o)===1,m=nB("userSelect","none");return P.createElement(pe,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(h,y),this.renderTravellerLayer(h,"startX"),this.renderTravellerLayer(y,"endX"),(v||d||x||w||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return P.createElement(P.Fragment,null,P.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),P.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),P.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return P.isValidElement(n)?a=P.cloneElement(n,i):V(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return Co({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?fB({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var p=i.scale.domain().map(function(h){return i.scale(h)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:p}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(B.PureComponent);Be(Tr,"displayName","Brush");Be(Tr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var pB=Tc;function hB(e,t){var r;return pB(e,function(n,i,a){return r=t(n,i,a),!r}),!!r}var dB=hB,vB=Vh,yB=Ct,mB=dB,gB=De,bB=Wa;function xB(e,t,r){var n=gB(e)?vB:mB;return r&&bB(e,t,r)&&(t=void 0),n(e,yB(t))}var wB=xB;const OB=ue(wB);var at=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},Cp=md;function SB(e,t,r){t=="__proto__"&&Cp?Cp(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var AB=SB,PB=AB,_B=vd,$B=Ct;function TB(e,t){var r={};return t=$B(t),_B(e,function(n,i,a){PB(r,i,t(n,i,a))}),r}var EB=TB;const jB=ue(EB);function MB(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}var CB=MB,IB=Tc;function kB(e,t){var r=!0;return IB(e,function(n,i,a){return r=!!t(n,i,a),r}),r}var DB=kB,NB=CB,BB=DB,RB=Ct,LB=De,FB=Wa;function zB(e,t,r){var n=LB(e)?NB:BB;return r&&FB(e,t,r)&&(t=void 0),n(e,RB(t))}var WB=zB;const Vv=ue(WB);var UB=["x","y"];function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}function zu(){return zu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zu.apply(this,arguments)}function Ip(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function sn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ip(Object(r),!0).forEach(function(n){qB(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ip(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function qB(e,t,r){return t=HB(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function HB(e){var t=GB(e,"string");return Kn(t)=="symbol"?t:t+""}function GB(e,t){if(Kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function KB(e,t){if(e==null)return{};var r=XB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function XB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function VB(e,t){var r=e.x,n=e.y,i=KB(e,UB),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),p=parseInt(l,10);return sn(sn(sn(sn(sn({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:p,name:t.name,radius:t.radius})}function kp(e){return P.createElement(CN,zu({shapeType:"rectangle",propTransformer:VB,activeClassName:"recharts-active-bar"},e))}var YB=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=R(n)||h0(n);return a?t(n,i):(a||Qt(),r)}},ZB=["value","background"],Yv;function Er(e){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(e)}function JB(e,t){if(e==null)return{};var r=QB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function QB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function la(){return la=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},la.apply(this,arguments)}function Dp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ye(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dp(Object(r),!0).forEach(function(n){Tt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function eR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Np(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Jv(n.key),n)}}function tR(e,t,r){return t&&Np(e.prototype,t),r&&Np(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function rR(e,t,r){return t=fa(t),nR(e,Zv()?Reflect.construct(t,r||[],fa(e).constructor):t.apply(e,r))}function nR(e,t){if(t&&(Er(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return iR(e)}function iR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Zv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Zv=function(){return!!e})()}function fa(e){return fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},fa(e)}function aR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Wu(e,t)}function Wu(e,t){return Wu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Wu(e,t)}function Tt(e,t,r){return t=Jv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Jv(e){var t=oR(e,"string");return Er(t)=="symbol"?t:t+""}function oR(e,t){if(Er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ai=function(e){function t(){var r;eR(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=rR(this,t,[].concat(i)),Tt(r,"state",{isAnimationFinished:!1}),Tt(r,"id",Qn("recharts-bar-")),Tt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Tt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return aR(t,e),tR(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=Z(this.props,!1);return n&&n.map(function(l,p){var h=p===c,y=h?s:o,v=ye(ye(ye({},f),l),{},{isActive:h,option:y,index:p,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return P.createElement(pe,la({className:"recharts-bar-rectangle"},Ai(i.props,l,p),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(p)}),P.createElement(kp,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,p=this.state.prevData;return P.createElement(bt,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(h){var y=h.t,v=a.map(function(d,x){var w=p&&p[x];if(w){var b=qe(w.x,d.x),O=qe(w.y,d.y),m=qe(w.width,d.width),g=qe(w.height,d.height);return ye(ye({},d),{},{x:b(y),y:O(y),width:m(y),height:g(y)})}if(o==="horizontal"){var S=qe(0,d.height),A=S(y);return ye(ye({},d),{},{y:d.y+d.height-A,height:A})}var _=qe(0,d.width),j=_(y);return ye(ye({},d),{},{width:j})});return P.createElement(pe,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Tn(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=Z(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,p=JB(s,ZB);if(!l)return null;var h=ye(ye(ye(ye(ye({},p),{},{fill:"#eee"},l),c),Ai(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return P.createElement(kp,la({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},h))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=et(f,Ja);if(!l)return null;var p=s==="vertical"?o[0].height/2:o[0].width/2,h=function(d,x){var w=Array.isArray(d.value)?d.value[1]:d.value;return{x:d.x,y:d.y,value:w,errorVal:Fe(d,x)}},y={clipPath:n?"url(#clipPath-".concat(i,")"):null};return P.createElement(pe,y,l.map(function(v){return P.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,p=n.height,h=n.isAnimationActive,y=n.background,v=n.id;if(i||!a||!a.length)return null;var d=this.state.isAnimationFinished,x=ee("recharts-bar",o),w=u&&u.allowDataOverflow,b=c&&c.allowDataOverflow,O=w||b,m=J(v)?this.id:v;return P.createElement(pe,{className:x},w||b?P.createElement("defs",null,P.createElement("clipPath",{id:"clipPath-".concat(m)},P.createElement("rect",{x:w?s:s-l/2,y:b?f:f-p/2,width:w?l:l*2,height:b?p:p*2}))):null,P.createElement(pe,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(m,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,m),(!h||d)&&jt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(B.PureComponent);Yv=ai;Tt(ai,"displayName","Bar");Tt(ai,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Xr.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Tt(ai,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,p=e.offset,h=NI(n,r);if(!h)return null;var y=t.layout,v=r.type.defaultProps,d=v!==void 0?ye(ye({},v),r.props):r.props,x=d.dataKey,w=d.children,b=d.minPointSize,O=y==="horizontal"?o:a,m=s?O.scale.domain():null,g=qI({numericAxis:O}),S=et(w,Ad),A=l.map(function(_,j){var $,T,C,I,M,k;s?$=BI(s[f+j],m):($=Fe(_,x),Array.isArray($)||($=[g,$]));var N=YB(b,Yv.defaultProps.minPointSize)($[1],j);if(y==="horizontal"){var L,F=[o.scale($[0]),o.scale($[1])],q=F[0],G=F[1];T=Df({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:_,index:j}),C=(L=G??q)!==null&&L!==void 0?L:void 0,I=h.size;var W=q-G;if(M=Number.isNaN(W)?0:W,k={x:T,y:o.y,width:I,height:o.height},Math.abs(N)>0&&Math.abs(M)<Math.abs(N)){var K=Qe(M||N)*(Math.abs(N)-Math.abs(M));C-=K,M+=K}}else{var ce=[a.scale($[0]),a.scale($[1])],ve=ce[0],Ne=ce[1];if(T=ve,C=Df({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:_,index:j}),I=Ne-ve,M=h.size,k={x:a.x,y:C,width:a.width,height:M},Math.abs(N)>0&&Math.abs(I)<Math.abs(N)){var Nt=Qe(I||N)*(Math.abs(N)-Math.abs(I));I+=Nt}}return ye(ye(ye({},_),{},{x:T,y:C,width:I,height:M,value:s?$:$[1],payload:_,background:k},S&&S[j]&&S[j].props),{},{tooltipPayload:[jv(r,_)],tooltipPosition:{x:T+I/2,y:C+M/2}})});return ye({data:A,layout:y},p)});function Xn(e){"@babel/helpers - typeof";return Xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xn(e)}function uR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qv(n.key),n)}}function cR(e,t,r){return t&&Bp(e.prototype,t),r&&Bp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Rp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ze(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rp(Object(r),!0).forEach(function(n){to(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function to(e,t,r){return t=Qv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qv(e){var t=sR(e,"string");return Xn(t)=="symbol"?t:t+""}function sR(e,t){if(Xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var lR=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},p=!!Re(s,ai);return f.reduce(function(h,y){var v=r[y],d=v.orientation,x=v.domain,w=v.padding,b=w===void 0?{}:w,O=v.mirror,m=v.reversed,g="".concat(d).concat(O?"Mirror":""),S,A,_,j,$;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var T=x[1]-x[0],C=1/0,I=v.categoricalDomain.sort(y0);if(I.forEach(function(ce,ve){ve>0&&(C=Math.min((ce||0)-(I[ve-1]||0),C))}),Number.isFinite(C)){var M=C/T,k=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(S=M*k/2),v.padding==="no-gap"){var N=Zt(t.barCategoryGap,M*k),L=M*k/2;S=L-N-(L-N)/k*N}}}i==="xAxis"?A=[n.left+(b.left||0)+(S||0),n.left+n.width-(b.right||0)-(S||0)]:i==="yAxis"?A=c==="horizontal"?[n.top+n.height-(b.bottom||0),n.top+(b.top||0)]:[n.top+(b.top||0)+(S||0),n.top+n.height-(b.bottom||0)-(S||0)]:A=v.range,m&&(A=[A[1],A[0]]);var F=kI(v,a,p),q=F.scale,G=F.realScaleType;q.domain(x).range(A),DI(q);var W=UI(q,Ze(Ze({},v),{},{realScaleType:G}));i==="xAxis"?($=d==="top"&&!O||d==="bottom"&&O,_=n.left,j=l[g]-$*v.height):i==="yAxis"&&($=d==="left"&&!O||d==="right"&&O,_=l[g]-$*v.width,j=n.top);var K=Ze(Ze(Ze({},v),W),{},{realScaleType:G,x:_,y:j,scale:q,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return K.bandSize=Ji(K,W),!v.hide&&i==="xAxis"?l[g]+=($?-1:1)*K.height:v.hide||(l[g]+=($?-1:1)*K.width),Ze(Ze({},h),{},to({},y,K))},{})},ey=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},fR=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return ey({x:r,y:n},{x:i,y:a})},ty=function(){function e(t){uR(this,e),this.scale=t}return cR(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();to(ty,"EPS",1e-4);var is=function(t){var r=Object.keys(t).reduce(function(n,i){return Ze(Ze({},n),{},to({},i,ty.create(t[i])))},{});return Ze(Ze({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return jB(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return Vv(i,function(a,o){return r[o].isInRange(a)})}})};function pR(e){return(e%180+180)%180}var hR=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=pR(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},dR=Ct,vR=ei,yR=Fa;function mR(e){return function(t,r,n){var i=Object(t);if(!vR(t)){var a=dR(r);t=yR(t),r=function(u){return a(i[u],u,i)}}var o=e(t,r,n);return o>-1?i[a?t[o]:o]:void 0}}var gR=mR,bR=Hv;function xR(e){var t=bR(e),r=t%1;return t===t?r?t-r:t:0}var wR=xR,OR=sd,SR=Ct,AR=wR,PR=Math.max;function _R(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var i=r==null?0:AR(r);return i<0&&(i=PR(n+i,0)),OR(e,SR(t),i)}var $R=_R,TR=gR,ER=$R,jR=TR(ER),MR=jR;const CR=ue(MR);var IR=bb(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),as=B.createContext(void 0),os=B.createContext(void 0),ry=B.createContext(void 0),ny=B.createContext({}),iy=B.createContext(void 0),ay=B.createContext(0),oy=B.createContext(0),Lp=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=IR(a);return P.createElement(as.Provider,{value:n},P.createElement(os.Provider,{value:i},P.createElement(ny.Provider,{value:a},P.createElement(ry.Provider,{value:f},P.createElement(iy.Provider,{value:o},P.createElement(ay.Provider,{value:s},P.createElement(oy.Provider,{value:c},u)))))))},kR=function(){return B.useContext(iy)},uy=function(t){var r=B.useContext(as);r==null&&Qt();var n=r[t];return n==null&&Qt(),n},DR=function(){var t=B.useContext(as);return _t(t)},NR=function(){var t=B.useContext(os),r=CR(t,function(n){return Vv(n.domain,Number.isFinite)});return r||_t(t)},cy=function(t){var r=B.useContext(os);r==null&&Qt();var n=r[t];return n==null&&Qt(),n},BR=function(){var t=B.useContext(ry);return t},RR=function(){return B.useContext(ny)},us=function(){return B.useContext(oy)},cs=function(){return B.useContext(ay)};function jr(e){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jr(e)}function LR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function FR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ly(n.key),n)}}function zR(e,t,r){return t&&FR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function WR(e,t,r){return t=pa(t),UR(e,sy()?Reflect.construct(t,r||[],pa(e).constructor):t.apply(e,r))}function UR(e,t){if(t&&(jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return qR(e)}function qR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function sy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(sy=function(){return!!e})()}function pa(e){return pa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},pa(e)}function HR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Uu(e,t)}function Uu(e,t){return Uu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Uu(e,t)}function Fp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Fp(Object(r),!0).forEach(function(n){ss(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ss(e,t,r){return t=ly(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ly(e){var t=GR(e,"string");return jr(t)=="symbol"?t:t+""}function GR(e,t){if(jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function KR(e,t){return ZR(e)||YR(e,t)||VR(e,t)||XR()}function XR(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function VR(e,t){if(e){if(typeof e=="string")return Wp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wp(e,t)}}function Wp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function YR(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function ZR(e){if(Array.isArray(e))return e}function qu(){return qu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qu.apply(this,arguments)}var JR=function(t,r){var n;return P.isValidElement(t)?n=P.cloneElement(t,r):V(t)?n=t(r):n=P.createElement("line",qu({},r,{className:"recharts-reference-line-line"})),n},QR=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,p=a.width,h=a.height;if(n){var y=s.y,v=t.y.apply(y,{position:o});if(at(s,"discard")&&!t.y.isInRange(v))return null;var d=[{x:f+p,y:v},{x:f,y:v}];return c==="left"?d.reverse():d}if(r){var x=s.x,w=t.x.apply(x,{position:o});if(at(s,"discard")&&!t.x.isInRange(w))return null;var b=[{x:w,y:l+h},{x:w,y:l}];return u==="top"?b.reverse():b}if(i){var O=s.segment,m=O.map(function(g){return t.apply(g,{position:o})});return at(s,"discard")&&OB(m,function(g){return!t.isInRange(g)})?null:m}return null};function eL(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=kR(),f=uy(i),l=cy(a),p=BR();if(!s||!p)return null;dt(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=is({x:f.scale,y:l.scale}),y=Oe(t),v=Oe(r),d=n&&n.length===2,x=QR(h,y,v,d,p,e.position,f.orientation,l.orientation,e);if(!x)return null;var w=KR(x,2),b=w[0],O=b.x,m=b.y,g=w[1],S=g.x,A=g.y,_=at(e,"hidden")?"url(#".concat(s,")"):void 0,j=zp(zp({clipPath:_},Z(e,!0)),{},{x1:O,y1:m,x2:S,y2:A});return P.createElement(pe,{className:ee("recharts-reference-line",u)},JR(o,j),Ee.renderCallByParent(e,fR({x1:O,y1:m,x2:S,y2:A})))}var ls=function(e){function t(){return LR(this,t),WR(this,t,arguments)}return HR(t,e),zR(t,[{key:"render",value:function(){return P.createElement(eL,this.props)}}])}(P.Component);ss(ls,"displayName","ReferenceLine");ss(ls,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Hu(){return Hu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Hu.apply(this,arguments)}function Mr(e){"@babel/helpers - typeof";return Mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(e)}function Up(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Up(Object(r),!0).forEach(function(n){ro(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Up(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rL(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,py(n.key),n)}}function nL(e,t,r){return t&&rL(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function iL(e,t,r){return t=ha(t),aL(e,fy()?Reflect.construct(t,r||[],ha(e).constructor):t.apply(e,r))}function aL(e,t){if(t&&(Mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return oL(e)}function oL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function fy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(fy=function(){return!!e})()}function ha(e){return ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ha(e)}function uL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gu(e,t)}function Gu(e,t){return Gu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Gu(e,t)}function ro(e,t,r){return t=py(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function py(e){var t=cL(e,"string");return Mr(t)=="symbol"?t:t+""}function cL(e,t){if(Mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var sL=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=is({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return at(t,"discard")&&!o.isInRange(u)?null:u},no=function(e){function t(){return tL(this,t),iL(this,t,arguments)}return uL(t,e),nL(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=Oe(i),f=Oe(a);if(dt(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=sL(this.props);if(!l)return null;var p=l.x,h=l.y,y=this.props,v=y.shape,d=y.className,x=at(this.props,"hidden")?"url(#".concat(c,")"):void 0,w=qp(qp({clipPath:x},Z(this.props,!0)),{},{cx:p,cy:h});return P.createElement(pe,{className:ee("recharts-reference-dot",d)},t.renderDot(v,w),Ee.renderCallByParent(this.props,{x:p-o,y:h-o,width:2*o,height:2*o}))}}])}(P.Component);ro(no,"displayName","ReferenceDot");ro(no,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});ro(no,"renderDot",function(e,t){var r;return P.isValidElement(e)?r=P.cloneElement(e,t):V(e)?r=e(t):r=P.createElement(ns,Hu({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Ku(){return Ku=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ku.apply(this,arguments)}function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function Hp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hp(Object(r),!0).forEach(function(n){io(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fL(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dy(n.key),n)}}function pL(e,t,r){return t&&fL(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function hL(e,t,r){return t=da(t),dL(e,hy()?Reflect.construct(t,r||[],da(e).constructor):t.apply(e,r))}function dL(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return vL(e)}function vL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function hy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(hy=function(){return!!e})()}function da(e){return da=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},da(e)}function yL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Xu(e,t)}function Xu(e,t){return Xu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Xu(e,t)}function io(e,t,r){return t=dy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dy(e){var t=mL(e,"string");return Cr(t)=="symbol"?t:t+""}function mL(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var gL=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var p=is({x:f.scale,y:l.scale}),h={x:t?p.x.apply(o,{position:"start"}):p.x.rangeMin,y:n?p.y.apply(c,{position:"start"}):p.y.rangeMin},y={x:r?p.x.apply(u,{position:"end"}):p.x.rangeMax,y:i?p.y.apply(s,{position:"end"}):p.y.rangeMax};return at(a,"discard")&&(!p.isInRange(h)||!p.isInRange(y))?null:ey(h,y)},ao=function(e){function t(){return lL(this,t),hL(this,t,arguments)}return yL(t,e),pL(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;dt(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=Oe(i),p=Oe(a),h=Oe(o),y=Oe(u),v=this.props.shape;if(!l&&!p&&!h&&!y&&!v)return null;var d=gL(l,p,h,y,this.props);if(!d&&!v)return null;var x=at(this.props,"hidden")?"url(#".concat(f,")"):void 0;return P.createElement(pe,{className:ee("recharts-reference-area",c)},t.renderRect(v,Gp(Gp({clipPath:x},Z(this.props,!0)),d)),Ee.renderCallByParent(this.props,d))}}])}(P.Component);io(ao,"displayName","ReferenceArea");io(ao,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});io(ao,"renderRect",function(e,t){var r;return P.isValidElement(e)?r=P.cloneElement(e,t):V(e)?r=e(t):r=P.createElement(rs,Ku({},t,{className:"recharts-reference-area-rect"})),r});function vy(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function bL(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return hR(n,r)}function xL(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function va(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function wL(e,t){return vy(e,t+1)}function OL(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var y=n==null?void 0:n[c];if(y===void 0)return{v:vy(n,s)};var v=c,d,x=function(){return d===void 0&&(d=r(y,v)),d},w=y.coordinate,b=c===0||va(e,w,x,f,u);b||(c=0,f=o,s+=1),b&&(f=w+e*(x()/2+i),c+=s)},p;s<=a.length;)if(p=l(),p)return p.v;return[]}function Vn(e){"@babel/helpers - typeof";return Vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(e)}function Kp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Te(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Kp(Object(r),!0).forEach(function(n){SL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Kp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SL(e,t,r){return t=AL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function AL(e){var t=PL(e,"string");return Vn(t)=="symbol"?t:t+""}function PL(e,t){if(Vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function _L(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(p){var h=a[p],y,v=function(){return y===void 0&&(y=r(h,p)),y};if(p===o-1){var d=e*(h.coordinate+e*v()/2-c);a[p]=h=Te(Te({},h),{},{tickCoord:d>0?h.coordinate-d*e:h.coordinate})}else a[p]=h=Te(Te({},h),{},{tickCoord:h.coordinate});var x=va(e,h.tickCoord,v,u,c);x&&(c=h.tickCoord-e*(v()/2+i),a[p]=Te(Te({},h),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function $L(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),p=e*(f.coordinate+e*l/2-s);o[u-1]=f=Te(Te({},f),{},{tickCoord:p>0?f.coordinate-p*e:f.coordinate});var h=va(e,f.tickCoord,function(){return l},c,s);h&&(s=f.tickCoord-e*(l/2+i),o[u-1]=Te(Te({},f),{},{isShow:!0}))}for(var y=a?u-1:u,v=function(w){var b=o[w],O,m=function(){return O===void 0&&(O=r(b,w)),O};if(w===0){var g=e*(b.coordinate-e*m()/2-c);o[w]=b=Te(Te({},b),{},{tickCoord:g<0?b.coordinate-g*e:b.coordinate})}else o[w]=b=Te(Te({},b),{},{tickCoord:b.coordinate});var S=va(e,b.tickCoord,m,c,s);S&&(c=b.tickCoord+e*(m()/2+i),o[w]=Te(Te({},b),{},{isShow:!0}))},d=0;d<y;d++)v(d);return o}function fs(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(R(c)||Xr.isSsr)return wL(i,typeof c=="number"&&R(c)?c:0);var p=[],h=u==="top"||u==="bottom"?"width":"height",y=f&&h==="width"?fn(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(b,O){var m=V(s)?s(b.value,O):b.value;return h==="width"?bL(fn(m,{fontSize:t,letterSpacing:r}),y,l):fn(m,{fontSize:t,letterSpacing:r})[h]},d=i.length>=2?Qe(i[1].coordinate-i[0].coordinate):1,x=xL(a,d,h);return c==="equidistantPreserveStart"?OL(d,x,v,i,o):(c==="preserveStart"||c==="preserveStartEnd"?p=$L(d,x,v,i,o,c==="preserveStartEnd"):p=_L(d,x,v,i,o),p.filter(function(w){return w.isShow}))}var TL=["viewBox"],EL=["viewBox"],jL=["ticks"];function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}function fr(){return fr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fr.apply(this,arguments)}function Xp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xp(Object(r),!0).forEach(function(n){ps(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Io(e,t){if(e==null)return{};var r=ML(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ML(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function CL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Vp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,my(n.key),n)}}function IL(e,t,r){return t&&Vp(e.prototype,t),r&&Vp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function kL(e,t,r){return t=ya(t),DL(e,yy()?Reflect.construct(t,r||[],ya(e).constructor):t.apply(e,r))}function DL(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return NL(e)}function NL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(yy=function(){return!!e})()}function ya(e){return ya=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ya(e)}function BL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Vu(e,t)}function Vu(e,t){return Vu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Vu(e,t)}function ps(e,t,r){return t=my(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function my(e){var t=RL(e,"string");return Ir(t)=="symbol"?t:t+""}function RL(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Jr=function(e){function t(r){var n;return CL(this,t),n=kL(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return BL(t,e),IL(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=Io(n,TL),u=this.props,c=u.viewBox,s=Io(u,EL);return!hr(a,c)||!hr(o,s)||!hr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,p=i.tickMargin,h,y,v,d,x,w,b=l?-1:1,O=n.tickSize||f,m=R(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":h=y=n.coordinate,d=o+ +!l*c,v=d-b*O,w=v-b*p,x=m;break;case"left":v=d=n.coordinate,y=a+ +!l*u,h=y-b*O,x=h-b*p,w=m;break;case"right":v=d=n.coordinate,y=a+ +l*u,h=y+b*O,x=h+b*p,w=m;break;default:h=y=n.coordinate,d=o+ +l*c,v=d+b*O,w=v+b*p,x=m;break}return{line:{x1:h,y1:v,x2:y,y2:d},tick:{x,y:w}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=xe(xe(xe({},Z(this.props,!1)),Z(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var p=+(c==="top"&&!s||c==="bottom"&&s);l=xe(xe({},l),{},{x1:i,y1:a+p*u,x2:i+o,y2:a+p*u})}else{var h=+(c==="left"&&!s||c==="right"&&s);l=xe(xe({},l),{},{x1:i+h*o,y1:a,x2:i+h*o,y2:a+u})}return P.createElement("line",fr({},l,{className:ee("recharts-cartesian-axis-line",Ge(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,p=u.unit,h=fs(xe(xe({},this.props),{},{ticks:n}),i,a),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),d=Z(this.props,!1),x=Z(f,!1),w=xe(xe({},d),{},{fill:"none"},Z(c,!1)),b=h.map(function(O,m){var g=o.getTickLineCoord(O),S=g.line,A=g.tick,_=xe(xe(xe(xe({textAnchor:y,verticalAnchor:v},d),{},{stroke:"none",fill:s},x),A),{},{index:m,payload:O,visibleTicksCount:h.length,tickFormatter:l});return P.createElement(pe,fr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(O.value,"-").concat(O.coordinate,"-").concat(O.tickCoord)},Ai(o.props,O,m)),c&&P.createElement("line",fr({},w,S,{className:ee("recharts-cartesian-axis-tick-line",Ge(c,"className"))})),f&&t.renderTickItem(f,_,"".concat(V(l)?l(O.value,m):O.value).concat(p||"")))});return P.createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,p=l.ticks,h=Io(l,jL),y=p;return V(c)&&(y=p&&p.length>0?c(this.props):c(h)),o<=0||u<=0||!y||!y.length?null:P.createElement(pe,{className:ee("recharts-cartesian-axis",s),ref:function(d){n.layerReference=d}},a&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),Ee.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o,u=ee(i.className,"recharts-cartesian-axis-tick-value");return P.isValidElement(n)?o=P.cloneElement(n,xe(xe({},i),{},{className:u})):V(n)?o=n(xe(xe({},i),{},{className:u})):o=P.createElement(Ri,fr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(B.Component);ps(Jr,"displayName","CartesianAxis");ps(Jr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var LL=["x1","y1","x2","y2","key"],FL=["offset"];function er(e){"@babel/helpers - typeof";return er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},er(e)}function Yp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yp(Object(r),!0).forEach(function(n){zL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zL(e,t,r){return t=WL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function WL(e){var t=UL(e,"string");return er(t)=="symbol"?t:t+""}function UL(e,t){if(er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Gt(){return Gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gt.apply(this,arguments)}function Zp(e,t){if(e==null)return{};var r=qL(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function qL(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var HL=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return P.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function gy(e,t){var r;if(P.isValidElement(e))r=P.cloneElement(e,t);else if(V(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Zp(t,LL),s=Z(c,!1);s.offset;var f=Zp(s,FL);r=P.createElement("line",Gt({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function GL(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=je(je({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return gy(i,s)});return P.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function KL(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=je(je({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return gy(i,s)});return P.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function XL(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(p){return Math.round(p+i-i)}).sort(function(p,h){return p-h});i!==f[0]&&f.unshift(0);var l=f.map(function(p,h){var y=!f[h+1],v=y?i+o-p:f[h+1]-p;if(v<=0)return null;var d=h%t.length;return P.createElement("rect",{key:"react-".concat(h),y:p,x:n,height:v,width:a,stroke:"none",fill:t[d],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function VL(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(p){return Math.round(p+a-a)}).sort(function(p,h){return p-h});a!==f[0]&&f.unshift(0);var l=f.map(function(p,h){var y=!f[h+1],v=y?a+u-p:f[h+1]-p;if(v<=0)return null;var d=h%n.length;return P.createElement("rect",{key:"react-".concat(h),x:p,y:o,width:v,height:c,stroke:"none",fill:n[d],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var YL=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return Tv(fs(je(je(je({},Jr.defaultProps),n),{},{ticks:pt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},ZL=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return Tv(fs(je(je(je({},Jr.defaultProps),n),{},{ticks:pt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},sr={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function JL(e){var t,r,n,i,a,o,u=us(),c=cs(),s=RR(),f=je(je({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:sr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:sr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:sr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:sr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:sr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:sr.verticalFill,x:R(e.x)?e.x:s.left,y:R(e.y)?e.y:s.top,width:R(e.width)?e.width:s.width,height:R(e.height)?e.height:s.height}),l=f.x,p=f.y,h=f.width,y=f.height,v=f.syncWithTicks,d=f.horizontalValues,x=f.verticalValues,w=DR(),b=NR();if(!R(h)||h<=0||!R(y)||y<=0||!R(l)||l!==+l||!R(p)||p!==+p)return null;var O=f.verticalCoordinatesGenerator||YL,m=f.horizontalCoordinatesGenerator||ZL,g=f.horizontalPoints,S=f.verticalPoints;if((!g||!g.length)&&V(m)){var A=d&&d.length,_=m({yAxis:b?je(je({},b),{},{ticks:A?d:b.ticks}):void 0,width:u,height:c,offset:s},A?!0:v);dt(Array.isArray(_),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(er(_),"]")),Array.isArray(_)&&(g=_)}if((!S||!S.length)&&V(O)){var j=x&&x.length,$=O({xAxis:w?je(je({},w),{},{ticks:j?x:w.ticks}):void 0,width:u,height:c,offset:s},j?!0:v);dt(Array.isArray($),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(er($),"]")),Array.isArray($)&&(S=$)}return P.createElement("g",{className:"recharts-cartesian-grid"},P.createElement(HL,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),P.createElement(GL,Gt({},f,{offset:s,horizontalPoints:g,xAxis:w,yAxis:b})),P.createElement(KL,Gt({},f,{offset:s,verticalPoints:S,xAxis:w,yAxis:b})),P.createElement(XL,Gt({},f,{horizontalPoints:g})),P.createElement(VL,Gt({},f,{verticalPoints:S})))}JL.displayName="CartesianGrid";var QL=["layout","type","stroke","connectNulls","isRange","ref"],eF=["key"],by;function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function xy(e,t){if(e==null)return{};var r=tF(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function tF(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Kt(){return Kt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Kt.apply(this,arguments)}function Jp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function At(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Jp(Object(r),!0).forEach(function(n){nt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Oy(n.key),n)}}function nF(e,t,r){return t&&Qp(e.prototype,t),r&&Qp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function iF(e,t,r){return t=ma(t),aF(e,wy()?Reflect.construct(t,r||[],ma(e).constructor):t.apply(e,r))}function aF(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return oF(e)}function oF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function wy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(wy=function(){return!!e})()}function ma(e){return ma=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ma(e)}function uF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yu(e,t)}function Yu(e,t){return Yu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Yu(e,t)}function nt(e,t,r){return t=Oy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Oy(e){var t=cF(e,"string");return kr(t)=="symbol"?t:t+""}function cF(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ar=function(e){function t(){var r;rF(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=iF(this,t,[].concat(i)),nt(r,"state",{isAnimationFinished:!0}),nt(r,"id",Qn("recharts-area-")),nt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),V(o)&&o()}),nt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),V(o)&&o()}),r}return uF(t,e),nF(t,[{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive,u=this.state.isAnimationFinished;if(o&&!u)return null;var c=this.props,s=c.dot,f=c.points,l=c.dataKey,p=Z(this.props,!1),h=Z(s,!0),y=f.map(function(d,x){var w=At(At(At({key:"dot-".concat(x),r:3},p),h),{},{index:x,cx:d.x,cy:d.y,dataKey:l,value:d.value,payload:d.payload,points:f});return t.renderDotItem(s,w)}),v={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return P.createElement(pe,Kt({className:"recharts-area-dots"},v),y)}},{key:"renderHorizontalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,c=o[0].x,s=o[o.length-1].x,f=n*Math.abs(c-s),l=$t(o.map(function(p){return p.y||0}));return R(a)&&typeof a=="number"?l=Math.max(a,l):a&&Array.isArray(a)&&a.length&&(l=Math.max($t(a.map(function(p){return p.y||0})),l)),R(l)?P.createElement("rect",{x:c<s?c:c-f,y:0,width:f,height:Math.floor(l+(u?parseInt("".concat(u),10):1))}):null}},{key:"renderVerticalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,c=o[0].y,s=o[o.length-1].y,f=n*Math.abs(c-s),l=$t(o.map(function(p){return p.x||0}));return R(a)&&typeof a=="number"?l=Math.max(a,l):a&&Array.isArray(a)&&a.length&&(l=Math.max($t(a.map(function(p){return p.x||0})),l)),R(l)?P.createElement("rect",{x:0,y:c<s?c:c-f,width:l+(u?parseInt("".concat(u),10):1),height:Math.floor(f)}):null}},{key:"renderClipRect",value:function(n){var i=this.props.layout;return i==="vertical"?this.renderVerticalRect(n):this.renderHorizontalRect(n)}},{key:"renderAreaStatically",value:function(n,i,a,o){var u=this.props,c=u.layout,s=u.type,f=u.stroke,l=u.connectNulls,p=u.isRange;u.ref;var h=xy(u,QL);return P.createElement(pe,{clipPath:a?"url(#clipPath-".concat(o,")"):null},P.createElement(dn,Kt({},Z(h,!0),{points:n,connectNulls:l,type:s,baseLine:i,layout:c,stroke:"none",className:"recharts-area-area"})),f!=="none"&&P.createElement(dn,Kt({},Z(this.props,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:l,fill:"none",points:n})),f!=="none"&&p&&P.createElement(dn,Kt({},Z(this.props,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:l,fill:"none",points:i})))}},{key:"renderAreaWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.baseLine,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,p=o.animationEasing,h=o.animationId,y=this.state,v=y.prevPoints,d=y.prevBaseLine;return P.createElement(bt,{begin:f,duration:l,isActive:s,easing:p,from:{t:0},to:{t:1},key:"area-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(x){var w=x.t;if(v){var b=v.length/u.length,O=u.map(function(A,_){var j=Math.floor(_*b);if(v[j]){var $=v[j],T=qe($.x,A.x),C=qe($.y,A.y);return At(At({},A),{},{x:T(w),y:C(w)})}return A}),m;if(R(c)&&typeof c=="number"){var g=qe(d,c);m=g(w)}else if(J(c)||qr(c)){var S=qe(d,0);m=S(w)}else m=c.map(function(A,_){var j=Math.floor(_*b);if(d[j]){var $=d[j],T=qe($.x,A.x),C=qe($.y,A.y);return At(At({},A),{},{x:T(w),y:C(w)})}return A});return a.renderAreaStatically(O,m,n,i)}return P.createElement(pe,null,P.createElement("defs",null,P.createElement("clipPath",{id:"animationClipPath-".concat(i)},a.renderClipRect(w))),P.createElement(pe,{clipPath:"url(#animationClipPath-".concat(i,")")},a.renderAreaStatically(u,c,n,i)))})}},{key:"renderArea",value:function(n,i){var a=this.props,o=a.points,u=a.baseLine,c=a.isAnimationActive,s=this.state,f=s.prevPoints,l=s.prevBaseLine,p=s.totalLength;return c&&o&&o.length&&(!f&&p>0||!Tn(f,o)||!Tn(l,u))?this.renderAreaWithAnimation(n,i):this.renderAreaStatically(o,u,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.top,f=i.left,l=i.xAxis,p=i.yAxis,h=i.width,y=i.height,v=i.isAnimationActive,d=i.id;if(a||!u||!u.length)return null;var x=this.state.isAnimationFinished,w=u.length===1,b=ee("recharts-area",c),O=l&&l.allowDataOverflow,m=p&&p.allowDataOverflow,g=O||m,S=J(d)?this.id:d,A=(n=Z(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},_=A.r,j=_===void 0?3:_,$=A.strokeWidth,T=$===void 0?2:$,C=P0(o)?o:{},I=C.clipDot,M=I===void 0?!0:I,k=j*2+T;return P.createElement(pe,{className:b},O||m?P.createElement("defs",null,P.createElement("clipPath",{id:"clipPath-".concat(S)},P.createElement("rect",{x:O?f:f-h/2,y:m?s:s-y/2,width:O?h:h*2,height:m?y:y*2})),!M&&P.createElement("clipPath",{id:"clipPath-dots-".concat(S)},P.createElement("rect",{x:f-k/2,y:s-k/2,width:h+k,height:y+k}))):null,w?null:this.renderArea(g,S),(o||w)&&this.renderDots(g,M,S),(!v||x)&&jt.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,curBaseLine:n.baseLine,prevPoints:i.curPoints,prevBaseLine:i.curBaseLine}:n.points!==i.curPoints||n.baseLine!==i.curBaseLine?{curPoints:n.points,curBaseLine:n.baseLine}:null}}])}(B.PureComponent);by=ar;nt(ar,"displayName","Area");nt(ar,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!Xr.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"});nt(ar,"getBaseValue",function(e,t,r,n){var i=e.layout,a=e.baseValue,o=t.props.baseValue,u=o??a;if(R(u)&&typeof u=="number")return u;var c=i==="horizontal"?n:r,s=c.scale.domain();if(c.type==="number"){var f=Math.max(s[0],s[1]),l=Math.min(s[0],s[1]);return u==="dataMin"?l:u==="dataMax"||f<0?f:Math.max(Math.min(s[0],s[1]),0)}return u==="dataMin"?s[0]:u==="dataMax"?s[1]:s[0]});nt(ar,"getComposedData",function(e){var t=e.props,r=e.item,n=e.xAxis,i=e.yAxis,a=e.xAxisTicks,o=e.yAxisTicks,u=e.bandSize,c=e.dataKey,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,p=e.offset,h=t.layout,y=s&&s.length,v=by.getBaseValue(t,r,n,i),d=h==="horizontal",x=!1,w=l.map(function(O,m){var g;y?g=s[f+m]:(g=Fe(O,c),Array.isArray(g)?x=!0:g=[v,g]);var S=g[1]==null||y&&Fe(O,c)==null;return d?{x:kf({axis:n,ticks:a,bandSize:u,entry:O,index:m}),y:S?null:i.scale(g[1]),value:g,payload:O}:{x:S?null:n.scale(g[1]),y:kf({axis:i,ticks:o,bandSize:u,entry:O,index:m}),value:g,payload:O}}),b;return y||x?b=w.map(function(O){var m=Array.isArray(O.value)?O.value[0]:null;return d?{x:O.x,y:m!=null&&O.y!=null?i.scale(m):null}:{x:m!=null?n.scale(m):null,y:O.y}}):b=d?i.scale(v):n.scale(v),At({points:w,baseLine:b,layout:h,isRange:x},p)});nt(ar,"renderDotItem",function(e,t){var r;if(P.isValidElement(e))r=P.cloneElement(e,t);else if(V(e))r=e(t);else{var n=ee("recharts-area-dot",typeof e!="boolean"?e.className:""),i=t.key,a=xy(t,eF);r=P.createElement(ns,Kt({},a,{key:i,className:n}))}return r});function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function sF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function lF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Py(n.key),n)}}function fF(e,t,r){return t&&lF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function pF(e,t,r){return t=ga(t),hF(e,Sy()?Reflect.construct(t,r||[],ga(e).constructor):t.apply(e,r))}function hF(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return dF(e)}function dF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Sy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Sy=function(){return!!e})()}function ga(e){return ga=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ga(e)}function vF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zu(e,t)}function Zu(e,t){return Zu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Zu(e,t)}function Ay(e,t,r){return t=Py(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Py(e){var t=yF(e,"string");return Dr(t)=="symbol"?t:t+""}function yF(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Ju(){return Ju=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ju.apply(this,arguments)}function mF(e){var t=e.xAxisId,r=us(),n=cs(),i=uy(t);return i==null?null:B.createElement(Jr,Ju({},i,{className:ee("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return pt(o,!0)}}))}var hs=function(e){function t(){return sF(this,t),pF(this,t,arguments)}return vF(t,e),fF(t,[{key:"render",value:function(){return B.createElement(mF,this.props)}}])}(B.Component);Ay(hs,"displayName","XAxis");Ay(hs,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Nr(e){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(e)}function gF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function bF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ty(n.key),n)}}function xF(e,t,r){return t&&bF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function wF(e,t,r){return t=ba(t),OF(e,_y()?Reflect.construct(t,r||[],ba(e).constructor):t.apply(e,r))}function OF(e,t){if(t&&(Nr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return SF(e)}function SF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _y(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(_y=function(){return!!e})()}function ba(e){return ba=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ba(e)}function AF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qu(e,t)}function Qu(e,t){return Qu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Qu(e,t)}function $y(e,t,r){return t=Ty(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ty(e){var t=PF(e,"string");return Nr(t)=="symbol"?t:t+""}function PF(e,t){if(Nr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function ec(){return ec=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ec.apply(this,arguments)}var _F=function(t){var r=t.yAxisId,n=us(),i=cs(),a=cy(r);return a==null?null:B.createElement(Jr,ec({},a,{className:ee("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return pt(u,!0)}}))},ds=function(e){function t(){return gF(this,t),wF(this,t,arguments)}return AF(t,e),xF(t,[{key:"render",value:function(){return B.createElement(_F,this.props)}}])}(B.Component);$y(ds,"displayName","YAxis");$y(ds,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function eh(e){return jF(e)||EF(e)||TF(e)||$F()}function $F(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function TF(e,t){if(e){if(typeof e=="string")return tc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tc(e,t)}}function EF(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function jF(e){if(Array.isArray(e))return tc(e)}function tc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var rc=function(t,r,n,i,a){var o=et(t,ls),u=et(t,no),c=[].concat(eh(o),eh(u)),s=et(t,ao),f="".concat(i,"Id"),l=i[0],p=r;if(c.length&&(p=c.reduce(function(v,d){if(d.props[f]===n&&at(d.props,"extendDomain")&&R(d.props[l])){var x=d.props[l];return[Math.min(v[0],x),Math.max(v[1],x)]}return v},p)),s.length){var h="".concat(l,"1"),y="".concat(l,"2");p=s.reduce(function(v,d){if(d.props[f]===n&&at(d.props,"extendDomain")&&R(d.props[h])&&R(d.props[y])){var x=d.props[h],w=d.props[y];return[Math.min(v[0],x,w),Math.max(v[1],x,w)]}return v},p)}return a&&a.length&&(p=a.reduce(function(v,d){return R(d)?[Math.min(v[0],d),Math.max(v[1],d)]:v},p)),p},Ey={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,p){if(typeof f!="function")throw new TypeError("The listener must be a function");var h=new i(f,l||c,p),y=r?r+s:s;return c._events[y]?c._events[y].fn?c._events[y]=[c._events[y],h]:c._events[y].push(h):(c._events[y]=h,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var p=0,h=l.length,y=new Array(h);p<h;p++)y[p]=l[p].fn;return y},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,p,h,y){var v=r?r+s:s;if(!this._events[v])return!1;var d=this._events[v],x=arguments.length,w,b;if(d.fn){switch(d.once&&this.removeListener(s,d.fn,void 0,!0),x){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,f),!0;case 3:return d.fn.call(d.context,f,l),!0;case 4:return d.fn.call(d.context,f,l,p),!0;case 5:return d.fn.call(d.context,f,l,p,h),!0;case 6:return d.fn.call(d.context,f,l,p,h,y),!0}for(b=1,w=new Array(x-1);b<x;b++)w[b-1]=arguments[b];d.fn.apply(d.context,w)}else{var O=d.length,m;for(b=0;b<O;b++)switch(d[b].once&&this.removeListener(s,d[b].fn,void 0,!0),x){case 1:d[b].fn.call(d[b].context);break;case 2:d[b].fn.call(d[b].context,f);break;case 3:d[b].fn.call(d[b].context,f,l);break;case 4:d[b].fn.call(d[b].context,f,l,p);break;default:if(!w)for(m=1,w=new Array(x-1);m<x;m++)w[m-1]=arguments[m];d[b].fn.apply(d[b].context,w)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,p){var h=r?r+s:s;if(!this._events[h])return this;if(!f)return o(this,h),this;var y=this._events[h];if(y.fn)y.fn===f&&(!p||y.once)&&(!l||y.context===l)&&o(this,h);else{for(var v=0,d=[],x=y.length;v<x;v++)(y[v].fn!==f||p&&!y[v].once||l&&y[v].context!==l)&&d.push(y[v]);d.length?this._events[h]=d.length===1?d[0]:d:o(this,h)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u})(Ey);var MF=Ey.exports;const CF=ue(MF);var ko=new CF,Do="recharts.syncMouseEvents";function Yn(e){"@babel/helpers - typeof";return Yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yn(e)}function IF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function kF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,jy(n.key),n)}}function DF(e,t,r){return t&&kF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function No(e,t,r){return t=jy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jy(e){var t=NF(e,"string");return Yn(t)=="symbol"?t:t+""}function NF(e,t){if(Yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var BF=function(){function e(){IF(this,e),No(this,"activeIndex",0),No(this,"coordinateList",[]),No(this,"layout","horizontal")}return DF(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,p=r.mouseHandlerCallback,h=p===void 0?null:p;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=s??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=h??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,p=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:p})}}}])}();function RF(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&R(n)&&R(i))return!0}return!1}function LF(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function My(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=$e(t,r,n,i),u=$e(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function FF(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,p=$e(u,c,s,l),h=$e(u,c,f,l);n=p.x,i=p.y,a=h.x,o=h.y}else return My(t);return[{x:n,y:i},{x:a,y:o}]}function Zn(e){"@babel/helpers - typeof";return Zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zn(e)}function th(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?th(Object(r),!0).forEach(function(n){zF(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):th(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zF(e,t,r){return t=WF(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function WF(e){var t=UF(e,"string");return Zn(t)=="symbol"?t:t+""}function UF(e,t){if(Zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function qF(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,p=e.chartName,h=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!h||!a||!o||p!=="ScatterChart"&&i!=="axis")return null;var y,v=dn;if(p==="ScatterChart")y=o,v=GD;else if(p==="BarChart")y=LF(l,o,c,f),v=rs;else if(l==="radial"){var d=My(o),x=d.cx,w=d.cy,b=d.radius,O=d.startAngle,m=d.endAngle;y={cx:x,cy:w,startAngle:O,endAngle:m,innerRadius:b,outerRadius:b},v=Iv}else y={points:FF(l,o,c)},v=dn;var g=xi(xi(xi(xi({stroke:"#ccc",pointerEvents:"none"},c),y),Z(h,!1)),{},{payload:u,payloadIndex:s,className:ee("recharts-tooltip-cursor",h.className)});return B.isValidElement(h)?B.cloneElement(h,g):B.createElement(v,g)}var HF=["item"],GF=["children","className","width","height","style","compact","title","desc"];function Br(e){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Br(e)}function pr(){return pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pr.apply(this,arguments)}function rh(e,t){return VF(e)||XF(e,t)||Iy(e,t)||KF()}function KF(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function XF(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function VF(e){if(Array.isArray(e))return e}function nh(e,t){if(e==null)return{};var r=YF(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function YF(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ZF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function JF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ky(n.key),n)}}function QF(e,t,r){return t&&JF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function e3(e,t,r){return t=xa(t),t3(e,Cy()?Reflect.construct(t,r||[],xa(e).constructor):t.apply(e,r))}function t3(e,t){if(t&&(Br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return r3(e)}function r3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Cy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Cy=function(){return!!e})()}function xa(e){return xa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},xa(e)}function n3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nc(e,t)}function nc(e,t){return nc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},nc(e,t)}function Rr(e){return o3(e)||a3(e)||Iy(e)||i3()}function i3(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Iy(e,t){if(e){if(typeof e=="string")return ic(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ic(e,t)}}function a3(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function o3(e){if(Array.isArray(e))return ic(e)}function ic(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ih(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ih(Object(r),!0).forEach(function(n){H(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ih(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H(e,t,r){return t=ky(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ky(e){var t=u3(e,"string");return Br(t)=="symbol"?t:t+""}function u3(e,t){if(Br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var c3={xAxis:["bottom","top"],yAxis:["left","right"]},s3={width:"100%",height:"100%"},Dy={x:0,y:0};function wi(e){return e}var l3=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},f3=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return E(E(E({},i),$e(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return E(E(E({},i),$e(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return Dy},oo=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(Rr(u),Rr(s)):u},[]);return o.length>0?o:t&&t.length&&R(i)&&R(a)?t.slice(i,a+1):[]};function Ny(e){return e==="number"?[0,"auto"]:void 0}var ac=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=oo(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var p;if(o.dataKey&&!o.allowDuplicatedCategory){var h=l===void 0?u:l;p=Oi(h,o.dataKey,i)}else p=l&&l[n]||u[n];return p?[].concat(Rr(c),[jv(s,p)]):c},[])},ah=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=l3(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=TI(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,p=ac(t,r,f,l),h=f3(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:p,activeCoordinate:h}}return null},p3=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=t.stackOffset,h=$v(f,a);return n.reduce(function(y,v){var d,x=v.type.defaultProps!==void 0?E(E({},v.type.defaultProps),v.props):v.props,w=x.type,b=x.dataKey,O=x.allowDataOverflow,m=x.allowDuplicatedCategory,g=x.scale,S=x.ticks,A=x.includeHidden,_=x[o];if(y[_])return y;var j=oo(t.data,{graphicalItems:i.filter(function(W){var K,ce=o in W.props?W.props[o]:(K=W.type.defaultProps)===null||K===void 0?void 0:K[o];return ce===_}),dataStartIndex:c,dataEndIndex:s}),$=j.length,T,C,I;RF(x.domain,O,w)&&(T=Au(x.domain,null,O),h&&(w==="number"||g!=="auto")&&(I=hn(j,b,"category")));var M=Ny(w);if(!T||T.length===0){var k,N=(k=x.domain)!==null&&k!==void 0?k:M;if(b){if(T=hn(j,b,w),w==="category"&&h){var L=v0(T);m&&L?(C=T,T=ua(0,$)):m||(T=Rf(N,T,v).reduce(function(W,K){return W.indexOf(K)>=0?W:[].concat(Rr(W),[K])},[]))}else if(w==="category")m?T=T.filter(function(W){return W!==""&&!J(W)}):T=Rf(N,T,v).reduce(function(W,K){return W.indexOf(K)>=0||K===""||J(K)?W:[].concat(Rr(W),[K])},[]);else if(w==="number"){var F=II(j,i.filter(function(W){var K,ce,ve=o in W.props?W.props[o]:(K=W.type.defaultProps)===null||K===void 0?void 0:K[o],Ne="hide"in W.props?W.props.hide:(ce=W.type.defaultProps)===null||ce===void 0?void 0:ce.hide;return ve===_&&(A||!Ne)}),b,a,f);F&&(T=F)}h&&(w==="number"||g!=="auto")&&(I=hn(j,b,"category"))}else h?T=ua(0,$):u&&u[_]&&u[_].hasStack&&w==="number"?T=p==="expand"?[0,1]:Ev(u[_].stackGroups,c,s):T=_v(j,i.filter(function(W){var K=o in W.props?W.props[o]:W.type.defaultProps[o],ce="hide"in W.props?W.props.hide:W.type.defaultProps.hide;return K===_&&(A||!ce)}),w,f,!0);if(w==="number")T=rc(l,T,_,a,S),N&&(T=Au(N,T,O));else if(w==="category"&&N){var q=N,G=T.every(function(W){return q.indexOf(W)>=0});G&&(T=q)}}return E(E({},y),{},H({},_,E(E({},x),{},{axisType:a,domain:T,categoricalDomain:I,duplicateDomain:C,originalDomain:(d=x.domain)!==null&&d!==void 0?d:M,isCategorical:h,layout:f})))},{})},h3=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=oo(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),h=p.length,y=$v(f,a),v=-1;return n.reduce(function(d,x){var w=x.type.defaultProps!==void 0?E(E({},x.type.defaultProps),x.props):x.props,b=w[o],O=Ny("number");if(!d[b]){v++;var m;return y?m=ua(0,h):u&&u[b]&&u[b].hasStack?(m=Ev(u[b].stackGroups,c,s),m=rc(l,m,b,a)):(m=Au(O,_v(p,n.filter(function(g){var S,A,_=o in g.props?g.props[o]:(S=g.type.defaultProps)===null||S===void 0?void 0:S[o],j="hide"in g.props?g.props.hide:(A=g.type.defaultProps)===null||A===void 0?void 0:A.hide;return _===b&&!j}),"number",f),i.defaultProps.allowDataOverflow),m=rc(l,m,b,a)),E(E({},d),{},H({},b,E(E({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ge(c3,"".concat(a,".").concat(v%2),null),domain:m,originalDomain:O,isCategorical:y,layout:f})))}return d},{})},d3=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),p=et(f,a),h={};return p&&p.length?h=p3(t,{axes:p,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(h=h3(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),h},v3=function(t){var r=_t(t),n=pt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:Ec(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:Ji(r,n)}},oh=function(t){var r=t.children,n=t.defaultShowTooltip,i=Re(r,Tr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},y3=function(t){return!t||!t.length?!1:t.some(function(r){var n=ht(r&&r.type);return n&&n.indexOf("Bar")>=0})},uh=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},m3=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,p=n.margin||{},h=Re(l,Tr),y=Re(l,dr),v=Object.keys(c).reduce(function(m,g){var S=c[g],A=S.orientation;return!S.mirror&&!S.hide?E(E({},m),{},H({},A,m[A]+S.width)):m},{left:p.left||0,right:p.right||0}),d=Object.keys(o).reduce(function(m,g){var S=o[g],A=S.orientation;return!S.mirror&&!S.hide?E(E({},m),{},H({},A,Ge(m,"".concat(A))+S.height)):m},{top:p.top||0,bottom:p.bottom||0}),x=E(E({},d),v),w=x.bottom;h&&(x.bottom+=h.props.height||Tr.defaultProps.height),y&&r&&(x=MI(x,i,n,r));var b=s-x.left-x.right,O=f-x.top-x.bottom;return E(E({brushBottom:w},x),{},{width:Math.max(b,0),height:Math.max(O,0)})},g3=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},b3=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,p=function(x,w){var b=w.graphicalItems,O=w.stackGroups,m=w.offset,g=w.updateId,S=w.dataStartIndex,A=w.dataEndIndex,_=x.barSize,j=x.layout,$=x.barGap,T=x.barCategoryGap,C=x.maxBarSize,I=uh(j),M=I.numericAxisName,k=I.cateAxisName,N=y3(b),L=[];return b.forEach(function(F,q){var G=oo(x.data,{graphicalItems:[F],dataStartIndex:S,dataEndIndex:A}),W=F.type.defaultProps!==void 0?E(E({},F.type.defaultProps),F.props):F.props,K=W.dataKey,ce=W.maxBarSize,ve=W["".concat(M,"Id")],Ne=W["".concat(k,"Id")],Nt={},Ce=c.reduce(function(Bt,Rt){var uo=w["".concat(Rt.axisType,"Map")],vs=W["".concat(Rt.axisType,"Id")];uo&&uo[vs]||Rt.axisType==="zAxis"||Qt();var ys=uo[vs];return E(E({},Bt),{},H(H({},Rt.axisType,ys),"".concat(Rt.axisType,"Ticks"),pt(ys)))},Nt),z=Ce[k],X=Ce["".concat(k,"Ticks")],Y=O&&O[ve]&&O[ve].hasStack&&HI(F,O[ve].stackGroups),D=ht(F.type).indexOf("Bar")>=0,he=Ji(z,X),Q=[],ge=N&&EI({barSize:_,stackGroups:O,totalSize:g3(Ce,k)});if(D){var be,Ie,St=J(ce)?C:ce,or=(be=(Ie=Ji(z,X,!0))!==null&&Ie!==void 0?Ie:St)!==null&&be!==void 0?be:0;Q=jI({barGap:$,barCategoryGap:T,bandSize:or!==he?or:he,sizeList:ge[Ne],maxBarSize:St}),or!==he&&(Q=Q.map(function(Bt){return E(E({},Bt),{},{position:E(E({},Bt.position),{},{offset:Bt.position.offset-or/2})})}))}var oi=F&&F.type&&F.type.getComposedData;oi&&L.push({props:E(E({},oi(E(E({},Ce),{},{displayedData:G,props:x,dataKey:K,item:F,bandSize:he,barPosition:Q,offset:m,stackedData:Y,layout:j,dataStartIndex:S,dataEndIndex:A}))),{},H(H(H({key:F.key||"item-".concat(q)},M,Ce[M]),k,Ce[k]),"animationId",g)),childIndex:T0(F,x.children),item:F})}),L},h=function(x,w){var b=x.props,O=x.dataStartIndex,m=x.dataEndIndex,g=x.updateId;if(!js({props:b}))return null;var S=b.children,A=b.layout,_=b.stackOffset,j=b.data,$=b.reverseStackOrder,T=uh(A),C=T.numericAxisName,I=T.cateAxisName,M=et(S,n),k=WI(j,M,"".concat(C,"Id"),"".concat(I,"Id"),_,$),N=c.reduce(function(W,K){var ce="".concat(K.axisType,"Map");return E(E({},W),{},H({},ce,d3(b,E(E({},K),{},{graphicalItems:M,stackGroups:K.axisType===C&&k,dataStartIndex:O,dataEndIndex:m}))))},{}),L=m3(E(E({},N),{},{props:b,graphicalItems:M}),w==null?void 0:w.legendBBox);Object.keys(N).forEach(function(W){N[W]=f(b,N[W],L,W.replace("Map",""),r)});var F=N["".concat(I,"Map")],q=v3(F),G=p(b,E(E({},N),{},{dataStartIndex:O,dataEndIndex:m,updateId:g,graphicalItems:M,stackGroups:k,offset:L}));return E(E({formattedGraphicalItems:G,graphicalItems:M,offset:L,stackGroups:k},q),N)},y=function(d){function x(w){var b,O,m;return ZF(this,x),m=e3(this,x,[w]),H(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),H(m,"accessibilityManager",new BF),H(m,"handleLegendBBoxUpdate",function(g){if(g){var S=m.state,A=S.dataStartIndex,_=S.dataEndIndex,j=S.updateId;m.setState(E({legendBBox:g},h({props:m.props,dataStartIndex:A,dataEndIndex:_,updateId:j},E(E({},m.state),{},{legendBBox:g}))))}}),H(m,"handleReceiveSyncEvent",function(g,S,A){if(m.props.syncId===g){if(A===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(S)}}),H(m,"handleBrushChange",function(g){var S=g.startIndex,A=g.endIndex;if(S!==m.state.dataStartIndex||A!==m.state.dataEndIndex){var _=m.state.updateId;m.setState(function(){return E({dataStartIndex:S,dataEndIndex:A},h({props:m.props,dataStartIndex:S,dataEndIndex:A,updateId:_},m.state))}),m.triggerSyncEvent({dataStartIndex:S,dataEndIndex:A})}}),H(m,"handleMouseEnter",function(g){var S=m.getMouseInfo(g);if(S){var A=E(E({},S),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var _=m.props.onMouseEnter;V(_)&&_(A,g)}}),H(m,"triggeredAfterMouseMove",function(g){var S=m.getMouseInfo(g),A=S?E(E({},S),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(A),m.triggerSyncEvent(A);var _=m.props.onMouseMove;V(_)&&_(A,g)}),H(m,"handleItemMouseEnter",function(g){m.setState(function(){return{isTooltipActive:!0,activeItem:g,activePayload:g.tooltipPayload,activeCoordinate:g.tooltipPosition||{x:g.cx,y:g.cy}}})}),H(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),H(m,"handleMouseMove",function(g){g.persist(),m.throttleTriggeredAfterMouseMove(g)}),H(m,"handleMouseLeave",function(g){m.throttleTriggeredAfterMouseMove.cancel();var S={isTooltipActive:!1};m.setState(S),m.triggerSyncEvent(S);var A=m.props.onMouseLeave;V(A)&&A(S,g)}),H(m,"handleOuterEvent",function(g){var S=$0(g),A=Ge(m.props,"".concat(S));if(S&&V(A)){var _,j;/.*touch.*/i.test(S)?j=m.getMouseInfo(g.changedTouches[0]):j=m.getMouseInfo(g),A((_=j)!==null&&_!==void 0?_:{},g)}}),H(m,"handleClick",function(g){var S=m.getMouseInfo(g);if(S){var A=E(E({},S),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var _=m.props.onClick;V(_)&&_(A,g)}}),H(m,"handleMouseDown",function(g){var S=m.props.onMouseDown;if(V(S)){var A=m.getMouseInfo(g);S(A,g)}}),H(m,"handleMouseUp",function(g){var S=m.props.onMouseUp;if(V(S)){var A=m.getMouseInfo(g);S(A,g)}}),H(m,"handleTouchMove",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(g.changedTouches[0])}),H(m,"handleTouchStart",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseDown(g.changedTouches[0])}),H(m,"handleTouchEnd",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseUp(g.changedTouches[0])}),H(m,"handleDoubleClick",function(g){var S=m.props.onDoubleClick;if(V(S)){var A=m.getMouseInfo(g);S(A,g)}}),H(m,"handleContextMenu",function(g){var S=m.props.onContextMenu;if(V(S)){var A=m.getMouseInfo(g);S(A,g)}}),H(m,"triggerSyncEvent",function(g){m.props.syncId!==void 0&&ko.emit(Do,m.props.syncId,g,m.eventEmitterSymbol)}),H(m,"applySyncEvent",function(g){var S=m.props,A=S.layout,_=S.syncMethod,j=m.state.updateId,$=g.dataStartIndex,T=g.dataEndIndex;if(g.dataStartIndex!==void 0||g.dataEndIndex!==void 0)m.setState(E({dataStartIndex:$,dataEndIndex:T},h({props:m.props,dataStartIndex:$,dataEndIndex:T,updateId:j},m.state)));else if(g.activeTooltipIndex!==void 0){var C=g.chartX,I=g.chartY,M=g.activeTooltipIndex,k=m.state,N=k.offset,L=k.tooltipTicks;if(!N)return;if(typeof _=="function")M=_(L,g);else if(_==="value"){M=-1;for(var F=0;F<L.length;F++)if(L[F].value===g.activeLabel){M=F;break}}var q=E(E({},N),{},{x:N.left,y:N.top}),G=Math.min(C,q.x+q.width),W=Math.min(I,q.y+q.height),K=L[M]&&L[M].value,ce=ac(m.state,m.props.data,M),ve=L[M]?{x:A==="horizontal"?L[M].coordinate:G,y:A==="horizontal"?W:L[M].coordinate}:Dy;m.setState(E(E({},g),{},{activeLabel:K,activeCoordinate:ve,activePayload:ce,activeTooltipIndex:M}))}else m.setState(g)}),H(m,"renderCursor",function(g){var S,A=m.state,_=A.isTooltipActive,j=A.activeCoordinate,$=A.activePayload,T=A.offset,C=A.activeTooltipIndex,I=A.tooltipAxisBandSize,M=m.getTooltipEventType(),k=(S=g.props.active)!==null&&S!==void 0?S:_,N=m.props.layout,L=g.key||"_recharts-cursor";return P.createElement(qF,{key:L,activeCoordinate:j,activePayload:$,activeTooltipIndex:C,chartName:r,element:g,isActive:k,layout:N,offset:T,tooltipAxisBandSize:I,tooltipEventType:M})}),H(m,"renderPolarAxis",function(g,S,A){var _=Ge(g,"type.axisType"),j=Ge(m.state,"".concat(_,"Map")),$=g.type.defaultProps,T=$!==void 0?E(E({},$),g.props):g.props,C=j&&j[T["".concat(_,"Id")]];return B.cloneElement(g,E(E({},C),{},{className:ee(_,C.className),key:g.key||"".concat(S,"-").concat(A),ticks:pt(C,!0)}))}),H(m,"renderPolarGrid",function(g){var S=g.props,A=S.radialLines,_=S.polarAngles,j=S.polarRadius,$=m.state,T=$.radiusAxisMap,C=$.angleAxisMap,I=_t(T),M=_t(C),k=M.cx,N=M.cy,L=M.innerRadius,F=M.outerRadius;return B.cloneElement(g,{polarAngles:Array.isArray(_)?_:pt(M,!0).map(function(q){return q.coordinate}),polarRadius:Array.isArray(j)?j:pt(I,!0).map(function(q){return q.coordinate}),cx:k,cy:N,innerRadius:L,outerRadius:F,key:g.key||"polar-grid",radialLines:A})}),H(m,"renderLegend",function(){var g=m.state.formattedGraphicalItems,S=m.props,A=S.children,_=S.width,j=S.height,$=m.props.margin||{},T=_-($.left||0)-($.right||0),C=Av({children:A,formattedGraphicalItems:g,legendWidth:T,legendContent:s});if(!C)return null;var I=C.item,M=nh(C,HF);return B.cloneElement(I,E(E({},M),{},{chartWidth:_,chartHeight:j,margin:$,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),H(m,"renderTooltip",function(){var g,S=m.props,A=S.children,_=S.accessibilityLayer,j=Re(A,st);if(!j)return null;var $=m.state,T=$.isTooltipActive,C=$.activeCoordinate,I=$.activePayload,M=$.activeLabel,k=$.offset,N=(g=j.props.active)!==null&&g!==void 0?g:T;return B.cloneElement(j,{viewBox:E(E({},k),{},{x:k.left,y:k.top}),active:N,label:M,payload:N?I:[],coordinate:C,accessibilityLayer:_})}),H(m,"renderBrush",function(g){var S=m.props,A=S.margin,_=S.data,j=m.state,$=j.offset,T=j.dataStartIndex,C=j.dataEndIndex,I=j.updateId;return B.cloneElement(g,{key:g.key||"_recharts-brush",onChange:yi(m.handleBrushChange,g.props.onChange),data:_,x:R(g.props.x)?g.props.x:$.left,y:R(g.props.y)?g.props.y:$.top+$.height+$.brushBottom-(A.bottom||0),width:R(g.props.width)?g.props.width:$.width,startIndex:T,endIndex:C,updateId:"brush-".concat(I)})}),H(m,"renderReferenceElement",function(g,S,A){if(!g)return null;var _=m,j=_.clipPathId,$=m.state,T=$.xAxisMap,C=$.yAxisMap,I=$.offset,M=g.type.defaultProps||{},k=g.props,N=k.xAxisId,L=N===void 0?M.xAxisId:N,F=k.yAxisId,q=F===void 0?M.yAxisId:F;return B.cloneElement(g,{key:g.key||"".concat(S,"-").concat(A),xAxis:T[L],yAxis:C[q],viewBox:{x:I.left,y:I.top,width:I.width,height:I.height},clipPathId:j})}),H(m,"renderActivePoints",function(g){var S=g.item,A=g.activePoint,_=g.basePoint,j=g.childIndex,$=g.isRange,T=[],C=S.props.key,I=S.item.type.defaultProps!==void 0?E(E({},S.item.type.defaultProps),S.item.props):S.item.props,M=I.activeDot,k=I.dataKey,N=E(E({index:j,dataKey:k,cx:A.x,cy:A.y,r:4,fill:ts(S.item),strokeWidth:2,stroke:"#fff",payload:A.payload,value:A.value},Z(M,!1)),Si(M));return T.push(x.renderActiveDot(M,N,"".concat(C,"-activePoint-").concat(j))),_?T.push(x.renderActiveDot(M,E(E({},N),{},{cx:_.x,cy:_.y}),"".concat(C,"-basePoint-").concat(j))):$&&T.push(null),T}),H(m,"renderGraphicChild",function(g,S,A){var _=m.filterFormatItem(g,S,A);if(!_)return null;var j=m.getTooltipEventType(),$=m.state,T=$.isTooltipActive,C=$.tooltipAxis,I=$.activeTooltipIndex,M=$.activeLabel,k=m.props.children,N=Re(k,st),L=_.props,F=L.points,q=L.isRange,G=L.baseLine,W=_.item.type.defaultProps!==void 0?E(E({},_.item.type.defaultProps),_.item.props):_.item.props,K=W.activeDot,ce=W.hide,ve=W.activeBar,Ne=W.activeShape,Nt=!!(!ce&&T&&N&&(K||ve||Ne)),Ce={};j!=="axis"&&N&&N.props.trigger==="click"?Ce={onClick:yi(m.handleItemMouseEnter,g.props.onClick)}:j!=="axis"&&(Ce={onMouseLeave:yi(m.handleItemMouseLeave,g.props.onMouseLeave),onMouseEnter:yi(m.handleItemMouseEnter,g.props.onMouseEnter)});var z=B.cloneElement(g,E(E({},_.props),Ce));function X(Rt){return typeof C.dataKey=="function"?C.dataKey(Rt.payload):null}if(Nt)if(I>=0){var Y,D;if(C.dataKey&&!C.allowDuplicatedCategory){var he=typeof C.dataKey=="function"?X:"payload.".concat(C.dataKey.toString());Y=Oi(F,he,M),D=q&&G&&Oi(G,he,M)}else Y=F==null?void 0:F[I],D=q&&G&&G[I];if(Ne||ve){var Q=g.props.activeIndex!==void 0?g.props.activeIndex:I;return[B.cloneElement(g,E(E(E({},_.props),Ce),{},{activeIndex:Q})),null,null]}if(!J(Y))return[z].concat(Rr(m.renderActivePoints({item:_,activePoint:Y,basePoint:D,childIndex:I,isRange:q})))}else{var ge,be=(ge=m.getItemByXY(m.state.activeCoordinate))!==null&&ge!==void 0?ge:{graphicalItem:z},Ie=be.graphicalItem,St=Ie.item,or=St===void 0?g:St,oi=Ie.childIndex,Bt=E(E(E({},_.props),Ce),{},{activeIndex:oi});return[B.cloneElement(or,Bt),null,null]}return q?[z,null,null]:[z,null]}),H(m,"renderCustomized",function(g,S,A){return B.cloneElement(g,E(E({key:"recharts-customized-".concat(A)},m.props),m.state))}),H(m,"renderMap",{CartesianGrid:{handler:wi,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:wi},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:wi},YAxis:{handler:wi},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((b=w.id)!==null&&b!==void 0?b:Qn("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=Sd(m.triggeredAfterMouseMove,(O=w.throttleDelay)!==null&&O!==void 0?O:1e3/60),m.state={},m}return n3(x,d),QF(x,[{key:"componentDidMount",value:function(){var b,O;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(b=this.props.margin.left)!==null&&b!==void 0?b:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var b=this.props,O=b.children,m=b.data,g=b.height,S=b.layout,A=Re(O,st);if(A){var _=A.props.defaultIndex;if(!(typeof _!="number"||_<0||_>this.state.tooltipTicks.length-1)){var j=this.state.tooltipTicks[_]&&this.state.tooltipTicks[_].value,$=ac(this.state,m,_,j),T=this.state.tooltipTicks[_].coordinate,C=(this.state.offset.top+g)/2,I=S==="horizontal",M=I?{x:T,y:C}:{y:T,x:C},k=this.state.formattedGraphicalItems.find(function(L){var F=L.item;return F.type.name==="Scatter"});k&&(M=E(E({},M),k.props.points[_].tooltipPosition),$=k.props.points[_].tooltipPayload);var N={activeTooltipIndex:_,isTooltipActive:!0,activeLabel:j,activePayload:$,activeCoordinate:M};this.setState(N),this.renderCursor(A),this.accessibilityManager.setIndex(_)}}}},{key:"getSnapshotBeforeUpdate",value:function(b,O){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==O.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==b.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==b.margin){var m,g;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(g=this.props.margin.top)!==null&&g!==void 0?g:0}})}return null}},{key:"componentDidUpdate",value:function(b){Lo([Re(b.children,st)],[Re(this.props.children,st)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var b=Re(this.props.children,st);if(b&&typeof b.props.shared=="boolean"){var O=b.props.shared?"axis":"item";return u.indexOf(O)>=0?O:a}return a}},{key:"getMouseInfo",value:function(b){if(!this.container)return null;var O=this.container,m=O.getBoundingClientRect(),g=eE(m),S={chartX:Math.round(b.pageX-g.left),chartY:Math.round(b.pageY-g.top)},A=m.width/O.offsetWidth||1,_=this.inRange(S.chartX,S.chartY,A);if(!_)return null;var j=this.state,$=j.xAxisMap,T=j.yAxisMap,C=this.getTooltipEventType(),I=ah(this.state,this.props.data,this.props.layout,_);if(C!=="axis"&&$&&T){var M=_t($).scale,k=_t(T).scale,N=M&&M.invert?M.invert(S.chartX):null,L=k&&k.invert?k.invert(S.chartY):null;return E(E({},S),{},{xValue:N,yValue:L},I)}return I?E(E({},S),I):null}},{key:"inRange",value:function(b,O){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=this.props.layout,S=b/m,A=O/m;if(g==="horizontal"||g==="vertical"){var _=this.state.offset,j=S>=_.left&&S<=_.left+_.width&&A>=_.top&&A<=_.top+_.height;return j?{x:S,y:A}:null}var $=this.state,T=$.angleAxisMap,C=$.radiusAxisMap;if(T&&C){var I=_t(T);return zf({x:S,y:A},I)}return null}},{key:"parseEventsOfWrapper",value:function(){var b=this.props.children,O=this.getTooltipEventType(),m=Re(b,st),g={};m&&O==="axis"&&(m.props.trigger==="click"?g={onClick:this.handleClick}:g={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var S=Si(this.props,this.handleOuterEvent);return E(E({},S),g)}},{key:"addListener",value:function(){ko.on(Do,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){ko.removeListener(Do,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(b,O,m){for(var g=this.state.formattedGraphicalItems,S=0,A=g.length;S<A;S++){var _=g[S];if(_.item===b||_.props.key===b.key||O===ht(_.item.type)&&m===_.childIndex)return _}return null}},{key:"renderClipPath",value:function(){var b=this.clipPathId,O=this.state.offset,m=O.left,g=O.top,S=O.height,A=O.width;return P.createElement("defs",null,P.createElement("clipPath",{id:b},P.createElement("rect",{x:m,y:g,height:S,width:A})))}},{key:"getXScales",value:function(){var b=this.state.xAxisMap;return b?Object.entries(b).reduce(function(O,m){var g=rh(m,2),S=g[0],A=g[1];return E(E({},O),{},H({},S,A.scale))},{}):null}},{key:"getYScales",value:function(){var b=this.state.yAxisMap;return b?Object.entries(b).reduce(function(O,m){var g=rh(m,2),S=g[0],A=g[1];return E(E({},O),{},H({},S,A.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(b){var O;return(O=this.state.xAxisMap)===null||O===void 0||(O=O[b])===null||O===void 0?void 0:O.scale}},{key:"getYScaleByAxisId",value:function(b){var O;return(O=this.state.yAxisMap)===null||O===void 0||(O=O[b])===null||O===void 0?void 0:O.scale}},{key:"getItemByXY",value:function(b){var O=this.state,m=O.formattedGraphicalItems,g=O.activeItem;if(m&&m.length)for(var S=0,A=m.length;S<A;S++){var _=m[S],j=_.props,$=_.item,T=$.type.defaultProps!==void 0?E(E({},$.type.defaultProps),$.props):$.props,C=ht($.type);if(C==="Bar"){var I=(j.data||[]).find(function(L){return ND(b,L)});if(I)return{graphicalItem:_,payload:I}}else if(C==="RadialBar"){var M=(j.data||[]).find(function(L){return zf(b,L)});if(M)return{graphicalItem:_,payload:M}}else if(Qa(_,g)||eo(_,g)||Hn(_,g)){var k=LN({graphicalItem:_,activeTooltipItem:g,itemData:T.data}),N=T.activeIndex===void 0?k:T.activeIndex;return{graphicalItem:E(E({},_),{},{childIndex:N}),payload:Hn(_,g)?T.data[k]:_.props.data[k]}}}return null}},{key:"render",value:function(){var b=this;if(!js(this))return null;var O=this.props,m=O.children,g=O.className,S=O.width,A=O.height,_=O.style,j=O.compact,$=O.title,T=O.desc,C=nh(O,GF),I=Z(C,!1);if(j)return P.createElement(Lp,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},P.createElement(zo,pr({},I,{width:S,height:A,title:$,desc:T}),this.renderClipPath(),Cs(m,this.renderMap)));if(this.props.accessibilityLayer){var M,k;I.tabIndex=(M=this.props.tabIndex)!==null&&M!==void 0?M:0,I.role=(k=this.props.role)!==null&&k!==void 0?k:"application",I.onKeyDown=function(L){b.accessibilityManager.keyboardEvent(L)},I.onFocus=function(){b.accessibilityManager.focus()}}var N=this.parseEventsOfWrapper();return P.createElement(Lp,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},P.createElement("div",pr({className:ee("recharts-wrapper",g),style:E({position:"relative",cursor:"default",width:S,height:A},_)},N,{ref:function(F){b.container=F}}),P.createElement(zo,pr({},I,{width:S,height:A,title:$,desc:T,style:s3}),this.renderClipPath(),Cs(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(B.Component);H(y,"displayName",r),H(y,"defaultProps",E({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),H(y,"getDerivedStateFromProps",function(d,x){var w=d.dataKey,b=d.data,O=d.children,m=d.width,g=d.height,S=d.layout,A=d.stackOffset,_=d.margin,j=x.dataStartIndex,$=x.dataEndIndex;if(x.updateId===void 0){var T=oh(d);return E(E(E({},T),{},{updateId:0},h(E(E({props:d},T),{},{updateId:0}),x)),{},{prevDataKey:w,prevData:b,prevWidth:m,prevHeight:g,prevLayout:S,prevStackOffset:A,prevMargin:_,prevChildren:O})}if(w!==x.prevDataKey||b!==x.prevData||m!==x.prevWidth||g!==x.prevHeight||S!==x.prevLayout||A!==x.prevStackOffset||!hr(_,x.prevMargin)){var C=oh(d),I={chartX:x.chartX,chartY:x.chartY,isTooltipActive:x.isTooltipActive},M=E(E({},ah(x,b,S)),{},{updateId:x.updateId+1}),k=E(E(E({},C),I),M);return E(E(E({},k),h(E({props:d},k),x)),{},{prevDataKey:w,prevData:b,prevWidth:m,prevHeight:g,prevLayout:S,prevStackOffset:A,prevMargin:_,prevChildren:O})}if(!Lo(O,x.prevChildren)){var N,L,F,q,G=Re(O,Tr),W=G&&(N=(L=G.props)===null||L===void 0?void 0:L.startIndex)!==null&&N!==void 0?N:j,K=G&&(F=(q=G.props)===null||q===void 0?void 0:q.endIndex)!==null&&F!==void 0?F:$,ce=W!==j||K!==$,ve=!J(b),Ne=ve&&!ce?x.updateId:x.updateId+1;return E(E({updateId:Ne},h(E(E({props:d},x),{},{updateId:Ne,dataStartIndex:W,dataEndIndex:K}),x)),{},{prevChildren:O,dataStartIndex:W,dataEndIndex:K})}return null}),H(y,"renderActiveDot",function(d,x,w){var b;return B.isValidElement(d)?b=B.cloneElement(d,x):V(d)?b=d(x):b=P.createElement(ns,x),P.createElement(pe,{className:"recharts-active-dot",key:w},b)});var v=B.forwardRef(function(x,w){return P.createElement(y,pr({},x,{ref:w}))});return v.displayName=y.displayName,v},O3=b3({chartName:"AreaChart",GraphicalChild:ar,axisComponents:[{axisType:"xAxis",AxisComp:hs},{axisType:"yAxis",AxisComp:ds}],formatAxisMap:lR});export{O3 as A,JL as C,dr as L,w3 as R,st as T,hs as X,ds as Y,ar as a,ee as c};
//# sourceMappingURL=charts-CBLeWV67.js.map
